{"options": {"routeBasePath": "/", "sidebarPath": "/Users/<USER>/Desktop/AdMesh/protocol/admesh-docs/sidebars.js", "editUrl": "https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/", "path": "docs", "editCurrentVersion": false, "editLocalizedFiles": false, "tagsBasePath": "tags", "include": ["**/*.{md,mdx}"], "exclude": ["**/_*.{js,jsx,ts,tsx,md,mdx}", "**/_*/**", "**/*.test.{js,jsx,ts,tsx}", "**/__tests__/**"], "sidebarCollapsible": true, "sidebarCollapsed": true, "docsRootComponent": "@theme/DocsRoot", "docVersionRootComponent": "@theme/DocVersionRoot", "docRootComponent": "@theme/DocRoot", "docItemComponent": "@theme/DocItem", "docTagsListComponent": "@theme/DocTagsListPage", "docTagDocListComponent": "@theme/DocTagDocListPage", "docCategoryGeneratedIndexComponent": "@theme/DocCategoryGeneratedIndexPage", "remarkPlugins": [], "rehypePlugins": [], "recmaPlugins": [], "beforeDefaultRemarkPlugins": [], "beforeDefaultRehypePlugins": [], "admonitions": true, "showLastUpdateTime": false, "showLastUpdateAuthor": false, "includeCurrentVersion": true, "disableVersioning": false, "versions": {}, "breadcrumbs": true, "onInlineTags": "warn", "id": "default"}, "versionsMetadata": [{"versionName": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "path": "/", "tagsPath": "/tags", "editUrl": "https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs", "editUrlLocalized": "https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/i18n/en/docusaurus-plugin-content-docs/current", "isLast": true, "routePriority": -1, "sidebarFilePath": "/Users/<USER>/Desktop/AdMesh/protocol/admesh-docs/sidebars.js", "contentPath": "/Users/<USER>/Desktop/AdMesh/protocol/admesh-docs/docs", "contentPathLocalized": "/Users/<USER>/Desktop/AdMesh/protocol/admesh-docs/i18n/en/docusaurus-plugin-content-docs/current"}]}