{"version": {"pluginId": "default", "version": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "isLast": true, "docsSidebars": {"tutorialSidebar": [{"type": "link", "label": "Introduction", "href": "/", "docId": "intro", "unlisted": false}, {"type": "category", "label": "Getting Started", "items": [{"type": "link", "label": "Overview", "href": "/getting-started/overview", "docId": "getting-started/overview", "unlisted": false}, {"type": "link", "label": "Getting Your API Key", "href": "/getting-started/api-keys", "docId": "getting-started/api-keys", "unlisted": false}, {"type": "link", "label": "Quick Start Guide", "href": "/getting-started/quick-start", "docId": "getting-started/quick-start", "unlisted": false}, {"type": "link", "label": "<PERSON><PERSON>esh <PERSON> Formats", "href": "/getting-started/ad-formats", "docId": "getting-started/ad-formats", "unlisted": false}, {"type": "link", "label": "AdMesh vs Traditional Advertising", "href": "/getting-started/admesh-vs-traditional", "docId": "getting-started/admesh-vs-traditional", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Python SDK", "items": [{"type": "link", "label": "Python SDK Installation", "href": "/python-sdk/installation", "docId": "python-sdk/installation", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "TypeScript SDK", "items": [{"type": "link", "label": "TypeScript SDK Installation", "href": "/typescript-sdk/installation", "docId": "typescript-sdk/installation", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "UI SDK", "items": [{"type": "link", "label": "UI SDK Installation", "href": "/ui-sdk/installation", "docId": "ui-sdk/installation", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "AI Agent Integration", "items": [{"type": "link", "label": "AI Agent Integration Overview", "href": "/ai-integration/overview", "docId": "ai-integration/overview", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "API Reference", "items": [{"type": "link", "label": "Authentication", "href": "/api/authentication", "docId": "api/authentication", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Examples", "items": [{"type": "link", "label": "AI Assistant Integration", "href": "/examples/ai-assistant", "docId": "examples/ai-assistant", "unlisted": false}], "collapsed": true, "collapsible": true}]}, "docs": {"ai-integration/overview": {"id": "ai-integration/overview", "title": "AI Agent Integration Overview", "description": "AdMesh provides enterprise-grade integration capabilities for AI applications, conversational interfaces, and intelligent agent systems. This guide covers technical implementation patterns for integrating AdMesh into AI platforms.", "sidebar": "tutorialSidebar"}, "api/authentication": {"id": "api/authentication", "title": "Authentication", "description": "Technical documentation for AdMesh API authentication using API keys and error handling procedures.", "sidebar": "tutorialSidebar"}, "examples/ai-assistant": {"id": "examples/ai-assistant", "title": "AI Assistant Integration", "description": "Technical implementation guide for building AI assistants with intelligent product recommendation capabilities using AdMesh. This example demonstrates production-ready conversational AI with contextual product suggestions.", "sidebar": "tutorialSidebar"}, "getting-started/ad-formats": {"id": "getting-started/ad-formats", "title": "<PERSON><PERSON>esh <PERSON> Formats", "description": "Learn about AdMesh's unique approach to advertising through conversational, citation-based, and contextual ad formats that differ fundamentally from traditional push/pull advertising models.", "sidebar": "tutorialSidebar"}, "getting-started/admesh-vs-traditional": {"id": "getting-started/admesh-vs-traditional", "title": "AdMesh vs Traditional Advertising", "description": "A comprehensive comparison showing how Ad<PERSON>esh's contextual intelligence model differs from traditional push and pull advertising approaches.", "sidebar": "tutorialSidebar"}, "getting-started/api-keys": {"id": "getting-started/api-keys", "title": "Getting Your API Key", "description": "To use AdMesh APIs and SDKs, you'll need an API key. This guide walks you through the process of creating an account and obtaining your API key.", "sidebar": "tutorialSidebar"}, "getting-started/overview": {"id": "getting-started/overview", "title": "Overview", "description": "This guide provides technical overview and core concepts for integrating AdMesh AI-powered product recommendation capabilities into enterprise applications.", "sidebar": "tutorialSidebar"}, "getting-started/quick-start": {"id": "getting-started/quick-start", "title": "Quick Start Guide", "description": "Get up and running with <PERSON><PERSON><PERSON> in under 5 minutes! This guide will walk you through making your first recommendation request using any of our SDKs.", "sidebar": "tutorialSidebar"}, "intro": {"id": "intro", "title": "AdMesh SDK Documentation", "description": "AdMesh SDK Documentation provides comprehensive technical guidance for integrating AI-powered product recommendation capabilities into enterprise applications and AI systems.", "sidebar": "tutorialSidebar"}, "python-sdk/installation": {"id": "python-sdk/installation", "title": "Python SDK Installation", "description": "The AdMesh Python SDK provides programmatic access to the AdMesh REST API for Python 3.8+ applications. The SDK includes comprehensive type definitions for all request parameters and response fields, with support for both synchronous and asynchronous operations.", "sidebar": "tutorialSidebar"}, "typescript-sdk/installation": {"id": "typescript-sdk/installation", "title": "TypeScript SDK Installation", "description": "Technical guide for installing and configuring the AdMesh TypeScript SDK for Node.js applications and serverless functions.", "sidebar": "tutorialSidebar"}, "ui-sdk/installation": {"id": "ui-sdk/installation", "title": "UI SDK Installation", "description": "The AdMesh UI SDK is a production-ready React + TypeScript component library for displaying product recommendations with integrated tracking, theming, and conversational interface support.", "sidebar": "tutorialSidebar"}}}}