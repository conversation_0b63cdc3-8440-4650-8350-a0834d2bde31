{"version": {"pluginId": "default", "version": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "isLast": true, "docsSidebars": {"tutorialSidebar": [{"type": "link", "label": "Introduction", "href": "/", "docId": "intro", "unlisted": false}, {"type": "category", "label": "Getting Started", "items": [{"type": "link", "label": "Overview", "href": "/getting-started/overview", "docId": "getting-started/overview", "unlisted": false}, {"type": "link", "label": "Getting Your API Key", "href": "/getting-started/api-keys", "docId": "getting-started/api-keys", "unlisted": false}, {"type": "link", "label": "Quick Start Guide", "href": "/getting-started/quick-start", "docId": "getting-started/quick-start", "unlisted": false}, {"type": "link", "label": "<PERSON><PERSON>esh <PERSON> Formats", "href": "/getting-started/ad-formats", "docId": "getting-started/ad-formats", "unlisted": false}, {"type": "link", "label": "AdMesh vs Traditional Advertising", "href": "/getting-started/admesh-vs-traditional", "docId": "getting-started/admesh-vs-traditional", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Python SDK", "items": [{"type": "link", "label": "Python SDK Installation", "href": "/python-sdk/installation", "docId": "python-sdk/installation", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "UI SDK", "items": [{"type": "link", "label": "UI SDK Installation", "href": "/ui-sdk/installation", "docId": "ui-sdk/installation", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "AI Agent Integration", "items": [{"type": "link", "label": "AI Agent Integration Overview", "href": "/ai-integration/overview", "docId": "ai-integration/overview", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "API Reference", "items": [{"type": "link", "label": "Authentication", "href": "/api/authentication", "docId": "api/authentication", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Examples", "items": [{"type": "link", "label": "AI Assistant Integration", "href": "/examples/ai-assistant", "docId": "examples/ai-assistant", "unlisted": false}], "collapsed": true, "collapsible": true}]}, "docs": {"ai-integration/overview": {"id": "ai-integration/overview", "title": "AI Agent Integration Overview", "description": "AdMesh is specifically designed for AI applications, chatbots, and intelligent agents. This guide covers how to integrate AdMesh into various AI platforms and create intelligent recommendation systems.", "sidebar": "tutorialSidebar"}, "api/authentication": {"id": "api/authentication", "title": "Authentication", "description": "Learn how to authenticate with the AdMesh API using API keys and handle authentication errors.", "sidebar": "tutorialSidebar"}, "examples/ai-assistant": {"id": "examples/ai-assistant", "title": "AI Assistant Integration", "description": "Learn how to build an AI assistant with intelligent product recommendations using AdMesh. This example shows how to create a conversational AI that provides contextual product suggestions.", "sidebar": "tutorialSidebar"}, "getting-started/ad-formats": {"id": "getting-started/ad-formats", "title": "<PERSON><PERSON>esh <PERSON> Formats", "description": "Learn about AdMesh's unique approach to advertising through conversational, citation-based, and contextual ad formats that differ fundamentally from traditional push/pull advertising models.", "sidebar": "tutorialSidebar"}, "getting-started/admesh-vs-traditional": {"id": "getting-started/admesh-vs-traditional", "title": "AdMesh vs Traditional Advertising", "description": "A comprehensive comparison showing how Ad<PERSON>esh's contextual intelligence model differs from traditional push and pull advertising approaches.", "sidebar": "tutorialSidebar"}, "getting-started/api-keys": {"id": "getting-started/api-keys", "title": "Getting Your API Key", "description": "To use AdMesh APIs and SDKs, you'll need an API key. This guide walks you through the process of creating an account and obtaining your API key.", "sidebar": "tutorialSidebar"}, "getting-started/overview": {"id": "getting-started/overview", "title": "Overview", "description": "Welcome to AdMesh! This guide will help you understand the core concepts and get started with integrating AI-powered product recommendations into your applications.", "sidebar": "tutorialSidebar"}, "getting-started/quick-start": {"id": "getting-started/quick-start", "title": "Quick Start Guide", "description": "Get up and running with <PERSON><PERSON><PERSON> in under 5 minutes! This guide will walk you through making your first recommendation request using any of our SDKs.", "sidebar": "tutorialSidebar"}, "intro": {"id": "intro", "title": "AdMesh SDK Documentation", "description": "Welcome to the AdMesh SDK Documentation - your comprehensive guide to integrating AI-powered product recommendations into your applications.", "sidebar": "tutorialSidebar"}, "python-sdk/installation": {"id": "python-sdk/installation", "title": "Python SDK Installation", "description": "The AdMesh Python SDK provides convenient access to the AdMesh REST API from any Python 3.8+ application. It includes type definitions for all request params and response fields, and offers both synchronous and asynchronous clients.", "sidebar": "tutorialSidebar"}, "ui-sdk/installation": {"id": "ui-sdk/installation", "title": "UI SDK Installation", "description": "The AdMesh UI SDK is a React + TypeScript component library for displaying product recommendations with built-in tracking, theming, and conversational ad support.", "sidebar": "tutorialSidebar"}}}}