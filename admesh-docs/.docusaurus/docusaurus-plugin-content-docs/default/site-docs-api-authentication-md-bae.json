{"id": "api/authentication", "title": "Authentication", "description": "Technical documentation for AdMesh API authentication using API keys and error handling procedures.", "source": "@site/docs/api/authentication.md", "sourceDirName": "api", "slug": "/api/authentication", "permalink": "/api/authentication", "draft": false, "unlisted": false, "editUrl": "https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/api/authentication.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "previous": {"title": "AI Agent Integration Overview", "permalink": "/ai-integration/overview"}, "next": {"title": "AI Assistant Integration", "permalink": "/examples/ai-assistant"}}