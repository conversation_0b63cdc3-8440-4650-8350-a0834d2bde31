{"allContent": {"docusaurus-plugin-content-docs": {"default": {"loadedVersions": [{"versionName": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "path": "/", "tagsPath": "/tags", "editUrl": "https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs", "editUrlLocalized": "https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/i18n/en/docusaurus-plugin-content-docs/current", "isLast": true, "routePriority": -1, "sidebarFilePath": "/Users/<USER>/Desktop/AdMesh/protocol/admesh-docs/sidebars.js", "contentPath": "/Users/<USER>/Desktop/AdMesh/protocol/admesh-docs/docs", "contentPathLocalized": "/Users/<USER>/Desktop/AdMesh/protocol/admesh-docs/i18n/en/docusaurus-plugin-content-docs/current", "docs": [{"id": "ai-integration/overview", "title": "AI Agent Integration Overview", "description": "AdMesh provides enterprise-grade integration capabilities for AI applications, conversational interfaces, and intelligent agent systems. This guide covers technical implementation patterns for integrating AdMesh into AI platforms.", "source": "@site/docs/ai-integration/overview.md", "sourceDirName": "ai-integration", "slug": "/ai-integration/overview", "permalink": "/ai-integration/overview", "draft": false, "unlisted": false, "editUrl": "https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/ai-integration/overview.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "previous": {"title": "UI SDK Installation", "permalink": "/ui-sdk/installation"}, "next": {"title": "Authentication", "permalink": "/api/authentication"}}, {"id": "api/authentication", "title": "Authentication", "description": "Technical documentation for AdMesh API authentication using API keys and error handling procedures.", "source": "@site/docs/api/authentication.md", "sourceDirName": "api", "slug": "/api/authentication", "permalink": "/api/authentication", "draft": false, "unlisted": false, "editUrl": "https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/api/authentication.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "previous": {"title": "AI Agent Integration Overview", "permalink": "/ai-integration/overview"}, "next": {"title": "AI Assistant Integration", "permalink": "/examples/ai-assistant"}}, {"id": "examples/ai-assistant", "title": "AI Assistant Integration", "description": "Technical implementation guide for building AI assistants with intelligent product recommendation capabilities using AdMesh. This example demonstrates production-ready conversational AI with contextual product suggestions.", "source": "@site/docs/examples/ai-assistant.md", "sourceDirName": "examples", "slug": "/examples/ai-assistant", "permalink": "/examples/ai-assistant", "draft": false, "unlisted": false, "editUrl": "https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/examples/ai-assistant.md", "tags": [], "version": "current", "sidebarPosition": 4, "frontMatter": {"sidebar_position": 4}, "sidebar": "tutorialSidebar", "previous": {"title": "Authentication", "permalink": "/api/authentication"}}, {"id": "getting-started/ad-formats", "title": "<PERSON><PERSON>esh <PERSON> Formats", "description": "Learn about AdMesh's unique approach to advertising through conversational, citation-based, and contextual ad formats that differ fundamentally from traditional push/pull advertising models.", "source": "@site/docs/getting-started/ad-formats.md", "sourceDirName": "getting-started", "slug": "/getting-started/ad-formats", "permalink": "/getting-started/ad-formats", "draft": false, "unlisted": false, "editUrl": "https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/getting-started/ad-formats.md", "tags": [], "version": "current", "sidebarPosition": 4, "frontMatter": {"sidebar_position": 4}, "sidebar": "tutorialSidebar", "previous": {"title": "Quick Start Guide", "permalink": "/getting-started/quick-start"}, "next": {"title": "AdMesh vs Traditional Advertising", "permalink": "/getting-started/admesh-vs-traditional"}}, {"id": "getting-started/admesh-vs-traditional", "title": "AdMesh vs Traditional Advertising", "description": "A comprehensive comparison showing how Ad<PERSON>esh's contextual intelligence model differs from traditional push and pull advertising approaches.", "source": "@site/docs/getting-started/admesh-vs-traditional.md", "sourceDirName": "getting-started", "slug": "/getting-started/admesh-vs-traditional", "permalink": "/getting-started/admesh-vs-traditional", "draft": false, "unlisted": false, "editUrl": "https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/getting-started/admesh-vs-traditional.md", "tags": [], "version": "current", "sidebarPosition": 5, "frontMatter": {"sidebar_position": 5}, "sidebar": "tutorialSidebar", "previous": {"title": "<PERSON><PERSON>esh <PERSON> Formats", "permalink": "/getting-started/ad-formats"}, "next": {"title": "Python SDK Installation", "permalink": "/python-sdk/installation"}}, {"id": "getting-started/api-keys", "title": "Getting Your API Key", "description": "To use AdMesh APIs and SDKs, you'll need an API key. This guide walks you through the process of creating an account and obtaining your API key.", "source": "@site/docs/getting-started/api-keys.md", "sourceDirName": "getting-started", "slug": "/getting-started/api-keys", "permalink": "/getting-started/api-keys", "draft": false, "unlisted": false, "editUrl": "https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/getting-started/api-keys.md", "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"sidebar_position": 2}, "sidebar": "tutorialSidebar", "previous": {"title": "Overview", "permalink": "/getting-started/overview"}, "next": {"title": "Quick Start Guide", "permalink": "/getting-started/quick-start"}}, {"id": "getting-started/overview", "title": "Overview", "description": "This guide provides technical overview and core concepts for integrating AdMesh AI-powered product recommendation capabilities into enterprise applications.", "source": "@site/docs/getting-started/overview.md", "sourceDirName": "getting-started", "slug": "/getting-started/overview", "permalink": "/getting-started/overview", "draft": false, "unlisted": false, "editUrl": "https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/getting-started/overview.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "previous": {"title": "Introduction", "permalink": "/"}, "next": {"title": "Getting Your API Key", "permalink": "/getting-started/api-keys"}}, {"id": "getting-started/quick-start", "title": "Quick Start Guide", "description": "Get up and running with <PERSON><PERSON><PERSON> in under 5 minutes! This guide will walk you through making your first recommendation request using any of our SDKs.", "source": "@site/docs/getting-started/quick-start.md", "sourceDirName": "getting-started", "slug": "/getting-started/quick-start", "permalink": "/getting-started/quick-start", "draft": false, "unlisted": false, "editUrl": "https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/getting-started/quick-start.md", "tags": [], "version": "current", "sidebarPosition": 3, "frontMatter": {"sidebar_position": 3}, "sidebar": "tutorialSidebar", "previous": {"title": "Getting Your API Key", "permalink": "/getting-started/api-keys"}, "next": {"title": "<PERSON><PERSON>esh <PERSON> Formats", "permalink": "/getting-started/ad-formats"}}, {"id": "intro", "title": "AdMesh SDK Documentation", "description": "AdMesh SDK Documentation provides comprehensive technical guidance for integrating AI-powered product recommendation capabilities into enterprise applications and AI systems.", "source": "@site/docs/intro.md", "sourceDirName": ".", "slug": "/", "permalink": "/", "draft": false, "unlisted": false, "editUrl": "https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/intro.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1, "slug": "/"}, "sidebar": "tutorialSidebar", "next": {"title": "Overview", "permalink": "/getting-started/overview"}}, {"id": "python-sdk/installation", "title": "Python SDK Installation", "description": "The AdMesh Python SDK provides programmatic access to the AdMesh REST API for Python 3.8+ applications. The SDK includes comprehensive type definitions for all request parameters and response fields, with support for both synchronous and asynchronous operations.", "source": "@site/docs/python-sdk/installation.md", "sourceDirName": "python-sdk", "slug": "/python-sdk/installation", "permalink": "/python-sdk/installation", "draft": false, "unlisted": false, "editUrl": "https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/python-sdk/installation.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "previous": {"title": "AdMesh vs Traditional Advertising", "permalink": "/getting-started/admesh-vs-traditional"}, "next": {"title": "TypeScript SDK Installation", "permalink": "/typescript-sdk/installation"}}, {"id": "typescript-sdk/installation", "title": "TypeScript SDK Installation", "description": "Technical guide for installing and configuring the AdMesh TypeScript SDK for Node.js applications and serverless functions.", "source": "@site/docs/typescript-sdk/installation.md", "sourceDirName": "typescript-sdk", "slug": "/typescript-sdk/installation", "permalink": "/typescript-sdk/installation", "draft": false, "unlisted": false, "editUrl": "https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/typescript-sdk/installation.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "previous": {"title": "Python SDK Installation", "permalink": "/python-sdk/installation"}, "next": {"title": "UI SDK Installation", "permalink": "/ui-sdk/installation"}}, {"id": "ui-sdk/installation", "title": "UI SDK Installation", "description": "The AdMesh UI SDK is a production-ready React + TypeScript component library for displaying product recommendations with integrated tracking, theming, and conversational interface support.", "source": "@site/docs/ui-sdk/installation.md", "sourceDirName": "ui-sdk", "slug": "/ui-sdk/installation", "permalink": "/ui-sdk/installation", "draft": false, "unlisted": false, "editUrl": "https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/ui-sdk/installation.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "previous": {"title": "TypeScript SDK Installation", "permalink": "/typescript-sdk/installation"}, "next": {"title": "AI Agent Integration Overview", "permalink": "/ai-integration/overview"}}], "drafts": [], "sidebars": {"tutorialSidebar": [{"type": "doc", "id": "intro", "label": "Introduction", "translatable": true}, {"type": "category", "label": "Getting Started", "items": [{"type": "doc", "id": "getting-started/overview"}, {"type": "doc", "id": "getting-started/api-keys"}, {"type": "doc", "id": "getting-started/quick-start"}, {"type": "doc", "id": "getting-started/ad-formats"}, {"type": "doc", "id": "getting-started/admesh-vs-traditional"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Python SDK", "items": [{"type": "doc", "id": "python-sdk/installation"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "TypeScript SDK", "items": [{"type": "doc", "id": "typescript-sdk/installation"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "UI SDK", "items": [{"type": "doc", "id": "ui-sdk/installation"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "AI Agent Integration", "items": [{"type": "doc", "id": "ai-integration/overview"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "API Reference", "items": [{"type": "doc", "id": "api/authentication"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Examples", "items": [{"type": "doc", "id": "examples/ai-assistant"}], "collapsed": true, "collapsible": true}]}}]}}, "docusaurus-plugin-content-pages": {"default": null}, "docusaurus-plugin-debug": {}, "docusaurus-plugin-svgr": {}, "docusaurus-theme-classic": {}, "docusaurus-theme-search-algolia": {}, "docusaurus-theme-mermaid": {}, "docusaurus-bootstrap-plugin": {}, "docusaurus-mdx-fallback-plugin": {}}}