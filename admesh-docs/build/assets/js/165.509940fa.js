/*! For license information please see 165.509940fa.js.LICENSE.txt */
"use strict";(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[165],{165:(e,t,n)=>{function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,c(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function o(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=h(e))||t){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){s=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(s)throw i}}}}function s(e,t,n){return(t=c(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i,o,s=[],l=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){u=!0,a=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw a}}return s}}(e,t)||h(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(e){return function(e){if(Array.isArray(e))return r(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||h(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==typeof t?t:t+""}function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function h(e,t){if(e){if("string"==typeof e)return r(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}n.d(t,{A:()=>Oh});var f="undefined"==typeof window?null:window,p=f?f.navigator:null;f&&f.document;var g,v,y,m,b,x,w,E,k,T,C,P,S,B,D,_,A,M,R,I,N,L,z,O,V,F,j,X,q=d(""),Y=d({}),W=d((function(){})),U="undefined"==typeof HTMLElement?"undefined":d(HTMLElement),H=function(e){return e&&e.instanceString&&G(e.instanceString)?e.instanceString():null},K=function(e){return null!=e&&d(e)==q},G=function(e){return null!=e&&d(e)===W},Z=function(e){return!ee(e)&&(Array.isArray?Array.isArray(e):null!=e&&e instanceof Array)},$=function(e){return null!=e&&d(e)===Y&&!Z(e)&&e.constructor===Object},Q=function(e){return null!=e&&d(e)===d(1)&&!isNaN(e)},J=function(e){return"undefined"===U?void 0:null!=e&&e instanceof HTMLElement},ee=function(e){return te(e)||ne(e)},te=function(e){return"collection"===H(e)&&e._private.single},ne=function(e){return"collection"===H(e)&&!e._private.single},re=function(e){return"core"===H(e)},ae=function(e){return"stylesheet"===H(e)},ie=function(e){return null==e||!(""!==e&&!e.match(/^\s+$/))},oe=function(e){return function(e){return null!=e&&d(e)===Y}(e)&&G(e.then)},se=function(e,t){t||(t=function(){if(1===arguments.length)return arguments[0];if(0===arguments.length)return"undefined";for(var e=[],t=0;t<arguments.length;t++)e.push(arguments[t]);return e.join("$")});var n=function(){var r,a=arguments,i=t.apply(this,a),o=n.cache;return(r=o[i])||(r=o[i]=e.apply(this,a)),r};return n.cache={},n},le=se((function(e){return e.replace(/([A-Z])/g,(function(e){return"-"+e.toLowerCase()}))})),ue=se((function(e){return e.replace(/(-\w)/g,(function(e){return e[1].toUpperCase()}))})),ce=se((function(e,t){return e+t[0].toUpperCase()+t.substring(1)}),(function(e,t){return e+"$"+t})),de=function(e){return ie(e)?e:e.charAt(0).toUpperCase()+e.substring(1)},he="(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))",fe="rgb[a]?\\(("+he+"[%]?)\\s*,\\s*("+he+"[%]?)\\s*,\\s*("+he+"[%]?)(?:\\s*,\\s*("+he+"))?\\)",pe="rgb[a]?\\((?:"+he+"[%]?)\\s*,\\s*(?:"+he+"[%]?)\\s*,\\s*(?:"+he+"[%]?)(?:\\s*,\\s*(?:"+he+"))?\\)",ge="hsl[a]?\\(("+he+")\\s*,\\s*("+he+"[%])\\s*,\\s*("+he+"[%])(?:\\s*,\\s*("+he+"))?\\)",ve="hsl[a]?\\((?:"+he+")\\s*,\\s*(?:"+he+"[%])\\s*,\\s*(?:"+he+"[%])(?:\\s*,\\s*(?:"+he+"))?\\)",ye=function(e,t){return e<t?-1:e>t?1:0},me=null!=Object.assign?Object.assign.bind(Object):function(e){for(var t=arguments,n=1;n<t.length;n++){var r=t[n];if(null!=r)for(var a=Object.keys(r),i=0;i<a.length;i++){var o=a[i];e[o]=r[o]}}return e},be=function(e){return(Z(e)?e:null)||function(e){return xe[e.toLowerCase()]}(e)||function(e){if((4===e.length||7===e.length)&&"#"===e[0]){var t,n,r,a=16;return 4===e.length?(t=parseInt(e[1]+e[1],a),n=parseInt(e[2]+e[2],a),r=parseInt(e[3]+e[3],a)):(t=parseInt(e[1]+e[2],a),n=parseInt(e[3]+e[4],a),r=parseInt(e[5]+e[6],a)),[t,n,r]}}(e)||function(e){var t,n=new RegExp("^"+fe+"$").exec(e);if(n){t=[];for(var r=[],a=1;a<=3;a++){var i=n[a];if("%"===i[i.length-1]&&(r[a]=!0),i=parseFloat(i),r[a]&&(i=i/100*255),i<0||i>255)return;t.push(Math.floor(i))}var o=r[1]||r[2]||r[3],s=r[1]&&r[2]&&r[3];if(o&&!s)return;var l=n[4];if(void 0!==l){if((l=parseFloat(l))<0||l>1)return;t.push(l)}}return t}(e)||function(e){var t,n,r,a,i,o,s,l;function u(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}var c=new RegExp("^"+ge+"$").exec(e);if(c){if((n=parseInt(c[1]))<0?n=(360- -1*n%360)%360:n>360&&(n%=360),n/=360,(r=parseFloat(c[2]))<0||r>100)return;if(r/=100,(a=parseFloat(c[3]))<0||a>100)return;if(a/=100,void 0!==(i=c[4])&&((i=parseFloat(i))<0||i>1))return;if(0===r)o=s=l=Math.round(255*a);else{var d=a<.5?a*(1+r):a+r-a*r,h=2*a-d;o=Math.round(255*u(h,d,n+1/3)),s=Math.round(255*u(h,d,n)),l=Math.round(255*u(h,d,n-1/3))}t=[o,s,l,i]}return t}(e)},xe={transparent:[0,0,0,0],aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],grey:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},we=function(e){for(var t=e.map,n=e.keys,r=n.length,a=0;a<r;a++){var i=n[a];if($(i))throw Error("Tried to set map with object key");a<n.length-1?(null==t[i]&&(t[i]={}),t=t[i]):t[i]=e.value}},Ee=function(e){for(var t=e.map,n=e.keys,r=n.length,a=0;a<r;a++){var i=n[a];if($(i))throw Error("Tried to get map with object key");if(null==(t=t[i]))return t}return t},ke="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function Te(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Ce(){if(v)return g;return v=1,g=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}}function Pe(){if(x)return b;x=1;var e=function(){if(m)return y;m=1;var e="object"==typeof ke&&ke&&ke.Object===Object&&ke;return y=e}(),t="object"==typeof self&&self&&self.Object===Object&&self,n=e||t||Function("return this")();return b=n}function Se(){if(P)return C;P=1;var e=function(){if(T)return k;T=1;var e=/\s/;return k=function(t){for(var n=t.length;n--&&e.test(t.charAt(n)););return n},k}(),t=/^\s+/;return C=function(n){return n?n.slice(0,e(n)+1).replace(t,""):n},C}function Be(){if(B)return S;B=1;var e=Pe().Symbol;return S=e}function De(){if(I)return R;I=1;var e=Be(),t=function(){if(_)return D;_=1;var e=Be(),t=Object.prototype,n=t.hasOwnProperty,r=t.toString,a=e?e.toStringTag:void 0;return D=function(e){var t=n.call(e,a),i=e[a];try{e[a]=void 0;var o=!0}catch(l){}var s=r.call(e);return o&&(t?e[a]=i:delete e[a]),s}}(),n=function(){if(M)return A;M=1;var e=Object.prototype.toString;return A=function(t){return e.call(t)}}(),r=e?e.toStringTag:void 0;return R=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":r&&r in Object(e)?t(e):n(e)}}function _e(){if(O)return z;O=1;var e=De(),t=L?N:(L=1,N=function(e){return null!=e&&"object"==typeof e});return z=function(n){return"symbol"==typeof n||t(n)&&"[object Symbol]"==e(n)}}var Ae=Te(function(){if(X)return j;X=1;var e=Ce(),t=function(){if(E)return w;E=1;var e=Pe();return w=function(){return e.Date.now()}}(),n=function(){if(F)return V;F=1;var e=Se(),t=Ce(),n=_e(),r=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,i=/^0o[0-7]+$/i,o=parseInt;return V=function(s){if("number"==typeof s)return s;if(n(s))return NaN;if(t(s)){var l="function"==typeof s.valueOf?s.valueOf():s;s=t(l)?l+"":l}if("string"!=typeof s)return 0===s?s:+s;s=e(s);var u=a.test(s);return u||i.test(s)?o(s.slice(2),u?2:8):r.test(s)?NaN:+s}}(),r=Math.max,a=Math.min;return j=function(i,o,s){var l,u,c,d,h,f,p=0,g=!1,v=!1,y=!0;if("function"!=typeof i)throw new TypeError("Expected a function");function m(e){var t=l,n=u;return l=u=void 0,p=e,d=i.apply(n,t)}function b(e){var t=e-f;return void 0===f||t>=o||t<0||v&&e-p>=c}function x(){var e=t();if(b(e))return w(e);h=setTimeout(x,function(e){var t=o-(e-f);return v?a(t,c-(e-p)):t}(e))}function w(e){return h=void 0,y&&l?m(e):(l=u=void 0,d)}function E(){var e=t(),n=b(e);if(l=arguments,u=this,f=e,n){if(void 0===h)return function(e){return p=e,h=setTimeout(x,o),g?m(e):d}(f);if(v)return clearTimeout(h),h=setTimeout(x,o),m(f)}return void 0===h&&(h=setTimeout(x,o)),d}return o=n(o)||0,e(s)&&(g=!!s.leading,c=(v="maxWait"in s)?r(n(s.maxWait)||0,o):c,y="trailing"in s?!!s.trailing:y),E.cancel=function(){void 0!==h&&clearTimeout(h),p=0,l=f=u=h=void 0},E.flush=function(){return void 0===h?d:w(t())},E}}()),Me=f?f.performance:null,Re=Me&&Me.now?function(){return Me.now()}:function(){return Date.now()},Ie=function(){if(f){if(f.requestAnimationFrame)return function(e){f.requestAnimationFrame(e)};if(f.mozRequestAnimationFrame)return function(e){f.mozRequestAnimationFrame(e)};if(f.webkitRequestAnimationFrame)return function(e){f.webkitRequestAnimationFrame(e)};if(f.msRequestAnimationFrame)return function(e){f.msRequestAnimationFrame(e)}}return function(e){e&&setTimeout((function(){e(Re())}),1e3/60)}}(),Ne=function(e){return Ie(e)},Le=Re,ze=9261,Oe=5381,Ve=function(e){for(var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ze;!(t=e.next()).done;)n=65599*n+t.value|0;return n},Fe=function(e){return 65599*(arguments.length>1&&void 0!==arguments[1]?arguments[1]:ze)+e|0},je=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Oe;return(t<<5)+t+e|0},Xe=function(e){return 2097152*e[0]+e[1]},qe=function(e,t){return[Fe(e[0],t[0]),je(e[1],t[1])]},Ye=function(e,t){var n={value:0,done:!1},r=0,a=e.length;return Ve({next:function(){return r<a?n.value=e[r++]:n.done=!0,n}},t)},We=function(e,t){var n={value:0,done:!1},r=0,a=e.length;return Ve({next:function(){return r<a?n.value=e.charCodeAt(r++):n.done=!0,n}},t)},Ue=function(){return He(arguments)},He=function(e){for(var t,n=0;n<e.length;n++){var r=e[n];t=0===n?We(r):We(r,t)}return t},Ke=!0,Ge=null!=console.warn,Ze=null!=console.trace,$e=Number.MAX_SAFE_INTEGER||9007199254740991,Qe=function(){return!0},Je=function(){return!1},et=function(){return 0},tt=function(){},nt=function(e){throw new Error(e)},rt=function(e){if(void 0===e)return Ke;Ke=!!e},at=function(e){rt()&&(Ge?console.warn(e):(console.log(e),Ze&&console.trace()))},it=function(e){return null==e?e:Z(e)?e.slice():$(e)?function(e){return me({},e)}(e):e},ot=function(e,t){for(t=e="";e++<36;t+=51*e&52?(15^e?8^Math.random()*(20^e?16:4):4).toString(16):"-");return t},st={},lt=function(){return st},ut=function(e){var t=Object.keys(e);return function(n){for(var r={},a=0;a<t.length;a++){var i=t[a],o=null==n?void 0:n[i];r[i]=void 0===o?e[i]:o}return r}},ct=function(e,t,n){for(var r=e.length-1;r>=0;r--)e[r]===t&&e.splice(r,1)},dt=function(e){e.splice(0,e.length)},ht=function(e,t,n){return n&&(t=ce(n,t)),e[t]},ft=function(e,t,n,r){n&&(t=ce(n,t)),e[t]=r},pt="undefined"!=typeof Map?Map:function(){return i((function e(){a(this,e),this._obj={}}),[{key:"set",value:function(e,t){return this._obj[e]=t,this}},{key:"delete",value:function(e){return this._obj[e]=void 0,this}},{key:"clear",value:function(){this._obj={}}},{key:"has",value:function(e){return void 0!==this._obj[e]}},{key:"get",value:function(e){return this._obj[e]}}])}(),gt=function(){return i((function e(t){if(a(this,e),this._obj=Object.create(null),this.size=0,null!=t){var n;n=null!=t.instanceString&&t.instanceString()===this.instanceString()?t.toArray():t;for(var r=0;r<n.length;r++)this.add(n[r])}}),[{key:"instanceString",value:function(){return"set"}},{key:"add",value:function(e){var t=this._obj;1!==t[e]&&(t[e]=1,this.size++)}},{key:"delete",value:function(e){var t=this._obj;1===t[e]&&(t[e]=0,this.size--)}},{key:"clear",value:function(){this._obj=Object.create(null)}},{key:"has",value:function(e){return 1===this._obj[e]}},{key:"toArray",value:function(){var e=this;return Object.keys(this._obj).filter((function(t){return e.has(t)}))}},{key:"forEach",value:function(e,t){return this.toArray().forEach(e,t)}}])}(),vt="undefined"!==("undefined"==typeof Set?"undefined":d(Set))?Set:gt,yt=function(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(void 0!==e&&void 0!==t&&re(e)){var r=t.group;if(null==r&&(r=t.data&&null!=t.data.source&&null!=t.data.target?"edges":"nodes"),"nodes"===r||"edges"===r){this.length=1,this[0]=this;var a=this._private={cy:e,single:!0,data:t.data||{},position:t.position||{x:0,y:0},autoWidth:void 0,autoHeight:void 0,autoPadding:void 0,compoundBoundsClean:!1,listeners:[],group:r,style:{},rstyle:{},styleCxts:[],styleKeys:{},removed:!0,selected:!!t.selected,selectable:void 0===t.selectable||!!t.selectable,locked:!!t.locked,grabbed:!1,grabbable:void 0===t.grabbable||!!t.grabbable,pannable:void 0===t.pannable?"edges"===r:!!t.pannable,active:!1,classes:new vt,animation:{current:[],queue:[]},rscratch:{},scratch:t.scratch||{},edges:[],children:[],parent:t.parent&&t.parent.isNode()?t.parent:null,traversalCache:{},backgrounding:!1,bbCache:null,bbCacheShift:{x:0,y:0},bodyBounds:null,overlayBounds:null,labelBounds:{all:null,source:null,target:null,main:null},arrowBounds:{source:null,target:null,"mid-source":null,"mid-target":null}};if(null==a.position.x&&(a.position.x=0),null==a.position.y&&(a.position.y=0),t.renderedPosition){var i=t.renderedPosition,o=e.pan(),s=e.zoom();a.position={x:(i.x-o.x)/s,y:(i.y-o.y)/s}}var l=[];Z(t.classes)?l=t.classes:K(t.classes)&&(l=t.classes.split(/\s+/));for(var u=0,c=l.length;u<c;u++){var d=l[u];d&&""!==d&&a.classes.add(d)}this.createEmitter(),(void 0===n||n)&&this.restore();var h=t.style||t.css;h&&(at("Setting a `style` bypass at element creation should be done only when absolutely necessary.  Try to use the stylesheet instead."),this.style(h))}else nt("An element must be of type `nodes` or `edges`; you specified `"+r+"`")}else nt("An element must have a core reference and parameters set")},mt=function(e){return e={bfs:e.bfs||!e.dfs,dfs:e.dfs||!e.bfs},function(t,n,r){var a;$(t)&&!ee(t)&&(t=(a=t).roots||a.root,n=a.visit,r=a.directed),r=2!==arguments.length||G(n)?r:n,n=G(n)?n:function(){};for(var i,o=this._private.cy,s=t=K(t)?this.filter(t):t,l=[],u=[],c={},d={},h={},f=0,p=this.byGroup(),g=p.nodes,v=p.edges,y=0;y<s.length;y++){var m=s[y],b=m.id();m.isNode()&&(l.unshift(m),e.bfs&&(h[b]=!0,u.push(m)),d[b]=0)}for(var x,w=function(){var t=e.bfs?l.shift():l.pop(),a=t.id();if(e.dfs){if(h[a])return 0;h[a]=!0,u.push(t)}var o,s=d[a],p=c[a],y=null!=p?p.source():null,m=null!=p?p.target():null,b=null==p?void 0:t.same(y)?m[0]:y[0];if(!0===(o=n(t,p,b,f++,s)))return i=t,1;if(!1===o)return 1;for(var x=t.connectedEdges().filter((function(e){return(!r||e.source().same(t))&&v.has(e)})),w=0;w<x.length;w++){var E=x[w],k=E.connectedNodes().filter((function(e){return!e.same(t)&&g.has(e)})),T=k.id();0===k.length||h[T]||(k=k[0],l.push(k),e.bfs&&(h[T]=!0,u.push(k)),c[T]=E,d[T]=d[a]+1)}};0!==l.length&&(0===(x=w())||1!==x););for(var E=o.collection(),k=0;k<u.length;k++){var T=u[k],C=c[T.id()];null!=C&&E.push(C),E.push(T)}return{path:o.collection(E),found:o.collection(i)}}},bt={breadthFirstSearch:mt({bfs:!0}),depthFirstSearch:mt({dfs:!0})};bt.bfs=bt.breadthFirstSearch,bt.dfs=bt.depthFirstSearch;var xt,wt,Et,kt={exports:{}},Tt=kt.exports;function Ct(){return xt||(xt=1,e=kt,function(){var t,n,r,a,i,o,s,l,u,c,d,h,f,p,g;r=Math.floor,c=Math.min,n=function(e,t){return e<t?-1:e>t?1:0},u=function(e,t,a,i,o){var s;if(null==a&&(a=0),null==o&&(o=n),a<0)throw new Error("lo must be non-negative");for(null==i&&(i=e.length);a<i;)o(t,e[s=r((a+i)/2)])<0?i=s:a=s+1;return[].splice.apply(e,[a,a-a].concat(t)),t},o=function(e,t,r){return null==r&&(r=n),e.push(t),p(e,0,e.length-1,r)},i=function(e,t){var r,a;return null==t&&(t=n),r=e.pop(),e.length?(a=e[0],e[0]=r,g(e,0,t)):a=r,a},l=function(e,t,r){var a;return null==r&&(r=n),a=e[0],e[0]=t,g(e,0,r),a},s=function(e,t,r){var a;return null==r&&(r=n),e.length&&r(e[0],t)<0&&(t=(a=[e[0],t])[0],e[0]=a[1],g(e,0,r)),t},a=function(e,t){var a,i,o,s,l,u;for(null==t&&(t=n),l=[],i=0,o=(s=function(){u=[];for(var t=0,n=r(e.length/2);0<=n?t<n:t>n;0<=n?t++:t--)u.push(t);return u}.apply(this).reverse()).length;i<o;i++)a=s[i],l.push(g(e,a,t));return l},f=function(e,t,r){var a;if(null==r&&(r=n),-1!==(a=e.indexOf(t)))return p(e,0,a,r),g(e,a,r)},d=function(e,t,r){var i,o,l,u,c;if(null==r&&(r=n),!(o=e.slice(0,t)).length)return o;for(a(o,r),l=0,u=(c=e.slice(t)).length;l<u;l++)i=c[l],s(o,i,r);return o.sort(r).reverse()},h=function(e,t,r){var o,s,l,d,h,f,p,g,v;if(null==r&&(r=n),10*t<=e.length){if(!(l=e.slice(0,t).sort(r)).length)return l;for(s=l[l.length-1],d=0,f=(p=e.slice(t)).length;d<f;d++)r(o=p[d],s)<0&&(u(l,o,0,null,r),l.pop(),s=l[l.length-1]);return l}for(a(e,r),v=[],h=0,g=c(t,e.length);0<=g?h<g:h>g;0<=g?++h:--h)v.push(i(e,r));return v},p=function(e,t,r,a){var i,o,s;for(null==a&&(a=n),i=e[r];r>t&&a(i,o=e[s=r-1>>1])<0;)e[r]=o,r=s;return e[r]=i},g=function(e,t,r){var a,i,o,s,l;for(null==r&&(r=n),i=e.length,l=t,o=e[t],a=2*t+1;a<i;)(s=a+1)<i&&!(r(e[a],e[s])<0)&&(a=s),e[t]=e[a],a=2*(t=a)+1;return e[t]=o,p(e,l,t,r)},t=function(){function e(e){this.cmp=null!=e?e:n,this.nodes=[]}return e.push=o,e.pop=i,e.replace=l,e.pushpop=s,e.heapify=a,e.updateItem=f,e.nlargest=d,e.nsmallest=h,e.prototype.push=function(e){return o(this.nodes,e,this.cmp)},e.prototype.pop=function(){return i(this.nodes,this.cmp)},e.prototype.peek=function(){return this.nodes[0]},e.prototype.contains=function(e){return-1!==this.nodes.indexOf(e)},e.prototype.replace=function(e){return l(this.nodes,e,this.cmp)},e.prototype.pushpop=function(e){return s(this.nodes,e,this.cmp)},e.prototype.heapify=function(){return a(this.nodes,this.cmp)},e.prototype.updateItem=function(e){return f(this.nodes,e,this.cmp)},e.prototype.clear=function(){return this.nodes=[]},e.prototype.empty=function(){return 0===this.nodes.length},e.prototype.size=function(){return this.nodes.length},e.prototype.clone=function(){var t;return(t=new e).nodes=this.nodes.slice(0),t},e.prototype.toArray=function(){return this.nodes.slice(0)},e.prototype.insert=e.prototype.push,e.prototype.top=e.prototype.peek,e.prototype.front=e.prototype.peek,e.prototype.has=e.prototype.contains,e.prototype.copy=e.prototype.clone,e}(),e.exports=t}.call(Tt)),kt.exports;var e}var Pt=Te(Et?wt:(Et=1,wt=Ct())),St=ut({root:null,weight:function(e){return 1},directed:!1}),Bt={dijkstra:function(e){if(!$(e)){var t=arguments;e={root:t[0],weight:t[1],directed:t[2]}}var n=St(e),r=n.root,a=n.weight,i=n.directed,o=this,s=a,l=K(r)?this.filter(r)[0]:r[0],u={},c={},d={},h=this.byGroup(),f=h.nodes,p=h.edges;p.unmergeBy((function(e){return e.isLoop()}));for(var g=function(e){return u[e.id()]},v=function(e,t){u[e.id()]=t,y.updateItem(e)},y=new Pt((function(e,t){return g(e)-g(t)})),m=0;m<f.length;m++){var b=f[m];u[b.id()]=b.same(l)?0:1/0,y.push(b)}for(var x=function(e,t){for(var n,r=(i?e.edgesTo(t):e.edgesWith(t)).intersect(p),a=1/0,o=0;o<r.length;o++){var l=r[o],u=s(l);(u<a||!n)&&(a=u,n=l)}return{edge:n,dist:a}};y.size()>0;){var w=y.pop(),E=g(w),k=w.id();if(d[k]=E,E!==1/0)for(var T=w.neighborhood().intersect(f),C=0;C<T.length;C++){var P=T[C],S=P.id(),B=x(w,P),D=E+B.dist;D<g(P)&&(v(P,D),c[S]={node:w,edge:B.edge})}}return{distanceTo:function(e){var t=K(e)?f.filter(e)[0]:e[0];return d[t.id()]},pathTo:function(e){var t=K(e)?f.filter(e)[0]:e[0],n=[],r=t,a=r.id();if(t.length>0)for(n.unshift(t);c[a];){var i=c[a];n.unshift(i.edge),n.unshift(i.node),a=(r=i.node).id()}return o.spawn(n)}}}},Dt={kruskal:function(e){e=e||function(e){return 1};for(var t=this.byGroup(),n=t.nodes,r=t.edges,a=n.length,i=new Array(a),o=n,s=function(e){for(var t=0;t<i.length;t++){if(i[t].has(e))return t}},l=0;l<a;l++)i[l]=this.spawn(n[l]);for(var u=r.sort((function(t,n){return e(t)-e(n)})),c=0;c<u.length;c++){var d=u[c],h=d.source()[0],f=d.target()[0],p=s(h),g=s(f),v=i[p],y=i[g];p!==g&&(o.merge(d),v.merge(y),i.splice(g,1))}return o}},_t=ut({root:null,goal:null,weight:function(e){return 1},heuristic:function(e){return 0},directed:!1}),At={aStar:function(e){var t=this.cy(),n=_t(e),r=n.root,a=n.goal,i=n.heuristic,o=n.directed,s=n.weight;r=t.collection(r)[0],a=t.collection(a)[0];var l,u,c=r.id(),d=a.id(),h={},f={},p={},g=new Pt((function(e,t){return f[e.id()]-f[t.id()]})),v=new vt,y={},m={},b=function(e,t){g.push(e),v.add(t)};b(r,c),h[c]=0,f[c]=i(r);for(var x,w=0;g.size()>0;){if(l=g.pop(),u=l.id(),v.delete(u),w++,u===d){for(var E=[],k=a,T=d,C=m[T];E.unshift(k),null!=C&&E.unshift(C),null!=(k=y[T]);)C=m[T=k.id()];return{found:!0,distance:h[u],path:this.spawn(E),steps:w}}p[u]=!0;for(var P=l._private.edges,S=0;S<P.length;S++){var B=P[S];if(this.hasElementWithId(B.id())&&(!o||B.data("source")===u)){var D=B.source(),_=B.target(),A=D.id()!==u?D:_,M=A.id();if(this.hasElementWithId(M)&&!p[M]){var R=h[u]+s(B);x=M,v.has(x)?R<h[M]&&(h[M]=R,f[M]=R+i(A),y[M]=l,m[M]=B):(h[M]=R,f[M]=R+i(A),b(A,M),y[M]=l,m[M]=B)}}}}return{found:!1,distance:void 0,path:void 0,steps:w}}},Mt=ut({weight:function(e){return 1},directed:!1}),Rt={floydWarshall:function(e){for(var t=this.cy(),n=Mt(e),r=n.weight,a=n.directed,i=r,o=this.byGroup(),s=o.nodes,l=o.edges,u=s.length,c=u*u,d=function(e){return s.indexOf(e)},h=function(e){return s[e]},f=new Array(c),p=0;p<c;p++){var g=p%u,v=(p-g)/u;f[p]=v===g?0:1/0}for(var y=new Array(c),m=new Array(c),b=0;b<l.length;b++){var x=l[b],w=x.source()[0],E=x.target()[0];if(w!==E){var k=d(w),T=d(E),C=k*u+T,P=i(x);if(f[C]>P&&(f[C]=P,y[C]=T,m[C]=x),!a){var S=T*u+k;!a&&f[S]>P&&(f[S]=P,y[S]=k,m[S]=x)}}}for(var B=0;B<u;B++)for(var D=0;D<u;D++)for(var _=D*u+B,A=0;A<u;A++){var M=D*u+A,R=B*u+A;f[_]+f[R]<f[M]&&(f[M]=f[_]+f[R],y[M]=y[_])}var I=function(e){return d(function(e){return(K(e)?t.filter(e):e)[0]}(e))},N={distance:function(e,t){var n=I(e),r=I(t);return f[n*u+r]},path:function(e,n){var r=I(e),a=I(n),i=h(r);if(r===a)return i.collection();if(null==y[r*u+a])return t.collection();var o,s=t.collection(),l=r;for(s.merge(i);r!==a;)l=r,r=y[r*u+a],o=m[l*u+r],s.merge(o),s.merge(h(r));return s}};return N}},It=ut({weight:function(e){return 1},directed:!1,root:null}),Nt={bellmanFord:function(e){var t=this,n=It(e),r=n.weight,a=n.directed,i=n.root,o=r,s=this,l=this.cy(),u=this.byGroup(),c=u.edges,d=u.nodes,h=d.length,f=new pt,p=!1,g=[];i=l.collection(i)[0],c.unmergeBy((function(e){return e.isLoop()}));for(var v=c.length,y=function(e){var t=f.get(e.id());return t||(t={},f.set(e.id(),t)),t},m=function(e){return(K(e)?l.$(e):e)[0]},b=0;b<h;b++){var x=d[b],w=y(x);x.same(i)?w.dist=0:w.dist=1/0,w.pred=null,w.edge=null}for(var E=!1,k=function(e,t,n,r,a,i){var o=r.dist+i;o<a.dist&&!n.same(r.edge)&&(a.dist=o,a.pred=e,a.edge=n,E=!0)},T=1;T<h;T++){E=!1;for(var C=0;C<v;C++){var P=c[C],S=P.source(),B=P.target(),D=o(P),_=y(S),A=y(B);k(S,0,P,_,A,D),a||k(B,0,P,A,_,D)}if(!E)break}if(E)for(var M=[],R=0;R<v;R++){var I=c[R],N=I.source(),L=I.target(),z=o(I),O=y(N).dist,V=y(L).dist;if(O+z<V||!a&&V+z<O){if(p||(at("Graph contains a negative weight cycle for Bellman-Ford"),p=!0),!1===e.findNegativeWeightCycles)break;var F=[];O+z<V&&F.push(N),!a&&V+z<O&&F.push(L);for(var j=F.length,X=0;X<j;X++){var q=F[X],Y=[q];Y.push(y(q).edge);for(var W=y(q).pred;-1===Y.indexOf(W);)Y.push(W),Y.push(y(W).edge),W=y(W).pred;for(var U=(Y=Y.slice(Y.indexOf(W)))[0].id(),H=0,G=2;G<Y.length;G+=2)Y[G].id()<U&&(U=Y[G].id(),H=G);(Y=Y.slice(H).concat(Y.slice(0,H))).push(Y[0]);var Z=Y.map((function(e){return e.id()})).join(",");-1===M.indexOf(Z)&&(g.push(s.spawn(Y)),M.push(Z))}}}return{distanceTo:function(e){return y(m(e)).dist},pathTo:function(e){for(var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i,r=[],a=m(e);;){if(null==a)return t.spawn();var o=y(a),l=o.edge,u=o.pred;if(r.unshift(a[0]),a.same(n)&&r.length>0)break;null!=l&&r.unshift(l),a=u}return s.spawn(r)},hasNegativeWeightCycle:p,negativeWeightCycles:g}}},Lt=Math.sqrt(2),zt=function(e,t,n){0===n.length&&nt("Karger-Stein must be run on a connected (sub)graph");for(var r=n[e],a=r[1],i=r[2],o=t[a],s=t[i],l=n,u=l.length-1;u>=0;u--){var c=l[u],d=c[1],h=c[2];(t[d]===o&&t[h]===s||t[d]===s&&t[h]===o)&&l.splice(u,1)}for(var f=0;f<l.length;f++){var p=l[f];p[1]===s?(l[f]=p.slice(),l[f][1]=o):p[2]===s&&(l[f]=p.slice(),l[f][2]=o)}for(var g=0;g<t.length;g++)t[g]===s&&(t[g]=o);return l},Ot=function(e,t,n,r){for(;n>r;){var a=Math.floor(Math.random()*t.length);t=zt(a,e,t),n--}return t},Vt={kargerStein:function(){var e=this,t=this.byGroup(),n=t.nodes,r=t.edges;r.unmergeBy((function(e){return e.isLoop()}));var a=n.length,i=r.length,o=Math.ceil(Math.pow(Math.log(a)/Math.LN2,2)),s=Math.floor(a/Lt);if(!(a<2)){for(var l=[],u=0;u<i;u++){var c=r[u];l.push([u,n.indexOf(c.source()),n.indexOf(c.target())])}for(var d=1/0,h=[],f=new Array(a),p=new Array(a),g=new Array(a),v=function(e,t){for(var n=0;n<a;n++)t[n]=e[n]},y=0;y<=o;y++){for(var m=0;m<a;m++)p[m]=m;var b=Ot(p,l.slice(),a,s),x=b.slice();v(p,g);var w=Ot(p,b,s,2),E=Ot(g,x,s,2);w.length<=E.length&&w.length<d?(d=w.length,h=w,v(p,f)):E.length<=w.length&&E.length<d&&(d=E.length,h=E,v(g,f))}for(var k=this.spawn(h.map((function(e){return r[e[0]]}))),T=this.spawn(),C=this.spawn(),P=f[0],S=0;S<f.length;S++){var B=f[S],D=n[S];B===P?T.merge(D):C.merge(D)}var _=function(t){var n=e.spawn();return t.forEach((function(t){n.merge(t),t.connectedEdges().forEach((function(t){e.contains(t)&&!k.contains(t)&&n.merge(t)}))})),n},A=[_(T),_(C)];return{cut:k,components:A,partition1:T,partition2:C}}nt("At least 2 nodes are required for Karger-Stein algorithm")}},Ft=function(e,t,n){return{x:e.x*t+n.x,y:e.y*t+n.y}},jt=function(e,t,n){return{x:(e.x-n.x)/t,y:(e.y-n.y)/t}},Xt=function(e){return{x:e[0],y:e[1]}},qt=function(e,t){return Math.atan2(t,e)-Math.PI/2},Yt=Math.log2||function(e){return Math.log(e)/Math.log(2)},Wt=function(e){return e>0?1:e<0?-1:0},Ut=function(e,t){return Math.sqrt(Ht(e,t))},Ht=function(e,t){var n=t.x-e.x,r=t.y-e.y;return n*n+r*r},Kt=function(e){for(var t=e.length,n=0,r=0;r<t;r++)n+=e[r];for(var a=0;a<t;a++)e[a]=e[a]/n;return e},Gt=function(e,t,n,r){return(1-r)*(1-r)*e+2*(1-r)*r*t+r*r*n},Zt=function(e,t,n,r){return{x:Gt(e.x,t.x,n.x,r),y:Gt(e.y,t.y,n.y,r)}},$t=function(e,t,n){return Math.max(e,Math.min(n,t))},Qt=function(e){if(null==e)return{x1:1/0,y1:1/0,x2:-1/0,y2:-1/0,w:0,h:0};if(null!=e.x1&&null!=e.y1){if(null!=e.x2&&null!=e.y2&&e.x2>=e.x1&&e.y2>=e.y1)return{x1:e.x1,y1:e.y1,x2:e.x2,y2:e.y2,w:e.x2-e.x1,h:e.y2-e.y1};if(null!=e.w&&null!=e.h&&e.w>=0&&e.h>=0)return{x1:e.x1,y1:e.y1,x2:e.x1+e.w,y2:e.y1+e.h,w:e.w,h:e.h}}},Jt=function(e,t){e.x1=Math.min(e.x1,t.x1),e.x2=Math.max(e.x2,t.x2),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,t.y1),e.y2=Math.max(e.y2,t.y2),e.h=e.y2-e.y1},en=function(e,t,n){e.x1=Math.min(e.x1,t),e.x2=Math.max(e.x2,t),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,n),e.y2=Math.max(e.y2,n),e.h=e.y2-e.y1},tn=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e.x1-=t,e.x2+=t,e.y1-=t,e.y2+=t,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},nn=function(e){var t,n,r,a,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[0];if(1===i.length)t=n=r=a=i[0];else if(2===i.length)t=r=i[0],a=n=i[1];else if(4===i.length){var o=l(i,4);t=o[0],n=o[1],r=o[2],a=o[3]}return e.x1-=a,e.x2+=n,e.y1-=t,e.y2+=r,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},rn=function(e,t){e.x1=t.x1,e.y1=t.y1,e.x2=t.x2,e.y2=t.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1},an=function(e,t){return!(e.x1>t.x2)&&(!(t.x1>e.x2)&&(!(e.x2<t.x1)&&(!(t.x2<e.x1)&&(!(e.y2<t.y1)&&(!(t.y2<e.y1)&&(!(e.y1>t.y2)&&!(t.y1>e.y2)))))))},on=function(e,t,n){return e.x1<=t&&t<=e.x2&&e.y1<=n&&n<=e.y2},sn=function(e,t,n,r,a,i,o){var s,l,u=arguments.length>7&&void 0!==arguments[7]?arguments[7]:"auto",c="auto"===u?Cn(a,i):u,d=a/2,h=i/2,f=(c=Math.min(c,d,h))!==d,p=c!==h;if(f){var g=r-h-o;if((s=bn(e,t,n,r,n-d+c-o,g,n+d-c+o,g,!1)).length>0)return s}if(p){var v=n+d+o;if((s=bn(e,t,n,r,v,r-h+c-o,v,r+h-c+o,!1)).length>0)return s}if(f){var y=r+h+o;if((s=bn(e,t,n,r,n-d+c-o,y,n+d-c+o,y,!1)).length>0)return s}if(p){var m=n-d-o;if((s=bn(e,t,n,r,m,r-h+c-o,m,r+h-c+o,!1)).length>0)return s}var b=n-d+c,x=r-h+c;if((l=yn(e,t,n,r,b,x,c+o)).length>0&&l[0]<=b&&l[1]<=x)return[l[0],l[1]];var w=n+d-c,E=r-h+c;if((l=yn(e,t,n,r,w,E,c+o)).length>0&&l[0]>=w&&l[1]<=E)return[l[0],l[1]];var k=n+d-c,T=r+h-c;if((l=yn(e,t,n,r,k,T,c+o)).length>0&&l[0]>=k&&l[1]>=T)return[l[0],l[1]];var C=n-d+c,P=r+h-c;return(l=yn(e,t,n,r,C,P,c+o)).length>0&&l[0]<=C&&l[1]>=P?[l[0],l[1]]:[]},ln=function(e,t,n,r,a,i,o){var s=o,l=Math.min(n,a),u=Math.max(n,a),c=Math.min(r,i),d=Math.max(r,i);return l-s<=e&&e<=u+s&&c-s<=t&&t<=d+s},un=function(e,t,n,r,a,i,o,s,l){var u=Math.min(n,o,a)-l,c=Math.max(n,o,a)+l,d=Math.min(r,s,i)-l,h=Math.max(r,s,i)+l;return!(e<u||e>c||t<d||t>h)},cn=function(e,t,n,r,a,i,o,s){var l=[];!function(e,t,n,r,a){var i,o,s,l,u,c,d,h;0===e&&(e=1e-5),s=-27*(r/=e)+(t/=e)*(9*(n/=e)-t*t*2),i=(o=(3*n-t*t)/9)*o*o+(s/=54)*s,a[1]=0,d=t/3,i>0?(u=(u=s+Math.sqrt(i))<0?-Math.pow(-u,1/3):Math.pow(u,1/3),c=(c=s-Math.sqrt(i))<0?-Math.pow(-c,1/3):Math.pow(c,1/3),a[0]=-d+u+c,d+=(u+c)/2,a[4]=a[2]=-d,d=Math.sqrt(3)*(-c+u)/2,a[3]=d,a[5]=-d):(a[5]=a[3]=0,0===i?(h=s<0?-Math.pow(-s,1/3):Math.pow(s,1/3),a[0]=2*h-d,a[4]=a[2]=-(h+d)):(l=(o=-o)*o*o,l=Math.acos(s/Math.sqrt(l)),h=2*Math.sqrt(o),a[0]=-d+h*Math.cos(l/3),a[2]=-d+h*Math.cos((l+2*Math.PI)/3),a[4]=-d+h*Math.cos((l+4*Math.PI)/3)))}(1*n*n-4*n*a+2*n*o+4*a*a-4*a*o+o*o+r*r-4*r*i+2*r*s+4*i*i-4*i*s+s*s,9*n*a-3*n*n-3*n*o-6*a*a+3*a*o+9*r*i-3*r*r-3*r*s-6*i*i+3*i*s,3*n*n-6*n*a+n*o-n*e+2*a*a+2*a*e-o*e+3*r*r-6*r*i+r*s-r*t+2*i*i+2*i*t-s*t,1*n*a-n*n+n*e-a*e+r*i-r*r+r*t-i*t,l);for(var u=[],c=0;c<6;c+=2)Math.abs(l[c+1])<1e-7&&l[c]>=0&&l[c]<=1&&u.push(l[c]);u.push(1),u.push(0);for(var d,h,f,p=-1,g=0;g<u.length;g++)d=Math.pow(1-u[g],2)*n+2*(1-u[g])*u[g]*a+u[g]*u[g]*o,h=Math.pow(1-u[g],2)*r+2*(1-u[g])*u[g]*i+u[g]*u[g]*s,f=Math.pow(d-e,2)+Math.pow(h-t,2),p>=0?f<p&&(p=f):p=f;return p},dn=function(e,t,n,r,a,i){var o=[e-n,t-r],s=[a-n,i-r],l=s[0]*s[0]+s[1]*s[1],u=o[0]*o[0]+o[1]*o[1],c=o[0]*s[0]+o[1]*s[1],d=c*c/l;return c<0?u:d>l?(e-a)*(e-a)+(t-i)*(t-i):u-d},hn=function(e,t,n){for(var r,a,i,o,s=0,l=0;l<n.length/2;l++)if(r=n[2*l],a=n[2*l+1],l+1<n.length/2?(i=n[2*(l+1)],o=n[2*(l+1)+1]):(i=n[2*(l+1-n.length/2)],o=n[2*(l+1-n.length/2)+1]),r==e&&i==e);else{if(!(r>=e&&e>=i||r<=e&&e<=i))continue;(e-r)/(i-r)*(o-a)+a>t&&s++}return s%2!=0},fn=function(e,t,n,r,a,i,o,s,l){var u,c=new Array(n.length);null!=s[0]?(u=Math.atan(s[1]/s[0]),s[0]<0?u+=Math.PI/2:u=-u-Math.PI/2):u=s;for(var d,h=Math.cos(-u),f=Math.sin(-u),p=0;p<c.length/2;p++)c[2*p]=i/2*(n[2*p]*h-n[2*p+1]*f),c[2*p+1]=o/2*(n[2*p+1]*h+n[2*p]*f),c[2*p]+=r,c[2*p+1]+=a;if(l>0){var g=gn(c,-l);d=pn(g)}else d=c;return hn(e,t,d)},pn=function(e){for(var t,n,r,a,i,o,s,l,u=new Array(e.length/2),c=0;c<e.length/4;c++){t=e[4*c],n=e[4*c+1],r=e[4*c+2],a=e[4*c+3],c<e.length/4-1?(i=e[4*(c+1)],o=e[4*(c+1)+1],s=e[4*(c+1)+2],l=e[4*(c+1)+3]):(i=e[0],o=e[1],s=e[2],l=e[3]);var d=bn(t,n,r,a,i,o,s,l,!0);u[2*c]=d[0],u[2*c+1]=d[1]}return u},gn=function(e,t){for(var n,r,a,i,o=new Array(2*e.length),s=0;s<e.length/2;s++){n=e[2*s],r=e[2*s+1],s<e.length/2-1?(a=e[2*(s+1)],i=e[2*(s+1)+1]):(a=e[0],i=e[1]);var l=i-r,u=-(a-n),c=Math.sqrt(l*l+u*u),d=l/c,h=u/c;o[4*s]=n+d*t,o[4*s+1]=r+h*t,o[4*s+2]=a+d*t,o[4*s+3]=i+h*t}return o},vn=function(e,t,n,r,a,i,o){return e-=a,t-=i,(e/=n/2+o)*e+(t/=r/2+o)*t<=1},yn=function(e,t,n,r,a,i,o){var s=[n-e,r-t],l=[e-a,t-i],u=s[0]*s[0]+s[1]*s[1],c=2*(l[0]*s[0]+l[1]*s[1]),d=c*c-4*u*(l[0]*l[0]+l[1]*l[1]-o*o);if(d<0)return[];var h=(-c+Math.sqrt(d))/(2*u),f=(-c-Math.sqrt(d))/(2*u),p=Math.min(h,f),g=Math.max(h,f),v=[];if(p>=0&&p<=1&&v.push(p),g>=0&&g<=1&&v.push(g),0===v.length)return[];var y=v[0]*s[0]+e,m=v[0]*s[1]+t;return v.length>1?v[0]==v[1]?[y,m]:[y,m,v[1]*s[0]+e,v[1]*s[1]+t]:[y,m]},mn=function(e,t,n){return t<=e&&e<=n||n<=e&&e<=t?e:e<=t&&t<=n||n<=t&&t<=e?t:n},bn=function(e,t,n,r,a,i,o,s,l){var u=e-a,c=n-e,d=o-a,h=t-i,f=r-t,p=s-i,g=d*h-p*u,v=c*h-f*u,y=p*c-d*f;if(0!==y){var m=g/y,b=v/y,x=-.001;return x<=m&&m<=1.001&&x<=b&&b<=1.001||l?[e+m*c,t+m*f]:[]}return 0===g||0===v?mn(e,n,o)===o?[o,s]:mn(e,n,a)===a?[a,i]:mn(a,o,n)===n?[n,r]:[]:[]},xn=function(e,t,n,r,a,i,o,s){var l,u,c,d,h,f,p=[],g=new Array(n.length),v=!0;if(null==i&&(v=!1),v){for(var y=0;y<g.length/2;y++)g[2*y]=n[2*y]*i+r,g[2*y+1]=n[2*y+1]*o+a;if(s>0){var m=gn(g,-s);u=pn(m)}else u=g}else u=n;for(var b=0;b<u.length/2;b++)c=u[2*b],d=u[2*b+1],b<u.length/2-1?(h=u[2*(b+1)],f=u[2*(b+1)+1]):(h=u[0],f=u[1]),0!==(l=bn(e,t,r,a,c,d,h,f)).length&&p.push(l[0],l[1]);return p},wn=function(e,t,n){var r=[e[0]-t[0],e[1]-t[1]],a=Math.sqrt(r[0]*r[0]+r[1]*r[1]),i=(a-n)/a;return i<0&&(i=1e-5),[t[0]+i*r[0],t[1]+i*r[1]]},En=function(e,t){var n=Tn(e,t);return n=kn(n)},kn=function(e){for(var t,n,r=e.length/2,a=1/0,i=1/0,o=-1/0,s=-1/0,l=0;l<r;l++)t=e[2*l],n=e[2*l+1],a=Math.min(a,t),o=Math.max(o,t),i=Math.min(i,n),s=Math.max(s,n);for(var u=2/(o-a),c=2/(s-i),d=0;d<r;d++)t=e[2*d]=e[2*d]*u,n=e[2*d+1]=e[2*d+1]*c,a=Math.min(a,t),o=Math.max(o,t),i=Math.min(i,n),s=Math.max(s,n);if(i<-1)for(var h=0;h<r;h++)n=e[2*h+1]=e[2*h+1]+(-1-i);return e},Tn=function(e,t){var n=1/e*2*Math.PI,r=e%2==0?Math.PI/2+n/2:Math.PI/2;r+=t;for(var a,i=new Array(2*e),o=0;o<e;o++)a=o*n+r,i[2*o]=Math.cos(a),i[2*o+1]=Math.sin(-a);return i},Cn=function(e,t){return Math.min(e/4,t/4,8)},Pn=function(e,t){return Math.min(e/10,t/10,8)},Sn=function(e,t){return{heightOffset:Math.min(15,.05*t),widthOffset:Math.min(100,.25*e),ctrlPtOffsetPct:.05}};function Bn(e,t){function n(e){for(var t=[],n=0;n<e.length;n++){var r=e[n],a=e[(n+1)%e.length],i={x:a.x-r.x,y:a.y-r.y},o={x:-i.y,y:i.x},s=Math.sqrt(o.x*o.x+o.y*o.y);t.push({x:o.x/s,y:o.y/s})}return t}function r(e,t){var n,r=1/0,a=-1/0,i=o(e);try{for(i.s();!(n=i.n()).done;){var s=n.value,l=s.x*t.x+s.y*t.y;r=Math.min(r,l),a=Math.max(a,l)}}catch(u){i.e(u)}finally{i.f()}return{min:r,max:a}}function a(e,t){return!(e.max<t.min||t.max<e.min)}var i,s=o([].concat(u(n(e)),u(n(t))));try{for(s.s();!(i=s.n()).done;){var l=i.value;if(!a(r(e,l),r(t,l)))return!1}}catch(c){s.e(c)}finally{s.f()}return!0}var Dn=ut({dampingFactor:.8,precision:1e-6,iterations:200,weight:function(e){return 1}}),_n={pageRank:function(e){for(var t=Dn(e),n=t.dampingFactor,r=t.precision,a=t.iterations,i=t.weight,o=this._private.cy,s=this.byGroup(),l=s.nodes,u=s.edges,c=l.length,d=c*c,h=u.length,f=new Array(d),p=new Array(c),g=(1-n)/c,v=0;v<c;v++){for(var y=0;y<c;y++){f[v*c+y]=0}p[v]=0}for(var m=0;m<h;m++){var b=u[m],x=b.data("source"),w=b.data("target");if(x!==w){var E=l.indexOfId(x),k=l.indexOfId(w),T=i(b);f[k*c+E]+=T,p[E]+=T}}for(var C=1/c+g,P=0;P<c;P++)if(0===p[P])for(var S=0;S<c;S++){f[S*c+P]=C}else for(var B=0;B<c;B++){var D=B*c+P;f[D]=f[D]/p[P]+g}for(var _,A=new Array(c),M=new Array(c),R=0;R<c;R++)A[R]=1;for(var I=0;I<a;I++){for(var N=0;N<c;N++)M[N]=0;for(var L=0;L<c;L++)for(var z=0;z<c;z++){var O=L*c+z;M[L]+=f[O]*A[z]}Kt(M),_=A,A=M,M=_;for(var V=0,F=0;F<c;F++){var j=_[F]-A[F];V+=j*j}if(V<r)break}return{rank:function(e){return e=o.collection(e)[0],A[l.indexOf(e)]}}}},An=ut({root:null,weight:function(e){return 1},directed:!1,alpha:0}),Mn={degreeCentralityNormalized:function(e){e=An(e);var t=this.cy(),n=this.nodes(),r=n.length;if(e.directed){for(var a={},i={},o=0,s=0,l=0;l<r;l++){var u=n[l],c=u.id();e.root=u;var d=this.degreeCentrality(e);o<d.indegree&&(o=d.indegree),s<d.outdegree&&(s=d.outdegree),a[c]=d.indegree,i[c]=d.outdegree}return{indegree:function(e){return 0==o?0:(K(e)&&(e=t.filter(e)),a[e.id()]/o)},outdegree:function(e){return 0===s?0:(K(e)&&(e=t.filter(e)),i[e.id()]/s)}}}for(var h={},f=0,p=0;p<r;p++){var g=n[p];e.root=g;var v=this.degreeCentrality(e);f<v.degree&&(f=v.degree),h[g.id()]=v.degree}return{degree:function(e){return 0===f?0:(K(e)&&(e=t.filter(e)),h[e.id()]/f)}}},degreeCentrality:function(e){e=An(e);var t=this.cy(),n=this,r=e,a=r.root,i=r.weight,o=r.directed,s=r.alpha;if(a=t.collection(a)[0],o){for(var l=a.connectedEdges(),u=l.filter((function(e){return e.target().same(a)&&n.has(e)})),c=l.filter((function(e){return e.source().same(a)&&n.has(e)})),d=u.length,h=c.length,f=0,p=0,g=0;g<u.length;g++)f+=i(u[g]);for(var v=0;v<c.length;v++)p+=i(c[v]);return{indegree:Math.pow(d,1-s)*Math.pow(f,s),outdegree:Math.pow(h,1-s)*Math.pow(p,s)}}for(var y=a.connectedEdges().intersection(n),m=y.length,b=0,x=0;x<y.length;x++)b+=i(y[x]);return{degree:Math.pow(m,1-s)*Math.pow(b,s)}}};Mn.dc=Mn.degreeCentrality,Mn.dcn=Mn.degreeCentralityNormalised=Mn.degreeCentralityNormalized;var Rn=ut({harmonic:!0,weight:function(){return 1},directed:!1,root:null}),In={closenessCentralityNormalized:function(e){for(var t=Rn(e),n=t.harmonic,r=t.weight,a=t.directed,i=this.cy(),o={},s=0,l=this.nodes(),u=this.floydWarshall({weight:r,directed:a}),c=0;c<l.length;c++){for(var d=0,h=l[c],f=0;f<l.length;f++)if(c!==f){var p=u.distance(h,l[f]);d+=n?1/p:p}n||(d=1/d),s<d&&(s=d),o[h.id()]=d}return{closeness:function(e){return 0==s?0:(e=K(e)?i.filter(e)[0].id():e.id(),o[e]/s)}}},closenessCentrality:function(e){var t=Rn(e),n=t.root,r=t.weight,a=t.directed,i=t.harmonic;n=this.filter(n)[0];for(var o=this.dijkstra({root:n,weight:r,directed:a}),s=0,l=this.nodes(),u=0;u<l.length;u++){var c=l[u];if(!c.same(n)){var d=o.distanceTo(c);s+=i?1/d:d}}return i?s:1/s}};In.cc=In.closenessCentrality,In.ccn=In.closenessCentralityNormalised=In.closenessCentralityNormalized;var Nn=ut({weight:null,directed:!1}),Ln={betweennessCentrality:function(e){for(var t=Nn(e),n=t.directed,r=t.weight,a=null!=r,i=this.cy(),o=this.nodes(),s={},l={},u=0,c=function(e,t){l[e]=t,t>u&&(u=t)},d=function(e){return l[e]},h=0;h<o.length;h++){var f=o[h],p=f.id();s[p]=n?f.outgoers().nodes():f.openNeighborhood().nodes(),c(p,0)}for(var g=function(){for(var e=o[v].id(),t=[],n={},l={},u={},h=new Pt((function(e,t){return u[e]-u[t]})),f=0;f<o.length;f++){var p=o[f].id();n[p]=[],l[p]=0,u[p]=1/0}for(l[e]=1,u[e]=0,h.push(e);!h.empty();){var g=h.pop();if(t.push(g),a)for(var y=0;y<s[g].length;y++){var m=s[g][y],b=i.getElementById(g),x=void 0;x=b.edgesTo(m).length>0?b.edgesTo(m)[0]:m.edgesTo(b)[0];var w=r(x);m=m.id(),u[m]>u[g]+w&&(u[m]=u[g]+w,h.nodes.indexOf(m)<0?h.push(m):h.updateItem(m),l[m]=0,n[m]=[]),u[m]==u[g]+w&&(l[m]=l[m]+l[g],n[m].push(g))}else for(var E=0;E<s[g].length;E++){var k=s[g][E].id();u[k]==1/0&&(h.push(k),u[k]=u[g]+1),u[k]==u[g]+1&&(l[k]=l[k]+l[g],n[k].push(g))}}for(var T={},C=0;C<o.length;C++)T[o[C].id()]=0;for(;t.length>0;){for(var P=t.pop(),S=0;S<n[P].length;S++){var B=n[P][S];T[B]=T[B]+l[B]/l[P]*(1+T[P])}P!=o[v].id()&&c(P,d(P)+T[P])}},v=0;v<o.length;v++)g();var y={betweenness:function(e){var t=i.collection(e).id();return d(t)},betweennessNormalized:function(e){if(0==u)return 0;var t=i.collection(e).id();return d(t)/u}};return y.betweennessNormalised=y.betweennessNormalized,y}};Ln.bc=Ln.betweennessCentrality;var zn=ut({expandFactor:2,inflateFactor:2,multFactor:1,maxIterations:20,attributes:[function(e){return 1}]}),On=function(e,t){for(var n=0,r=0;r<t.length;r++)n+=t[r](e);return n},Vn=function(e,t){for(var n,r=0;r<t;r++){n=0;for(var a=0;a<t;a++)n+=e[a*t+r];for(var i=0;i<t;i++)e[i*t+r]=e[i*t+r]/n}},Fn=function(e,t,n){for(var r=new Array(n*n),a=0;a<n;a++){for(var i=0;i<n;i++)r[a*n+i]=0;for(var o=0;o<n;o++)for(var s=0;s<n;s++)r[a*n+s]+=e[a*n+o]*t[o*n+s]}return r},jn=function(e,t,n){for(var r=e.slice(0),a=1;a<n;a++)e=Fn(e,r,t);return e},Xn=function(e,t,n){for(var r=new Array(t*t),a=0;a<t*t;a++)r[a]=Math.pow(e[a],n);return Vn(r,t),r},qn=function(e,t,n,r){for(var a=0;a<n;a++){if(Math.round(e[a]*Math.pow(10,r))/Math.pow(10,r)!==Math.round(t[a]*Math.pow(10,r))/Math.pow(10,r))return!1}return!0},Yn=function(e,t){for(var n=0;n<e.length;n++)if(!t[n]||e[n].id()!==t[n].id())return!1;return!0},Wn=function(e){for(var t=this.nodes(),n=this.edges(),r=this.cy(),a=function(e){return zn(e)}(e),i={},o=0;o<t.length;o++)i[t[o].id()]=o;for(var s,l=t.length,u=l*l,c=new Array(u),d=0;d<u;d++)c[d]=0;for(var h=0;h<n.length;h++){var f=n[h],p=i[f.source().id()],g=i[f.target().id()],v=On(f,a.attributes);c[p*l+g]+=v,c[g*l+p]+=v}!function(e,t,n){for(var r=0;r<t;r++)e[r*t+r]=n}(c,l,a.multFactor),Vn(c,l);for(var y=!0,m=0;y&&m<a.maxIterations;)y=!1,s=jn(c,l,a.expandFactor),c=Xn(s,l,a.inflateFactor),qn(c,s,u,4)||(y=!0),m++;var b=function(e,t,n,r){for(var a=[],i=0;i<t;i++){for(var o=[],s=0;s<t;s++)Math.round(1e3*e[i*t+s])/1e3>0&&o.push(n[s]);0!==o.length&&a.push(r.collection(o))}return a}(c,l,t,r);return b=function(e){for(var t=0;t<e.length;t++)for(var n=0;n<e.length;n++)t!=n&&Yn(e[t],e[n])&&e.splice(n,1);return e}(b),b},Un={markovClustering:Wn,mcl:Wn},Hn=function(e){return e},Kn=function(e,t){return Math.abs(t-e)},Gn=function(e,t,n){return e+Kn(t,n)},Zn=function(e,t,n){return e+Math.pow(n-t,2)},$n=function(e){return Math.sqrt(e)},Qn=function(e,t,n){return Math.max(e,Kn(t,n))},Jn=function(e,t,n,r,a){for(var i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:Hn,o=r,s=0;s<e;s++)o=a(o,t(s),n(s));return i(o)},er={euclidean:function(e,t,n){return e>=2?Jn(e,t,n,0,Zn,$n):Jn(e,t,n,0,Gn)},squaredEuclidean:function(e,t,n){return Jn(e,t,n,0,Zn)},manhattan:function(e,t,n){return Jn(e,t,n,0,Gn)},max:function(e,t,n){return Jn(e,t,n,-1/0,Qn)}};function tr(e,t,n,r,a,i){var o;return o=G(e)?e:er[e]||er.euclidean,0===t&&G(e)?o(a,i):o(t,n,r,a,i)}er["squared-euclidean"]=er.squaredEuclidean,er.squaredeuclidean=er.squaredEuclidean;var nr=ut({k:2,m:2,sensitivityThreshold:1e-4,distance:"euclidean",maxIterations:10,attributes:[],testMode:!1,testCentroids:null}),rr=function(e){return nr(e)},ar=function(e,t,n,r,a){var i="kMedoids"!==a?function(e){return n[e]}:function(e){return r[e](n)},o=n,s=t;return tr(e,r.length,i,(function(e){return r[e](t)}),o,s)},ir=function(e,t,n){for(var r=n.length,a=new Array(r),i=new Array(r),o=new Array(t),s=null,l=0;l<r;l++)a[l]=e.min(n[l]).value,i[l]=e.max(n[l]).value;for(var u=0;u<t;u++){s=[];for(var c=0;c<r;c++)s[c]=Math.random()*(i[c]-a[c])+a[c];o[u]=s}return o},or=function(e,t,n,r,a){for(var i=1/0,o=0,s=0;s<t.length;s++){var l=ar(n,e,t[s],r,a);l<i&&(i=l,o=s)}return o},sr=function(e,t,n){for(var r=[],a=null,i=0;i<t.length;i++)n[(a=t[i]).id()]===e&&r.push(a);return r},lr=function(e,t,n){return Math.abs(t-e)<=n},ur=function(e,t,n){for(var r=0;r<e.length;r++)for(var a=0;a<e[r].length;a++){if(Math.abs(e[r][a]-t[r][a])>n)return!1}return!0},cr=function(e,t,n){for(var r=0;r<n;r++)if(e===t[r])return!0;return!1},dr=function(e,t){var n=new Array(t);if(e.length<50)for(var r=0;r<t;r++){for(var a=e[Math.floor(Math.random()*e.length)];cr(a,n,r);)a=e[Math.floor(Math.random()*e.length)];n[r]=a}else for(var i=0;i<t;i++)n[i]=e[Math.floor(Math.random()*e.length)];return n},hr=function(e,t,n){for(var r=0,a=0;a<t.length;a++)r+=ar("manhattan",t[a],e,n,"kMedoids");return r},fr=function(e,t,n,r,a){for(var i,o,s=0;s<t.length;s++)for(var l=0;l<e.length;l++)r[s][l]=Math.pow(n[s][l],a.m);for(var u=0;u<e.length;u++)for(var c=0;c<a.attributes.length;c++){i=0,o=0;for(var d=0;d<t.length;d++)i+=r[d][u]*a.attributes[c](t[d]),o+=r[d][u];e[u][c]=i/o}},pr=function(e,t,n,r,a){for(var i=0;i<e.length;i++)t[i]=e[i].slice();for(var o,s,l,u=2/(a.m-1),c=0;c<n.length;c++)for(var d=0;d<r.length;d++){o=0;for(var h=0;h<n.length;h++)s=ar(a.distance,r[d],n[c],a.attributes,"cmeans"),l=ar(a.distance,r[d],n[h],a.attributes,"cmeans"),o+=Math.pow(s/l,u);e[d][c]=1/o}},gr=function(e){var t,n,r,a,i,o=this.cy(),s=this.nodes(),l=rr(e);a=new Array(s.length);for(var u=0;u<s.length;u++)a[u]=new Array(l.k);r=new Array(s.length);for(var c=0;c<s.length;c++)r[c]=new Array(l.k);for(var d=0;d<s.length;d++){for(var h=0,f=0;f<l.k;f++)r[d][f]=Math.random(),h+=r[d][f];for(var p=0;p<l.k;p++)r[d][p]=r[d][p]/h}n=new Array(l.k);for(var g=0;g<l.k;g++)n[g]=new Array(l.attributes.length);i=new Array(s.length);for(var v=0;v<s.length;v++)i[v]=new Array(l.k);for(var y=!0,m=0;y&&m<l.maxIterations;)y=!1,fr(n,s,r,i,l),pr(r,a,n,s,l),ur(r,a,l.sensitivityThreshold)||(y=!0),m++;return t=function(e,t,n,r){for(var a,i,o=new Array(n.k),s=0;s<o.length;s++)o[s]=[];for(var l=0;l<t.length;l++){a=-1/0,i=-1;for(var u=0;u<t[0].length;u++)t[l][u]>a&&(a=t[l][u],i=u);o[i].push(e[l])}for(var c=0;c<o.length;c++)o[c]=r.collection(o[c]);return o}(s,r,l,o),{clusters:t,degreeOfMembership:r}},vr={kMeans:function(e){var t,n=this.cy(),r=this.nodes(),a=null,i=rr(e),o=new Array(i.k),s={};i.testMode?"number"==typeof i.testCentroids?(i.testCentroids,t=ir(r,i.k,i.attributes)):t="object"===d(i.testCentroids)?i.testCentroids:ir(r,i.k,i.attributes):t=ir(r,i.k,i.attributes);for(var l=!0,u=0;l&&u<i.maxIterations;){for(var c=0;c<r.length;c++)s[(a=r[c]).id()]=or(a,t,i.distance,i.attributes,"kMeans");l=!1;for(var h=0;h<i.k;h++){var f=sr(h,r,s);if(0!==f.length){for(var p=i.attributes.length,g=t[h],v=new Array(p),y=new Array(p),m=0;m<p;m++){y[m]=0;for(var b=0;b<f.length;b++)a=f[b],y[m]+=i.attributes[m](a);v[m]=y[m]/f.length,lr(v[m],g[m],i.sensitivityThreshold)||(l=!0)}t[h]=v,o[h]=n.collection(f)}}u++}return o},kMedoids:function(e){var t,n,r=this.cy(),a=this.nodes(),i=null,o=rr(e),s=new Array(o.k),l={},u=new Array(o.k);o.testMode?"number"==typeof o.testCentroids||(t="object"===d(o.testCentroids)?o.testCentroids:dr(a,o.k)):t=dr(a,o.k);for(var c=!0,h=0;c&&h<o.maxIterations;){for(var f=0;f<a.length;f++)l[(i=a[f]).id()]=or(i,t,o.distance,o.attributes,"kMedoids");c=!1;for(var p=0;p<t.length;p++){var g=sr(p,a,l);if(0!==g.length){u[p]=hr(t[p],g,o.attributes);for(var v=0;v<g.length;v++)(n=hr(g[v],g,o.attributes))<u[p]&&(u[p]=n,t[p]=g[v],c=!0);s[p]=r.collection(g)}}h++}return s},fuzzyCMeans:gr,fcm:gr},yr=ut({distance:"euclidean",linkage:"min",mode:"threshold",threshold:1/0,addDendrogram:!1,dendrogramDepth:0,attributes:[]}),mr={single:"min",complete:"max"},br=function(e,t,n,r,a){for(var i,o=0,s=1/0,l=a.attributes,u=function(e,t){return tr(a.distance,l.length,(function(t){return l[t](e)}),(function(e){return l[e](t)}),e,t)},c=0;c<e.length;c++){var d=e[c].key,h=n[d][r[d]];h<s&&(o=d,s=h)}if("threshold"===a.mode&&s>=a.threshold||"dendrogram"===a.mode&&1===e.length)return!1;var f,p=t[o],g=t[r[o]];f="dendrogram"===a.mode?{left:p,right:g,key:p.key}:{value:p.value.concat(g.value),key:p.key},e[p.index]=f,e.splice(g.index,1),t[p.key]=f;for(var v=0;v<e.length;v++){var y=e[v];p.key===y.key?i=1/0:"min"===a.linkage?(i=n[p.key][y.key],n[p.key][y.key]>n[g.key][y.key]&&(i=n[g.key][y.key])):"max"===a.linkage?(i=n[p.key][y.key],n[p.key][y.key]<n[g.key][y.key]&&(i=n[g.key][y.key])):i="mean"===a.linkage?(n[p.key][y.key]*p.size+n[g.key][y.key]*g.size)/(p.size+g.size):"dendrogram"===a.mode?u(y.value,p.value):u(y.value[0],p.value[0]),n[p.key][y.key]=n[y.key][p.key]=i}for(var m=0;m<e.length;m++){var b=e[m].key;if(r[b]===p.key||r[b]===g.key){for(var x=b,w=0;w<e.length;w++){var E=e[w].key;n[b][E]<n[b][x]&&(x=E)}r[b]=x}e[m].index=m}return p.key=g.key=p.index=g.index=null,!0},xr=function(e,t,n){e&&(e.value?t.push(e.value):(e.left&&xr(e.left,t),e.right&&xr(e.right,t)))},wr=function(e,t){if(!e)return"";if(e.left&&e.right){var n=wr(e.left,t),r=wr(e.right,t),a=t.add({group:"nodes",data:{id:n+","+r}});return t.add({group:"edges",data:{source:n,target:a.id()}}),t.add({group:"edges",data:{source:r,target:a.id()}}),a.id()}return e.value?e.value.id():void 0},Er=function(e,t,n){if(!e)return[];var r=[],a=[],i=[];return 0===t?(e.left&&xr(e.left,r),e.right&&xr(e.right,a),i=r.concat(a),[n.collection(i)]):1===t?e.value?[n.collection(e.value)]:(e.left&&xr(e.left,r),e.right&&xr(e.right,a),[n.collection(r),n.collection(a)]):e.value?[n.collection(e.value)]:(e.left&&(r=Er(e.left,t-1,n)),e.right&&(a=Er(e.right,t-1,n)),r.concat(a))},kr=function(e){for(var t=this.cy(),n=this.nodes(),r=function(e){var t=yr(e),n=mr[t.linkage];return null!=n&&(t.linkage=n),t}(e),a=r.attributes,i=function(e,t){return tr(r.distance,a.length,(function(t){return a[t](e)}),(function(e){return a[e](t)}),e,t)},o=[],s=[],l=[],u=[],c=0;c<n.length;c++){var d={value:"dendrogram"===r.mode?n[c]:[n[c]],key:c,index:c};o[c]=d,u[c]=d,s[c]=[],l[c]=0}for(var h=0;h<o.length;h++)for(var f=0;f<=h;f++){var p=void 0;p="dendrogram"===r.mode?h===f?1/0:i(o[h].value,o[f].value):h===f?1/0:i(o[h].value[0],o[f].value[0]),s[h][f]=p,s[f][h]=p,p<s[h][l[h]]&&(l[h]=f)}for(var g,v=br(o,u,s,l,r);v;)v=br(o,u,s,l,r);return"dendrogram"===r.mode?(g=Er(o[0],r.dendrogramDepth,t),r.addDendrogram&&wr(o[0],t)):(g=new Array(o.length),o.forEach((function(e,n){e.key=e.index=null,g[n]=t.collection(e.value)}))),g},Tr={hierarchicalClustering:kr,hca:kr},Cr=ut({distance:"euclidean",preference:"median",damping:.8,maxIterations:1e3,minIterations:100,attributes:[]}),Pr=function(e,t,n,r){var a=function(e,t){return r[t](e)};return-tr(e,r.length,(function(e){return a(t,e)}),(function(e){return a(n,e)}),t,n)},Sr=function(e,t){var n=null;return n="median"===t?function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.length,r=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],a=!(arguments.length>5&&void 0!==arguments[5])||arguments[5];arguments.length>3&&void 0!==arguments[3]&&!arguments[3]?(n<e.length&&e.splice(n,e.length-n),t>0&&e.splice(0,t)):e=e.slice(t,n);for(var i=0,o=e.length-1;o>=0;o--){var s=e[o];a?isFinite(s)||(e[o]=-1/0,i++):e.splice(o,1)}r&&e.sort((function(e,t){return e-t}));var l=e.length,u=Math.floor(l/2);return l%2!=0?e[u+1+i]:(e[u-1+i]+e[u+i])/2}(e):"mean"===t?function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.length,r=0,a=0,i=t;i<n;i++){var o=e[i];isFinite(o)&&(r+=o,a++)}return r/a}(e):"min"===t?function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.length,r=1/0,a=t;a<n;a++){var i=e[a];isFinite(i)&&(r=Math.min(i,r))}return r}(e):"max"===t?function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.length,r=-1/0,a=t;a<n;a++){var i=e[a];isFinite(i)&&(r=Math.max(i,r))}return r}(e):t,n},Br=function(e,t,n){for(var r=[],a=0;a<e;a++){for(var i=-1,o=-1/0,s=0;s<n.length;s++){var l=n[s];t[a*e+l]>o&&(i=l,o=t[a*e+l])}i>0&&r.push(i)}for(var u=0;u<n.length;u++)r[n[u]]=n[u];return r},Dr=function(e){for(var t,n,r,a,i,o,s=this.cy(),l=this.nodes(),u=function(e){var t=e.damping,n=e.preference;.5<=t&&t<1||nt("Damping must range on [0.5, 1).  Got: ".concat(t));var r=["median","mean","min","max"];return r.some((function(e){return e===n}))||Q(n)||nt("Preference must be one of [".concat(r.map((function(e){return"'".concat(e,"'")})).join(", "),"] or a number.  Got: ").concat(n)),Cr(e)}(e),c={},d=0;d<l.length;d++)c[l[d].id()]=d;n=(t=l.length)*t,r=new Array(n);for(var h=0;h<n;h++)r[h]=-1/0;for(var f=0;f<t;f++)for(var p=0;p<t;p++)f!==p&&(r[f*t+p]=Pr(u.distance,l[f],l[p],u.attributes));a=Sr(r,u.preference);for(var g=0;g<t;g++)r[g*t+g]=a;i=new Array(n);for(var v=0;v<n;v++)i[v]=0;o=new Array(n);for(var y=0;y<n;y++)o[y]=0;for(var m=new Array(t),b=new Array(t),x=new Array(t),w=0;w<t;w++)m[w]=0,b[w]=0,x[w]=0;for(var E,k=new Array(t*u.minIterations),T=0;T<k.length;T++)k[T]=0;for(E=0;E<u.maxIterations;E++){for(var C=0;C<t;C++){for(var P=-1/0,S=-1/0,B=-1,D=0,_=0;_<t;_++)m[_]=i[C*t+_],(D=o[C*t+_]+r[C*t+_])>=P?(S=P,P=D,B=_):D>S&&(S=D);for(var A=0;A<t;A++)i[C*t+A]=(1-u.damping)*(r[C*t+A]-P)+u.damping*m[A];i[C*t+B]=(1-u.damping)*(r[C*t+B]-S)+u.damping*m[B]}for(var M=0;M<t;M++){for(var R=0,I=0;I<t;I++)m[I]=o[I*t+M],b[I]=Math.max(0,i[I*t+M]),R+=b[I];R-=b[M],b[M]=i[M*t+M],R+=b[M];for(var N=0;N<t;N++)o[N*t+M]=(1-u.damping)*Math.min(0,R-b[N])+u.damping*m[N];o[M*t+M]=(1-u.damping)*(R-b[M])+u.damping*m[M]}for(var L=0,z=0;z<t;z++){var O=o[z*t+z]+i[z*t+z]>0?1:0;k[E%u.minIterations*t+z]=O,L+=O}if(L>0&&(E>=u.minIterations-1||E==u.maxIterations-1)){for(var V=0,F=0;F<t;F++){x[F]=0;for(var j=0;j<u.minIterations;j++)x[F]+=k[j*t+F];0!==x[F]&&x[F]!==u.minIterations||V++}if(V===t)break}}for(var X=function(e,t,n){for(var r=[],a=0;a<e;a++)t[a*e+a]+n[a*e+a]>0&&r.push(a);return r}(t,i,o),q=function(e,t,n){for(var r=Br(e,t,n),a=0;a<n.length;a++){for(var i=[],o=0;o<r.length;o++)r[o]===n[a]&&i.push(o);for(var s=-1,l=-1/0,u=0;u<i.length;u++){for(var c=0,d=0;d<i.length;d++)c+=t[i[d]*e+i[u]];c>l&&(s=u,l=c)}n[a]=i[s]}return Br(e,t,n)}(t,r,X),Y={},W=0;W<X.length;W++)Y[X[W]]=[];for(var U=0;U<l.length;U++){var H=q[c[l[U].id()]];null!=H&&Y[H].push(l[U])}for(var K=new Array(X.length),G=0;G<X.length;G++)K[G]=s.collection(Y[X[G]]);return K},_r={affinityPropagation:Dr,ap:Dr},Ar=ut({root:void 0,directed:!1}),Mr=function(){var e=this,t={},n=0,r=0,a=[],i=[],o={},s=function(l,u,c){l===c&&(r+=1),t[u]={id:n,low:n++,cutVertex:!1};var d,h,f,p,g=e.getElementById(u).connectedEdges().intersection(e);0===g.size()?a.push(e.spawn(e.getElementById(u))):g.forEach((function(n){d=n.source().id(),h=n.target().id(),(f=d===u?h:d)!==c&&(p=n.id(),o[p]||(o[p]=!0,i.push({x:u,y:f,edge:n})),f in t?t[u].low=Math.min(t[u].low,t[f].id):(s(l,f,u),t[u].low=Math.min(t[u].low,t[f].low),t[u].id<=t[f].low&&(t[u].cutVertex=!0,function(n,r){for(var o=i.length-1,s=[],l=e.spawn();i[o].x!=n||i[o].y!=r;)s.push(i.pop().edge),o--;s.push(i.pop().edge),s.forEach((function(n){var r=n.connectedNodes().intersection(e);l.merge(n),r.forEach((function(n){var r=n.id(),a=n.connectedEdges().intersection(e);l.merge(n),t[r].cutVertex?l.merge(a.filter((function(e){return e.isLoop()}))):l.merge(a)}))})),a.push(l)}(u,f))))}))};e.forEach((function(e){if(e.isNode()){var n=e.id();n in t||(r=0,s(n,n),t[n].cutVertex=r>1)}}));var l=Object.keys(t).filter((function(e){return t[e].cutVertex})).map((function(t){return e.getElementById(t)}));return{cut:e.spawn(l),components:a}},Rr=function(){var e=this,t={},n=0,r=[],a=[],i=e.spawn(e),o=function(s){if(a.push(s),t[s]={index:n,low:n++,explored:!1},e.getElementById(s).connectedEdges().intersection(e).forEach((function(e){var n=e.target().id();n!==s&&(n in t||o(n),t[n].explored||(t[s].low=Math.min(t[s].low,t[n].low)))})),t[s].index===t[s].low){for(var l=e.spawn();;){var u=a.pop();if(l.merge(e.getElementById(u)),t[u].low=t[s].index,t[u].explored=!0,u===s)break}var c=l.edgesWith(l),d=l.merge(c);r.push(d),i=i.difference(d)}};return e.forEach((function(e){if(e.isNode()){var n=e.id();n in t||o(n)}})),{cut:i,components:r}},Ir={};[bt,Bt,Dt,At,Rt,Nt,Vt,_n,Mn,In,Ln,Un,vr,Tr,_r,{hierholzer:function(e){if(!$(e)){var t=arguments;e={root:t[0],directed:t[1]}}var n,r,a,i=Ar(e),o=i.root,s=i.directed,l=this,u=!1;o&&(a=K(o)?this.filter(o)[0].id():o[0].id());var c={},d={};s?l.forEach((function(e){var t=e.id();if(e.isNode()){var a=e.indegree(!0),i=e.outdegree(!0),o=a-i,s=i-a;1==o?n?u=!0:n=t:1==s?r?u=!0:r=t:(s>1||o>1)&&(u=!0),c[t]=[],e.outgoers().forEach((function(e){e.isEdge()&&c[t].push(e.id())}))}else d[t]=[void 0,e.target().id()]})):l.forEach((function(e){var t=e.id();e.isNode()?(e.degree(!0)%2&&(n?r?u=!0:r=t:n=t),c[t]=[],e.connectedEdges().forEach((function(e){return c[t].push(e.id())}))):d[t]=[e.source().id(),e.target().id()]}));var h={found:!1,trail:void 0};if(u)return h;if(r&&n)if(s){if(a&&r!=a)return h;a=r}else{if(a&&r!=a&&n!=a)return h;a||(a=r)}else a||(a=l[0].id());var f=function(e){for(var t,n,r,a=e,i=[e];c[a].length;)t=c[a].shift(),n=d[t][0],a!=(r=d[t][1])?(c[r]=c[r].filter((function(e){return e!=t})),a=r):s||a==n||(c[n]=c[n].filter((function(e){return e!=t})),a=n),i.unshift(t),i.unshift(a);return i},p=[],g=[];for(g=f(a);1!=g.length;)0==c[g[0]].length?(p.unshift(l.getElementById(g.shift())),p.unshift(l.getElementById(g.shift()))):g=f(g.shift()).concat(g);for(var v in p.unshift(l.getElementById(g.shift())),c)if(c[v].length)return h;return h.found=!0,h.trail=this.spawn(p,!0),h}},{hopcroftTarjanBiconnected:Mr,htbc:Mr,htb:Mr,hopcroftTarjanBiconnectedComponents:Mr},{tarjanStronglyConnected:Rr,tsc:Rr,tscc:Rr,tarjanStronglyConnectedComponents:Rr}].forEach((function(e){me(Ir,e)}));var Nr=function(e){if(!(this instanceof Nr))return new Nr(e);this.id="Thenable/1.0.7",this.state=0,this.fulfillValue=void 0,this.rejectReason=void 0,this.onFulfilled=[],this.onRejected=[],this.proxy={then:this.then.bind(this)},"function"==typeof e&&e.call(this,this.fulfill.bind(this),this.reject.bind(this))};Nr.prototype={fulfill:function(e){return Lr(this,1,"fulfillValue",e)},reject:function(e){return Lr(this,2,"rejectReason",e)},then:function(e,t){var n=this,r=new Nr;return n.onFulfilled.push(Vr(e,r,"fulfill")),n.onRejected.push(Vr(t,r,"reject")),zr(n),r.proxy}};var Lr=function(e,t,n,r){return 0===e.state&&(e.state=t,e[n]=r,zr(e)),e},zr=function(e){1===e.state?Or(e,"onFulfilled",e.fulfillValue):2===e.state&&Or(e,"onRejected",e.rejectReason)},Or=function(e,t,n){if(0!==e[t].length){var r=e[t];e[t]=[];var a=function(){for(var e=0;e<r.length;e++)r[e](n)};"function"==typeof setImmediate?setImmediate(a):setTimeout(a,0)}},Vr=function(e,t,n){return function(r){if("function"!=typeof e)t[n].call(t,r);else{var a;try{a=e(r)}catch(i){return void t.reject(i)}Fr(t,a)}}},Fr=function(e,t){if(e!==t&&e.proxy!==t){var n;if("object"===d(t)&&null!==t||"function"==typeof t)try{n=t.then}catch(a){return void e.reject(a)}if("function"!=typeof n)e.fulfill(t);else{var r=!1;try{n.call(t,(function(n){r||(r=!0,n===t?e.reject(new TypeError("circular thenable chain")):Fr(e,n))}),(function(t){r||(r=!0,e.reject(t))}))}catch(a){r||e.reject(a)}}}else e.reject(new TypeError("cannot resolve promise with itself"))};Nr.all=function(e){return new Nr((function(t,n){for(var r=new Array(e.length),a=0,i=function(n,i){r[n]=i,++a===e.length&&t(r)},o=0;o<e.length;o++)!function(t){var r=e[t];null!=r&&null!=r.then?r.then((function(e){i(t,e)}),(function(e){n(e)})):i(t,r)}(o)}))},Nr.resolve=function(e){return new Nr((function(t,n){t(e)}))},Nr.reject=function(e){return new Nr((function(t,n){n(e)}))};var jr="undefined"!=typeof Promise?Promise:Nr,Xr=function(e,t,n){var r=re(e),a=!r,i=this._private=me({duration:1e3},t,n);if(i.target=e,i.style=i.style||i.css,i.started=!1,i.playing=!1,i.hooked=!1,i.applying=!1,i.progress=0,i.completes=[],i.frames=[],i.complete&&G(i.complete)&&i.completes.push(i.complete),a){var o=e.position();i.startPosition=i.startPosition||{x:o.x,y:o.y},i.startStyle=i.startStyle||e.cy().style().getAnimationStartStyle(e,i.style)}if(r){var s=e.pan();i.startPan={x:s.x,y:s.y},i.startZoom=e.zoom()}this.length=1,this[0]=this},qr=Xr.prototype;me(qr,{instanceString:function(){return"animation"},hook:function(){var e=this._private;if(!e.hooked){var t=e.target._private.animation;(e.queue?t.queue:t.current).push(this),ee(e.target)&&e.target.cy().addToAnimationPool(e.target),e.hooked=!0}return this},play:function(){var e=this._private;return 1===e.progress&&(e.progress=0),e.playing=!0,e.started=!1,e.stopped=!1,this.hook(),this},playing:function(){return this._private.playing},apply:function(){var e=this._private;return e.applying=!0,e.started=!1,e.stopped=!1,this.hook(),this},applying:function(){return this._private.applying},pause:function(){var e=this._private;return e.playing=!1,e.started=!1,this},stop:function(){var e=this._private;return e.playing=!1,e.started=!1,e.stopped=!0,this},rewind:function(){return this.progress(0)},fastforward:function(){return this.progress(1)},time:function(e){var t=this._private;return void 0===e?t.progress*t.duration:this.progress(e/t.duration)},progress:function(e){var t=this._private,n=t.playing;return void 0===e?t.progress:(n&&this.pause(),t.progress=e,t.started=!1,n&&this.play(),this)},completed:function(){return 1===this._private.progress},reverse:function(){var e=this._private,t=e.playing;t&&this.pause(),e.progress=1-e.progress,e.started=!1;var n=function(t,n){var r=e[t];null!=r&&(e[t]=e[n],e[n]=r)};if(n("zoom","startZoom"),n("pan","startPan"),n("position","startPosition"),e.style)for(var r=0;r<e.style.length;r++){var a=e.style[r],i=a.name,o=e.startStyle[i];e.startStyle[i]=a,e.style[r]=o}return t&&this.play(),this},promise:function(e){var t,n=this._private;if("frame"===e)t=n.frames;else t=n.completes;return new jr((function(e,n){t.push((function(){e()}))}))}}),qr.complete=qr.completed,qr.run=qr.play,qr.running=qr.playing;var Yr,Wr,Ur,Hr,Kr,Gr,Zr,$r,Qr,Jr,ea,ta,na,ra,aa,ia,oa,sa,la,ua,ca,da,ha,fa,pa,ga,va,ya,ma,ba,xa,wa,Ea,ka,Ta,Ca,Pa,Sa,Ba,Da,_a,Aa,Ma,Ra,Ia,Na,La,za,Oa,Va,Fa,ja,Xa,qa,Ya,Wa,Ua,Ha,Ka,Ga,Za,$a,Qa,Ja,ei,ti,ni,ri,ai,ii,oi,si,li,ui,ci,di,hi,fi,pi,gi,vi,yi,mi,bi,xi,wi,Ei={animated:function(){return function(){var e=this,t=void 0!==e.length?e:[e];if(!(this._private.cy||this).styleEnabled())return!1;var n=t[0];return n?n._private.animation.current.length>0:void 0}},clearQueue:function(){return function(){var e=this,t=void 0!==e.length?e:[e];if(!(this._private.cy||this).styleEnabled())return this;for(var n=0;n<t.length;n++){t[n]._private.animation.queue=[]}return this}},delay:function(){return function(e,t){return(this._private.cy||this).styleEnabled()?this.animate({delay:e,duration:e,complete:t}):this}},delayAnimation:function(){return function(e,t){return(this._private.cy||this).styleEnabled()?this.animation({delay:e,duration:e,complete:t}):this}},animation:function(){return function(e,t){var n=this,r=void 0!==n.length,a=r?n:[n],i=this._private.cy||this,o=!r,s=!o;if(!i.styleEnabled())return this;var l=i.style();if(e=me({},e,t),0===Object.keys(e).length)return new Xr(a[0],e);switch(void 0===e.duration&&(e.duration=400),e.duration){case"slow":e.duration=600;break;case"fast":e.duration=200}if(s&&(e.style=l.getPropsList(e.style||e.css),e.css=void 0),s&&null!=e.renderedPosition){var u=e.renderedPosition,c=i.pan(),d=i.zoom();e.position=jt(u,d,c)}if(o&&null!=e.panBy){var h=e.panBy,f=i.pan();e.pan={x:f.x+h.x,y:f.y+h.y}}var p=e.center||e.centre;if(o&&null!=p){var g=i.getCenterPan(p.eles,e.zoom);null!=g&&(e.pan=g)}if(o&&null!=e.fit){var v=e.fit,y=i.getFitViewport(v.eles||v.boundingBox,v.padding);null!=y&&(e.pan=y.pan,e.zoom=y.zoom)}if(o&&$(e.zoom)){var m=i.getZoomedViewport(e.zoom);null!=m?(m.zoomed&&(e.zoom=m.zoom),m.panned&&(e.pan=m.pan)):e.zoom=null}return new Xr(a[0],e)}},animate:function(){return function(e,t){var n=this,r=void 0!==n.length?n:[n];if(!(this._private.cy||this).styleEnabled())return this;t&&(e=me({},e,t));for(var a=0;a<r.length;a++){var i=r[a],o=i.animated()&&(void 0===e.queue||e.queue);i.animation(e,o?{queue:!0}:void 0).play()}return this}},stop:function(){return function(e,t){var n=this,r=void 0!==n.length?n:[n],a=this._private.cy||this;if(!a.styleEnabled())return this;for(var i=0;i<r.length;i++){for(var o=r[i]._private,s=o.animation.current,l=0;l<s.length;l++){var u=s[l]._private;t&&(u.duration=0)}e&&(o.animation.queue=[]),t||(o.animation.current=[])}return a.notify("draw"),this}}};function ki(){if(Wr)return Yr;Wr=1;var e=Array.isArray;return Yr=e}function Ti(){if(Jr)return Qr;Jr=1;var e,t=function(){if($r)return Zr;$r=1;var e=Pe()["__core-js_shared__"];return Zr=e}(),n=(e=/[^.]+$/.exec(t&&t.keys&&t.keys.IE_PROTO||""))?"Symbol(src)_1."+e:"";return Qr=function(e){return!!n&&n in e}}function Ci(){if(ra)return na;ra=1;var e=function(){if(Gr)return Kr;Gr=1;var e=De(),t=Ce();return Kr=function(n){if(!t(n))return!1;var r=e(n);return"[object Function]"==r||"[object GeneratorFunction]"==r||"[object AsyncFunction]"==r||"[object Proxy]"==r}}(),t=Ti(),n=Ce(),r=function(){if(ta)return ea;ta=1;var e=Function.prototype.toString;return ea=function(t){if(null!=t){try{return e.call(t)}catch(n){}try{return t+""}catch(n){}}return""}}(),a=/^\[object .+?Constructor\]$/,i=Function.prototype,o=Object.prototype,s=i.toString,l=o.hasOwnProperty,u=RegExp("^"+s.call(l).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");return na=function(i){return!(!n(i)||t(i))&&(e(i)?u:a).test(r(i))}}function Pi(){if(sa)return oa;sa=1;var e=Ci(),t=(ia||(ia=1,aa=function(e,t){return null==e?void 0:e[t]}),aa);return oa=function(n,r){var a=t(n,r);return e(a)?a:void 0},oa}function Si(){if(ua)return la;ua=1;var e=Pi()(Object,"create");return la=e}function Bi(){if(wa)return xa;wa=1;var e=function(){if(da)return ca;da=1;var e=Si();return ca=function(){this.__data__=e?e(null):{},this.size=0}}(),t=fa?ha:(fa=1,ha=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}),n=function(){if(ga)return pa;ga=1;var e=Si(),t=Object.prototype.hasOwnProperty;return pa=function(n){var r=this.__data__;if(e){var a=r[n];return"__lodash_hash_undefined__"===a?void 0:a}return t.call(r,n)?r[n]:void 0},pa}(),r=function(){if(ya)return va;ya=1;var e=Si(),t=Object.prototype.hasOwnProperty;return va=function(n){var r=this.__data__;return e?void 0!==r[n]:t.call(r,n)},va}(),a=function(){if(ba)return ma;ba=1;var e=Si();return ma=function(t,n){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=e&&void 0===n?"__lodash_hash_undefined__":n,this},ma}();function i(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}return i.prototype.clear=e,i.prototype.delete=t,i.prototype.get=n,i.prototype.has=r,i.prototype.set=a,xa=i}function Di(){if(Ca)return Ta;return Ca=1,Ta=function(e,t){return e===t||e!=e&&t!=t}}function _i(){if(Sa)return Pa;Sa=1;var e=Di();return Pa=function(t,n){for(var r=t.length;r--;)if(e(t[r][0],n))return r;return-1},Pa}function Ai(){if(za)return La;za=1;var e=ka?Ea:(ka=1,Ea=function(){this.__data__=[],this.size=0}),t=function(){if(Da)return Ba;Da=1;var e=_i(),t=Array.prototype.splice;return Ba=function(n){var r=this.__data__,a=e(r,n);return!(a<0||(a==r.length-1?r.pop():t.call(r,a,1),--this.size,0))},Ba}(),n=function(){if(Aa)return _a;Aa=1;var e=_i();return _a=function(t){var n=this.__data__,r=e(n,t);return r<0?void 0:n[r][1]},_a}(),r=function(){if(Ra)return Ma;Ra=1;var e=_i();return Ma=function(t){return e(this.__data__,t)>-1}}(),a=function(){if(Na)return Ia;Na=1;var e=_i();return Ia=function(t,n){var r=this.__data__,a=e(r,t);return a<0?(++this.size,r.push([t,n])):r[a][1]=n,this},Ia}();function i(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}return i.prototype.clear=e,i.prototype.delete=t,i.prototype.get=n,i.prototype.has=r,i.prototype.set=a,La=i}function Mi(){if(ja)return Fa;ja=1;var e=Bi(),t=Ai(),n=function(){if(Va)return Oa;Va=1;var e=Pi()(Pe(),"Map");return Oa=e}();return Fa=function(){this.size=0,this.__data__={hash:new e,map:new(n||t),string:new e}}}function Ri(){if(Wa)return Ya;Wa=1;var e=qa?Xa:(qa=1,Xa=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e});return Ya=function(t,n){var r=t.__data__;return e(n)?r["string"==typeof n?"string":"hash"]:r.map},Ya}function Ii(){if(ti)return ei;ti=1;var e=Mi(),t=function(){if(Ha)return Ua;Ha=1;var e=Ri();return Ua=function(t){var n=e(this,t).delete(t);return this.size-=n?1:0,n}}(),n=function(){if(Ga)return Ka;Ga=1;var e=Ri();return Ka=function(t){return e(this,t).get(t)}}(),r=function(){if($a)return Za;$a=1;var e=Ri();return Za=function(t){return e(this,t).has(t)}}(),a=function(){if(Ja)return Qa;Ja=1;var e=Ri();return Qa=function(t,n){var r=e(this,t),a=r.size;return r.set(t,n),this.size+=r.size==a?0:1,this},Qa}();function i(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}return i.prototype.clear=e,i.prototype.delete=t,i.prototype.get=n,i.prototype.has=r,i.prototype.set=a,ei=i}function Ni(){if(ii)return ai;ii=1;var e=function(){if(ri)return ni;ri=1;var e=Ii();function t(n,r){if("function"!=typeof n||null!=r&&"function"!=typeof r)throw new TypeError("Expected a function");var a=function(){var e=arguments,t=r?r.apply(this,e):e[0],i=a.cache;if(i.has(t))return i.get(t);var o=n.apply(this,e);return a.cache=i.set(t,o)||i,o};return a.cache=new(t.Cache||e),a}return t.Cache=e,ni=t}();return ai=function(t){var n=e(t,(function(e){return 500===r.size&&r.clear(),e})),r=n.cache;return n},ai}function Li(){if(si)return oi;si=1;var e=Ni(),t=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,n=/\\(\\)?/g,r=e((function(e){var r=[];return 46===e.charCodeAt(0)&&r.push(""),e.replace(t,(function(e,t,a,i){r.push(a?i.replace(n,"$1"):t||e)})),r}));return oi=r}function zi(){if(ui)return li;return ui=1,li=function(e,t){for(var n=-1,r=null==e?0:e.length,a=Array(r);++n<r;)a[n]=t(e[n],n,e);return a},li}function Oi(){if(fi)return hi;fi=1;var e=function(){if(di)return ci;di=1;var e=Be(),t=zi(),n=ki(),r=_e(),a=e?e.prototype:void 0,i=a?a.toString:void 0;return ci=function e(a){if("string"==typeof a)return a;if(n(a))return t(a,e)+"";if(r(a))return i?i.call(a):"";var o=a+"";return"0"==o&&1/a==-1/0?"-0":o},ci}();return hi=function(t){return null==t?"":e(t)}}function Vi(){if(gi)return pi;gi=1;var e=ki(),t=function(){if(Hr)return Ur;Hr=1;var e=ki(),t=_e(),n=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,r=/^\w*$/;return Ur=function(a,i){if(e(a))return!1;var o=typeof a;return!("number"!=o&&"symbol"!=o&&"boolean"!=o&&null!=a&&!t(a))||r.test(a)||!n.test(a)||null!=i&&a in Object(i)},Ur}(),n=Li(),r=Oi();return pi=function(a,i){return e(a)?a:t(a,i)?[a]:n(r(a))},pi}function Fi(){if(yi)return vi;yi=1;var e=_e();return vi=function(t){if("string"==typeof t||e(t))return t;var n=t+"";return"0"==n&&1/t==-1/0?"-0":n}}var ji,Xi,qi,Yi,Wi,Ui,Hi,Ki,Gi,Zi,$i,Qi,Ji=function(){if(wi)return xi;wi=1;var e=function(){if(bi)return mi;bi=1;var e=Vi(),t=Fi();return mi=function(n,r){for(var a=0,i=(r=e(r,n)).length;null!=n&&a<i;)n=n[t(r[a++])];return a&&a==i?n:void 0},mi}();return xi=function(t,n,r){var a=null==t?void 0:e(t,n);return void 0===a?r:a},xi}(),eo=Te(Ji);function to(){if(Yi)return qi;Yi=1;var e=function(){if(Xi)return ji;Xi=1;var e=Pi(),t=function(){try{var t=e(Object,"defineProperty");return t({},"",{}),t}catch(n){}}();return ji=t}();return qi=function(t,n,r){"__proto__"==n&&e?e(t,n,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[n]=r},qi}function no(){if(Zi)return Gi;Zi=1;var e=function(){if(Ui)return Wi;Ui=1;var e=to(),t=Di(),n=Object.prototype.hasOwnProperty;return Wi=function(r,a,i){var o=r[a];n.call(r,a)&&t(o,i)&&(void 0!==i||a in r)||e(r,a,i)},Wi}(),t=Vi(),n=function(){if(Ki)return Hi;Ki=1;var e=/^(?:0|[1-9]\d*)$/;return Hi=function(t,n){var r=typeof t;return!!(n=null==n?9007199254740991:n)&&("number"==r||"symbol"!=r&&e.test(t))&&t>-1&&t%1==0&&t<n}}(),r=Ce(),a=Fi();return Gi=function(i,o,s,l){if(!r(i))return i;for(var u=-1,c=(o=t(o,i)).length,d=c-1,h=i;null!=h&&++u<c;){var f=a(o[u]),p=s;if("__proto__"===f||"constructor"===f||"prototype"===f)return i;if(u!=d){var g=h[f];void 0===(p=l?l(g,f,h):void 0)&&(p=r(g)?g:n(o[u+1])?[]:{})}e(h,f,p),h=h[f]}return i},Gi}var ro,ao,io,oo,so=function(){if(Qi)return $i;Qi=1;var e=no();return $i=function(t,n,r){return null==t?t:e(t,n,r)},$i}(),lo=Te(so);var uo=function(){if(oo)return io;oo=1;var e=zi(),t=(ao||(ao=1,ro=function(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t}),ro),n=ki(),r=_e(),a=Li(),i=Fi(),o=Oi();return io=function(s){return n(s)?e(s,i):r(s)?[s]:t(a(o(s)))}}(),co=Te(uo),ho={data:function(e){return e=me({},{field:"data",bindingEvent:"data",allowBinding:!1,allowSetting:!1,allowGetting:!1,settingEvent:"data",settingTriggersEvent:!1,triggerFnName:"trigger",immutableKeys:{},updateStyle:!1,beforeGet:function(e){},beforeSet:function(e,t){},onSet:function(e){},canSet:function(e){return!0}},e),function(t,n){var r=e,a=this,i=void 0!==a.length,o=i?a:[a],l=i?a[0]:a;if(K(t)){var u,c=-1!==t.indexOf(".")&&co(t);if(r.allowGetting&&void 0===n)return l&&(r.beforeGet(l),u=c&&void 0===l._private[r.field][t]?eo(l._private[r.field],c):l._private[r.field][t]),u;if(r.allowSetting&&void 0!==n&&!r.immutableKeys[t]){var d=s({},t,n);r.beforeSet(a,d);for(var h=0,f=o.length;h<f;h++){var p=o[h];r.canSet(p)&&(c&&void 0===l._private[r.field][t]?lo(p._private[r.field],c,n):p._private[r.field][t]=n)}r.updateStyle&&a.updateStyle(),r.onSet(a),r.settingTriggersEvent&&a[r.triggerFnName](r.settingEvent)}}else if(r.allowSetting&&$(t)){var g,v,y=t,m=Object.keys(y);r.beforeSet(a,y);for(var b=0;b<m.length;b++){if(v=y[g=m[b]],!r.immutableKeys[g])for(var x=0;x<o.length;x++){var w=o[x];r.canSet(w)&&(w._private[r.field][g]=v)}}r.updateStyle&&a.updateStyle(),r.onSet(a),r.settingTriggersEvent&&a[r.triggerFnName](r.settingEvent)}else if(r.allowBinding&&G(t)){var E=t;a.on(r.bindingEvent,E)}else if(r.allowGetting&&void 0===t){var k;return l&&(r.beforeGet(l),k=l._private[r.field]),k}return a}},removeData:function(e){return e=me({},{field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!1,immutableKeys:{}},e),function(t){var n=e,r=this,a=void 0!==r.length?r:[r];if(K(t)){for(var i=t.split(/\s+/),o=i.length,s=0;s<o;s++){var l=i[s];if(!ie(l))if(!n.immutableKeys[l])for(var u=0,c=a.length;u<c;u++)a[u]._private[n.field][l]=void 0}n.triggerEvent&&r[n.triggerFnName](n.event)}else if(void 0===t){for(var d=0,h=a.length;d<h;d++)for(var f=a[d]._private[n.field],p=Object.keys(f),g=0;g<p.length;g++){var v=p[g];!n.immutableKeys[v]&&(f[v]=void 0)}n.triggerEvent&&r[n.triggerFnName](n.event)}return r}}},fo={eventAliasesOn:function(e){var t=e;t.addListener=t.listen=t.bind=t.on,t.unlisten=t.unbind=t.off=t.removeListener,t.trigger=t.emit,t.pon=t.promiseOn=function(e,t){var n=this,r=Array.prototype.slice.call(arguments,0);return new jr((function(e,t){var a=r.concat([function(t){n.off.apply(n,i),e(t)}]),i=a.concat([]);n.on.apply(n,a)}))}}},po={};[Ei,ho,fo].forEach((function(e){me(po,e)}));var go={animate:po.animate(),animation:po.animation(),animated:po.animated(),clearQueue:po.clearQueue(),delay:po.delay(),delayAnimation:po.delayAnimation(),stop:po.stop()},vo={classes:function(e){var t=this;if(void 0===e){var n=[];return t[0]._private.classes.forEach((function(e){return n.push(e)})),n}Z(e)||(e=(e||"").match(/\S+/g)||[]);for(var r=[],a=new vt(e),i=0;i<t.length;i++){for(var o=t[i],s=o._private,l=s.classes,u=!1,c=0;c<e.length;c++){var d=e[c];if(!l.has(d)){u=!0;break}}u||(u=l.size!==e.length),u&&(s.classes=a,r.push(o))}return r.length>0&&this.spawn(r).updateStyle().emit("class"),t},addClass:function(e){return this.toggleClass(e,!0)},hasClass:function(e){var t=this[0];return null!=t&&t._private.classes.has(e)},toggleClass:function(e,t){Z(e)||(e=e.match(/\S+/g)||[]);for(var n=this,r=void 0===t,a=[],i=0,o=n.length;i<o;i++)for(var s=n[i],l=s._private.classes,u=!1,c=0;c<e.length;c++){var d=e[c],h=l.has(d),f=!1;t||r&&!h?(l.add(d),f=!0):(!t||r&&h)&&(l.delete(d),f=!0),!u&&f&&(a.push(s),u=!0)}return a.length>0&&this.spawn(a).updateStyle().emit("class"),n},removeClass:function(e){return this.toggleClass(e,!1)},flashClass:function(e,t){var n=this;if(null==t)t=250;else if(0===t)return n;return n.addClass(e),setTimeout((function(){n.removeClass(e)}),t),n}};vo.className=vo.classNames=vo.classes;var yo={metaChar:"[\\!\\\"\\#\\$\\%\\&\\'\\(\\)\\*\\+\\,\\.\\/\\:\\;\\<\\=\\>\\?\\@\\[\\]\\^\\`\\{\\|\\}\\~]",comparatorOp:"=|\\!=|>|>=|<|<=|\\$=|\\^=|\\*=",boolOp:"\\?|\\!|\\^",string:"\"(?:\\\\\"|[^\"])*\"|'(?:\\\\'|[^'])*'",number:he,meta:"degree|indegree|outdegree",separator:"\\s*,\\s*",descendant:"\\s+",child:"\\s+>\\s+",subject:"\\$",group:"node|edge|\\*",directedEdge:"\\s+->\\s+",undirectedEdge:"\\s+<->\\s+"};yo.variable="(?:[\\w-.]|(?:\\\\"+yo.metaChar+"))+",yo.className="(?:[\\w-]|(?:\\\\"+yo.metaChar+"))+",yo.value=yo.string+"|"+yo.number,yo.id=yo.variable,function(){var e,t,n;for(e=yo.comparatorOp.split("|"),n=0;n<e.length;n++)t=e[n],yo.comparatorOp+="|@"+t;for(e=yo.comparatorOp.split("|"),n=0;n<e.length;n++)(t=e[n]).indexOf("!")>=0||"="!==t&&(yo.comparatorOp+="|\\!"+t)}();var mo=0,bo=1,xo=2,wo=3,Eo=4,ko=5,To=6,Co=7,Po=8,So=9,Bo=10,Do=11,_o=12,Ao=13,Mo=14,Ro=15,Io=16,No=17,Lo=18,zo=19,Oo=20,Vo=[{selector:":selected",matches:function(e){return e.selected()}},{selector:":unselected",matches:function(e){return!e.selected()}},{selector:":selectable",matches:function(e){return e.selectable()}},{selector:":unselectable",matches:function(e){return!e.selectable()}},{selector:":locked",matches:function(e){return e.locked()}},{selector:":unlocked",matches:function(e){return!e.locked()}},{selector:":visible",matches:function(e){return e.visible()}},{selector:":hidden",matches:function(e){return!e.visible()}},{selector:":transparent",matches:function(e){return e.transparent()}},{selector:":grabbed",matches:function(e){return e.grabbed()}},{selector:":free",matches:function(e){return!e.grabbed()}},{selector:":removed",matches:function(e){return e.removed()}},{selector:":inside",matches:function(e){return!e.removed()}},{selector:":grabbable",matches:function(e){return e.grabbable()}},{selector:":ungrabbable",matches:function(e){return!e.grabbable()}},{selector:":animated",matches:function(e){return e.animated()}},{selector:":unanimated",matches:function(e){return!e.animated()}},{selector:":parent",matches:function(e){return e.isParent()}},{selector:":childless",matches:function(e){return e.isChildless()}},{selector:":child",matches:function(e){return e.isChild()}},{selector:":orphan",matches:function(e){return e.isOrphan()}},{selector:":nonorphan",matches:function(e){return e.isChild()}},{selector:":compound",matches:function(e){return e.isNode()?e.isParent():e.source().isParent()||e.target().isParent()}},{selector:":loop",matches:function(e){return e.isLoop()}},{selector:":simple",matches:function(e){return e.isSimple()}},{selector:":active",matches:function(e){return e.active()}},{selector:":inactive",matches:function(e){return!e.active()}},{selector:":backgrounding",matches:function(e){return e.backgrounding()}},{selector:":nonbackgrounding",matches:function(e){return!e.backgrounding()}}].sort((function(e,t){return function(e,t){return-1*ye(e,t)}(e.selector,t.selector)})),Fo=function(){for(var e,t={},n=0;n<Vo.length;n++)t[(e=Vo[n]).selector]=e.matches;return t}(),jo="("+Vo.map((function(e){return e.selector})).join("|")+")",Xo=function(e){return e.replace(new RegExp("\\\\("+yo.metaChar+")","g"),(function(e,t){return t}))},qo=function(e,t,n){e[e.length-1]=n},Yo=[{name:"group",query:!0,regex:"("+yo.group+")",populate:function(e,t,n){var r=l(n,1)[0];t.checks.push({type:mo,value:"*"===r?r:r+"s"})}},{name:"state",query:!0,regex:jo,populate:function(e,t,n){var r=l(n,1)[0];t.checks.push({type:Co,value:r})}},{name:"id",query:!0,regex:"\\#("+yo.id+")",populate:function(e,t,n){var r=l(n,1)[0];t.checks.push({type:Po,value:Xo(r)})}},{name:"className",query:!0,regex:"\\.("+yo.className+")",populate:function(e,t,n){var r=l(n,1)[0];t.checks.push({type:So,value:Xo(r)})}},{name:"dataExists",query:!0,regex:"\\[\\s*("+yo.variable+")\\s*\\]",populate:function(e,t,n){var r=l(n,1)[0];t.checks.push({type:Eo,field:Xo(r)})}},{name:"dataCompare",query:!0,regex:"\\[\\s*("+yo.variable+")\\s*("+yo.comparatorOp+")\\s*("+yo.value+")\\s*\\]",populate:function(e,t,n){var r=l(n,3),a=r[0],i=r[1],o=r[2];o=null!=new RegExp("^"+yo.string+"$").exec(o)?o.substring(1,o.length-1):parseFloat(o),t.checks.push({type:wo,field:Xo(a),operator:i,value:o})}},{name:"dataBool",query:!0,regex:"\\[\\s*("+yo.boolOp+")\\s*("+yo.variable+")\\s*\\]",populate:function(e,t,n){var r=l(n,2),a=r[0],i=r[1];t.checks.push({type:ko,field:Xo(i),operator:a})}},{name:"metaCompare",query:!0,regex:"\\[\\[\\s*("+yo.meta+")\\s*("+yo.comparatorOp+")\\s*("+yo.number+")\\s*\\]\\]",populate:function(e,t,n){var r=l(n,3),a=r[0],i=r[1],o=r[2];t.checks.push({type:To,field:Xo(a),operator:i,value:parseFloat(o)})}},{name:"nextQuery",separator:!0,regex:yo.separator,populate:function(e,t){var n=e.currentSubject,r=e.edgeCount,a=e.compoundCount,i=e[e.length-1];return null!=n&&(i.subject=n,e.currentSubject=null),i.edgeCount=r,i.compoundCount=a,e.edgeCount=0,e.compoundCount=0,e[e.length++]={checks:[]}}},{name:"directedEdge",separator:!0,regex:yo.directedEdge,populate:function(e,t){if(null==e.currentSubject){var n={checks:[]},r=t,a={checks:[]};return n.checks.push({type:Do,source:r,target:a}),qo(e,0,n),e.edgeCount++,a}var i={checks:[]},o=t,s={checks:[]};return i.checks.push({type:_o,source:o,target:s}),qo(e,0,i),e.edgeCount++,s}},{name:"undirectedEdge",separator:!0,regex:yo.undirectedEdge,populate:function(e,t){if(null==e.currentSubject){var n={checks:[]},r=t,a={checks:[]};return n.checks.push({type:Bo,nodes:[r,a]}),qo(e,0,n),e.edgeCount++,a}var i={checks:[]},o=t,s={checks:[]};return i.checks.push({type:Mo,node:o,neighbor:s}),qo(e,0,i),s}},{name:"child",separator:!0,regex:yo.child,populate:function(e,t){if(null==e.currentSubject){var n={checks:[]},r={checks:[]},a=e[e.length-1];return n.checks.push({type:Ro,parent:a,child:r}),qo(e,0,n),e.compoundCount++,r}if(e.currentSubject===t){var i={checks:[]},o=e[e.length-1],s={checks:[]},l={checks:[]},u={checks:[]},c={checks:[]};return i.checks.push({type:zo,left:o,right:s,subject:l}),l.checks=t.checks,t.checks=[{type:Oo}],c.checks.push({type:Oo}),s.checks.push({type:No,parent:c,child:u}),qo(e,0,i),e.currentSubject=l,e.compoundCount++,u}var d={checks:[]},h={checks:[]},f=[{type:No,parent:d,child:h}];return d.checks=t.checks,t.checks=f,e.compoundCount++,h}},{name:"descendant",separator:!0,regex:yo.descendant,populate:function(e,t){if(null==e.currentSubject){var n={checks:[]},r={checks:[]},a=e[e.length-1];return n.checks.push({type:Io,ancestor:a,descendant:r}),qo(e,0,n),e.compoundCount++,r}if(e.currentSubject===t){var i={checks:[]},o=e[e.length-1],s={checks:[]},l={checks:[]},u={checks:[]},c={checks:[]};return i.checks.push({type:zo,left:o,right:s,subject:l}),l.checks=t.checks,t.checks=[{type:Oo}],c.checks.push({type:Oo}),s.checks.push({type:Lo,ancestor:c,descendant:u}),qo(e,0,i),e.currentSubject=l,e.compoundCount++,u}var d={checks:[]},h={checks:[]},f=[{type:Lo,ancestor:d,descendant:h}];return d.checks=t.checks,t.checks=f,e.compoundCount++,h}},{name:"subject",modifier:!0,regex:yo.subject,populate:function(e,t){if(null!=e.currentSubject&&e.currentSubject!==t)return at("Redefinition of subject in selector `"+e.toString()+"`"),!1;e.currentSubject=t;var n=e[e.length-1].checks[0],r=null==n?null:n.type;r===Do?n.type=Ao:r===Bo&&(n.type=Mo,n.node=n.nodes[1],n.neighbor=n.nodes[0],n.nodes=null)}}];Yo.forEach((function(e){return e.regexObj=new RegExp("^"+e.regex)}));var Wo=function(e){for(var t,n,r,a=0;a<Yo.length;a++){var i=Yo[a],o=i.name,s=e.match(i.regexObj);if(null!=s){n=s,t=i,r=o;var l=s[0];e=e.substring(l.length);break}}return{expr:t,match:n,name:r,remaining:e}},Uo={parse:function(e){var t=this,n=t.inputText=e,r=t[0]={checks:[]};for(t.length=1,n=function(e){var t=e.match(/^\s+/);if(t){var n=t[0];e=e.substring(n.length)}return e}(n);;){var a=Wo(n);if(null==a.expr)return at("The selector `"+e+"`is invalid"),!1;var i=a.match.slice(1),o=a.expr.populate(t,r,i);if(!1===o)return!1;if(null!=o&&(r=o),(n=a.remaining).match(/^\s*$/))break}var s=t[t.length-1];null!=t.currentSubject&&(s.subject=t.currentSubject),s.edgeCount=t.edgeCount,s.compoundCount=t.compoundCount;for(var l=0;l<t.length;l++){var u=t[l];if(u.compoundCount>0&&u.edgeCount>0)return at("The selector `"+e+"` is invalid because it uses both a compound selector and an edge selector"),!1;if(u.edgeCount>1)return at("The selector `"+e+"` is invalid because it uses multiple edge selectors"),!1;1===u.edgeCount&&at("The selector `"+e+"` is deprecated.  Edge selectors do not take effect on changes to source and target nodes after an edge is added, for performance reasons.  Use a class or data selector on edges instead, updating the class or data of an edge when your app detects a change in source or target nodes.")}return!0},toString:function(){if(null!=this.toStringCache)return this.toStringCache;for(var e=function(e){return null==e?"":e},t=function(t){return K(t)?'"'+t+'"':e(t)},n=function(e){return" "+e+" "},r=function(r,i){var o=r.type,s=r.value;switch(o){case mo:var l=e(s);return l.substring(0,l.length-1);case wo:var u=r.field,c=r.operator;return"["+u+n(e(c))+t(s)+"]";case ko:var d=r.operator,h=r.field;return"["+e(d)+h+"]";case Eo:return"["+r.field+"]";case To:var f=r.operator;return"[["+r.field+n(e(f))+t(s)+"]]";case Co:return s;case Po:return"#"+s;case So:return"."+s;case No:case Ro:return a(r.parent,i)+n(">")+a(r.child,i);case Lo:case Io:return a(r.ancestor,i)+" "+a(r.descendant,i);case zo:var p=a(r.left,i),g=a(r.subject,i),v=a(r.right,i);return p+(p.length>0?" ":"")+g+v;case Oo:return""}},a=function(e,t){return e.checks.reduce((function(n,a,i){return n+(t===e&&0===i?"$":"")+r(a,t)}),"")},i="",o=0;o<this.length;o++){var s=this[o];i+=a(s,s.subject),this.length>1&&o<this.length-1&&(i+=", ")}return this.toStringCache=i,i}},Ho=function(e,t,n){var r,a,i,o=K(e),s=Q(e),l=K(n),u=!1,c=!1,d=!1;switch(t.indexOf("!")>=0&&(t=t.replace("!",""),c=!0),t.indexOf("@")>=0&&(t=t.replace("@",""),u=!0),(o||l||u)&&(a=o||s?""+e:"",i=""+n),u&&(e=a=a.toLowerCase(),n=i=i.toLowerCase()),t){case"*=":r=a.indexOf(i)>=0;break;case"$=":r=a.indexOf(i,a.length-i.length)>=0;break;case"^=":r=0===a.indexOf(i);break;case"=":r=e===n;break;case">":d=!0,r=e>n;break;case">=":d=!0,r=e>=n;break;case"<":d=!0,r=e<n;break;case"<=":d=!0,r=e<=n;break;default:r=!1}return!c||null==e&&d||(r=!r),r},Ko=function(e,t){return e.data(t)},Go=[],Zo=function(e,t){return e.checks.every((function(e){return Go[e.type](e,t)}))};Go[mo]=function(e,t){var n=e.value;return"*"===n||n===t.group()},Go[Co]=function(e,t){return function(e,t){return Fo[e](t)}(e.value,t)},Go[Po]=function(e,t){var n=e.value;return t.id()===n},Go[So]=function(e,t){var n=e.value;return t.hasClass(n)},Go[To]=function(e,t){var n=e.field,r=e.operator,a=e.value;return Ho(function(e,t){return e[t]()}(t,n),r,a)},Go[wo]=function(e,t){var n=e.field,r=e.operator,a=e.value;return Ho(Ko(t,n),r,a)},Go[ko]=function(e,t){var n=e.field,r=e.operator;return function(e,t){switch(t){case"?":return!!e;case"!":return!e;case"^":return void 0===e}}(Ko(t,n),r)},Go[Eo]=function(e,t){var n=e.field;return e.operator,void 0!==Ko(t,n)},Go[Bo]=function(e,t){var n=e.nodes[0],r=e.nodes[1],a=t.source(),i=t.target();return Zo(n,a)&&Zo(r,i)||Zo(r,a)&&Zo(n,i)},Go[Mo]=function(e,t){return Zo(e.node,t)&&t.neighborhood().some((function(t){return t.isNode()&&Zo(e.neighbor,t)}))},Go[Do]=function(e,t){return Zo(e.source,t.source())&&Zo(e.target,t.target())},Go[_o]=function(e,t){return Zo(e.source,t)&&t.outgoers().some((function(t){return t.isNode()&&Zo(e.target,t)}))},Go[Ao]=function(e,t){return Zo(e.target,t)&&t.incomers().some((function(t){return t.isNode()&&Zo(e.source,t)}))},Go[Ro]=function(e,t){return Zo(e.child,t)&&Zo(e.parent,t.parent())},Go[No]=function(e,t){return Zo(e.parent,t)&&t.children().some((function(t){return Zo(e.child,t)}))},Go[Io]=function(e,t){return Zo(e.descendant,t)&&t.ancestors().some((function(t){return Zo(e.ancestor,t)}))},Go[Lo]=function(e,t){return Zo(e.ancestor,t)&&t.descendants().some((function(t){return Zo(e.descendant,t)}))},Go[zo]=function(e,t){return Zo(e.subject,t)&&Zo(e.left,t)&&Zo(e.right,t)},Go[Oo]=function(){return!0},Go[bo]=function(e,t){return e.value.has(t)},Go[xo]=function(e,t){return(0,e.value)(t)};var $o={matches:function(e){for(var t=0;t<this.length;t++){var n=this[t];if(Zo(n,e))return!0}return!1},filter:function(e){var t=this;if(1===t.length&&1===t[0].checks.length&&t[0].checks[0].type===Po)return e.getElementById(t[0].checks[0].value).collection();var n=function(e){for(var n=0;n<t.length;n++){var r=t[n];if(Zo(r,e))return!0}return!1};return null==t.text()&&(n=function(){return!0}),e.filter(n)}},Qo=function(e){this.inputText=e,this.currentSubject=null,this.compoundCount=0,this.edgeCount=0,this.length=0,null==e||K(e)&&e.match(/^\s*$/)||(ee(e)?this.addQuery({checks:[{type:bo,value:e.collection()}]}):G(e)?this.addQuery({checks:[{type:xo,value:e}]}):K(e)?this.parse(e)||(this.invalid=!0):nt("A selector must be created from a string; found "))},Jo=Qo.prototype;[Uo,$o].forEach((function(e){return me(Jo,e)})),Jo.text=function(){return this.inputText},Jo.size=function(){return this.length},Jo.eq=function(e){return this[e]},Jo.sameText=function(e){return!this.invalid&&!e.invalid&&this.text()===e.text()},Jo.addQuery=function(e){this[this.length++]=e},Jo.selector=Jo.toString;var es={allAre:function(e){var t=new Qo(e);return this.every((function(e){return t.matches(e)}))},is:function(e){var t=new Qo(e);return this.some((function(e){return t.matches(e)}))},some:function(e,t){for(var n=0;n<this.length;n++){if(t?e.apply(t,[this[n],n,this]):e(this[n],n,this))return!0}return!1},every:function(e,t){for(var n=0;n<this.length;n++){if(!(t?e.apply(t,[this[n],n,this]):e(this[n],n,this)))return!1}return!0},same:function(e){if(this===e)return!0;e=this.cy().collection(e);var t=this.length;return t===e.length&&(1===t?this[0]===e[0]:this.every((function(t){return e.hasElementWithId(t.id())})))},anySame:function(e){return e=this.cy().collection(e),this.some((function(t){return e.hasElementWithId(t.id())}))},allAreNeighbors:function(e){e=this.cy().collection(e);var t=this.neighborhood();return e.every((function(e){return t.hasElementWithId(e.id())}))},contains:function(e){e=this.cy().collection(e);var t=this;return e.every((function(e){return t.hasElementWithId(e.id())}))}};es.allAreNeighbours=es.allAreNeighbors,es.has=es.contains,es.equal=es.equals=es.same;var ts,ns,rs=function(e,t){return function(n,r,a,i){var o,s=n,l=this;if(null==s?o="":ee(s)&&1===s.length&&(o=s.id()),1===l.length&&o){var u=l[0]._private,c=u.traversalCache=u.traversalCache||{},d=c[t]=c[t]||[],h=We(o),f=d[h];return f||(d[h]=e.call(l,n,r,a,i))}return e.call(l,n,r,a,i)}},as={parent:function(e){var t=[];if(1===this.length){var n=this[0]._private.parent;if(n)return n}for(var r=0;r<this.length;r++){var a=this[r]._private.parent;a&&t.push(a)}return this.spawn(t,!0).filter(e)},parents:function(e){for(var t=[],n=this.parent();n.nonempty();){for(var r=0;r<n.length;r++){var a=n[r];t.push(a)}n=n.parent()}return this.spawn(t,!0).filter(e)},commonAncestors:function(e){for(var t,n=0;n<this.length;n++){var r=this[n].parents();t=(t=t||r).intersect(r)}return t.filter(e)},orphans:function(e){return this.stdFilter((function(e){return e.isOrphan()})).filter(e)},nonorphans:function(e){return this.stdFilter((function(e){return e.isChild()})).filter(e)},children:rs((function(e){for(var t=[],n=0;n<this.length;n++)for(var r=this[n]._private.children,a=0;a<r.length;a++)t.push(r[a]);return this.spawn(t,!0).filter(e)}),"children"),siblings:function(e){return this.parent().children().not(this).filter(e)},isParent:function(){var e=this[0];if(e)return e.isNode()&&0!==e._private.children.length},isChildless:function(){var e=this[0];if(e)return e.isNode()&&0===e._private.children.length},isChild:function(){var e=this[0];if(e)return e.isNode()&&null!=e._private.parent},isOrphan:function(){var e=this[0];if(e)return e.isNode()&&null==e._private.parent},descendants:function(e){var t=[];return function e(n){for(var r=0;r<n.length;r++){var a=n[r];t.push(a),a.children().nonempty()&&e(a.children())}}(this.children()),this.spawn(t,!0).filter(e)}};function is(e,t,n,r){for(var a=[],i=new vt,o=e.cy().hasCompoundNodes(),s=0;s<e.length;s++){var l=e[s];n?a.push(l):o&&r(a,i,l)}for(;a.length>0;){var u=a.shift();t(u),i.add(u.id()),o&&r(a,i,u)}return e}function os(e,t,n){if(n.isParent())for(var r=n._private.children,a=0;a<r.length;a++){var i=r[a];t.has(i.id())||e.push(i)}}function ss(e,t,n){if(n.isChild()){var r=n._private.parent;t.has(r.id())||e.push(r)}}function ls(e,t,n){ss(e,t,n),os(e,t,n)}as.forEachDown=function(e){return is(this,e,!(arguments.length>1&&void 0!==arguments[1])||arguments[1],os)},as.forEachUp=function(e){return is(this,e,!(arguments.length>1&&void 0!==arguments[1])||arguments[1],ss)},as.forEachUpAndDown=function(e){return is(this,e,!(arguments.length>1&&void 0!==arguments[1])||arguments[1],ls)},as.ancestors=as.parents,(ts=ns={data:po.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),removeData:po.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),scratch:po.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:po.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),rscratch:po.data({field:"rscratch",allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!0}),removeRscratch:po.removeData({field:"rscratch",triggerEvent:!1}),id:function(){var e=this[0];if(e)return e._private.data.id}}).attr=ts.data,ts.removeAttr=ts.removeData;var us,cs,ds=ns,hs={};function fs(e){return function(t){var n=this;if(void 0===t&&(t=!0),0!==n.length&&n.isNode()&&!n.removed()){for(var r=0,a=n[0],i=a._private.edges,o=0;o<i.length;o++){var s=i[o];!t&&s.isLoop()||(r+=e(a,s))}return r}}}function ps(e,t){return function(n){for(var r,a=this.nodes(),i=0;i<a.length;i++){var o=a[i][e](n);void 0===o||void 0!==r&&!t(o,r)||(r=o)}return r}}me(hs,{degree:fs((function(e,t){return t.source().same(t.target())?2:1})),indegree:fs((function(e,t){return t.target().same(e)?1:0})),outdegree:fs((function(e,t){return t.source().same(e)?1:0}))}),me(hs,{minDegree:ps("degree",(function(e,t){return e<t})),maxDegree:ps("degree",(function(e,t){return e>t})),minIndegree:ps("indegree",(function(e,t){return e<t})),maxIndegree:ps("indegree",(function(e,t){return e>t})),minOutdegree:ps("outdegree",(function(e,t){return e<t})),maxOutdegree:ps("outdegree",(function(e,t){return e>t}))}),me(hs,{totalDegree:function(e){for(var t=0,n=this.nodes(),r=0;r<n.length;r++)t+=n[r].degree(e);return t}});var gs=function(e,t,n){for(var r=0;r<e.length;r++){var a=e[r];if(!a.locked()){var i=a._private.position,o={x:null!=t.x?t.x-i.x:0,y:null!=t.y?t.y-i.y:0};!a.isParent()||0===o.x&&0===o.y||a.children().shift(o,n),a.dirtyBoundingBoxCache()}}},vs={field:"position",bindingEvent:"position",allowBinding:!0,allowSetting:!0,settingEvent:"position",settingTriggersEvent:!0,triggerFnName:"emitAndNotify",allowGetting:!0,validKeys:["x","y"],beforeGet:function(e){e.updateCompoundBounds()},beforeSet:function(e,t){gs(e,t,!1)},onSet:function(e){e.dirtyCompoundBoundsCache()},canSet:function(e){return!e.locked()}};us=cs={position:po.data(vs),silentPosition:po.data(me({},vs,{allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!1,beforeSet:function(e,t){gs(e,t,!0)},onSet:function(e){e.dirtyCompoundBoundsCache()}})),positions:function(e,t){if($(e))t?this.silentPosition(e):this.position(e);else if(G(e)){var n=e,r=this.cy();r.startBatch();for(var a=0;a<this.length;a++){var i,o=this[a];(i=n(o,a))&&(t?o.silentPosition(i):o.position(i))}r.endBatch()}return this},silentPositions:function(e){return this.positions(e,!0)},shift:function(e,t,n){var r;if($(e)?(r={x:Q(e.x)?e.x:0,y:Q(e.y)?e.y:0},n=t):K(e)&&Q(t)&&((r={x:0,y:0})[e]=t),null!=r){var a=this.cy();a.startBatch();for(var i=0;i<this.length;i++){var o=this[i];if(!(a.hasCompoundNodes()&&o.isChild()&&o.ancestors().anySame(this))){var s=o.position(),l={x:s.x+r.x,y:s.y+r.y};n?o.silentPosition(l):o.position(l)}}a.endBatch()}return this},silentShift:function(e,t){return $(e)?this.shift(e,!0):K(e)&&Q(t)&&this.shift(e,t,!0),this},renderedPosition:function(e,t){var n=this[0],r=this.cy(),a=r.zoom(),i=r.pan(),o=$(e)?e:void 0,s=void 0!==o||void 0!==t&&K(e);if(n&&n.isNode()){if(!s){var l=n.position();return o=Ft(l,a,i),void 0===e?o:o[e]}for(var u=0;u<this.length;u++){var c=this[u];void 0!==t?c.position(e,(t-i[e])/a):void 0!==o&&c.position(jt(o,a,i))}}else if(!s)return;return this},relativePosition:function(e,t){var n=this[0],r=this.cy(),a=$(e)?e:void 0,i=void 0!==a||void 0!==t&&K(e),o=r.hasCompoundNodes();if(n&&n.isNode()){if(!i){var s=n.position(),l=o?n.parent():null,u=l&&l.length>0,c=u;u&&(l=l[0]);var d=c?l.position():{x:0,y:0};return a={x:s.x-d.x,y:s.y-d.y},void 0===e?a:a[e]}for(var h=0;h<this.length;h++){var f=this[h],p=o?f.parent():null,g=p&&p.length>0,v=g;g&&(p=p[0]);var y=v?p.position():{x:0,y:0};void 0!==t?f.position(e,t+y[e]):void 0!==a&&f.position({x:a.x+y.x,y:a.y+y.y})}}else if(!i)return;return this}},us.modelPosition=us.point=us.position,us.modelPositions=us.points=us.positions,us.renderedPoint=us.renderedPosition,us.relativePoint=us.relativePosition;var ys,ms,bs=cs;ys=ms={},ms.renderedBoundingBox=function(e){var t=this.boundingBox(e),n=this.cy(),r=n.zoom(),a=n.pan(),i=t.x1*r+a.x,o=t.x2*r+a.x,s=t.y1*r+a.y,l=t.y2*r+a.y;return{x1:i,x2:o,y1:s,y2:l,w:o-i,h:l-s}},ms.dirtyCompoundBoundsCache=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.cy();return t.styleEnabled()&&t.hasCompoundNodes()?(this.forEachUp((function(t){if(t.isParent()){var n=t._private;n.compoundBoundsClean=!1,n.bbCache=null,e||t.emitAndNotify("bounds")}})),this):this},ms.updateCompoundBounds=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.cy();if(!t.styleEnabled()||!t.hasCompoundNodes())return this;if(!e&&t.batching())return this;function n(e){if(e.isParent()){var t=e._private,n=e.children(),r="include"===e.pstyle("compound-sizing-wrt-labels").value,a={width:{val:e.pstyle("min-width").pfValue,left:e.pstyle("min-width-bias-left"),right:e.pstyle("min-width-bias-right")},height:{val:e.pstyle("min-height").pfValue,top:e.pstyle("min-height-bias-top"),bottom:e.pstyle("min-height-bias-bottom")}},i=n.boundingBox({includeLabels:r,includeOverlays:!1,useCache:!1}),o=t.position;0!==i.w&&0!==i.h||((i={w:e.pstyle("width").pfValue,h:e.pstyle("height").pfValue}).x1=o.x-i.w/2,i.x2=o.x+i.w/2,i.y1=o.y-i.h/2,i.y2=o.y+i.h/2);var s=a.width.left.value;"px"===a.width.left.units&&a.width.val>0&&(s=100*s/a.width.val);var l=a.width.right.value;"px"===a.width.right.units&&a.width.val>0&&(l=100*l/a.width.val);var u=a.height.top.value;"px"===a.height.top.units&&a.height.val>0&&(u=100*u/a.height.val);var c=a.height.bottom.value;"px"===a.height.bottom.units&&a.height.val>0&&(c=100*c/a.height.val);var d=y(a.width.val-i.w,s,l),h=d.biasDiff,f=d.biasComplementDiff,p=y(a.height.val-i.h,u,c),g=p.biasDiff,v=p.biasComplementDiff;t.autoPadding=function(e,t,n,r){if("%"!==n.units)return"px"===n.units?n.pfValue:0;switch(r){case"width":return e>0?n.pfValue*e:0;case"height":return t>0?n.pfValue*t:0;case"average":return e>0&&t>0?n.pfValue*(e+t)/2:0;case"min":return e>0&&t>0?e>t?n.pfValue*t:n.pfValue*e:0;case"max":return e>0&&t>0?e>t?n.pfValue*e:n.pfValue*t:0;default:return 0}}(i.w,i.h,e.pstyle("padding"),e.pstyle("padding-relative-to").value),t.autoWidth=Math.max(i.w,a.width.val),o.x=(-h+i.x1+i.x2+f)/2,t.autoHeight=Math.max(i.h,a.height.val),o.y=(-g+i.y1+i.y2+v)/2}function y(e,t,n){var r=0,a=0,i=t+n;return e>0&&i>0&&(r=t/i*e,a=n/i*e),{biasDiff:r,biasComplementDiff:a}}}for(var r=0;r<this.length;r++){var a=this[r],i=a._private;i.compoundBoundsClean&&!e||(n(a),t.batching()||(i.compoundBoundsClean=!0))}return this};var xs=function(e){return e===1/0||e===-1/0?0:e},ws=function(e,t,n,r,a){r-t!==0&&a-n!==0&&null!=t&&null!=n&&null!=r&&null!=a&&(e.x1=t<e.x1?t:e.x1,e.x2=r>e.x2?r:e.x2,e.y1=n<e.y1?n:e.y1,e.y2=a>e.y2?a:e.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1)},Es=function(e,t){return null==t?e:ws(e,t.x1,t.y1,t.x2,t.y2)},ks=function(e,t,n){return ht(e,t,n)},Ts=function(e,t,n){if(!t.cy().headless()){var r,a,i=t._private,o=i.rstyle,s=o.arrowWidth/2;if("none"!==t.pstyle(n+"-arrow-shape").value){"source"===n?(r=o.srcX,a=o.srcY):"target"===n?(r=o.tgtX,a=o.tgtY):(r=o.midX,a=o.midY);var l=i.arrowBounds=i.arrowBounds||{},u=l[n]=l[n]||{};u.x1=r-s,u.y1=a-s,u.x2=r+s,u.y2=a+s,u.w=u.x2-u.x1,u.h=u.y2-u.y1,tn(u,1),ws(e,u.x1,u.y1,u.x2,u.y2)}}},Cs=function(e,t,n){if(!t.cy().headless()){var r;r=n?n+"-":"";var a=t._private,i=a.rstyle;if(t.pstyle(r+"label").strValue){var o,s,l,u,c=t.pstyle("text-halign"),d=t.pstyle("text-valign"),h=ks(i,"labelWidth",n),f=ks(i,"labelHeight",n),p=ks(i,"labelX",n),g=ks(i,"labelY",n),v=t.pstyle(r+"text-margin-x").pfValue,y=t.pstyle(r+"text-margin-y").pfValue,m=t.isEdge(),b=t.pstyle(r+"text-rotation"),x=t.pstyle("text-outline-width").pfValue,w=t.pstyle("text-border-width").pfValue/2,E=t.pstyle("text-background-padding").pfValue,k=f,T=h,C=T/2,P=k/2;if(m)o=p-C,s=p+C,l=g-P,u=g+P;else{switch(c.value){case"left":o=p-T,s=p;break;case"center":o=p-C,s=p+C;break;case"right":o=p,s=p+T}switch(d.value){case"top":l=g-k,u=g;break;case"center":l=g-P,u=g+P;break;case"bottom":l=g,u=g+k}}var S=v-Math.max(x,w)-E-2,B=v+Math.max(x,w)+E+2,D=y-Math.max(x,w)-E-2,_=y+Math.max(x,w)+E+2;o+=S,s+=B,l+=D,u+=_;var A=n||"main",M=a.labelBounds,R=M[A]=M[A]||{};R.x1=o,R.y1=l,R.x2=s,R.y2=u,R.w=s-o,R.h=u-l,R.leftPad=S,R.rightPad=B,R.topPad=D,R.botPad=_;var I=m&&"autorotate"===b.strValue,N=null!=b.pfValue&&0!==b.pfValue;if(I||N){var L=I?ks(a.rstyle,"labelAngle",n):b.pfValue,z=Math.cos(L),O=Math.sin(L),V=(o+s)/2,F=(l+u)/2;if(!m){switch(c.value){case"left":V=s;break;case"right":V=o}switch(d.value){case"top":F=u;break;case"bottom":F=l}}var j=function(e,t){return{x:(e-=V)*z-(t-=F)*O+V,y:e*O+t*z+F}},X=j(o,l),q=j(o,u),Y=j(s,l),W=j(s,u);o=Math.min(X.x,q.x,Y.x,W.x),s=Math.max(X.x,q.x,Y.x,W.x),l=Math.min(X.y,q.y,Y.y,W.y),u=Math.max(X.y,q.y,Y.y,W.y)}var U=A+"Rot",H=M[U]=M[U]||{};H.x1=o,H.y1=l,H.x2=s,H.y2=u,H.w=s-o,H.h=u-l,ws(e,o,l,s,u),ws(a.labelBounds.all,o,l,s,u)}return e}},Ps=function(e,t){var n,r,a,i,o,s,l,u=e._private.cy,c=u.styleEnabled(),d=u.headless(),h=Qt(),f=e._private,p=e.isNode(),g=e.isEdge(),v=f.rstyle,y=p&&c?e.pstyle("bounds-expansion").pfValue:[0],m=function(e){return"none"!==e.pstyle("display").value},b=!c||m(e)&&(!g||m(e.source())&&m(e.target()));if(b){var x=0;c&&t.includeOverlays&&0!==e.pstyle("overlay-opacity").value&&(x=e.pstyle("overlay-padding").value);var w=0;c&&t.includeUnderlays&&0!==e.pstyle("underlay-opacity").value&&(w=e.pstyle("underlay-padding").value);var E=Math.max(x,w),k=0;if(c&&(k=e.pstyle("width").pfValue/2),p&&t.includeNodes){var T=e.position();o=T.x,s=T.y;var C=e.outerWidth()/2,P=e.outerHeight()/2;ws(h,n=o-C,a=s-P,r=o+C,i=s+P),c&&t.includeOutlines&&function(e,t){if(!t.cy().headless()){var n,r,a,i=t.pstyle("outline-opacity").value,o=t.pstyle("outline-width").value;if(i>0&&o>0){var s=t.pstyle("outline-offset").value,l=t.pstyle("shape").value,u=o+s,c=(e.w+2*u)/e.w,d=(e.h+2*u)/e.h,h=0;["diamond","pentagon","round-triangle"].includes(l)?(c=(e.w+2.4*u)/e.w,h=-u/3.6):["concave-hexagon","rhomboid","right-rhomboid"].includes(l)?c=(e.w+2.4*u)/e.w:"star"===l?(c=(e.w+2.8*u)/e.w,d=(e.h+2.6*u)/e.h,h=-u/3.8):"triangle"===l?(c=(e.w+2.8*u)/e.w,d=(e.h+2.4*u)/e.h,h=-u/1.4):"vee"===l&&(c=(e.w+4.4*u)/e.w,d=(e.h+3.8*u)/e.h,h=.5*-u);var f=e.h*d-e.h,p=e.w*c-e.w;if(nn(e,[Math.ceil(f/2),Math.ceil(p/2)]),0!==h){var g=(r=0,a=h,{x1:(n=e).x1+r,x2:n.x2+r,y1:n.y1+a,y2:n.y2+a,w:n.w,h:n.h});Jt(e,g)}}}}(h,e)}else if(g&&t.includeEdges)if(c&&!d){var S=e.pstyle("curve-style").strValue;if(n=Math.min(v.srcX,v.midX,v.tgtX),r=Math.max(v.srcX,v.midX,v.tgtX),a=Math.min(v.srcY,v.midY,v.tgtY),i=Math.max(v.srcY,v.midY,v.tgtY),ws(h,n-=k,a-=k,r+=k,i+=k),"haystack"===S){var B=v.haystackPts;if(B&&2===B.length){if(n=B[0].x,a=B[0].y,n>(r=B[1].x)){var D=n;n=r,r=D}if(a>(i=B[1].y)){var _=a;a=i,i=_}ws(h,n-k,a-k,r+k,i+k)}}else if("bezier"===S||"unbundled-bezier"===S||S.endsWith("segments")||S.endsWith("taxi")){var A;switch(S){case"bezier":case"unbundled-bezier":A=v.bezierPts;break;case"segments":case"taxi":case"round-segments":case"round-taxi":A=v.linePts}if(null!=A)for(var M=0;M<A.length;M++){var R=A[M];n=R.x-k,r=R.x+k,a=R.y-k,i=R.y+k,ws(h,n,a,r,i)}}}else{var I=e.source().position(),N=e.target().position();if((n=I.x)>(r=N.x)){var L=n;n=r,r=L}if((a=I.y)>(i=N.y)){var z=a;a=i,i=z}ws(h,n-=k,a-=k,r+=k,i+=k)}if(c&&t.includeEdges&&g&&(Ts(h,e,"mid-source"),Ts(h,e,"mid-target"),Ts(h,e,"source"),Ts(h,e,"target")),c)if("yes"===e.pstyle("ghost").value){var O=e.pstyle("ghost-offset-x").pfValue,V=e.pstyle("ghost-offset-y").pfValue;ws(h,h.x1+O,h.y1+V,h.x2+O,h.y2+V)}var F=f.bodyBounds=f.bodyBounds||{};rn(F,h),nn(F,y),tn(F,1),c&&(n=h.x1,r=h.x2,a=h.y1,i=h.y2,ws(h,n-E,a-E,r+E,i+E));var j=f.overlayBounds=f.overlayBounds||{};rn(j,h),nn(j,y),tn(j,1);var X=f.labelBounds=f.labelBounds||{};null!=X.all?((l=X.all).x1=1/0,l.y1=1/0,l.x2=-1/0,l.y2=-1/0,l.w=0,l.h=0):X.all=Qt(),c&&t.includeLabels&&(t.includeMainLabels&&Cs(h,e,null),g&&(t.includeSourceLabels&&Cs(h,e,"source"),t.includeTargetLabels&&Cs(h,e,"target")))}return h.x1=xs(h.x1),h.y1=xs(h.y1),h.x2=xs(h.x2),h.y2=xs(h.y2),h.w=xs(h.x2-h.x1),h.h=xs(h.y2-h.y1),h.w>0&&h.h>0&&b&&(nn(h,y),tn(h,1)),h},Ss=function(e){var t=0,n=function(e){return(e?1:0)<<t++},r=0;return r+=n(e.incudeNodes),r+=n(e.includeEdges),r+=n(e.includeLabels),r+=n(e.includeMainLabels),r+=n(e.includeSourceLabels),r+=n(e.includeTargetLabels),r+=n(e.includeOverlays),r+=n(e.includeOutlines)},Bs=function(e){var t=function(e){return Math.round(e)};if(e.isEdge()){var n=e.source().position(),r=e.target().position();return Ye([t(n.x),t(n.y),t(r.x),t(r.y)])}var a=e.position();return Ye([t(a.x),t(a.y)])},Ds=function(e,t){var n,r=e._private,a=e.isEdge(),i=(null==t?As:Ss(t))===As;if(null==r.bbCache?(n=Ps(e,_s),r.bbCache=n,r.bbCachePosKey=Bs(e)):n=r.bbCache,!i){var o=e.isNode();n=Qt(),(t.includeNodes&&o||t.includeEdges&&!o)&&(t.includeOverlays?Es(n,r.overlayBounds):Es(n,r.bodyBounds)),t.includeLabels&&(t.includeMainLabels&&(!a||t.includeSourceLabels&&t.includeTargetLabels)?Es(n,r.labelBounds.all):(t.includeMainLabels&&Es(n,r.labelBounds.mainRot),t.includeSourceLabels&&Es(n,r.labelBounds.sourceRot),t.includeTargetLabels&&Es(n,r.labelBounds.targetRot))),n.w=n.x2-n.x1,n.h=n.y2-n.y1}return n},_s={includeNodes:!0,includeEdges:!0,includeLabels:!0,includeMainLabels:!0,includeSourceLabels:!0,includeTargetLabels:!0,includeOverlays:!0,includeUnderlays:!0,includeOutlines:!0,useCache:!0},As=Ss(_s),Ms=ut(_s);ms.boundingBox=function(e){var t,n=void 0===e||void 0===e.useCache||!0===e.useCache,r=se((function(e){var t=e._private;return null==t.bbCache||t.styleDirty||t.bbCachePosKey!==Bs(e)}),(function(e){return e.id()}));if(n&&1===this.length&&!r(this[0]))e=void 0===e?_s:Ms(e),t=Ds(this[0],e);else{t=Qt();var a=Ms(e=e||_s),i=this,o=i.cy().styleEnabled();this.edges().forEach(r),this.nodes().forEach(r),o&&this.recalculateRenderedStyle(n),this.updateCompoundBounds(!n);for(var s=0;s<i.length;s++){var l=i[s];r(l)&&l.dirtyBoundingBoxCache(),Es(t,Ds(l,a))}}return t.x1=xs(t.x1),t.y1=xs(t.y1),t.x2=xs(t.x2),t.y2=xs(t.y2),t.w=xs(t.x2-t.x1),t.h=xs(t.y2-t.y1),t},ms.dirtyBoundingBoxCache=function(){for(var e=0;e<this.length;e++){var t=this[e]._private;t.bbCache=null,t.bbCachePosKey=null,t.bodyBounds=null,t.overlayBounds=null,t.labelBounds.all=null,t.labelBounds.source=null,t.labelBounds.target=null,t.labelBounds.main=null,t.labelBounds.sourceRot=null,t.labelBounds.targetRot=null,t.labelBounds.mainRot=null,t.arrowBounds.source=null,t.arrowBounds.target=null,t.arrowBounds["mid-source"]=null,t.arrowBounds["mid-target"]=null}return this.emitAndNotify("bounds"),this},ms.boundingBoxAt=function(e){var t=this.nodes(),n=this.cy(),r=n.hasCompoundNodes(),a=n.collection();if(r&&(a=t.filter((function(e){return e.isParent()})),t=t.not(a)),$(e)){var i=e;e=function(){return i}}n.startBatch(),t.forEach((function(t,n){return t._private.bbAtOldPos=e(t,n)})).silentPositions(e),r&&(a.dirtyCompoundBoundsCache(),a.dirtyBoundingBoxCache(),a.updateCompoundBounds(!0));var o=function(e){return{x1:e.x1,x2:e.x2,w:e.w,y1:e.y1,y2:e.y2,h:e.h}}(this.boundingBox({useCache:!1}));return t.silentPositions((function(e){return e._private.bbAtOldPos})),r&&(a.dirtyCompoundBoundsCache(),a.dirtyBoundingBoxCache(),a.updateCompoundBounds(!0)),n.endBatch(),o},ys.boundingbox=ys.bb=ys.boundingBox,ys.renderedBoundingbox=ys.renderedBoundingBox;var Rs,Is,Ns=ms;Rs=Is={};var Ls=function(e){e.uppercaseName=de(e.name),e.autoName="auto"+e.uppercaseName,e.labelName="label"+e.uppercaseName,e.outerName="outer"+e.uppercaseName,e.uppercaseOuterName=de(e.outerName),Rs[e.name]=function(){var t=this[0],n=t._private,r=n.cy._private.styleEnabled;if(t){if(r){if(t.isParent())return t.updateCompoundBounds(),n[e.autoName]||0;var a=t.pstyle(e.name);return"label"===a.strValue?(t.recalculateRenderedStyle(),n.rstyle[e.labelName]||0):a.pfValue}return 1}},Rs["outer"+e.uppercaseName]=function(){var t=this[0],n=t._private.cy._private.styleEnabled;if(t){if(n){var r=t[e.name](),a=t.pstyle("border-position").value;return r+("center"===a?t.pstyle("border-width").pfValue:"outside"===a?2*t.pstyle("border-width").pfValue:0)+2*t.padding()}return 1}},Rs["rendered"+e.uppercaseName]=function(){var t=this[0];if(t)return t[e.name]()*this.cy().zoom()},Rs["rendered"+e.uppercaseOuterName]=function(){var t=this[0];if(t)return t[e.outerName]()*this.cy().zoom()}};Ls({name:"width"}),Ls({name:"height"}),Is.padding=function(){var e=this[0],t=e._private;return e.isParent()?(e.updateCompoundBounds(),void 0!==t.autoPadding?t.autoPadding:e.pstyle("padding").pfValue):e.pstyle("padding").pfValue},Is.paddedHeight=function(){var e=this[0];return e.height()+2*e.padding()},Is.paddedWidth=function(){var e=this[0];return e.width()+2*e.padding()};var zs=Is,Os={controlPoints:{get:function(e){return e.renderer().getControlPoints(e)},mult:!0},segmentPoints:{get:function(e){return e.renderer().getSegmentPoints(e)},mult:!0},sourceEndpoint:{get:function(e){return e.renderer().getSourceEndpoint(e)}},targetEndpoint:{get:function(e){return e.renderer().getTargetEndpoint(e)}},midpoint:{get:function(e){return e.renderer().getEdgeMidpoint(e)}}},Vs=Object.keys(Os).reduce((function(e,t){var n=Os[t],r=function(e){return"rendered"+e[0].toUpperCase()+e.substr(1)}(t);return e[t]=function(){return function(e,t){if(e.isEdge()&&e.takesUpSpace())return t(e)}(this,n.get)},n.mult?e[r]=function(){return function(e,t){if(e.isEdge()&&e.takesUpSpace()){var n=e.cy(),r=n.pan(),a=n.zoom();return t(e).map((function(e){return Ft(e,a,r)}))}}(this,n.get)}:e[r]=function(){return function(e,t){if(e.isEdge()&&e.takesUpSpace()){var n=e.cy();return Ft(t(e),n.zoom(),n.pan())}}(this,n.get)},e}),{}),Fs=me({},bs,Ns,zs,Vs),js=function(e,t){this.recycle(e,t)};function Xs(){return!1}function qs(){return!0}js.prototype={instanceString:function(){return"event"},recycle:function(e,t){if(this.isImmediatePropagationStopped=this.isPropagationStopped=this.isDefaultPrevented=Xs,null!=e&&e.preventDefault?(this.type=e.type,this.isDefaultPrevented=e.defaultPrevented?qs:Xs):null!=e&&e.type?t=e:this.type=e,null!=t&&(this.originalEvent=t.originalEvent,this.type=null!=t.type?t.type:this.type,this.cy=t.cy,this.target=t.target,this.position=t.position,this.renderedPosition=t.renderedPosition,this.namespace=t.namespace,this.layout=t.layout),null!=this.cy&&null!=this.position&&null==this.renderedPosition){var n=this.position,r=this.cy.zoom(),a=this.cy.pan();this.renderedPosition={x:n.x*r+a.x,y:n.y*r+a.y}}this.timeStamp=e&&e.timeStamp||Date.now()},preventDefault:function(){this.isDefaultPrevented=qs;var e=this.originalEvent;e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){this.isPropagationStopped=qs;var e=this.originalEvent;e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=qs,this.stopPropagation()},isDefaultPrevented:Xs,isPropagationStopped:Xs,isImmediatePropagationStopped:Xs};var Ys=/^([^.]+)(\.(?:[^.]+))?$/,Ws={qualifierCompare:function(e,t){return e===t},eventMatches:function(){return!0},addEventFields:function(){},callbackContext:function(e){return e},beforeEmit:function(){},afterEmit:function(){},bubble:function(){return!1},parent:function(){return null},context:null},Us=Object.keys(Ws),Hs={};function Ks(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Hs,t=arguments.length>1?arguments[1]:void 0,n=0;n<Us.length;n++){var r=Us[n];this[r]=e[r]||Ws[r]}this.context=t||this.context,this.listeners=[],this.emitting=0}var Gs=Ks.prototype,Zs=function(e,t,n,r,a,i,o){G(r)&&(a=r,r=null),o&&(i=null==i?o:me({},i,o));for(var s=Z(n)?n:n.split(/\s+/),l=0;l<s.length;l++){var u=s[l];if(!ie(u)){var c=u.match(Ys);if(c)if(!1===t(e,u,c[1],c[2]?c[2]:null,r,a,i))break}}},$s=function(e,t){return e.addEventFields(e.context,t),new js(t.type,t)},Qs=function(e,t,n){if("event"!==H(n))if($(n))t(e,$s(e,n));else for(var r=Z(n)?n:n.split(/\s+/),a=0;a<r.length;a++){var i=r[a];if(!ie(i)){var o=i.match(Ys);if(o){var s=o[1],l=o[2]?o[2]:null;t(e,$s(e,{type:s,namespace:l,target:e.context}))}}}else t(e,n)};Gs.on=Gs.addListener=function(e,t,n,r,a){return Zs(this,(function(e,t,n,r,a,i,o){G(i)&&e.listeners.push({event:t,callback:i,type:n,namespace:r,qualifier:a,conf:o})}),e,t,n,r,a),this},Gs.one=function(e,t,n,r){return this.on(e,t,n,r,{one:!0})},Gs.removeListener=Gs.off=function(e,t,n,r){var a=this;0!==this.emitting&&(this.listeners=this.listeners.slice());for(var i=this.listeners,o=function(o){var s=i[o];Zs(a,(function(t,n,r,a,l,u){if((s.type===r||"*"===e)&&(!a&&".*"!==s.namespace||s.namespace===a)&&(!l||t.qualifierCompare(s.qualifier,l))&&(!u||s.callback===u))return i.splice(o,1),!1}),e,t,n,r)},s=i.length-1;s>=0;s--)o(s);return this},Gs.removeAllListeners=function(){return this.removeListener("*")},Gs.emit=Gs.trigger=function(e,t,n){var r=this.listeners,a=r.length;return this.emitting++,Z(t)||(t=[t]),Qs(this,(function(e,i){null!=n&&(r=[{event:i.event,type:i.type,namespace:i.namespace,callback:n}],a=r.length);for(var o=function(){var n=r[s];if(n.type===i.type&&(!n.namespace||n.namespace===i.namespace||".*"===n.namespace)&&e.eventMatches(e.context,n,i)){var a=[i];null!=t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];e.push(r)}}(a,t),e.beforeEmit(e.context,n,i),n.conf&&n.conf.one&&(e.listeners=e.listeners.filter((function(e){return e!==n})));var o=e.callbackContext(e.context,n,i),l=n.callback.apply(o,a);e.afterEmit(e.context,n,i),!1===l&&(i.stopPropagation(),i.preventDefault())}},s=0;s<a;s++)o();e.bubble(e.context)&&!i.isPropagationStopped()&&e.parent(e.context).emit(i,t)}),e),this.emitting--,this};var Js={qualifierCompare:function(e,t){return null==e||null==t?null==e&&null==t:e.sameText(t)},eventMatches:function(e,t,n){var r=t.qualifier;return null==r||e!==n.target&&te(n.target)&&r.matches(n.target)},addEventFields:function(e,t){t.cy=e.cy(),t.target=e},callbackContext:function(e,t,n){return null!=t.qualifier?n.target:e},beforeEmit:function(e,t){t.conf&&t.conf.once&&t.conf.onceCollection.removeListener(t.event,t.qualifier,t.callback)},bubble:function(){return!0},parent:function(e){return e.isChild()?e.parent():e.cy()}},el=function(e){return K(e)?new Qo(e):e},tl={createEmitter:function(){for(var e=0;e<this.length;e++){var t=this[e],n=t._private;n.emitter||(n.emitter=new Ks(Js,t))}return this},emitter:function(){return this._private.emitter},on:function(e,t,n){for(var r=el(t),a=0;a<this.length;a++){this[a].emitter().on(e,r,n)}return this},removeListener:function(e,t,n){for(var r=el(t),a=0;a<this.length;a++){this[a].emitter().removeListener(e,r,n)}return this},removeAllListeners:function(){for(var e=0;e<this.length;e++){this[e].emitter().removeAllListeners()}return this},one:function(e,t,n){for(var r=el(t),a=0;a<this.length;a++){this[a].emitter().one(e,r,n)}return this},once:function(e,t,n){for(var r=el(t),a=0;a<this.length;a++){this[a].emitter().on(e,r,n,{once:!0,onceCollection:this})}},emit:function(e,t){for(var n=0;n<this.length;n++){this[n].emitter().emit(e,t)}return this},emitAndNotify:function(e,t){if(0!==this.length)return this.cy().notify(e,this),this.emit(e,t),this}};po.eventAliasesOn(tl);var nl={nodes:function(e){return this.filter((function(e){return e.isNode()})).filter(e)},edges:function(e){return this.filter((function(e){return e.isEdge()})).filter(e)},byGroup:function(){for(var e=this.spawn(),t=this.spawn(),n=0;n<this.length;n++){var r=this[n];r.isNode()?e.push(r):t.push(r)}return{nodes:e,edges:t}},filter:function(e,t){if(void 0===e)return this;if(K(e)||ee(e))return new Qo(e).filter(this);if(G(e)){for(var n=this.spawn(),r=this,a=0;a<r.length;a++){var i=r[a];(t?e.apply(t,[i,a,r]):e(i,a,r))&&n.push(i)}return n}return this.spawn()},not:function(e){if(e){K(e)&&(e=this.filter(e));for(var t=this.spawn(),n=0;n<this.length;n++){var r=this[n];e.has(r)||t.push(r)}return t}return this},absoluteComplement:function(){return this.cy().mutableElements().not(this)},intersect:function(e){if(K(e)){var t=e;return this.filter(t)}for(var n=this.spawn(),r=e,a=this.length<e.length,i=a?this:r,o=a?r:this,s=0;s<i.length;s++){var l=i[s];o.has(l)&&n.push(l)}return n},xor:function(e){var t=this._private.cy;K(e)&&(e=t.$(e));var n=this.spawn(),r=e,a=function(e,t){for(var r=0;r<e.length;r++){var a=e[r],i=a._private.data.id;t.hasElementWithId(i)||n.push(a)}};return a(this,r),a(r,this),n},diff:function(e){var t=this._private.cy;K(e)&&(e=t.$(e));var n=this.spawn(),r=this.spawn(),a=this.spawn(),i=e,o=function(e,t,n){for(var r=0;r<e.length;r++){var i=e[r],o=i._private.data.id;t.hasElementWithId(o)?a.merge(i):n.push(i)}};return o(this,i,n),o(i,this,r),{left:n,right:r,both:a}},add:function(e){var t=this._private.cy;if(!e)return this;if(K(e)){var n=e;e=t.mutableElements().filter(n)}for(var r=this.spawnSelf(),a=0;a<e.length;a++){var i=e[a],o=!this.has(i);o&&r.push(i)}return r},merge:function(e){var t=this._private,n=t.cy;if(!e)return this;if(e&&K(e)){var r=e;e=n.mutableElements().filter(r)}for(var a=t.map,i=0;i<e.length;i++){var o=e[i],s=o._private.data.id;if(!a.has(s)){var l=this.length++;this[l]=o,a.set(s,{ele:o,index:l})}}return this},unmergeAt:function(e){var t=this[e].id(),n=this._private.map;this[e]=void 0,n.delete(t);var r=e===this.length-1;if(this.length>1&&!r){var a=this.length-1,i=this[a],o=i._private.data.id;this[a]=void 0,this[e]=i,n.set(o,{ele:i,index:e})}return this.length--,this},unmergeOne:function(e){e=e[0];var t=this._private,n=e._private.data.id,r=t.map.get(n);if(!r)return this;var a=r.index;return this.unmergeAt(a),this},unmerge:function(e){var t=this._private.cy;if(!e)return this;if(e&&K(e)){var n=e;e=t.mutableElements().filter(n)}for(var r=0;r<e.length;r++)this.unmergeOne(e[r]);return this},unmergeBy:function(e){for(var t=this.length-1;t>=0;t--){e(this[t])&&this.unmergeAt(t)}return this},map:function(e,t){for(var n=[],r=this,a=0;a<r.length;a++){var i=r[a],o=t?e.apply(t,[i,a,r]):e(i,a,r);n.push(o)}return n},reduce:function(e,t){for(var n=t,r=this,a=0;a<r.length;a++)n=e(n,r[a],a,r);return n},max:function(e,t){for(var n,r=-1/0,a=this,i=0;i<a.length;i++){var o=a[i],s=t?e.apply(t,[o,i,a]):e(o,i,a);s>r&&(r=s,n=o)}return{value:r,ele:n}},min:function(e,t){for(var n,r=1/0,a=this,i=0;i<a.length;i++){var o=a[i],s=t?e.apply(t,[o,i,a]):e(o,i,a);s<r&&(r=s,n=o)}return{value:r,ele:n}}},rl=nl;rl.u=rl["|"]=rl["+"]=rl.union=rl.or=rl.add,rl["\\"]=rl["!"]=rl["-"]=rl.difference=rl.relativeComplement=rl.subtract=rl.not,rl.n=rl["&"]=rl["."]=rl.and=rl.intersection=rl.intersect,rl["^"]=rl["(+)"]=rl["(-)"]=rl.symmetricDifference=rl.symdiff=rl.xor,rl.fnFilter=rl.filterFn=rl.stdFilter=rl.filter,rl.complement=rl.abscomp=rl.absoluteComplement;var al=function(e,t){var n=e.cy().hasCompoundNodes();function r(e){var t=e.pstyle("z-compound-depth");return"auto"===t.value?n?e.zDepth():0:"bottom"===t.value?-1:"top"===t.value?$e:0}var a=r(e)-r(t);if(0!==a)return a;function i(e){return"auto"===e.pstyle("z-index-compare").value&&e.isNode()?1:0}var o=i(e)-i(t);if(0!==o)return o;var s=e.pstyle("z-index").value-t.pstyle("z-index").value;return 0!==s?s:e.poolIndex()-t.poolIndex()},il={forEach:function(e,t){if(G(e))for(var n=this.length,r=0;r<n;r++){var a=this[r];if(!1===(t?e.apply(t,[a,r,this]):e(a,r,this)))break}return this},toArray:function(){for(var e=[],t=0;t<this.length;t++)e.push(this[t]);return e},slice:function(e,t){var n=[],r=this.length;null==t&&(t=r),null==e&&(e=0),e<0&&(e=r+e),t<0&&(t=r+t);for(var a=e;a>=0&&a<t&&a<r;a++)n.push(this[a]);return this.spawn(n)},size:function(){return this.length},eq:function(e){return this[e]||this.spawn()},first:function(){return this[0]||this.spawn()},last:function(){return this[this.length-1]||this.spawn()},empty:function(){return 0===this.length},nonempty:function(){return!this.empty()},sort:function(e){if(!G(e))return this;var t=this.toArray().sort(e);return this.spawn(t)},sortByZIndex:function(){return this.sort(al)},zDepth:function(){var e=this[0];if(e){var t=e._private;if("nodes"===t.group){var n=t.data.parent?e.parents().size():0;return e.isParent()?n:$e-1}var r=t.source,a=t.target,i=r.zDepth(),o=a.zDepth();return Math.max(i,o,0)}}};il.each=il.forEach;var ol;ol="undefined",("undefined"==typeof Symbol?"undefined":d(Symbol))!=ol&&d(Symbol.iterator)!=ol&&(il[Symbol.iterator]=function(){var e=this,t={value:void 0,done:!1},n=0,r=this.length;return s({next:function(){return n<r?t.value=e[n++]:(t.value=void 0,t.done=!0),t}},Symbol.iterator,(function(){return this}))});var sl=ut({nodeDimensionsIncludeLabels:!1}),ll={layoutDimensions:function(e){var t;if(e=sl(e),this.takesUpSpace())if(e.nodeDimensionsIncludeLabels){var n=this.boundingBox();t={w:n.w,h:n.h}}else t={w:this.outerWidth(),h:this.outerHeight()};else t={w:0,h:0};return 0!==t.w&&0!==t.h||(t.w=t.h=1),t},layoutPositions:function(e,t,n){var r=this.nodes().filter((function(e){return!e.isParent()})),a=this.cy(),i=t.eles,o=function(e){return e.id()},s=se(n,o);e.emit({type:"layoutstart",layout:e}),e.animations=[];var l=t.spacingFactor&&1!==t.spacingFactor,u=function(){if(!l)return null;for(var e=Qt(),t=0;t<r.length;t++){var n=r[t],a=s(n,t);en(e,a.x,a.y)}return e}(),c=se((function(e,n){var r=s(e,n);l&&(r=function(e,t,n){var r=t.x1+t.w/2,a=t.y1+t.h/2;return{x:r+(n.x-r)*e,y:a+(n.y-a)*e}}(Math.abs(t.spacingFactor),u,r));return null!=t.transform&&(r=t.transform(e,r)),r}),o);if(t.animate){for(var d=0;d<r.length;d++){var h=r[d],f=c(h,d);if(null==t.animateFilter||t.animateFilter(h,d)){var p=h.animation({position:f,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(p)}else h.position(f)}if(t.fit){var g=a.animation({fit:{boundingBox:i.boundingBoxAt(c),padding:t.padding},duration:t.animationDuration,easing:t.animationEasing});e.animations.push(g)}else if(void 0!==t.zoom&&void 0!==t.pan){var v=a.animation({zoom:t.zoom,pan:t.pan,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(v)}e.animations.forEach((function(e){return e.play()})),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),jr.all(e.animations.map((function(e){return e.promise()}))).then((function(){e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e})}))}else r.positions(c),t.fit&&a.fit(t.eles,t.padding),null!=t.zoom&&a.zoom(t.zoom),t.pan&&a.pan(t.pan),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e});return this},layout:function(e){return this.cy().makeLayout(me({},e,{eles:this}))}};function ul(e,t,n){var r,a=n._private,i=a.styleCache=a.styleCache||[];return null!=(r=i[e])?r:r=i[e]=t(n)}function cl(e,t){return e=We(e),function(n){return ul(e,t,n)}}function dl(e,t){e=We(e);var n=function(e){return t.call(e)};return function(){var t=this[0];if(t)return ul(e,n,t)}}ll.createLayout=ll.makeLayout=ll.layout;var hl={recalculateRenderedStyle:function(e){var t=this.cy(),n=t.renderer(),r=t.styleEnabled();return n&&r&&n.recalculateRenderedStyle(this,e),this},dirtyStyleCache:function(){var e,t=this.cy(),n=function(e){return e._private.styleCache=null};t.hasCompoundNodes()?((e=this.spawnSelf().merge(this.descendants()).merge(this.parents())).merge(e.connectedEdges()),e.forEach(n)):this.forEach((function(e){n(e),e.connectedEdges().forEach(n)}));return this},updateStyle:function(e){var t=this._private.cy;if(!t.styleEnabled())return this;if(t.batching())return t._private.batchStyleEles.merge(this),this;var n=this;e=!(!e&&void 0!==e),t.hasCompoundNodes()&&(n=this.spawnSelf().merge(this.descendants()).merge(this.parents()));var r=n;return e?r.emitAndNotify("style"):r.emit("style"),n.forEach((function(e){return e._private.styleDirty=!0})),this},cleanStyle:function(){var e=this.cy();if(e.styleEnabled())for(var t=0;t<this.length;t++){var n=this[t];n._private.styleDirty&&(n._private.styleDirty=!1,e.style().apply(n))}},parsedStyle:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=this[0],r=n.cy();if(r.styleEnabled()&&n){n._private.styleDirty&&(n._private.styleDirty=!1,r.style().apply(n));var a=n._private.style[e];return null!=a?a:t?r.style().getDefaultProperty(e):null}},numericStyle:function(e){var t=this[0];if(t.cy().styleEnabled()&&t){var n=t.pstyle(e);return void 0!==n.pfValue?n.pfValue:n.value}},numericStyleUnits:function(e){var t=this[0];if(t.cy().styleEnabled())return t?t.pstyle(e).units:void 0},renderedStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var n=this[0];return n?t.style().getRenderedStyle(n,e):void 0},style:function(e,t){var n=this.cy();if(!n.styleEnabled())return this;var r=!1,a=n.style();if($(e)){var i=e;a.applyBypass(this,i,r),this.emitAndNotify("style")}else if(K(e)){if(void 0===t){var o=this[0];return o?a.getStylePropertyValue(o,e):void 0}a.applyBypass(this,e,t,r),this.emitAndNotify("style")}else if(void 0===e){var s=this[0];return s?a.getRawStyle(s):void 0}return this},removeStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var n=!1,r=t.style(),a=this;if(void 0===e)for(var i=0;i<a.length;i++){var o=a[i];r.removeAllBypasses(o,n)}else{e=e.split(/\s+/);for(var s=0;s<a.length;s++){var l=a[s];r.removeBypasses(l,e,n)}}return this.emitAndNotify("style"),this},show:function(){return this.css("display","element"),this},hide:function(){return this.css("display","none"),this},effectiveOpacity:function(){var e=this.cy();if(!e.styleEnabled())return 1;var t=e.hasCompoundNodes(),n=this[0];if(n){var r=n._private,a=n.pstyle("opacity").value;if(!t)return a;var i=r.data.parent?n.parents():null;if(i)for(var o=0;o<i.length;o++){a*=i[o].pstyle("opacity").value}return a}},transparent:function(){if(!this.cy().styleEnabled())return!1;var e=this[0],t=e.cy().hasCompoundNodes();return e?t?0===e.effectiveOpacity():0===e.pstyle("opacity").value:void 0},backgrounding:function(){return!!this.cy().styleEnabled()&&!!this[0]._private.backgrounding}};function fl(e,t){var n=e._private.data.parent?e.parents():null;if(n)for(var r=0;r<n.length;r++){if(!t(n[r]))return!1}return!0}function pl(e){var t=e.ok,n=e.edgeOkViaNode||e.ok,r=e.parentOk||e.ok;return function(){var e=this.cy();if(!e.styleEnabled())return!0;var a=this[0],i=e.hasCompoundNodes();if(a){var o=a._private;if(!t(a))return!1;if(a.isNode())return!i||fl(a,r);var s=o.source,l=o.target;return n(s)&&(!i||fl(s,n))&&(s===l||n(l)&&(!i||fl(l,n)))}}}var gl=cl("eleTakesUpSpace",(function(e){return"element"===e.pstyle("display").value&&0!==e.width()&&(!e.isNode()||0!==e.height())}));hl.takesUpSpace=dl("takesUpSpace",pl({ok:gl}));var vl=cl("eleInteractive",(function(e){return"yes"===e.pstyle("events").value&&"visible"===e.pstyle("visibility").value&&gl(e)})),yl=cl("parentInteractive",(function(e){return"visible"===e.pstyle("visibility").value&&gl(e)}));hl.interactive=dl("interactive",pl({ok:vl,parentOk:yl,edgeOkViaNode:gl})),hl.noninteractive=function(){var e=this[0];if(e)return!e.interactive()};var ml=cl("eleVisible",(function(e){return"visible"===e.pstyle("visibility").value&&0!==e.pstyle("opacity").pfValue&&gl(e)})),bl=gl;hl.visible=dl("visible",pl({ok:ml,edgeOkViaNode:bl})),hl.hidden=function(){var e=this[0];if(e)return!e.visible()},hl.isBundledBezier=dl("isBundledBezier",(function(){return!!this.cy().styleEnabled()&&(!this.removed()&&"bezier"===this.pstyle("curve-style").value&&this.takesUpSpace())})),hl.bypass=hl.css=hl.style,hl.renderedCss=hl.renderedStyle,hl.removeBypass=hl.removeCss=hl.removeStyle,hl.pstyle=hl.parsedStyle;var xl={};function wl(e){return function(){var t=arguments,n=[];if(2===t.length){var r=t[0],a=t[1];this.on(e.event,r,a)}else if(1===t.length&&G(t[0])){var i=t[0];this.on(e.event,i)}else if(0===t.length||1===t.length&&Z(t[0])){for(var o=1===t.length?t[0]:null,s=0;s<this.length;s++){var l=this[s],u=!e.ableField||l._private[e.ableField],c=l._private[e.field]!=e.value;if(e.overrideAble){var d=e.overrideAble(l);if(void 0!==d&&(u=d,!d))return this}u&&(l._private[e.field]=e.value,c&&n.push(l))}var h=this.spawn(n);h.updateStyle(),h.emit(e.event),o&&h.emit(o)}return this}}function El(e){xl[e.field]=function(){var t=this[0];if(t){if(e.overrideField){var n=e.overrideField(t);if(void 0!==n)return n}return t._private[e.field]}},xl[e.on]=wl({event:e.on,field:e.field,ableField:e.ableField,overrideAble:e.overrideAble,value:!0}),xl[e.off]=wl({event:e.off,field:e.field,ableField:e.ableField,overrideAble:e.overrideAble,value:!1})}El({field:"locked",overrideField:function(e){return!!e.cy().autolock()||void 0},on:"lock",off:"unlock"}),El({field:"grabbable",overrideField:function(e){return!e.cy().autoungrabify()&&!e.pannable()&&void 0},on:"grabify",off:"ungrabify"}),El({field:"selected",ableField:"selectable",overrideAble:function(e){return!e.cy().autounselectify()&&void 0},on:"select",off:"unselect"}),El({field:"selectable",overrideField:function(e){return!e.cy().autounselectify()&&void 0},on:"selectify",off:"unselectify"}),xl.deselect=xl.unselect,xl.grabbed=function(){var e=this[0];if(e)return e._private.grabbed},El({field:"active",on:"activate",off:"unactivate"}),El({field:"pannable",on:"panify",off:"unpanify"}),xl.inactive=function(){var e=this[0];if(e)return!e._private.active};var kl={},Tl=function(e){return function(t){for(var n=[],r=0;r<this.length;r++){var a=this[r];if(a.isNode()){for(var i=!1,o=a.connectedEdges(),s=0;s<o.length;s++){var l=o[s],u=l.source(),c=l.target();if(e.noIncomingEdges&&c===a&&u!==a||e.noOutgoingEdges&&u===a&&c!==a){i=!0;break}}i||n.push(a)}}return this.spawn(n,!0).filter(t)}},Cl=function(e){return function(t){for(var n=[],r=0;r<this.length;r++){var a=this[r];if(a.isNode())for(var i=a.connectedEdges(),o=0;o<i.length;o++){var s=i[o],l=s.source(),u=s.target();e.outgoing&&l===a?(n.push(s),n.push(u)):e.incoming&&u===a&&(n.push(s),n.push(l))}}return this.spawn(n,!0).filter(t)}},Pl=function(e){return function(t){for(var n=this,r=[],a={};;){var i=e.outgoing?n.outgoers():n.incomers();if(0===i.length)break;for(var o=!1,s=0;s<i.length;s++){var l=i[s],u=l.id();a[u]||(a[u]=!0,r.push(l),o=!0)}if(!o)break;n=i}return this.spawn(r,!0).filter(t)}};function Sl(e){return function(t){for(var n=[],r=0;r<this.length;r++){var a=this[r]._private[e.attr];a&&n.push(a)}return this.spawn(n,!0).filter(t)}}function Bl(e){return function(t){var n=[],r=this._private.cy,a=e||{};K(t)&&(t=r.$(t));for(var i=0;i<t.length;i++)for(var o=t[i]._private.edges,s=0;s<o.length;s++){var l=o[s],u=l._private.data,c=this.hasElementWithId(u.source)&&t.hasElementWithId(u.target),d=t.hasElementWithId(u.source)&&this.hasElementWithId(u.target);if(c||d){if(a.thisIsSrc||a.thisIsTgt){if(a.thisIsSrc&&!c)continue;if(a.thisIsTgt&&!d)continue}n.push(l)}}return this.spawn(n,!0)}}function Dl(e){return e=me({},{codirected:!1},e),function(t){for(var n=[],r=this.edges(),a=e,i=0;i<r.length;i++)for(var o=r[i]._private,s=o.source,l=s._private.data.id,u=o.data.target,c=s._private.edges,d=0;d<c.length;d++){var h=c[d],f=h._private.data,p=f.target,g=f.source,v=p===u&&g===l,y=l===p&&u===g;(a.codirected&&v||!a.codirected&&(v||y))&&n.push(h)}return this.spawn(n,!0).filter(t)}}kl.clearTraversalCache=function(){for(var e=0;e<this.length;e++)this[e]._private.traversalCache=null},me(kl,{roots:Tl({noIncomingEdges:!0}),leaves:Tl({noOutgoingEdges:!0}),outgoers:rs(Cl({outgoing:!0}),"outgoers"),successors:Pl({outgoing:!0}),incomers:rs(Cl({incoming:!0}),"incomers"),predecessors:Pl({})}),me(kl,{neighborhood:rs((function(e){for(var t=[],n=this.nodes(),r=0;r<n.length;r++)for(var a=n[r],i=a.connectedEdges(),o=0;o<i.length;o++){var s=i[o],l=s.source(),u=s.target(),c=a===l?u:l;c.length>0&&t.push(c[0]),t.push(s[0])}return this.spawn(t,!0).filter(e)}),"neighborhood"),closedNeighborhood:function(e){return this.neighborhood().add(this).filter(e)},openNeighborhood:function(e){return this.neighborhood(e)}}),kl.neighbourhood=kl.neighborhood,kl.closedNeighbourhood=kl.closedNeighborhood,kl.openNeighbourhood=kl.openNeighborhood,me(kl,{source:rs((function(e){var t,n=this[0];return n&&(t=n._private.source||n.cy().collection()),t&&e?t.filter(e):t}),"source"),target:rs((function(e){var t,n=this[0];return n&&(t=n._private.target||n.cy().collection()),t&&e?t.filter(e):t}),"target"),sources:Sl({attr:"source"}),targets:Sl({attr:"target"})}),me(kl,{edgesWith:rs(Bl(),"edgesWith"),edgesTo:rs(Bl({thisIsSrc:!0}),"edgesTo")}),me(kl,{connectedEdges:rs((function(e){for(var t=[],n=0;n<this.length;n++){var r=this[n];if(r.isNode())for(var a=r._private.edges,i=0;i<a.length;i++){var o=a[i];t.push(o)}}return this.spawn(t,!0).filter(e)}),"connectedEdges"),connectedNodes:rs((function(e){for(var t=[],n=0;n<this.length;n++){var r=this[n];r.isEdge()&&(t.push(r.source()[0]),t.push(r.target()[0]))}return this.spawn(t,!0).filter(e)}),"connectedNodes"),parallelEdges:rs(Dl(),"parallelEdges"),codirectedEdges:rs(Dl({codirected:!0}),"codirectedEdges")}),me(kl,{components:function(e){var t=this,n=t.cy(),r=n.collection(),a=null==e?t.nodes():e.nodes(),i=[];null!=e&&a.empty()&&(a=e.sources());var o=function(e,t){r.merge(e),a.unmerge(e),t.merge(e)};if(a.empty())return t.spawn();var s=function(){var e=n.collection();i.push(e);var r=a[0];o(r,e),t.bfs({directed:!1,roots:r,visit:function(t){return o(t,e)}}),e.forEach((function(n){n.connectedEdges().forEach((function(n){t.has(n)&&e.has(n.source())&&e.has(n.target())&&e.merge(n)}))}))};do{s()}while(a.length>0);return i},component:function(){var e=this[0];return e.cy().mutableElements().components(e)[0]}}),kl.componentsOf=kl.components;var _l=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(void 0!==e){var a=new pt,i=!1;if(t){if(t.length>0&&$(t[0])&&!te(t[0])){i=!0;for(var o=[],s=new vt,l=0,u=t.length;l<u;l++){var c=t[l];null==c.data&&(c.data={});var d=c.data;if(null==d.id)d.id=ot();else if(e.hasElementWithId(d.id)||s.has(d.id))continue;var h=new yt(e,c,!1);o.push(h),s.add(d.id)}t=o}}else t=[];this.length=0;for(var f=0,p=t.length;f<p;f++){var g=t[f][0];if(null!=g){var v=g._private.data.id;n&&a.has(v)||(n&&a.set(v,{index:this.length,ele:g}),this[this.length]=g,this.length++)}}this._private={eles:this,cy:e,get map(){return null==this.lazyMap&&this.rebuildMap(),this.lazyMap},set map(e){this.lazyMap=e},rebuildMap:function(){for(var e=this.lazyMap=new pt,t=this.eles,n=0;n<t.length;n++){var r=t[n];e.set(r.id(),{index:n,ele:r})}}},n&&(this._private.map=a),i&&!r&&this.restore()}else nt("A collection must have a reference to the core")},Al=yt.prototype=_l.prototype=Object.create(Array.prototype);Al.instanceString=function(){return"collection"},Al.spawn=function(e,t){return new _l(this.cy(),e,t)},Al.spawnSelf=function(){return this.spawn(this)},Al.cy=function(){return this._private.cy},Al.renderer=function(){return this._private.cy.renderer()},Al.element=function(){return this[0]},Al.collection=function(){return ne(this)?this:new _l(this._private.cy,[this])},Al.unique=function(){return new _l(this._private.cy,this,!0)},Al.hasElementWithId=function(e){return e=""+e,this._private.map.has(e)},Al.getElementById=function(e){e=""+e;var t=this._private.cy,n=this._private.map.get(e);return n?n.ele:new _l(t)},Al.$id=Al.getElementById,Al.poolIndex=function(){var e=this._private.cy._private.elements,t=this[0]._private.data.id;return e._private.map.get(t).index},Al.indexOf=function(e){var t=e[0]._private.data.id;return this._private.map.get(t).index},Al.indexOfId=function(e){return e=""+e,this._private.map.get(e).index},Al.json=function(e){var t=this.element(),n=this.cy();if(null==t&&e)return this;if(null!=t){var r=t._private;if($(e)){if(n.startBatch(),e.data){t.data(e.data);var a=r.data;if(t.isEdge()){var i=!1,o={},s=e.data.source,l=e.data.target;null!=s&&s!=a.source&&(o.source=""+s,i=!0),null!=l&&l!=a.target&&(o.target=""+l,i=!0),i&&(t=t.move(o))}else{var u="parent"in e.data,c=e.data.parent;!u||null==c&&null==a.parent||c==a.parent||(void 0===c&&(c=null),null!=c&&(c=""+c),t=t.move({parent:c}))}}e.position&&t.position(e.position);var d=function(n,a,i){var o=e[n];null!=o&&o!==r[n]&&(o?t[a]():t[i]())};return d("removed","remove","restore"),d("selected","select","unselect"),d("selectable","selectify","unselectify"),d("locked","lock","unlock"),d("grabbable","grabify","ungrabify"),d("pannable","panify","unpanify"),null!=e.classes&&t.classes(e.classes),n.endBatch(),this}if(void 0===e){var h={data:it(r.data),position:it(r.position),group:r.group,removed:r.removed,selected:r.selected,selectable:r.selectable,locked:r.locked,grabbable:r.grabbable,pannable:r.pannable,classes:null};h.classes="";var f=0;return r.classes.forEach((function(e){return h.classes+=0===f++?e:" "+e})),h}}},Al.jsons=function(){for(var e=[],t=0;t<this.length;t++){var n=this[t].json();e.push(n)}return e},Al.clone=function(){for(var e=this.cy(),t=[],n=0;n<this.length;n++){var r=this[n].json(),a=new yt(e,r,!1);t.push(a)}return new _l(e,t)},Al.copy=Al.clone,Al.restore=function(){for(var e,t,n=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],a=this,i=a.cy(),o=i._private,s=[],l=[],u=0,c=a.length;u<c;u++){var d=a[u];r&&!d.removed()||(d.isNode()?s.push(d):l.push(d))}e=s.concat(l);var h=function(){e.splice(t,1),t--};for(t=0;t<e.length;t++){var f=e[t],p=f._private,g=p.data;if(f.clearTraversalCache(),r||p.removed)if(void 0===g.id)g.id=ot();else if(Q(g.id))g.id=""+g.id;else{if(ie(g.id)||!K(g.id)){nt("Can not create element with invalid string ID `"+g.id+"`"),h();continue}if(i.hasElementWithId(g.id)){nt("Can not create second element with ID `"+g.id+"`"),h();continue}}else;var v=g.id;if(f.isNode()){var y=p.position;null==y.x&&(y.x=0),null==y.y&&(y.y=0)}if(f.isEdge()){for(var m=f,b=["source","target"],x=b.length,w=!1,E=0;E<x;E++){var k=b[E],T=g[k];Q(T)&&(T=g[k]=""+g[k]),null==T||""===T?(nt("Can not create edge `"+v+"` with unspecified "+k),w=!0):i.hasElementWithId(T)||(nt("Can not create edge `"+v+"` with nonexistant "+k+" `"+T+"`"),w=!0)}if(w){h();continue}var C=i.getElementById(g.source),P=i.getElementById(g.target);C.same(P)?C._private.edges.push(m):(C._private.edges.push(m),P._private.edges.push(m)),m._private.source=C,m._private.target=P}p.map=new pt,p.map.set(v,{ele:f,index:0}),p.removed=!1,r&&i.addToPool(f)}for(var S=0;S<s.length;S++){var B=s[S],D=B._private.data;Q(D.parent)&&(D.parent=""+D.parent);var _=D.parent;if(null!=_||B._private.parent){var A=B._private.parent?i.collection().merge(B._private.parent):i.getElementById(_);if(A.empty())D.parent=void 0;else if(A[0].removed())at("Node added with missing parent, reference to parent removed"),D.parent=void 0,B._private.parent=null;else{for(var M=!1,R=A;!R.empty();){if(B.same(R)){M=!0,D.parent=void 0;break}R=R.parent()}M||(A[0]._private.children.push(B),B._private.parent=A[0],o.hasCompoundNodes=!0)}}}if(e.length>0){for(var I=e.length===a.length?a:new _l(i,e),N=0;N<I.length;N++){var L=I[N];L.isNode()||(L.parallelEdges().clearTraversalCache(),L.source().clearTraversalCache(),L.target().clearTraversalCache())}(o.hasCompoundNodes?i.collection().merge(I).merge(I.connectedNodes()).merge(I.parent()):I).dirtyCompoundBoundsCache().dirtyBoundingBoxCache().updateStyle(n),n?I.emitAndNotify("add"):r&&I.emit("add")}return a},Al.removed=function(){var e=this[0];return e&&e._private.removed},Al.inside=function(){var e=this[0];return e&&!e._private.removed},Al.remove=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=this,r=[],a={},i=n._private.cy;function o(e){var n=a[e.id()];t&&e.removed()||n||(a[e.id()]=!0,e.isNode()?(r.push(e),function(e){for(var t=e._private.edges,n=0;n<t.length;n++)o(t[n])}(e),function(e){for(var t=e._private.children,n=0;n<t.length;n++)o(t[n])}(e)):r.unshift(e))}for(var s=0,l=n.length;s<l;s++){o(n[s])}function u(e,t){var n=e._private.edges;ct(n,t),e.clearTraversalCache()}function c(e){e.clearTraversalCache()}var d=[];function h(e,t){t=t[0];var n=(e=e[0])._private.children,r=e.id();ct(n,t),t._private.parent=null,d.ids[r]||(d.ids[r]=!0,d.push(e))}d.ids={},n.dirtyCompoundBoundsCache(),t&&i.removeFromPool(r);for(var f=0;f<r.length;f++){var p=r[f];if(p.isEdge()){var g=p.source()[0],v=p.target()[0];u(g,p),u(v,p);for(var y=p.parallelEdges(),m=0;m<y.length;m++){var b=y[m];c(b),b.isBundledBezier()&&b.dirtyBoundingBoxCache()}}else{var x=p.parent();0!==x.length&&h(x,p)}t&&(p._private.removed=!0)}var w=i._private.elements;i._private.hasCompoundNodes=!1;for(var E=0;E<w.length;E++){if(w[E].isParent()){i._private.hasCompoundNodes=!0;break}}var k=new _l(this.cy(),r);k.size()>0&&(e?k.emitAndNotify("remove"):t&&k.emit("remove"));for(var T=0;T<d.length;T++){var C=d[T];t&&C.removed()||C.updateStyle()}return k},Al.move=function(e){var t=this._private.cy,n=this,r=!1,a=!1,i=function(e){return null==e?e:""+e};if(void 0!==e.source||void 0!==e.target){var o=i(e.source),s=i(e.target),l=null!=o&&t.hasElementWithId(o),u=null!=s&&t.hasElementWithId(s);(l||u)&&(t.batch((function(){n.remove(r,a),n.emitAndNotify("moveout");for(var e=0;e<n.length;e++){var t=n[e],i=t._private.data;t.isEdge()&&(l&&(i.source=o),u&&(i.target=s))}n.restore(r,a)})),n.emitAndNotify("move"))}else if(void 0!==e.parent){var c=i(e.parent);if(null===c||t.hasElementWithId(c)){var d=null===c?void 0:c;t.batch((function(){var e=n.remove(r,a);e.emitAndNotify("moveout");for(var t=0;t<n.length;t++){var i=n[t],o=i._private.data;i.isNode()&&(o.parent=d)}e.restore(r,a)})),n.emitAndNotify("move")}}return this},[Ir,go,vo,es,as,ds,hs,Fs,tl,nl,{isNode:function(){return"nodes"===this.group()},isEdge:function(){return"edges"===this.group()},isLoop:function(){return this.isEdge()&&this.source()[0]===this.target()[0]},isSimple:function(){return this.isEdge()&&this.source()[0]!==this.target()[0]},group:function(){var e=this[0];if(e)return e._private.group}},il,ll,hl,xl,kl].forEach((function(e){me(Al,e)}));var Ml={add:function(e){var t,n=this;if(ee(e)){var r=e;if(r._private.cy===n)t=r.restore();else{for(var a=[],i=0;i<r.length;i++){var o=r[i];a.push(o.json())}t=new _l(n,a)}}else if(Z(e)){t=new _l(n,e)}else if($(e)&&(Z(e.nodes)||Z(e.edges))){for(var s=e,l=[],u=["nodes","edges"],c=0,d=u.length;c<d;c++){var h=u[c],f=s[h];if(Z(f))for(var p=0,g=f.length;p<g;p++){var v=me({group:h},f[p]);l.push(v)}}t=new _l(n,l)}else{t=new yt(n,e).collection()}return t},remove:function(e){if(ee(e));else if(K(e)){var t=e;e=this.$(t)}return e.remove()}};function Rl(e,t,n,r){var a=.1,i="undefined"!=typeof Float32Array;if(4!==arguments.length)return!1;for(var o=0;o<4;++o)if("number"!=typeof arguments[o]||isNaN(arguments[o])||!isFinite(arguments[o]))return!1;e=Math.min(e,1),n=Math.min(n,1),e=Math.max(e,0),n=Math.max(n,0);var s=i?new Float32Array(11):new Array(11);function l(e,t){return 1-3*t+3*e}function u(e,t){return 3*t-6*e}function c(e){return 3*e}function d(e,t,n){return((l(t,n)*e+u(t,n))*e+c(t))*e}function h(e,t,n){return 3*l(t,n)*e*e+2*u(t,n)*e+c(t)}function f(t){for(var r=0,i=1;10!==i&&s[i]<=t;++i)r+=a;--i;var o=r+(t-s[i])/(s[i+1]-s[i])*a,l=h(o,e,n);return l>=.001?function(t,r){for(var a=0;a<4;++a){var i=h(r,e,n);if(0===i)return r;r-=(d(r,e,n)-t)/i}return r}(t,o):0===l?o:function(t,r,a){var i,o,s=0;do{(i=d(o=r+(a-r)/2,e,n)-t)>0?a=o:r=o}while(Math.abs(i)>1e-7&&++s<10);return o}(t,r,r+a)}var p=!1;function g(){p=!0,e===t&&n===r||function(){for(var t=0;t<11;++t)s[t]=d(t*a,e,n)}()}var v=function(a){return p||g(),e===t&&n===r?a:0===a?0:1===a?1:d(f(a),t,r)};v.getControlPoints=function(){return[{x:e,y:t},{x:n,y:r}]};var y="generateBezier("+[e,t,n,r]+")";return v.toString=function(){return y},v}var Il=function(){function e(e){return-e.tension*e.x-e.friction*e.v}function t(t,n,r){var a={x:t.x+r.dx*n,v:t.v+r.dv*n,tension:t.tension,friction:t.friction};return{dx:a.v,dv:e(a)}}function n(n,r){var a={dx:n.v,dv:e(n)},i=t(n,.5*r,a),o=t(n,.5*r,i),s=t(n,r,o),l=1/6*(a.dx+2*(i.dx+o.dx)+s.dx),u=1/6*(a.dv+2*(i.dv+o.dv)+s.dv);return n.x=n.x+l*r,n.v=n.v+u*r,n}return function e(t,r,a){var i,o,s,l={x:-1,v:0,tension:null,friction:null},u=[0],c=0,d=1e-4;for(t=parseFloat(t)||500,r=parseFloat(r)||20,a=a||null,l.tension=t,l.friction=r,o=(i=null!==a)?(c=e(t,r))/a*.016:.016;s=n(s||l,o),u.push(1+s.x),c+=16,Math.abs(s.x)>d&&Math.abs(s.v)>d;);return i?function(e){return u[e*(u.length-1)|0]}:c}}(),Nl=function(e,t,n,r){var a=Rl(e,t,n,r);return function(e,t,n){return e+(t-e)*a(n)}},Ll={linear:function(e,t,n){return e+(t-e)*n},ease:Nl(.25,.1,.25,1),"ease-in":Nl(.42,0,1,1),"ease-out":Nl(0,0,.58,1),"ease-in-out":Nl(.42,0,.58,1),"ease-in-sine":Nl(.47,0,.745,.715),"ease-out-sine":Nl(.39,.575,.565,1),"ease-in-out-sine":Nl(.445,.05,.55,.95),"ease-in-quad":Nl(.55,.085,.68,.53),"ease-out-quad":Nl(.25,.46,.45,.94),"ease-in-out-quad":Nl(.455,.03,.515,.955),"ease-in-cubic":Nl(.55,.055,.675,.19),"ease-out-cubic":Nl(.215,.61,.355,1),"ease-in-out-cubic":Nl(.645,.045,.355,1),"ease-in-quart":Nl(.895,.03,.685,.22),"ease-out-quart":Nl(.165,.84,.44,1),"ease-in-out-quart":Nl(.77,0,.175,1),"ease-in-quint":Nl(.755,.05,.855,.06),"ease-out-quint":Nl(.23,1,.32,1),"ease-in-out-quint":Nl(.86,0,.07,1),"ease-in-expo":Nl(.95,.05,.795,.035),"ease-out-expo":Nl(.19,1,.22,1),"ease-in-out-expo":Nl(1,0,0,1),"ease-in-circ":Nl(.6,.04,.98,.335),"ease-out-circ":Nl(.075,.82,.165,1),"ease-in-out-circ":Nl(.785,.135,.15,.86),spring:function(e,t,n){if(0===n)return Ll.linear;var r=Il(e,t,n);return function(e,t,n){return e+(t-e)*r(n)}},"cubic-bezier":Nl};function zl(e,t,n,r,a){if(1===r)return n;if(t===n)return n;var i=a(t,n,r);return null==e||((e.roundValue||e.color)&&(i=Math.round(i)),void 0!==e.min&&(i=Math.max(i,e.min)),void 0!==e.max&&(i=Math.min(i,e.max))),i}function Ol(e,t){return null!=e.pfValue||null!=e.value?null==e.pfValue||null!=t&&"%"===t.type.units?e.value:e.pfValue:e}function Vl(e,t,n,r,a){var i=null!=a?a.type:null;n<0?n=0:n>1&&(n=1);var o=Ol(e,a),s=Ol(t,a);if(Q(o)&&Q(s))return zl(i,o,s,n,r);if(Z(o)&&Z(s)){for(var l=[],u=0;u<s.length;u++){var c=o[u],d=s[u];if(null!=c&&null!=d){var h=zl(i,c,d,n,r);l.push(h)}else l.push(d)}return l}}function Fl(e,t,n,r){var a=!r,i=e._private,o=t._private,s=o.easing,l=o.startTime,u=(r?e:e.cy()).style();if(!o.easingImpl)if(null==s)o.easingImpl=Ll.linear;else{var c,d,h;if(K(s))c=u.parse("transition-timing-function",s).value;else c=s;K(c)?(d=c,h=[]):(d=c[1],h=c.slice(2).map((function(e){return+e}))),h.length>0?("spring"===d&&h.push(o.duration),o.easingImpl=Ll[d].apply(null,h)):o.easingImpl=Ll[d]}var f,p=o.easingImpl;if(f=0===o.duration?1:(n-l)/o.duration,o.applying&&(f=o.progress),f<0?f=0:f>1&&(f=1),null==o.delay){var g=o.startPosition,v=o.position;if(v&&a&&!e.locked()){var y={};jl(g.x,v.x)&&(y.x=Vl(g.x,v.x,f,p)),jl(g.y,v.y)&&(y.y=Vl(g.y,v.y,f,p)),e.position(y)}var m=o.startPan,b=o.pan,x=i.pan,w=null!=b&&r;w&&(jl(m.x,b.x)&&(x.x=Vl(m.x,b.x,f,p)),jl(m.y,b.y)&&(x.y=Vl(m.y,b.y,f,p)),e.emit("pan"));var E=o.startZoom,k=o.zoom,T=null!=k&&r;T&&(jl(E,k)&&(i.zoom=$t(i.minZoom,Vl(E,k,f,p),i.maxZoom)),e.emit("zoom")),(w||T)&&e.emit("viewport");var C=o.style;if(C&&C.length>0&&a){for(var P=0;P<C.length;P++){var S=C[P],B=S.name,D=S,_=o.startStyle[B],A=Vl(_,D,f,p,u.properties[_.name]);u.overrideBypass(e,B,A)}e.emit("style")}}return o.progress=f,f}function jl(e,t){return null!=e&&null!=t&&(!(!Q(e)||!Q(t))||!(!e||!t))}function Xl(e,t,n,r){var a=t._private;a.started=!0,a.startTime=n-a.progress*a.duration}function ql(e,t){var n=t._private.aniEles,r=[];function a(t,n){var a=t._private,i=a.animation.current,o=a.animation.queue,s=!1;if(0===i.length){var l=o.shift();l&&i.push(l)}for(var u=function(e){for(var t=e.length-1;t>=0;t--){(0,e[t])()}e.splice(0,e.length)},c=i.length-1;c>=0;c--){var d=i[c],h=d._private;h.stopped?(i.splice(c,1),h.hooked=!1,h.playing=!1,h.started=!1,u(h.frames)):(h.playing||h.applying)&&(h.playing&&h.applying&&(h.applying=!1),h.started||Xl(0,d,e),Fl(t,d,e,n),h.applying&&(h.applying=!1),u(h.frames),null!=h.step&&h.step(e),d.completed()&&(i.splice(c,1),h.hooked=!1,h.playing=!1,h.started=!1,u(h.completes)),s=!0)}return n||0!==i.length||0!==o.length||r.push(t),s}for(var i=!1,o=0;o<n.length;o++){var s=a(n[o]);i=i||s}var l=a(t,!0);(i||l)&&(n.length>0?t.notify("draw",n):t.notify("draw")),n.unmerge(r),t.emit("step")}var Yl={animate:po.animate(),animation:po.animation(),animated:po.animated(),clearQueue:po.clearQueue(),delay:po.delay(),delayAnimation:po.delayAnimation(),stop:po.stop(),addToAnimationPool:function(e){this.styleEnabled()&&this._private.aniEles.merge(e)},stopAnimationLoop:function(){this._private.animationsRunning=!1},startAnimationLoop:function(){var e=this;if(e._private.animationsRunning=!0,e.styleEnabled()){var t=e.renderer();t&&t.beforeRender?t.beforeRender((function(t,n){ql(n,e)}),t.beforeRenderPriorities.animations):function t(){e._private.animationsRunning&&Ne((function(n){ql(n,e),t()}))}()}}},Wl={qualifierCompare:function(e,t){return null==e||null==t?null==e&&null==t:e.sameText(t)},eventMatches:function(e,t,n){var r=t.qualifier;return null==r||e!==n.target&&te(n.target)&&r.matches(n.target)},addEventFields:function(e,t){t.cy=e,t.target=e},callbackContext:function(e,t,n){return null!=t.qualifier?n.target:e}},Ul=function(e){return K(e)?new Qo(e):e},Hl={createEmitter:function(){var e=this._private;return e.emitter||(e.emitter=new Ks(Wl,this)),this},emitter:function(){return this._private.emitter},on:function(e,t,n){return this.emitter().on(e,Ul(t),n),this},removeListener:function(e,t,n){return this.emitter().removeListener(e,Ul(t),n),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},one:function(e,t,n){return this.emitter().one(e,Ul(t),n),this},once:function(e,t,n){return this.emitter().one(e,Ul(t),n),this},emit:function(e,t){return this.emitter().emit(e,t),this},emitAndNotify:function(e,t){return this.emit(e),this.notify(e,t),this}};po.eventAliasesOn(Hl);var Kl={png:function(e){return e=e||{},this._private.renderer.png(e)},jpg:function(e){var t=this._private.renderer;return(e=e||{}).bg=e.bg||"#fff",t.jpg(e)}};Kl.jpeg=Kl.jpg;var Gl={layout:function(e){var t=this;if(null!=e)if(null!=e.name){var n=e.name,r=t.extension("layout",n);if(null!=r){var a;a=K(e.eles)?t.$(e.eles):null!=e.eles?e.eles:t.$();var i=new r(me({},e,{cy:t,eles:a}));return i}nt("No such layout `"+n+"` found.  Did you forget to import it and `cytoscape.use()` it?")}else nt("A `name` must be specified to make a layout");else nt("Layout options must be specified to make a layout")}};Gl.createLayout=Gl.makeLayout=Gl.layout;var Zl={notify:function(e,t){var n=this._private;if(this.batching()){n.batchNotifications=n.batchNotifications||{};var r=n.batchNotifications[e]=n.batchNotifications[e]||this.collection();null!=t&&r.merge(t)}else if(n.notificationsEnabled){var a=this.renderer();!this.destroyed()&&a&&a.notify(e,t)}},notifications:function(e){var t=this._private;return void 0===e?t.notificationsEnabled:(t.notificationsEnabled=!!e,this)},noNotifications:function(e){this.notifications(!1),e(),this.notifications(!0)},batching:function(){return this._private.batchCount>0},startBatch:function(){var e=this._private;return null==e.batchCount&&(e.batchCount=0),0===e.batchCount&&(e.batchStyleEles=this.collection(),e.batchNotifications={}),e.batchCount++,this},endBatch:function(){var e=this._private;if(0===e.batchCount)return this;if(e.batchCount--,0===e.batchCount){e.batchStyleEles.updateStyle();var t=this.renderer();Object.keys(e.batchNotifications).forEach((function(n){var r=e.batchNotifications[n];r.empty()?t.notify(n):t.notify(n,r)}))}return this},batch:function(e){return this.startBatch(),e(),this.endBatch(),this},batchData:function(e){var t=this;return this.batch((function(){for(var n=Object.keys(e),r=0;r<n.length;r++){var a=n[r],i=e[a];t.getElementById(a).data(i)}}))}},$l=ut({hideEdgesOnViewport:!1,textureOnViewport:!1,motionBlur:!1,motionBlurOpacity:.05,pixelRatio:void 0,desktopTapThreshold:4,touchTapThreshold:8,wheelSensitivity:1,debug:!1,showFps:!1,webgl:!1,webglDebug:!1,webglDebugShowAtlases:!1,webglTexSize:2048,webglTexRows:36,webglTexRowsNodes:18,webglBatchSize:2048,webglTexPerBatch:14,webglBgColor:[255,255,255]}),Ql={renderTo:function(e,t,n,r){return this._private.renderer.renderTo(e,t,n,r),this},renderer:function(){return this._private.renderer},forceRender:function(){return this.notify("draw"),this},resize:function(){return this.invalidateSize(),this.emitAndNotify("resize"),this},initRenderer:function(e){var t=this,n=t.extension("renderer",e.name);if(null!=n){void 0!==e.wheelSensitivity&&at("You have set a custom wheel sensitivity.  This will make your app zoom unnaturally when using mainstream mice.  You should change this value from the default only if you can guarantee that all your users will use the same hardware and OS configuration as your current machine.");var r=$l(e);r.cy=t,t._private.renderer=new n(r),this.notify("init")}else nt("Can not initialise: No such renderer `".concat(e.name,"` found. Did you forget to import it and `cytoscape.use()` it?"))},destroyRenderer:function(){var e=this;e.notify("destroy");var t=e.container();if(t)for(t._cyreg=null;t.childNodes.length>0;)t.removeChild(t.childNodes[0]);e._private.renderer=null,e.mutableElements().forEach((function(e){var t=e._private;t.rscratch={},t.rstyle={},t.animation.current=[],t.animation.queue=[]}))},onRender:function(e){return this.on("render",e)},offRender:function(e){return this.off("render",e)}};Ql.invalidateDimensions=Ql.resize;var Jl={collection:function(e,t){return K(e)?this.$(e):ee(e)?e.collection():Z(e)?(t||(t={}),new _l(this,e,t.unique,t.removed)):new _l(this)},nodes:function(e){var t=this.$((function(e){return e.isNode()}));return e?t.filter(e):t},edges:function(e){var t=this.$((function(e){return e.isEdge()}));return e?t.filter(e):t},$:function(e){var t=this._private.elements;return e?t.filter(e):t.spawnSelf()},mutableElements:function(){return this._private.elements}};Jl.elements=Jl.filter=Jl.$;var eu={},tu="t";eu.apply=function(e){for(var t=this,n=t._private.cy.collection(),r=0;r<e.length;r++){var a=e[r],i=t.getContextMeta(a);if(!i.empty){var o=t.getContextStyle(i),s=t.applyContextStyle(i,o,a);a._private.appliedInitStyle?t.updateTransitions(a,s.diffProps):a._private.appliedInitStyle=!0,t.updateStyleHints(a)&&n.push(a)}}return n},eu.getPropertiesDiff=function(e,t){var n=this,r=n._private.propDiffs=n._private.propDiffs||{},a=e+"-"+t,i=r[a];if(i)return i;for(var o=[],s={},l=0;l<n.length;l++){var u=n[l],c=e[l]===tu,d=t[l]===tu,h=c!==d,f=u.mappedProperties.length>0;if(h||d&&f){var p=void 0;h&&f||h?p=u.properties:f&&(p=u.mappedProperties);for(var g=0;g<p.length;g++){for(var v=p[g],y=v.name,m=!1,b=l+1;b<n.length;b++){var x=n[b];if(t[b]===tu&&(m=null!=x.properties[v.name]))break}s[y]||m||(s[y]=!0,o.push(y))}}}return r[a]=o,o},eu.getContextMeta=function(e){for(var t,n=this,r="",a=e._private.styleCxtKey||"",i=0;i<n.length;i++){var o=n[i];r+=o.selector&&o.selector.matches(e)?tu:"f"}return t=n.getPropertiesDiff(a,r),e._private.styleCxtKey=r,{key:r,diffPropNames:t,empty:0===t.length}},eu.getContextStyle=function(e){var t=e.key,n=this._private.contextStyles=this._private.contextStyles||{};if(n[t])return n[t];for(var r={_private:{key:t}},a=0;a<this.length;a++){var i=this[a];if(t[a]===tu)for(var o=0;o<i.properties.length;o++){var s=i.properties[o];r[s.name]=s}}return n[t]=r,r},eu.applyContextStyle=function(e,t,n){for(var r=e.diffPropNames,a={},i=this.types,o=0;o<r.length;o++){var s=r[o],l=t[s],u=n.pstyle(s);if(!l){if(!u)continue;l=u.bypass?{name:s,deleteBypassed:!0}:{name:s,delete:!0}}if(u!==l){if(l.mapped===i.fn&&null!=u&&null!=u.mapping&&u.mapping.value===l.value){var c=u.mapping;if((c.fnValue=l.value(n))===c.prevFnValue)continue}var d=a[s]={prev:u};this.applyParsedProperty(n,l),d.next=n.pstyle(s),d.next&&d.next.bypass&&(d.next=d.next.bypassed)}}return{diffProps:a}},eu.updateStyleHints=function(e){var t=e._private,n=this,r=n.propertyGroupNames,a=n.propertyGroupKeys,i=function(e,t,r){return n.getPropertiesHash(e,t,r)},o=t.styleKey;if(e.removed())return!1;var s="nodes"===t.group,l=e._private.style;r=Object.keys(l);for(var u=0;u<a.length;u++){var c=a[u];t.styleKeys[c]=[ze,Oe]}for(var d,h=function(e,n){return t.styleKeys[n][0]=Fe(e,t.styleKeys[n][0])},f=function(e,n){return t.styleKeys[n][1]=je(e,t.styleKeys[n][1])},p=function(e,t){h(e,t),f(e,t)},g=function(e,t){for(var n=0;n<e.length;n++){var r=e.charCodeAt(n);h(r,t),f(r,t)}},v=0;v<r.length;v++){var y=r[v],m=l[y];if(null!=m){var b=this.properties[y],x=b.type,w=b.groupKey,E=void 0;null!=b.hashOverride?E=b.hashOverride(e,m):null!=m.pfValue&&(E=m.pfValue);var k=null==b.enums?m.value:null,T=null!=E,C=T||null!=k,P=m.units;if(x.number&&C&&!x.multiple)p(-128<(d=T?E:k)&&d<128&&Math.floor(d)!==d?2e9-(1024*d|0):d,w),T||null==P||g(P,w);else g(m.strValue,w)}}for(var S,B,D=[ze,Oe],_=0;_<a.length;_++){var A=a[_],M=t.styleKeys[A];D[0]=Fe(M[0],D[0]),D[1]=je(M[1],D[1])}t.styleKey=(S=D[0],B=D[1],2097152*S+B);var R=t.styleKeys;t.labelDimsKey=Xe(R.labelDimensions);var I=i(e,["label"],R.labelDimensions);if(t.labelKey=Xe(I),t.labelStyleKey=Xe(qe(R.commonLabel,I)),!s){var N=i(e,["source-label"],R.labelDimensions);t.sourceLabelKey=Xe(N),t.sourceLabelStyleKey=Xe(qe(R.commonLabel,N));var L=i(e,["target-label"],R.labelDimensions);t.targetLabelKey=Xe(L),t.targetLabelStyleKey=Xe(qe(R.commonLabel,L))}if(s){var z=t.styleKeys,O=z.nodeBody,V=z.nodeBorder,F=z.nodeOutline,j=z.backgroundImage,X=z.compound,q=z.pie,Y=z.stripe,W=[O,V,F,j,X,q,Y].filter((function(e){return null!=e})).reduce(qe,[ze,Oe]);t.nodeKey=Xe(W),t.hasPie=null!=q&&q[0]!==ze&&q[1]!==Oe,t.hasStripe=null!=Y&&Y[0]!==ze&&Y[1]!==Oe}return o!==t.styleKey},eu.clearStyleHints=function(e){var t=e._private;t.styleCxtKey="",t.styleKeys={},t.styleKey=null,t.labelKey=null,t.labelStyleKey=null,t.sourceLabelKey=null,t.sourceLabelStyleKey=null,t.targetLabelKey=null,t.targetLabelStyleKey=null,t.nodeKey=null,t.hasPie=null,t.hasStripe=null},eu.applyParsedProperty=function(e,t){var n,r=this,a=t,i=e._private.style,o=r.types,s=r.properties[a.name].type,l=a.bypass,u=i[a.name],c=u&&u.bypass,d=e._private,h="mapping",f=function(e){return null==e?null:null!=e.pfValue?e.pfValue:e.value},p=function(){var t=f(u),n=f(a);r.checkTriggers(e,a.name,t,n)};if("curve-style"===t.name&&e.isEdge()&&("bezier"!==t.value&&e.isLoop()||"haystack"===t.value&&(e.source().isParent()||e.target().isParent()))&&(a=t=this.parse(t.name,"bezier",l)),a.delete)return i[a.name]=void 0,p(),!0;if(a.deleteBypassed)return u?!!u.bypass&&(u.bypassed=void 0,p(),!0):(p(),!0);if(a.deleteBypass)return u?!!u.bypass&&(i[a.name]=u.bypassed,p(),!0):(p(),!0);var g=function(){at("Do not assign mappings to elements without corresponding data (i.e. ele `"+e.id()+"` has no mapping for property `"+a.name+"` with data field `"+a.field+"`); try a `["+a.field+"]` selector to limit scope to elements with `"+a.field+"` defined")};switch(a.mapped){case o.mapData:for(var v,y=a.field.split("."),m=d.data,b=0;b<y.length&&m;b++){m=m[y[b]]}if(null==m)return g(),!1;if(!Q(m))return at("Do not use continuous mappers without specifying numeric data (i.e. `"+a.field+": "+m+"` for `"+e.id()+"` is non-numeric)"),!1;var x=a.fieldMax-a.fieldMin;if((v=0===x?0:(m-a.fieldMin)/x)<0?v=0:v>1&&(v=1),s.color){var w=a.valueMin[0],E=a.valueMax[0],k=a.valueMin[1],T=a.valueMax[1],C=a.valueMin[2],P=a.valueMax[2],S=null==a.valueMin[3]?1:a.valueMin[3],B=null==a.valueMax[3]?1:a.valueMax[3],D=[Math.round(w+(E-w)*v),Math.round(k+(T-k)*v),Math.round(C+(P-C)*v),Math.round(S+(B-S)*v)];n={bypass:a.bypass,name:a.name,value:D,strValue:"rgb("+D[0]+", "+D[1]+", "+D[2]+")"}}else{if(!s.number)return!1;var _=a.valueMin+(a.valueMax-a.valueMin)*v;n=this.parse(a.name,_,a.bypass,h)}if(!n)return g(),!1;n.mapping=a,a=n;break;case o.data:for(var A=a.field.split("."),M=d.data,R=0;R<A.length&&M;R++){M=M[A[R]]}if(null!=M&&(n=this.parse(a.name,M,a.bypass,h)),!n)return g(),!1;n.mapping=a,a=n;break;case o.fn:var I=a.value,N=null!=a.fnValue?a.fnValue:I(e);if(a.prevFnValue=N,null==N)return at("Custom function mappers may not return null (i.e. `"+a.name+"` for ele `"+e.id()+"` is null)"),!1;if(!(n=this.parse(a.name,N,a.bypass,h)))return at("Custom function mappers may not return invalid values for the property type (i.e. `"+a.name+"` for ele `"+e.id()+"` is invalid)"),!1;n.mapping=it(a),a=n;break;case void 0:break;default:return!1}return l?(a.bypassed=c?u.bypassed:u,i[a.name]=a):c?u.bypassed=a:i[a.name]=a,p(),!0},eu.cleanElements=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(this.clearStyleHints(r),r.dirtyCompoundBoundsCache(),r.dirtyBoundingBoxCache(),t)for(var a=r._private.style,i=Object.keys(a),o=0;o<i.length;o++){var s=i[o],l=a[s];null!=l&&(l.bypass?l.bypassed=null:a[s]=null)}else r._private.style={}}},eu.update=function(){this._private.cy.mutableElements().updateStyle()},eu.updateTransitions=function(e,t){var n=this,r=e._private,a=e.pstyle("transition-property").value,i=e.pstyle("transition-duration").pfValue,o=e.pstyle("transition-delay").pfValue;if(a.length>0&&i>0){for(var s={},l=!1,u=0;u<a.length;u++){var c=a[u],d=e.pstyle(c),h=t[c];if(h){var f=h.prev,p=null!=h.next?h.next:d,g=!1,v=void 0,y=1e-6;f&&(Q(f.pfValue)&&Q(p.pfValue)?(g=p.pfValue-f.pfValue,v=f.pfValue+y*g):Q(f.value)&&Q(p.value)?(g=p.value-f.value,v=f.value+y*g):Z(f.value)&&Z(p.value)&&(g=f.value[0]!==p.value[0]||f.value[1]!==p.value[1]||f.value[2]!==p.value[2],v=f.strValue),g&&(s[c]=p.strValue,this.applyBypass(e,c,v),l=!0))}}if(!l)return;r.transitioning=!0,new jr((function(t){o>0?e.delayAnimation(o).play().promise().then(t):t()})).then((function(){return e.animation({style:s,duration:i,easing:e.pstyle("transition-timing-function").value,queue:!1}).play().promise()})).then((function(){n.removeBypasses(e,a),e.emitAndNotify("style"),r.transitioning=!1}))}else r.transitioning&&(this.removeBypasses(e,a),e.emitAndNotify("style"),r.transitioning=!1)},eu.checkTrigger=function(e,t,n,r,a,i){var o=this.properties[t],s=a(o);e.removed()||null!=s&&s(n,r,e)&&i(o)},eu.checkZOrderTrigger=function(e,t,n,r){var a=this;this.checkTrigger(e,t,n,r,(function(e){return e.triggersZOrder}),(function(){a._private.cy.notify("zorder",e)}))},eu.checkBoundsTrigger=function(e,t,n,r){this.checkTrigger(e,t,n,r,(function(e){return e.triggersBounds}),(function(t){e.dirtyCompoundBoundsCache(),e.dirtyBoundingBoxCache()}))},eu.checkConnectedEdgesBoundsTrigger=function(e,t,n,r){this.checkTrigger(e,t,n,r,(function(e){return e.triggersBoundsOfConnectedEdges}),(function(t){e.connectedEdges().forEach((function(e){e.dirtyBoundingBoxCache()}))}))},eu.checkParallelEdgesBoundsTrigger=function(e,t,n,r){this.checkTrigger(e,t,n,r,(function(e){return e.triggersBoundsOfParallelEdges}),(function(t){e.parallelEdges().forEach((function(e){e.dirtyBoundingBoxCache()}))}))},eu.checkTriggers=function(e,t,n,r){e.dirtyStyleCache(),this.checkZOrderTrigger(e,t,n,r),this.checkBoundsTrigger(e,t,n,r),this.checkConnectedEdgesBoundsTrigger(e,t,n,r),this.checkParallelEdgesBoundsTrigger(e,t,n,r)};var nu={applyBypass:function(e,t,n,r){var a=[];if("*"===t||"**"===t){if(void 0!==n)for(var i=0;i<this.properties.length;i++){var o=this.properties[i].name,s=this.parse(o,n,!0);s&&a.push(s)}}else if(K(t)){var l=this.parse(t,n,!0);l&&a.push(l)}else{if(!$(t))return!1;var u=t;r=n;for(var c=Object.keys(u),d=0;d<c.length;d++){var h=c[d],f=u[h];if(void 0===f&&(f=u[ue(h)]),void 0!==f){var p=this.parse(h,f,!0);p&&a.push(p)}}}if(0===a.length)return!1;for(var g=!1,v=0;v<e.length;v++){for(var y=e[v],m={},b=void 0,x=0;x<a.length;x++){var w=a[x];if(r){var E=y.pstyle(w.name);b=m[w.name]={prev:E}}g=this.applyParsedProperty(y,it(w))||g,r&&(b.next=y.pstyle(w.name))}g&&this.updateStyleHints(y),r&&this.updateTransitions(y,m,true)}return g},overrideBypass:function(e,t,n){t=le(t);for(var r=0;r<e.length;r++){var a=e[r],i=a._private.style[t],o=this.properties[t].type,s=o.color,l=o.mutiple,u=i?null!=i.pfValue?i.pfValue:i.value:null;i&&i.bypass?(i.value=n,null!=i.pfValue&&(i.pfValue=n),i.strValue=s?"rgb("+n.join(",")+")":l?n.join(" "):""+n,this.updateStyleHints(a)):this.applyBypass(a,t,n),this.checkTriggers(a,t,u,n)}},removeAllBypasses:function(e,t){return this.removeBypasses(e,this.propertyNames,t)},removeBypasses:function(e,t,n){for(var r=0;r<e.length;r++){for(var a=e[r],i={},o=0;o<t.length;o++){var s=t[o],l=this.properties[s],u=a.pstyle(l.name);if(u&&u.bypass){var c=this.parse(s,"",!0),d=i[l.name]={prev:u};this.applyParsedProperty(a,c),d.next=a.pstyle(l.name)}}this.updateStyleHints(a),n&&this.updateTransitions(a,i,true)}}},ru={getEmSizeInPixels:function(){var e=this.containerCss("font-size");return null!=e?parseFloat(e):1},containerCss:function(e){var t=this._private.cy,n=t.container(),r=t.window();if(r&&n&&r.getComputedStyle)return r.getComputedStyle(n).getPropertyValue(e)}},au={getRenderedStyle:function(e,t){return t?this.getStylePropertyValue(e,t,!0):this.getRawStyle(e,!0)},getRawStyle:function(e,t){var n=this;if(e=e[0]){for(var r={},a=0;a<n.properties.length;a++){var i=n.properties[a],o=n.getStylePropertyValue(e,i.name,t);null!=o&&(r[i.name]=o,r[ue(i.name)]=o)}return r}},getIndexedStyle:function(e,t,n,r){var a=e.pstyle(t)[n][r];return null!=a?a:e.cy().style().getDefaultProperty(t)[n][0]},getStylePropertyValue:function(e,t,n){if(e=e[0]){var r=this.properties[t];r.alias&&(r=r.pointsTo);var a=r.type,i=e.pstyle(r.name);if(i){var o=i.value,s=i.units,l=i.strValue;if(n&&a.number&&null!=o&&Q(o)){var u=e.cy().zoom(),c=function(e){return e*u},d=function(e,t){return c(e)+t},h=Z(o);return(h?s.every((function(e){return null!=e})):null!=s)?h?o.map((function(e,t){return d(e,s[t])})).join(" "):d(o,s):h?o.map((function(e){return K(e)?e:""+c(e)})).join(" "):""+c(o)}if(null!=l)return l}return null}},getAnimationStartStyle:function(e,t){for(var n={},r=0;r<t.length;r++){var a=t[r].name,i=e.pstyle(a);void 0!==i&&(i=$(i)?this.parse(a,i.strValue):this.parse(a,i)),i&&(n[a]=i)}return n},getPropsList:function(e){var t=[],n=e,r=this.properties;if(n)for(var a=Object.keys(n),i=0;i<a.length;i++){var o=a[i],s=n[o],l=r[o]||r[le(o)],u=this.parse(l.name,s);u&&t.push(u)}return t},getNonDefaultPropertiesHash:function(e,t,n){var r,a,i,o,s,l,u=n.slice();for(s=0;s<t.length;s++)if(r=t[s],null!=(a=e.pstyle(r,!1)))if(null!=a.pfValue)u[0]=Fe(o,u[0]),u[1]=je(o,u[1]);else for(i=a.strValue,l=0;l<i.length;l++)o=i.charCodeAt(l),u[0]=Fe(o,u[0]),u[1]=je(o,u[1]);return u}};au.getPropertiesHash=au.getNonDefaultPropertiesHash;var iu={appendFromJson:function(e){for(var t=this,n=0;n<e.length;n++){var r=e[n],a=r.selector,i=r.style||r.css,o=Object.keys(i);t.selector(a);for(var s=0;s<o.length;s++){var l=o[s],u=i[l];t.css(l,u)}}return t},fromJson:function(e){var t=this;return t.resetToDefault(),t.appendFromJson(e),t},json:function(){for(var e=[],t=this.defaultLength;t<this.length;t++){for(var n=this[t],r=n.selector,a=n.properties,i={},o=0;o<a.length;o++){var s=a[o];i[s.name]=s.strValue}e.push({selector:r?r.toString():"core",style:i})}return e}},ou={appendFromString:function(e){var t,n,r,a=this,i=""+e;function o(){i=i.length>t.length?i.substr(t.length):""}function s(){n=n.length>r.length?n.substr(r.length):""}for(i=i.replace(/[/][*](\s|.)+?[*][/]/g,"");;){if(i.match(/^\s*$/))break;var l=i.match(/^\s*((?:.|\s)+?)\s*\{((?:.|\s)+?)\}/);if(!l){at("Halting stylesheet parsing: String stylesheet contains more to parse but no selector and block found in: "+i);break}t=l[0];var u=l[1];if("core"!==u)if(new Qo(u).invalid){at("Skipping parsing of block: Invalid selector found in string stylesheet: "+u),o();continue}var c=l[2],d=!1;n=c;for(var h=[];;){if(n.match(/^\s*$/))break;var f=n.match(/^\s*(.+?)\s*:\s*(.+?)(?:\s*;|\s*$)/);if(!f){at("Skipping parsing of block: Invalid formatting of style property and value definitions found in:"+c),d=!0;break}r=f[0];var p=f[1],g=f[2];if(this.properties[p])a.parse(p,g)?(h.push({name:p,val:g}),s()):(at("Skipping property: Invalid property definition in: "+r),s());else at("Skipping property: Invalid property name in: "+r),s()}if(d){o();break}a.selector(u);for(var v=0;v<h.length;v++){var y=h[v];a.css(y.name,y.val)}o()}return a},fromString:function(e){var t=this;return t.resetToDefault(),t.appendFromString(e),t}},su={};!function(){var e=he,t=pe,n=ve,r=function(e){return"^"+e+"\\s*\\(\\s*([\\w\\.]+)\\s*\\)$"},a=function(r){var a=e+"|\\w+|"+t+"|"+n+"|\\#[0-9a-fA-F]{3}|\\#[0-9a-fA-F]{6}";return"^"+r+"\\s*\\(([\\w\\.]+)\\s*\\,\\s*("+e+")\\s*\\,\\s*("+e+")\\s*,\\s*("+a+")\\s*\\,\\s*("+a+")\\)$"},i=["^url\\s*\\(\\s*['\"]?(.+?)['\"]?\\s*\\)$","^(none)$","^(.+)$"];su.types={time:{number:!0,min:0,units:"s|ms",implicitUnits:"ms"},percent:{number:!0,min:0,max:100,units:"%",implicitUnits:"%"},percentages:{number:!0,min:0,max:100,units:"%",implicitUnits:"%",multiple:!0},zeroOneNumber:{number:!0,min:0,max:1,unitless:!0},zeroOneNumbers:{number:!0,min:0,max:1,unitless:!0,multiple:!0},nOneOneNumber:{number:!0,min:-1,max:1,unitless:!0},nonNegativeInt:{number:!0,min:0,integer:!0,unitless:!0},nonNegativeNumber:{number:!0,min:0,unitless:!0},position:{enums:["parent","origin"]},nodeSize:{number:!0,min:0,enums:["label"]},number:{number:!0,unitless:!0},numbers:{number:!0,unitless:!0,multiple:!0},positiveNumber:{number:!0,unitless:!0,min:0,strictMin:!0},size:{number:!0,min:0},bidirectionalSize:{number:!0},bidirectionalSizeMaybePercent:{number:!0,allowPercent:!0},bidirectionalSizes:{number:!0,multiple:!0},sizeMaybePercent:{number:!0,min:0,allowPercent:!0},axisDirection:{enums:["horizontal","leftward","rightward","vertical","upward","downward","auto"]},axisDirectionExplicit:{enums:["leftward","rightward","upward","downward"]},axisDirectionPrimary:{enums:["horizontal","vertical"]},paddingRelativeTo:{enums:["width","height","average","min","max"]},bgWH:{number:!0,min:0,allowPercent:!0,enums:["auto"],multiple:!0},bgPos:{number:!0,allowPercent:!0,multiple:!0},bgRelativeTo:{enums:["inner","include-padding"],multiple:!0},bgRepeat:{enums:["repeat","repeat-x","repeat-y","no-repeat"],multiple:!0},bgFit:{enums:["none","contain","cover"],multiple:!0},bgCrossOrigin:{enums:["anonymous","use-credentials","null"],multiple:!0},bgClip:{enums:["none","node"],multiple:!0},bgContainment:{enums:["inside","over"],multiple:!0},color:{color:!0},colors:{color:!0,multiple:!0},fill:{enums:["solid","linear-gradient","radial-gradient"]},bool:{enums:["yes","no"]},bools:{enums:["yes","no"],multiple:!0},lineStyle:{enums:["solid","dotted","dashed"]},lineCap:{enums:["butt","round","square"]},linePosition:{enums:["center","inside","outside"]},lineJoin:{enums:["round","bevel","miter"]},borderStyle:{enums:["solid","dotted","dashed","double"]},curveStyle:{enums:["bezier","unbundled-bezier","haystack","segments","straight","straight-triangle","taxi","round-segments","round-taxi"]},radiusType:{enums:["arc-radius","influence-radius"],multiple:!0},fontFamily:{regex:'^([\\w- \\"]+(?:\\s*,\\s*[\\w- \\"]+)*)$'},fontStyle:{enums:["italic","normal","oblique"]},fontWeight:{enums:["normal","bold","bolder","lighter","100","200","300","400","500","600","800","900",100,200,300,400,500,600,700,800,900]},textDecoration:{enums:["none","underline","overline","line-through"]},textTransform:{enums:["none","uppercase","lowercase"]},textWrap:{enums:["none","wrap","ellipsis"]},textOverflowWrap:{enums:["whitespace","anywhere"]},textBackgroundShape:{enums:["rectangle","roundrectangle","round-rectangle"]},nodeShape:{enums:["rectangle","roundrectangle","round-rectangle","cutrectangle","cut-rectangle","bottomroundrectangle","bottom-round-rectangle","barrel","ellipse","triangle","round-triangle","square","pentagon","round-pentagon","hexagon","round-hexagon","concavehexagon","concave-hexagon","heptagon","round-heptagon","octagon","round-octagon","tag","round-tag","star","diamond","round-diamond","vee","rhomboid","right-rhomboid","polygon"]},overlayShape:{enums:["roundrectangle","round-rectangle","ellipse"]},cornerRadius:{number:!0,min:0,units:"px|em",implicitUnits:"px",enums:["auto"]},compoundIncludeLabels:{enums:["include","exclude"]},arrowShape:{enums:["tee","triangle","triangle-tee","circle-triangle","triangle-cross","triangle-backcurve","vee","square","circle","diamond","chevron","none"]},arrowFill:{enums:["filled","hollow"]},arrowWidth:{number:!0,units:"%|px|em",implicitUnits:"px",enums:["match-line"]},display:{enums:["element","none"]},visibility:{enums:["hidden","visible"]},zCompoundDepth:{enums:["bottom","orphan","auto","top"]},zIndexCompare:{enums:["auto","manual"]},valign:{enums:["top","center","bottom"]},halign:{enums:["left","center","right"]},justification:{enums:["left","center","right","auto"]},text:{string:!0},data:{mapping:!0,regex:r("data")},layoutData:{mapping:!0,regex:r("layoutData")},scratch:{mapping:!0,regex:r("scratch")},mapData:{mapping:!0,regex:a("mapData")},mapLayoutData:{mapping:!0,regex:a("mapLayoutData")},mapScratch:{mapping:!0,regex:a("mapScratch")},fn:{mapping:!0,fn:!0},url:{regexes:i,singleRegexMatchValue:!0},urls:{regexes:i,singleRegexMatchValue:!0,multiple:!0},propList:{propList:!0},angle:{number:!0,units:"deg|rad",implicitUnits:"rad"},textRotation:{number:!0,units:"deg|rad",implicitUnits:"rad",enums:["none","autorotate"]},polygonPointList:{number:!0,multiple:!0,evenMultiple:!0,min:-1,max:1,unitless:!0},edgeDistances:{enums:["intersection","node-position","endpoints"]},edgeEndpoint:{number:!0,multiple:!0,units:"%|px|em|deg|rad",implicitUnits:"px",enums:["inside-to-node","outside-to-node","outside-to-node-or-label","outside-to-line","outside-to-line-or-label"],singleEnum:!0,validate:function(e,t){switch(e.length){case 2:return"deg"!==t[0]&&"rad"!==t[0]&&"deg"!==t[1]&&"rad"!==t[1];case 1:return K(e[0])||"deg"===t[0]||"rad"===t[0];default:return!1}}},easing:{regexes:["^(spring)\\s*\\(\\s*("+e+")\\s*,\\s*("+e+")\\s*\\)$","^(cubic-bezier)\\s*\\(\\s*("+e+")\\s*,\\s*("+e+")\\s*,\\s*("+e+")\\s*,\\s*("+e+")\\s*\\)$"],enums:["linear","ease","ease-in","ease-out","ease-in-out","ease-in-sine","ease-out-sine","ease-in-out-sine","ease-in-quad","ease-out-quad","ease-in-out-quad","ease-in-cubic","ease-out-cubic","ease-in-out-cubic","ease-in-quart","ease-out-quart","ease-in-out-quart","ease-in-quint","ease-out-quint","ease-in-out-quint","ease-in-expo","ease-out-expo","ease-in-out-expo","ease-in-circ","ease-out-circ","ease-in-out-circ"]},gradientDirection:{enums:["to-bottom","to-top","to-left","to-right","to-bottom-right","to-bottom-left","to-top-right","to-top-left","to-right-bottom","to-left-bottom","to-right-top","to-left-top"]},boundsExpansion:{number:!0,multiple:!0,min:0,validate:function(e){var t=e.length;return 1===t||2===t||4===t}}};var o={zeroNonZero:function(e,t){return(null==e||null==t)&&e!==t||(0==e&&0!=t||0!=e&&0==t)},any:function(e,t){return e!=t},emptyNonEmpty:function(e,t){var n=ie(e),r=ie(t);return n&&!r||!n&&r}},s=su.types,l=[{name:"label",type:s.text,triggersBounds:o.any,triggersZOrder:o.emptyNonEmpty},{name:"text-rotation",type:s.textRotation,triggersBounds:o.any},{name:"text-margin-x",type:s.bidirectionalSize,triggersBounds:o.any},{name:"text-margin-y",type:s.bidirectionalSize,triggersBounds:o.any}],u=[{name:"source-label",type:s.text,triggersBounds:o.any},{name:"source-text-rotation",type:s.textRotation,triggersBounds:o.any},{name:"source-text-margin-x",type:s.bidirectionalSize,triggersBounds:o.any},{name:"source-text-margin-y",type:s.bidirectionalSize,triggersBounds:o.any},{name:"source-text-offset",type:s.size,triggersBounds:o.any}],c=[{name:"target-label",type:s.text,triggersBounds:o.any},{name:"target-text-rotation",type:s.textRotation,triggersBounds:o.any},{name:"target-text-margin-x",type:s.bidirectionalSize,triggersBounds:o.any},{name:"target-text-margin-y",type:s.bidirectionalSize,triggersBounds:o.any},{name:"target-text-offset",type:s.size,triggersBounds:o.any}],d=[{name:"font-family",type:s.fontFamily,triggersBounds:o.any},{name:"font-style",type:s.fontStyle,triggersBounds:o.any},{name:"font-weight",type:s.fontWeight,triggersBounds:o.any},{name:"font-size",type:s.size,triggersBounds:o.any},{name:"text-transform",type:s.textTransform,triggersBounds:o.any},{name:"text-wrap",type:s.textWrap,triggersBounds:o.any},{name:"text-overflow-wrap",type:s.textOverflowWrap,triggersBounds:o.any},{name:"text-max-width",type:s.size,triggersBounds:o.any},{name:"text-outline-width",type:s.size,triggersBounds:o.any},{name:"line-height",type:s.positiveNumber,triggersBounds:o.any}],h=[{name:"text-valign",type:s.valign,triggersBounds:o.any},{name:"text-halign",type:s.halign,triggersBounds:o.any},{name:"color",type:s.color},{name:"text-outline-color",type:s.color},{name:"text-outline-opacity",type:s.zeroOneNumber},{name:"text-background-color",type:s.color},{name:"text-background-opacity",type:s.zeroOneNumber},{name:"text-background-padding",type:s.size,triggersBounds:o.any},{name:"text-border-opacity",type:s.zeroOneNumber},{name:"text-border-color",type:s.color},{name:"text-border-width",type:s.size,triggersBounds:o.any},{name:"text-border-style",type:s.borderStyle,triggersBounds:o.any},{name:"text-background-shape",type:s.textBackgroundShape,triggersBounds:o.any},{name:"text-justification",type:s.justification},{name:"box-select-labels",type:s.bool,triggersBounds:o.any}],f=[{name:"events",type:s.bool,triggersZOrder:o.any},{name:"text-events",type:s.bool,triggersZOrder:o.any}],p=[{name:"display",type:s.display,triggersZOrder:o.any,triggersBounds:o.any,triggersBoundsOfConnectedEdges:o.any,triggersBoundsOfParallelEdges:function(e,t,n){return e!==t&&"bezier"===n.pstyle("curve-style").value}},{name:"visibility",type:s.visibility,triggersZOrder:o.any},{name:"opacity",type:s.zeroOneNumber,triggersZOrder:o.zeroNonZero},{name:"text-opacity",type:s.zeroOneNumber},{name:"min-zoomed-font-size",type:s.size},{name:"z-compound-depth",type:s.zCompoundDepth,triggersZOrder:o.any},{name:"z-index-compare",type:s.zIndexCompare,triggersZOrder:o.any},{name:"z-index",type:s.number,triggersZOrder:o.any}],g=[{name:"overlay-padding",type:s.size,triggersBounds:o.any},{name:"overlay-color",type:s.color},{name:"overlay-opacity",type:s.zeroOneNumber,triggersBounds:o.zeroNonZero},{name:"overlay-shape",type:s.overlayShape,triggersBounds:o.any},{name:"overlay-corner-radius",type:s.cornerRadius}],v=[{name:"underlay-padding",type:s.size,triggersBounds:o.any},{name:"underlay-color",type:s.color},{name:"underlay-opacity",type:s.zeroOneNumber,triggersBounds:o.zeroNonZero},{name:"underlay-shape",type:s.overlayShape,triggersBounds:o.any},{name:"underlay-corner-radius",type:s.cornerRadius}],y=[{name:"transition-property",type:s.propList},{name:"transition-duration",type:s.time},{name:"transition-delay",type:s.time},{name:"transition-timing-function",type:s.easing}],m=function(e,t){return"label"===t.value?-e.poolIndex():t.pfValue},b=[{name:"height",type:s.nodeSize,triggersBounds:o.any,hashOverride:m},{name:"width",type:s.nodeSize,triggersBounds:o.any,hashOverride:m},{name:"shape",type:s.nodeShape,triggersBounds:o.any},{name:"shape-polygon-points",type:s.polygonPointList,triggersBounds:o.any},{name:"corner-radius",type:s.cornerRadius},{name:"background-color",type:s.color},{name:"background-fill",type:s.fill},{name:"background-opacity",type:s.zeroOneNumber},{name:"background-blacken",type:s.nOneOneNumber},{name:"background-gradient-stop-colors",type:s.colors},{name:"background-gradient-stop-positions",type:s.percentages},{name:"background-gradient-direction",type:s.gradientDirection},{name:"padding",type:s.sizeMaybePercent,triggersBounds:o.any},{name:"padding-relative-to",type:s.paddingRelativeTo,triggersBounds:o.any},{name:"bounds-expansion",type:s.boundsExpansion,triggersBounds:o.any}],x=[{name:"border-color",type:s.color},{name:"border-opacity",type:s.zeroOneNumber},{name:"border-width",type:s.size,triggersBounds:o.any},{name:"border-style",type:s.borderStyle},{name:"border-cap",type:s.lineCap},{name:"border-join",type:s.lineJoin},{name:"border-dash-pattern",type:s.numbers},{name:"border-dash-offset",type:s.number},{name:"border-position",type:s.linePosition}],w=[{name:"outline-color",type:s.color},{name:"outline-opacity",type:s.zeroOneNumber},{name:"outline-width",type:s.size,triggersBounds:o.any},{name:"outline-style",type:s.borderStyle},{name:"outline-offset",type:s.size,triggersBounds:o.any}],E=[{name:"background-image",type:s.urls},{name:"background-image-crossorigin",type:s.bgCrossOrigin},{name:"background-image-opacity",type:s.zeroOneNumbers},{name:"background-image-containment",type:s.bgContainment},{name:"background-image-smoothing",type:s.bools},{name:"background-position-x",type:s.bgPos},{name:"background-position-y",type:s.bgPos},{name:"background-width-relative-to",type:s.bgRelativeTo},{name:"background-height-relative-to",type:s.bgRelativeTo},{name:"background-repeat",type:s.bgRepeat},{name:"background-fit",type:s.bgFit},{name:"background-clip",type:s.bgClip},{name:"background-width",type:s.bgWH},{name:"background-height",type:s.bgWH},{name:"background-offset-x",type:s.bgPos},{name:"background-offset-y",type:s.bgPos}],k=[{name:"position",type:s.position,triggersBounds:o.any},{name:"compound-sizing-wrt-labels",type:s.compoundIncludeLabels,triggersBounds:o.any},{name:"min-width",type:s.size,triggersBounds:o.any},{name:"min-width-bias-left",type:s.sizeMaybePercent,triggersBounds:o.any},{name:"min-width-bias-right",type:s.sizeMaybePercent,triggersBounds:o.any},{name:"min-height",type:s.size,triggersBounds:o.any},{name:"min-height-bias-top",type:s.sizeMaybePercent,triggersBounds:o.any},{name:"min-height-bias-bottom",type:s.sizeMaybePercent,triggersBounds:o.any}],T=[{name:"line-style",type:s.lineStyle},{name:"line-color",type:s.color},{name:"line-fill",type:s.fill},{name:"line-cap",type:s.lineCap},{name:"line-opacity",type:s.zeroOneNumber},{name:"line-dash-pattern",type:s.numbers},{name:"line-dash-offset",type:s.number},{name:"line-outline-width",type:s.size},{name:"line-outline-color",type:s.color},{name:"line-gradient-stop-colors",type:s.colors},{name:"line-gradient-stop-positions",type:s.percentages},{name:"curve-style",type:s.curveStyle,triggersBounds:o.any,triggersBoundsOfParallelEdges:function(e,t){return e!==t&&("bezier"===e||"bezier"===t)}},{name:"haystack-radius",type:s.zeroOneNumber,triggersBounds:o.any},{name:"source-endpoint",type:s.edgeEndpoint,triggersBounds:o.any},{name:"target-endpoint",type:s.edgeEndpoint,triggersBounds:o.any},{name:"control-point-step-size",type:s.size,triggersBounds:o.any},{name:"control-point-distances",type:s.bidirectionalSizes,triggersBounds:o.any},{name:"control-point-weights",type:s.numbers,triggersBounds:o.any},{name:"segment-distances",type:s.bidirectionalSizes,triggersBounds:o.any},{name:"segment-weights",type:s.numbers,triggersBounds:o.any},{name:"segment-radii",type:s.numbers,triggersBounds:o.any},{name:"radius-type",type:s.radiusType,triggersBounds:o.any},{name:"taxi-turn",type:s.bidirectionalSizeMaybePercent,triggersBounds:o.any},{name:"taxi-turn-min-distance",type:s.size,triggersBounds:o.any},{name:"taxi-direction",type:s.axisDirection,triggersBounds:o.any},{name:"taxi-radius",type:s.number,triggersBounds:o.any},{name:"edge-distances",type:s.edgeDistances,triggersBounds:o.any},{name:"arrow-scale",type:s.positiveNumber,triggersBounds:o.any},{name:"loop-direction",type:s.angle,triggersBounds:o.any},{name:"loop-sweep",type:s.angle,triggersBounds:o.any},{name:"source-distance-from-node",type:s.size,triggersBounds:o.any},{name:"target-distance-from-node",type:s.size,triggersBounds:o.any}],C=[{name:"ghost",type:s.bool,triggersBounds:o.any},{name:"ghost-offset-x",type:s.bidirectionalSize,triggersBounds:o.any},{name:"ghost-offset-y",type:s.bidirectionalSize,triggersBounds:o.any},{name:"ghost-opacity",type:s.zeroOneNumber}],P=[{name:"selection-box-color",type:s.color},{name:"selection-box-opacity",type:s.zeroOneNumber},{name:"selection-box-border-color",type:s.color},{name:"selection-box-border-width",type:s.size},{name:"active-bg-color",type:s.color},{name:"active-bg-opacity",type:s.zeroOneNumber},{name:"active-bg-size",type:s.size},{name:"outside-texture-bg-color",type:s.color},{name:"outside-texture-bg-opacity",type:s.zeroOneNumber}],S=[];su.pieBackgroundN=16,S.push({name:"pie-size",type:s.sizeMaybePercent}),S.push({name:"pie-hole",type:s.sizeMaybePercent}),S.push({name:"pie-start-angle",type:s.angle});for(var B=1;B<=su.pieBackgroundN;B++)S.push({name:"pie-"+B+"-background-color",type:s.color}),S.push({name:"pie-"+B+"-background-size",type:s.percent}),S.push({name:"pie-"+B+"-background-opacity",type:s.zeroOneNumber});var D=[];su.stripeBackgroundN=16,D.push({name:"stripe-size",type:s.sizeMaybePercent}),D.push({name:"stripe-direction",type:s.axisDirectionPrimary});for(var _=1;_<=su.stripeBackgroundN;_++)D.push({name:"stripe-"+_+"-background-color",type:s.color}),D.push({name:"stripe-"+_+"-background-size",type:s.percent}),D.push({name:"stripe-"+_+"-background-opacity",type:s.zeroOneNumber});var A=[],M=su.arrowPrefixes=["source","mid-source","target","mid-target"];[{name:"arrow-shape",type:s.arrowShape,triggersBounds:o.any},{name:"arrow-color",type:s.color},{name:"arrow-fill",type:s.arrowFill},{name:"arrow-width",type:s.arrowWidth}].forEach((function(e){M.forEach((function(t){var n=t+"-"+e.name,r=e.type,a=e.triggersBounds;A.push({name:n,type:r,triggersBounds:a})}))}),{});var R=su.properties=[].concat(f,y,p,g,v,C,h,d,l,u,c,b,x,w,E,S,D,k,T,A,P),I=su.propertyGroups={behavior:f,transition:y,visibility:p,overlay:g,underlay:v,ghost:C,commonLabel:h,labelDimensions:d,mainLabel:l,sourceLabel:u,targetLabel:c,nodeBody:b,nodeBorder:x,nodeOutline:w,backgroundImage:E,pie:S,stripe:D,compound:k,edgeLine:T,edgeArrow:A,core:P},N=su.propertyGroupNames={};(su.propertyGroupKeys=Object.keys(I)).forEach((function(e){N[e]=I[e].map((function(e){return e.name})),I[e].forEach((function(t){return t.groupKey=e}))}));var L=su.aliases=[{name:"content",pointsTo:"label"},{name:"control-point-distance",pointsTo:"control-point-distances"},{name:"control-point-weight",pointsTo:"control-point-weights"},{name:"segment-distance",pointsTo:"segment-distances"},{name:"segment-weight",pointsTo:"segment-weights"},{name:"segment-radius",pointsTo:"segment-radii"},{name:"edge-text-rotation",pointsTo:"text-rotation"},{name:"padding-left",pointsTo:"padding"},{name:"padding-right",pointsTo:"padding"},{name:"padding-top",pointsTo:"padding"},{name:"padding-bottom",pointsTo:"padding"}];su.propertyNames=R.map((function(e){return e.name}));for(var z=0;z<R.length;z++){var O=R[z];R[O.name]=O}for(var V=0;V<L.length;V++){var F=L[V],j=R[F.pointsTo],X={name:F.name,alias:!0,pointsTo:j};R.push(X),R[F.name]=X}}(),su.getDefaultProperty=function(e){return this.getDefaultProperties()[e]},su.getDefaultProperties=function(){var e=this._private;if(null!=e.defaultProperties)return e.defaultProperties;for(var t=me({"selection-box-color":"#ddd","selection-box-opacity":.65,"selection-box-border-color":"#aaa","selection-box-border-width":1,"active-bg-color":"black","active-bg-opacity":.15,"active-bg-size":30,"outside-texture-bg-color":"#000","outside-texture-bg-opacity":.125,events:"yes","text-events":"no","text-valign":"top","text-halign":"center","text-justification":"auto","line-height":1,color:"#000","text-outline-color":"#000","text-outline-width":0,"text-outline-opacity":1,"text-opacity":1,"text-decoration":"none","text-transform":"none","text-wrap":"none","text-overflow-wrap":"whitespace","text-max-width":9999,"text-background-color":"#000","text-background-opacity":0,"text-background-shape":"rectangle","text-background-padding":0,"text-border-opacity":0,"text-border-width":0,"text-border-style":"solid","text-border-color":"#000","font-family":"Helvetica Neue, Helvetica, sans-serif","font-style":"normal","font-weight":"normal","font-size":16,"min-zoomed-font-size":0,"text-rotation":"none","source-text-rotation":"none","target-text-rotation":"none",visibility:"visible",display:"element",opacity:1,"z-compound-depth":"auto","z-index-compare":"auto","z-index":0,label:"","text-margin-x":0,"text-margin-y":0,"source-label":"","source-text-offset":0,"source-text-margin-x":0,"source-text-margin-y":0,"target-label":"","target-text-offset":0,"target-text-margin-x":0,"target-text-margin-y":0,"overlay-opacity":0,"overlay-color":"#000","overlay-padding":10,"overlay-shape":"round-rectangle","overlay-corner-radius":"auto","underlay-opacity":0,"underlay-color":"#000","underlay-padding":10,"underlay-shape":"round-rectangle","underlay-corner-radius":"auto","transition-property":"none","transition-duration":0,"transition-delay":0,"transition-timing-function":"linear","box-select-labels":"no","background-blacken":0,"background-color":"#999","background-fill":"solid","background-opacity":1,"background-image":"none","background-image-crossorigin":"anonymous","background-image-opacity":1,"background-image-containment":"inside","background-image-smoothing":"yes","background-position-x":"50%","background-position-y":"50%","background-offset-x":0,"background-offset-y":0,"background-width-relative-to":"include-padding","background-height-relative-to":"include-padding","background-repeat":"no-repeat","background-fit":"none","background-clip":"node","background-width":"auto","background-height":"auto","border-color":"#000","border-opacity":1,"border-width":0,"border-style":"solid","border-dash-pattern":[4,2],"border-dash-offset":0,"border-cap":"butt","border-join":"miter","border-position":"center","outline-color":"#999","outline-opacity":1,"outline-width":0,"outline-offset":0,"outline-style":"solid",height:30,width:30,shape:"ellipse","shape-polygon-points":"-1, -1,   1, -1,   1, 1,   -1, 1","corner-radius":"auto","bounds-expansion":0,"background-gradient-direction":"to-bottom","background-gradient-stop-colors":"#999","background-gradient-stop-positions":"0%",ghost:"no","ghost-offset-y":0,"ghost-offset-x":0,"ghost-opacity":0,padding:0,"padding-relative-to":"width",position:"origin","compound-sizing-wrt-labels":"include","min-width":0,"min-width-bias-left":0,"min-width-bias-right":0,"min-height":0,"min-height-bias-top":0,"min-height-bias-bottom":0},{"pie-size":"100%","pie-hole":0,"pie-start-angle":"0deg"},[{name:"pie-{{i}}-background-color",value:"black"},{name:"pie-{{i}}-background-size",value:"0%"},{name:"pie-{{i}}-background-opacity",value:1}].reduce((function(e,t){for(var n=1;n<=su.pieBackgroundN;n++){var r=t.name.replace("{{i}}",n),a=t.value;e[r]=a}return e}),{}),{"stripe-size":"100%","stripe-direction":"horizontal"},[{name:"stripe-{{i}}-background-color",value:"black"},{name:"stripe-{{i}}-background-size",value:"0%"},{name:"stripe-{{i}}-background-opacity",value:1}].reduce((function(e,t){for(var n=1;n<=su.stripeBackgroundN;n++){var r=t.name.replace("{{i}}",n),a=t.value;e[r]=a}return e}),{}),{"line-style":"solid","line-color":"#999","line-fill":"solid","line-cap":"butt","line-opacity":1,"line-outline-width":0,"line-outline-color":"#000","line-gradient-stop-colors":"#999","line-gradient-stop-positions":"0%","control-point-step-size":40,"control-point-weights":.5,"segment-weights":.5,"segment-distances":20,"segment-radii":15,"radius-type":"arc-radius","taxi-turn":"50%","taxi-radius":15,"taxi-turn-min-distance":10,"taxi-direction":"auto","edge-distances":"intersection","curve-style":"haystack","haystack-radius":0,"arrow-scale":1,"loop-direction":"-45deg","loop-sweep":"-90deg","source-distance-from-node":0,"target-distance-from-node":0,"source-endpoint":"outside-to-node","target-endpoint":"outside-to-node","line-dash-pattern":[6,3],"line-dash-offset":0},[{name:"arrow-shape",value:"none"},{name:"arrow-color",value:"#999"},{name:"arrow-fill",value:"filled"},{name:"arrow-width",value:1}].reduce((function(e,t){return su.arrowPrefixes.forEach((function(n){var r=n+"-"+t.name,a=t.value;e[r]=a})),e}),{})),n={},r=0;r<this.properties.length;r++){var a=this.properties[r];if(!a.pointsTo){var i=a.name,o=t[i],s=this.parse(i,o);n[i]=s}}return e.defaultProperties=n,e.defaultProperties},su.addDefaultStylesheet=function(){this.selector(":parent").css({shape:"rectangle",padding:10,"background-color":"#eee","border-color":"#ccc","border-width":1}).selector("edge").css({width:3}).selector(":loop").css({"curve-style":"bezier"}).selector("edge:compound").css({"curve-style":"bezier","source-endpoint":"outside-to-line","target-endpoint":"outside-to-line"}).selector(":selected").css({"background-color":"#0169D9","line-color":"#0169D9","source-arrow-color":"#0169D9","target-arrow-color":"#0169D9","mid-source-arrow-color":"#0169D9","mid-target-arrow-color":"#0169D9"}).selector(":parent:selected").css({"background-color":"#CCE1F9","border-color":"#aec8e5"}).selector(":active").css({"overlay-color":"black","overlay-padding":10,"overlay-opacity":.25}),this.defaultLength=this.length};var lu={parse:function(e,t,n,r){var a=this;if(G(t))return a.parseImplWarn(e,t,n,r);var i,o=Ue(e,""+t,n?"t":"f","mapping"===r||!0===r||!1===r||null==r?"dontcare":r),s=a.propCache=a.propCache||[];return(i=s[o])||(i=s[o]=a.parseImplWarn(e,t,n,r)),(n||"mapping"===r)&&(i=it(i))&&(i.value=it(i.value)),i},parseImplWarn:function(e,t,n,r){var a=this.parseImpl(e,t,n,r);return a||null==t||at("The style property `".concat(e,": ").concat(t,"` is invalid")),!a||"width"!==a.name&&"height"!==a.name||"label"!==t||at("The style value of `label` is deprecated for `"+a.name+"`"),a}};lu.parseImpl=function(e,t,n,r){var a=this;e=le(e);var i=a.properties[e],o=t,s=a.types;if(!i)return null;if(void 0===t)return null;i.alias&&(i=i.pointsTo,e=i.name);var l=K(t);l&&(t=t.trim());var u,c,d=i.type;if(!d)return null;if(n&&(""===t||null===t))return{name:e,value:t,bypass:!0,deleteBypass:!0};if(G(t))return{name:e,value:t,strValue:"fn",mapped:s.fn,bypass:n};if(!l||r||t.length<7||"a"!==t[1]);else{if(t.length>=7&&"d"===t[0]&&(u=new RegExp(s.data.regex).exec(t))){if(n)return!1;var h=s.data;return{name:e,value:u,strValue:""+t,mapped:h,field:u[1],bypass:n}}if(t.length>=10&&"m"===t[0]&&(c=new RegExp(s.mapData.regex).exec(t))){if(n)return!1;if(d.multiple)return!1;var f=s.mapData;if(!d.color&&!d.number)return!1;var p=this.parse(e,c[4]);if(!p||p.mapped)return!1;var g=this.parse(e,c[5]);if(!g||g.mapped)return!1;if(p.pfValue===g.pfValue||p.strValue===g.strValue)return at("`"+e+": "+t+"` is not a valid mapper because the output range is zero; converting to `"+e+": "+p.strValue+"`"),this.parse(e,p.strValue);if(d.color){var v=p.value,y=g.value;if(!(v[0]!==y[0]||v[1]!==y[1]||v[2]!==y[2]||v[3]!==y[3]&&(null!=v[3]&&1!==v[3]||null!=y[3]&&1!==y[3])))return!1}return{name:e,value:c,strValue:""+t,mapped:f,field:c[1],fieldMin:parseFloat(c[2]),fieldMax:parseFloat(c[3]),valueMin:p.value,valueMax:g.value,bypass:n}}}if(d.multiple&&"multiple"!==r){var m;if(m=l?t.split(/\s+/):Z(t)?t:[t],d.evenMultiple&&m.length%2!=0)return null;for(var b=[],x=[],w=[],E="",k=!1,T=0;T<m.length;T++){var C=a.parse(e,m[T],n,"multiple");k=k||K(C.value),b.push(C.value),w.push(null!=C.pfValue?C.pfValue:C.value),x.push(C.units),E+=(T>0?" ":"")+C.strValue}return d.validate&&!d.validate(b,x)?null:d.singleEnum&&k?1===b.length&&K(b[0])?{name:e,value:b[0],strValue:b[0],bypass:n}:null:{name:e,value:b,pfValue:w,strValue:E,bypass:n,units:x}}var P,S,B=function(){for(var r=0;r<d.enums.length;r++){if(d.enums[r]===t)return{name:e,value:t,strValue:""+t,bypass:n}}return null};if(d.number){var D,_="px";if(d.units&&(D=d.units),d.implicitUnits&&(_=d.implicitUnits),!d.unitless)if(l){var A="px|em"+(d.allowPercent?"|\\%":"");D&&(A=D);var M=t.match("^("+he+")("+A+")?$");M&&(t=M[1],D=M[2]||_)}else D&&!d.implicitUnits||(D=_);if(t=parseFloat(t),isNaN(t)&&void 0===d.enums)return null;if(isNaN(t)&&void 0!==d.enums)return t=o,B();if(d.integer&&(!Q(S=t)||Math.floor(S)!==S))return null;if(void 0!==d.min&&(t<d.min||d.strictMin&&t===d.min)||void 0!==d.max&&(t>d.max||d.strictMax&&t===d.max))return null;var R={name:e,value:t,strValue:""+t+(D||""),units:D,bypass:n};return d.unitless||"px"!==D&&"em"!==D?R.pfValue=t:R.pfValue="px"!==D&&D?this.getEmSizeInPixels()*t:t,"ms"!==D&&"s"!==D||(R.pfValue="ms"===D?t:1e3*t),"deg"!==D&&"rad"!==D||(R.pfValue="rad"===D?t:(P=t,Math.PI*P/180)),"%"===D&&(R.pfValue=t/100),R}if(d.propList){var I=[],N=""+t;if("none"===N);else{for(var L=N.split(/\s*,\s*|\s+/),z=0;z<L.length;z++){var O=L[z].trim();a.properties[O]?I.push(O):at("`"+O+"` is not a valid property name")}if(0===I.length)return null}return{name:e,value:I,strValue:0===I.length?"none":I.join(" "),bypass:n}}if(d.color){var V=be(t);return V?{name:e,value:V,pfValue:V,strValue:"rgb("+V[0]+","+V[1]+","+V[2]+")",bypass:n}:null}if(d.regex||d.regexes){if(d.enums){var F=B();if(F)return F}for(var j=d.regexes?d.regexes:[d.regex],X=0;X<j.length;X++){var q=new RegExp(j[X]).exec(t);if(q)return{name:e,value:d.singleRegexMatchValue?q[1]:q,strValue:""+t,bypass:n}}return null}return d.string?{name:e,value:""+t,strValue:""+t,bypass:n}:d.enums?B():null};var uu=function(e){if(!(this instanceof uu))return new uu(e);re(e)?(this._private={cy:e,coreStyle:{}},this.length=0,this.resetToDefault()):nt("A style must have a core reference")},cu=uu.prototype;cu.instanceString=function(){return"style"},cu.clear=function(){for(var e=this._private,t=e.cy.elements(),n=0;n<this.length;n++)this[n]=void 0;return this.length=0,e.contextStyles={},e.propDiffs={},this.cleanElements(t,!0),t.forEach((function(e){var t=e[0]._private;t.styleDirty=!0,t.appliedInitStyle=!1})),this},cu.resetToDefault=function(){return this.clear(),this.addDefaultStylesheet(),this},cu.core=function(e){return this._private.coreStyle[e]||this.getDefaultProperty(e)},cu.selector=function(e){var t="core"===e?null:new Qo(e),n=this.length++;return this[n]={selector:t,properties:[],mappedProperties:[],index:n},this},cu.css=function(){var e=arguments;if(1===e.length)for(var t=e[0],n=0;n<this.properties.length;n++){var r=this.properties[n],a=t[r.name];void 0===a&&(a=t[ue(r.name)]),void 0!==a&&this.cssRule(r.name,a)}else 2===e.length&&this.cssRule(e[0],e[1]);return this},cu.style=cu.css,cu.cssRule=function(e,t){var n=this.parse(e,t);if(n){var r=this.length-1;this[r].properties.push(n),this[r].properties[n.name]=n,n.name.match(/pie-(\d+)-background-size/)&&n.value&&(this._private.hasPie=!0),n.name.match(/stripe-(\d+)-background-size/)&&n.value&&(this._private.hasStripe=!0),n.mapped&&this[r].mappedProperties.push(n),!this[r].selector&&(this._private.coreStyle[n.name]=n)}return this},cu.append=function(e){return ae(e)?e.appendToStyle(this):Z(e)?this.appendFromJson(e):K(e)&&this.appendFromString(e),this},uu.fromJson=function(e,t){var n=new uu(e);return n.fromJson(t),n},uu.fromString=function(e,t){return new uu(e).fromString(t)},[eu,nu,ru,au,iu,ou,su,lu].forEach((function(e){me(cu,e)})),uu.types=cu.types,uu.properties=cu.properties,uu.propertyGroups=cu.propertyGroups,uu.propertyGroupNames=cu.propertyGroupNames,uu.propertyGroupKeys=cu.propertyGroupKeys;var du={style:function(e){e&&this.setStyle(e).update();return this._private.style},setStyle:function(e){var t=this._private;return ae(e)?t.style=e.generateStyle(this):Z(e)?t.style=uu.fromJson(this,e):K(e)?t.style=uu.fromString(this,e):t.style=uu(this),t.style},updateStyle:function(){this.mutableElements().updateStyle()}},hu={autolock:function(e){return void 0===e?this._private.autolock:(this._private.autolock=!!e,this)},autoungrabify:function(e){return void 0===e?this._private.autoungrabify:(this._private.autoungrabify=!!e,this)},autounselectify:function(e){return void 0===e?this._private.autounselectify:(this._private.autounselectify=!!e,this)},selectionType:function(e){var t=this._private;return null==t.selectionType&&(t.selectionType="single"),void 0===e?t.selectionType:("additive"!==e&&"single"!==e||(t.selectionType=e),this)},panningEnabled:function(e){return void 0===e?this._private.panningEnabled:(this._private.panningEnabled=!!e,this)},userPanningEnabled:function(e){return void 0===e?this._private.userPanningEnabled:(this._private.userPanningEnabled=!!e,this)},zoomingEnabled:function(e){return void 0===e?this._private.zoomingEnabled:(this._private.zoomingEnabled=!!e,this)},userZoomingEnabled:function(e){return void 0===e?this._private.userZoomingEnabled:(this._private.userZoomingEnabled=!!e,this)},boxSelectionEnabled:function(e){return void 0===e?this._private.boxSelectionEnabled:(this._private.boxSelectionEnabled=!!e,this)},pan:function(){var e,t,n,r,a,i=arguments,o=this._private.pan;switch(i.length){case 0:return o;case 1:if(K(i[0]))return o[e=i[0]];if($(i[0])){if(!this._private.panningEnabled)return this;r=(n=i[0]).x,a=n.y,Q(r)&&(o.x=r),Q(a)&&(o.y=a),this.emit("pan viewport")}break;case 2:if(!this._private.panningEnabled)return this;t=i[1],"x"!==(e=i[0])&&"y"!==e||!Q(t)||(o[e]=t),this.emit("pan viewport")}return this.notify("viewport"),this},panBy:function(e,t){var n,r,a,i,o,s=arguments,l=this._private.pan;if(!this._private.panningEnabled)return this;switch(s.length){case 1:$(e)&&(i=(a=s[0]).x,o=a.y,Q(i)&&(l.x+=i),Q(o)&&(l.y+=o),this.emit("pan viewport"));break;case 2:r=t,"x"!==(n=e)&&"y"!==n||!Q(r)||(l[n]+=r),this.emit("pan viewport")}return this.notify("viewport"),this},gc:function(){this.notify("gc")},fit:function(e,t){var n=this.getFitViewport(e,t);if(n){var r=this._private;r.zoom=n.zoom,r.pan=n.pan,this.emit("pan zoom viewport"),this.notify("viewport")}return this},getFitViewport:function(e,t){if(Q(e)&&void 0===t&&(t=e,e=void 0),this._private.panningEnabled&&this._private.zoomingEnabled){var n,r;if(K(e)){var a=e;e=this.$(a)}else if($(r=e)&&Q(r.x1)&&Q(r.x2)&&Q(r.y1)&&Q(r.y2)){var i=e;(n={x1:i.x1,y1:i.y1,x2:i.x2,y2:i.y2}).w=n.x2-n.x1,n.h=n.y2-n.y1}else ee(e)||(e=this.mutableElements());if(!ee(e)||!e.empty()){n=n||e.boundingBox();var o,s=this.width(),l=this.height();if(t=Q(t)?t:0,!isNaN(s)&&!isNaN(l)&&s>0&&l>0&&!isNaN(n.w)&&!isNaN(n.h)&&n.w>0&&n.h>0)return{zoom:o=(o=(o=Math.min((s-2*t)/n.w,(l-2*t)/n.h))>this._private.maxZoom?this._private.maxZoom:o)<this._private.minZoom?this._private.minZoom:o,pan:{x:(s-o*(n.x1+n.x2))/2,y:(l-o*(n.y1+n.y2))/2}}}}},zoomRange:function(e,t){var n=this._private;if(null==t){var r=e;e=r.min,t=r.max}return Q(e)&&Q(t)&&e<=t?(n.minZoom=e,n.maxZoom=t):Q(e)&&void 0===t&&e<=n.maxZoom?n.minZoom=e:Q(t)&&void 0===e&&t>=n.minZoom&&(n.maxZoom=t),this},minZoom:function(e){return void 0===e?this._private.minZoom:this.zoomRange({min:e})},maxZoom:function(e){return void 0===e?this._private.maxZoom:this.zoomRange({max:e})},getZoomedViewport:function(e){var t,n,r=this._private,a=r.pan,i=r.zoom,o=!1;if(r.zoomingEnabled||(o=!0),Q(e)?n=e:$(e)&&(n=e.level,null!=e.position?t=Ft(e.position,i,a):null!=e.renderedPosition&&(t=e.renderedPosition),null==t||r.panningEnabled||(o=!0)),n=(n=n>r.maxZoom?r.maxZoom:n)<r.minZoom?r.minZoom:n,o||!Q(n)||n===i||null!=t&&(!Q(t.x)||!Q(t.y)))return null;if(null!=t){var s=a,l=i,u=n;return{zoomed:!0,panned:!0,zoom:u,pan:{x:-u/l*(t.x-s.x)+t.x,y:-u/l*(t.y-s.y)+t.y}}}return{zoomed:!0,panned:!1,zoom:n,pan:a}},zoom:function(e){if(void 0===e)return this._private.zoom;var t=this.getZoomedViewport(e),n=this._private;return null!=t&&t.zoomed?(n.zoom=t.zoom,t.panned&&(n.pan.x=t.pan.x,n.pan.y=t.pan.y),this.emit("zoom"+(t.panned?" pan":"")+" viewport"),this.notify("viewport"),this):this},viewport:function(e){var t=this._private,n=!0,r=!0,a=[],i=!1,o=!1;if(!e)return this;if(Q(e.zoom)||(n=!1),$(e.pan)||(r=!1),!n&&!r)return this;if(n){var s=e.zoom;s<t.minZoom||s>t.maxZoom||!t.zoomingEnabled?i=!0:(t.zoom=s,a.push("zoom"))}if(r&&(!i||!e.cancelOnFailedZoom)&&t.panningEnabled){var l=e.pan;Q(l.x)&&(t.pan.x=l.x,o=!1),Q(l.y)&&(t.pan.y=l.y,o=!1),o||a.push("pan")}return a.length>0&&(a.push("viewport"),this.emit(a.join(" ")),this.notify("viewport")),this},center:function(e){var t=this.getCenterPan(e);return t&&(this._private.pan=t,this.emit("pan viewport"),this.notify("viewport")),this},getCenterPan:function(e,t){if(this._private.panningEnabled){if(K(e)){var n=e;e=this.mutableElements().filter(n)}else ee(e)||(e=this.mutableElements());if(0!==e.length){var r=e.boundingBox(),a=this.width(),i=this.height();return{x:(a-(t=void 0===t?this._private.zoom:t)*(r.x1+r.x2))/2,y:(i-t*(r.y1+r.y2))/2}}}},reset:function(){return this._private.panningEnabled&&this._private.zoomingEnabled?(this.viewport({pan:{x:0,y:0},zoom:1}),this):this},invalidateSize:function(){this._private.sizeCache=null},size:function(){var e,t,n=this._private,r=n.container,a=this;return n.sizeCache=n.sizeCache||(r?(e=a.window().getComputedStyle(r),t=function(t){return parseFloat(e.getPropertyValue(t))},{width:r.clientWidth-t("padding-left")-t("padding-right"),height:r.clientHeight-t("padding-top")-t("padding-bottom")}):{width:1,height:1})},width:function(){return this.size().width},height:function(){return this.size().height},extent:function(){var e=this._private.pan,t=this._private.zoom,n=this.renderedExtent(),r={x1:(n.x1-e.x)/t,x2:(n.x2-e.x)/t,y1:(n.y1-e.y)/t,y2:(n.y2-e.y)/t};return r.w=r.x2-r.x1,r.h=r.y2-r.y1,r},renderedExtent:function(){var e=this.width(),t=this.height();return{x1:0,y1:0,x2:e,y2:t,w:e,h:t}},multiClickDebounceTime:function(e){return e?(this._private.multiClickDebounceTime=e,this):this._private.multiClickDebounceTime}};hu.centre=hu.center,hu.autolockNodes=hu.autolock,hu.autoungrabifyNodes=hu.autoungrabify;var fu={data:po.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeData:po.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),scratch:po.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:po.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0})};fu.attr=fu.data,fu.removeAttr=fu.removeData;var pu=function(e){var t=this,n=(e=me({},e)).container;n&&!J(n)&&J(n[0])&&(n=n[0]);var r=n?n._cyreg:null;(r=r||{})&&r.cy&&(r.cy.destroy(),r={});var a=r.readies=r.readies||[];n&&(n._cyreg=r),r.cy=t;var i=void 0!==f&&void 0!==n&&!e.headless,o=e;o.layout=me({name:i?"grid":"null"},o.layout),o.renderer=me({name:i?"canvas":"null"},o.renderer);var s=function(e,t,n){return void 0!==t?t:void 0!==n?n:e},l=this._private={container:n,ready:!1,options:o,elements:new _l(this),listeners:[],aniEles:new _l(this),data:o.data||{},scratch:{},layout:null,renderer:null,destroyed:!1,notificationsEnabled:!0,minZoom:1e-50,maxZoom:1e50,zoomingEnabled:s(!0,o.zoomingEnabled),userZoomingEnabled:s(!0,o.userZoomingEnabled),panningEnabled:s(!0,o.panningEnabled),userPanningEnabled:s(!0,o.userPanningEnabled),boxSelectionEnabled:s(!0,o.boxSelectionEnabled),autolock:s(!1,o.autolock,o.autolockNodes),autoungrabify:s(!1,o.autoungrabify,o.autoungrabifyNodes),autounselectify:s(!1,o.autounselectify),styleEnabled:void 0===o.styleEnabled?i:o.styleEnabled,zoom:Q(o.zoom)?o.zoom:1,pan:{x:$(o.pan)&&Q(o.pan.x)?o.pan.x:0,y:$(o.pan)&&Q(o.pan.y)?o.pan.y:0},animation:{current:[],queue:[]},hasCompoundNodes:!1,multiClickDebounceTime:s(250,o.multiClickDebounceTime)};this.createEmitter(),this.selectionType(o.selectionType),this.zoomRange({min:o.minZoom,max:o.maxZoom});l.styleEnabled&&t.setStyle([]);var u=me({},o,o.renderer);t.initRenderer(u);!function(e,t){if(e.some(oe))return jr.all(e).then(t);t(e)}([o.style,o.elements],(function(e){var n=e[0],i=e[1];l.styleEnabled&&t.style().append(n),function(e,n,r){t.notifications(!1);var a=t.mutableElements();a.length>0&&a.remove(),null!=e&&($(e)||Z(e))&&t.add(e),t.one("layoutready",(function(e){t.notifications(!0),t.emit(e),t.one("load",n),t.emitAndNotify("load")})).one("layoutstop",(function(){t.one("done",r),t.emit("done")}));var i=me({},t._private.options.layout);i.eles=t.elements(),t.layout(i).run()}(i,(function(){t.startAnimationLoop(),l.ready=!0,G(o.ready)&&t.on("ready",o.ready);for(var e=0;e<a.length;e++){var n=a[e];t.on("ready",n)}r&&(r.readies=[]),t.emit("ready")}),o.done)}))},gu=pu.prototype;me(gu,{instanceString:function(){return"core"},isReady:function(){return this._private.ready},destroyed:function(){return this._private.destroyed},ready:function(e){return this.isReady()?this.emitter().emit("ready",[],e):this.on("ready",e),this},destroy:function(){var e=this;if(!e.destroyed())return e.stopAnimationLoop(),e.destroyRenderer(),this.emit("destroy"),e._private.destroyed=!0,e},hasElementWithId:function(e){return this._private.elements.hasElementWithId(e)},getElementById:function(e){return this._private.elements.getElementById(e)},hasCompoundNodes:function(){return this._private.hasCompoundNodes},headless:function(){return this._private.renderer.isHeadless()},styleEnabled:function(){return this._private.styleEnabled},addToPool:function(e){return this._private.elements.merge(e),this},removeFromPool:function(e){return this._private.elements.unmerge(e),this},container:function(){return this._private.container||null},window:function(){if(null==this._private.container)return f;var e=this._private.container.ownerDocument;return void 0===e||null==e?f:e.defaultView||f},mount:function(e){if(null!=e){var t=this,n=t._private,r=n.options;return!J(e)&&J(e[0])&&(e=e[0]),t.stopAnimationLoop(),t.destroyRenderer(),n.container=e,n.styleEnabled=!0,t.invalidateSize(),t.initRenderer(me({},r,r.renderer,{name:"null"===r.renderer.name?"canvas":r.renderer.name})),t.startAnimationLoop(),t.style(r.style),t.emit("mount"),t}},unmount:function(){var e=this;return e.stopAnimationLoop(),e.destroyRenderer(),e.initRenderer({name:"null"}),e.emit("unmount"),e},options:function(){return it(this._private.options)},json:function(e){var t=this,n=t._private,r=t.mutableElements();if($(e)){if(t.startBatch(),e.elements){var a={},i=function(e,n){for(var r=[],i=[],o=0;o<e.length;o++){var s=e[o];if(s.data.id){var l=""+s.data.id,u=t.getElementById(l);a[l]=!0,0!==u.length?i.push({ele:u,json:s}):n?(s.group=n,r.push(s)):r.push(s)}else at("cy.json() cannot handle elements without an ID attribute")}t.add(r);for(var c=0;c<i.length;c++){var d=i[c],h=d.ele,f=d.json;h.json(f)}};if(Z(e.elements))i(e.elements);else for(var o=["nodes","edges"],s=0;s<o.length;s++){var l=o[s],u=e.elements[l];Z(u)&&i(u,l)}var c=t.collection();r.filter((function(e){return!a[e.id()]})).forEach((function(e){e.isParent()?c.merge(e):e.remove()})),c.forEach((function(e){return e.children().move({parent:null})})),c.forEach((function(e){return function(e){return t.getElementById(e.id())}(e).remove()}))}e.style&&t.style(e.style),null!=e.zoom&&e.zoom!==n.zoom&&t.zoom(e.zoom),e.pan&&(e.pan.x===n.pan.x&&e.pan.y===n.pan.y||t.pan(e.pan)),e.data&&t.data(e.data);for(var d=["minZoom","maxZoom","zoomingEnabled","userZoomingEnabled","panningEnabled","userPanningEnabled","boxSelectionEnabled","autolock","autoungrabify","autounselectify","multiClickDebounceTime"],h=0;h<d.length;h++){var f=d[h];null!=e[f]&&t[f](e[f])}return t.endBatch(),this}var p={};!!e?p.elements=this.elements().map((function(e){return e.json()})):(p.elements={},r.forEach((function(e){var t=e.group();p.elements[t]||(p.elements[t]=[]),p.elements[t].push(e.json())}))),this._private.styleEnabled&&(p.style=t.style().json()),p.data=it(t.data());var g=n.options;return p.zoomingEnabled=n.zoomingEnabled,p.userZoomingEnabled=n.userZoomingEnabled,p.zoom=n.zoom,p.minZoom=n.minZoom,p.maxZoom=n.maxZoom,p.panningEnabled=n.panningEnabled,p.userPanningEnabled=n.userPanningEnabled,p.pan=it(n.pan),p.boxSelectionEnabled=n.boxSelectionEnabled,p.renderer=it(g.renderer),p.hideEdgesOnViewport=g.hideEdgesOnViewport,p.textureOnViewport=g.textureOnViewport,p.wheelSensitivity=g.wheelSensitivity,p.motionBlur=g.motionBlur,p.multiClickDebounceTime=g.multiClickDebounceTime,p}}),gu.$id=gu.getElementById,[Ml,Yl,Hl,Kl,Gl,Zl,Ql,Jl,du,hu,fu].forEach((function(e){me(gu,e)}));var vu={fit:!0,directed:!1,padding:30,circle:!1,grid:!1,spacingFactor:1.75,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,roots:void 0,depthSort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}},yu={maximal:!1,acyclic:!1},mu=function(e){return e.scratch("breadthfirst")},bu=function(e,t){return e.scratch("breadthfirst",t)};function xu(e){this.options=me({},vu,yu,e)}xu.prototype.run=function(){var e,t=this.options,n=t.cy,r=t.eles,a=r.nodes().filter((function(e){return e.isChildless()})),i=r,o=t.directed,s=t.acyclic||t.maximal||t.maximalAdjustments>0,l=!!t.boundingBox,u=n.extent(),c=Qt(l?t.boundingBox:{x1:u.x1,y1:u.y1,w:u.w,h:u.h});if(ee(t.roots))e=t.roots;else if(Z(t.roots)){for(var d=[],h=0;h<t.roots.length;h++){var f=t.roots[h],p=n.getElementById(f);d.push(p)}e=n.collection(d)}else if(K(t.roots))e=n.$(t.roots);else if(o)e=a.roots();else{var g=r.components();e=n.collection();for(var v=function(){var t=g[y],n=t.maxDegree(!1),r=t.filter((function(e){return e.degree(!1)===n}));e=e.add(r)},y=0;y<g.length;y++)v()}var m=[],b={},x=function(e,t){null==m[t]&&(m[t]=[]);var n=m[t].length;m[t].push(e),bu(e,{index:n,depth:t})};i.bfs({roots:e,directed:t.directed,visit:function(e,t,n,r,a){var i=e[0],o=i.id();i.isChildless()&&x(i,a),b[o]=!0}});for(var w=[],E=0;E<a.length;E++){var k=a[E];b[k.id()]||w.push(k)}var T=function(e){for(var t=m[e],n=0;n<t.length;n++){var r=t[n];null!=r?bu(r,{depth:e,index:n}):(t.splice(n,1),n--)}},C=function(e,n){for(var a=mu(e),i=e.incomers().filter((function(e){return e.isNode()&&r.has(e)})),o=-1,s=e.id(),l=0;l<i.length;l++){var u=i[l],c=mu(u);o=Math.max(o,c.depth)}if(a.depth<=o){if(!t.acyclic&&n[s])return null;var d=o+1;return function(e,t){var n=mu(e),r=n.depth,a=n.index;m[r][a]=null,e.isChildless()&&x(e,t)}(e,d),n[s]=d,!0}return!1};if(o&&s){var P=[],S={},B=function(e){return P.push(e)};for(a.forEach((function(e){return P.push(e)}));P.length>0;){var D=P.shift(),_=C(D,S);if(_)D.outgoers().filter((function(e){return e.isNode()&&r.has(e)})).forEach(B);else if(null===_){at("Detected double maximal shift for node `"+D.id()+"`.  Bailing maximal adjustment due to cycle.  Use `options.maximal: true` only on DAGs.");break}}}var A=0;if(t.avoidOverlap)for(var M=0;M<a.length;M++){var R=a[M].layoutDimensions(t),I=R.w,N=R.h;A=Math.max(A,I,N)}var L={},z=function(e){if(L[e.id()])return L[e.id()];for(var t=mu(e).depth,n=e.neighborhood(),r=0,i=0,o=0;o<n.length;o++){var s=n[o];if(!s.isEdge()&&!s.isParent()&&a.has(s)){var l=mu(s);if(null!=l){var u=l.index,c=l.depth;if(null!=u&&null!=c){var d=m[c].length;c<t&&(r+=u/d,i++)}}}}return r/=i=Math.max(1,i),0===i&&(r=0),L[e.id()]=r,r},O=function(e,t){var n=z(e)-z(t);return 0===n?ye(e.id(),t.id()):n};void 0!==t.depthSort&&(O=t.depthSort);for(var V=m.length,F=0;F<V;F++)m[F].sort(O),T(F);for(var j=[],X=0;X<w.length;X++)j.push(w[X]);j.length&&(m.unshift(j),V=m.length,function(){for(var e=0;e<V;e++)T(e)}());for(var q=0,Y=0;Y<V;Y++)q=Math.max(m[Y].length,q);var W=c.x1+c.w/2,U=c.y1+c.h/2,H=a.reduce((function(e,n){return r=n.boundingBox({includeLabels:t.nodeDimensionsIncludeLabels}),{w:-1===e.w?r.w:(e.w+r.w)/2,h:-1===e.h?r.h:(e.h+r.h)/2};var r}),{w:-1,h:-1}),G=Math.max(1===V?0:l?(c.h-2*t.padding-H.h)/(V-1):(c.h-2*t.padding-H.h)/(V+1),A),$=m.reduce((function(e,t){return Math.max(e,t.length)}),0);return r.nodes().layoutPositions(this,t,(function(e){var n=mu(e),r=n.depth,a=n.index;if(t.circle){var i=Math.min(c.w/2/V,c.h/2/V),o=(i=Math.max(i,A))*r+i-(V>0&&m[0].length<=3?i/2:0),s=2*Math.PI/m[r].length*a;return 0===r&&1===m[0].length&&(o=1),{x:W+o*Math.cos(s),y:U+o*Math.sin(s)}}var u=m[r].length,d=Math.max(1===u?0:l?(c.w-2*t.padding-H.w)/((t.grid?$:u)-1):(c.w-2*t.padding-H.w)/((t.grid?$:u)+1),A);return{x:W+(a+1-(u+1)/2)*d,y:U+(r+1-(V+1)/2)*G}})),this};var wu={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,radius:void 0,startAngle:1.5*Math.PI,sweep:void 0,clockwise:!0,sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function Eu(e){this.options=me({},wu,e)}Eu.prototype.run=function(){var e=this.options,t=e,n=e.cy,r=t.eles,a=void 0!==t.counterclockwise?!t.counterclockwise:t.clockwise,i=r.nodes().not(":parent");t.sort&&(i=i.sort(t.sort));for(var o,s=Qt(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:n.width(),h:n.height()}),l=s.x1+s.w/2,u=s.y1+s.h/2,c=(void 0===t.sweep?2*Math.PI-2*Math.PI/i.length:t.sweep)/Math.max(1,i.length-1),d=0,h=0;h<i.length;h++){var f=i[h].layoutDimensions(t),p=f.w,g=f.h;d=Math.max(d,p,g)}if(o=Q(t.radius)?t.radius:i.length<=1?0:Math.min(s.h,s.w)/2-d,i.length>1&&t.avoidOverlap){d*=1.75;var v=Math.cos(c)-Math.cos(0),y=Math.sin(c)-Math.sin(0),m=Math.sqrt(d*d/(v*v+y*y));o=Math.max(m,o)}return r.nodes().layoutPositions(this,t,(function(e,n){var r=t.startAngle+n*c*(a?1:-1),i=o*Math.cos(r),s=o*Math.sin(r);return{x:l+i,y:u+s}})),this};var ku,Tu={fit:!0,padding:30,startAngle:1.5*Math.PI,sweep:void 0,clockwise:!0,equidistant:!1,minNodeSpacing:10,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,height:void 0,width:void 0,spacingFactor:void 0,concentric:function(e){return e.degree()},levelWidth:function(e){return e.maxDegree()/4},animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function Cu(e){this.options=me({},Tu,e)}Cu.prototype.run=function(){for(var e=this.options,t=e,n=void 0!==t.counterclockwise?!t.counterclockwise:t.clockwise,r=e.cy,a=t.eles,i=a.nodes().not(":parent"),o=Qt(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:r.width(),h:r.height()}),s=o.x1+o.w/2,l=o.y1+o.h/2,u=[],c=0,d=0;d<i.length;d++){var h,f=i[d];h=t.concentric(f),u.push({value:h,node:f}),f._private.scratch.concentric=h}i.updateStyle();for(var p=0;p<i.length;p++){var g=i[p].layoutDimensions(t);c=Math.max(c,g.w,g.h)}u.sort((function(e,t){return t.value-e.value}));for(var v=t.levelWidth(i),y=[[]],m=y[0],b=0;b<u.length;b++){var x=u[b];if(m.length>0)Math.abs(m[0].value-x.value)>=v&&(m=[],y.push(m));m.push(x)}var w=c+t.minNodeSpacing;if(!t.avoidOverlap){var E=y.length>0&&y[0].length>1,k=(Math.min(o.w,o.h)/2-w)/(y.length+E?1:0);w=Math.min(w,k)}for(var T=0,C=0;C<y.length;C++){var P=y[C],S=void 0===t.sweep?2*Math.PI-2*Math.PI/P.length:t.sweep,B=P.dTheta=S/Math.max(1,P.length-1);if(P.length>1&&t.avoidOverlap){var D=Math.cos(B)-Math.cos(0),_=Math.sin(B)-Math.sin(0),A=Math.sqrt(w*w/(D*D+_*_));T=Math.max(A,T)}P.r=T,T+=w}if(t.equidistant){for(var M=0,R=0,I=0;I<y.length;I++){var N=y[I].r-R;M=Math.max(M,N)}R=0;for(var L=0;L<y.length;L++){var z=y[L];0===L&&(R=z.r),z.r=R,R+=M}}for(var O={},V=0;V<y.length;V++)for(var F=y[V],j=F.dTheta,X=F.r,q=0;q<F.length;q++){var Y=F[q],W=t.startAngle+(n?1:-1)*j*q,U={x:s+X*Math.cos(W),y:l+X*Math.sin(W)};O[Y.node.id()]=U}return a.nodes().layoutPositions(this,t,(function(e){var t=e.id();return O[t]})),this};var Pu={ready:function(){},stop:function(){},animate:!0,animationEasing:void 0,animationDuration:void 0,animateFilter:function(e,t){return!0},animationThreshold:250,refresh:20,fit:!0,padding:30,boundingBox:void 0,nodeDimensionsIncludeLabels:!1,randomize:!1,componentSpacing:40,nodeRepulsion:function(e){return 2048},nodeOverlap:4,idealEdgeLength:function(e){return 32},edgeElasticity:function(e){return 32},nestingFactor:1.2,gravity:1,numIter:1e3,initialTemp:1e3,coolingFactor:.99,minTemp:1};function Su(e){this.options=me({},Pu,e),this.options.layout=this;var t=this.options.eles.nodes(),n=this.options.eles.edges().filter((function(e){var n=e.source().data("id"),r=e.target().data("id"),a=t.some((function(e){return e.data("id")===n})),i=t.some((function(e){return e.data("id")===r}));return!a||!i}));this.options.eles=this.options.eles.not(n)}Su.prototype.run=function(){var e=this.options,t=e.cy,n=this;n.stopped=!1,!0!==e.animate&&!1!==e.animate||n.emit({type:"layoutstart",layout:n}),ku=!0===e.debug;var r=Bu(t,n,e);ku&&undefined(r),e.randomize&&Au(r);var a=Le(),i=function(){Ru(r,t,e),!0===e.fit&&t.fit(e.padding)},o=function(t){return!(n.stopped||t>=e.numIter)&&(Iu(r,e),r.temperature=r.temperature*e.coolingFactor,!(r.temperature<e.minTemp))},s=function(){if(!0===e.animate||!1===e.animate)i(),n.one("layoutstop",e.stop),n.emit({type:"layoutstop",layout:n});else{var t=e.eles.nodes(),a=Mu(r,e,t);t.layoutPositions(n,e,a)}},l=0,u=!0;if(!0===e.animate){var c=function(){for(var t=0;u&&t<e.refresh;)u=o(l),l++,t++;u?(Le()-a>=e.animationThreshold&&i(),Ne(c)):(Uu(r,e),s())};c()}else{for(;u;)u=o(l),l++;Uu(r,e),s()}return this},Su.prototype.stop=function(){return this.stopped=!0,this.thread&&this.thread.stop(),this.emit("layoutstop"),this},Su.prototype.destroy=function(){return this.thread&&this.thread.stop(),this};var Bu=function(e,t,n){for(var r=n.eles.edges(),a=n.eles.nodes(),i=Qt(n.boundingBox?n.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),o={isCompound:e.hasCompoundNodes(),layoutNodes:[],idToIndex:{},nodeSize:a.size(),graphSet:[],indexToGraph:[],layoutEdges:[],edgeSize:r.size(),temperature:n.initialTemp,clientWidth:i.w,clientHeight:i.h,boundingBox:i},s=n.eles.components(),l={},u=0;u<s.length;u++)for(var c=s[u],d=0;d<c.length;d++){l[c[d].id()]=u}for(u=0;u<o.nodeSize;u++){var h=(y=a[u]).layoutDimensions(n);(R={}).isLocked=y.locked(),R.id=y.data("id"),R.parentId=y.data("parent"),R.cmptId=l[y.id()],R.children=[],R.positionX=y.position("x"),R.positionY=y.position("y"),R.offsetX=0,R.offsetY=0,R.height=h.w,R.width=h.h,R.maxX=R.positionX+R.width/2,R.minX=R.positionX-R.width/2,R.maxY=R.positionY+R.height/2,R.minY=R.positionY-R.height/2,R.padLeft=parseFloat(y.style("padding")),R.padRight=parseFloat(y.style("padding")),R.padTop=parseFloat(y.style("padding")),R.padBottom=parseFloat(y.style("padding")),R.nodeRepulsion=G(n.nodeRepulsion)?n.nodeRepulsion(y):n.nodeRepulsion,o.layoutNodes.push(R),o.idToIndex[R.id]=u}var f=[],p=0,g=-1,v=[];for(u=0;u<o.nodeSize;u++){var y,m=(y=o.layoutNodes[u]).parentId;null!=m?o.layoutNodes[o.idToIndex[m]].children.push(y.id):(f[++g]=y.id,v.push(y.id))}for(o.graphSet.push(v);p<=g;){var b=f[p++],x=o.idToIndex[b],w=o.layoutNodes[x].children;if(w.length>0){o.graphSet.push(w);for(u=0;u<w.length;u++)f[++g]=w[u]}}for(u=0;u<o.graphSet.length;u++){var E=o.graphSet[u];for(d=0;d<E.length;d++){var k=o.idToIndex[E[d]];o.indexToGraph[k]=u}}for(u=0;u<o.edgeSize;u++){var T=r[u],C={};C.id=T.data("id"),C.sourceId=T.data("source"),C.targetId=T.data("target");var P=G(n.idealEdgeLength)?n.idealEdgeLength(T):n.idealEdgeLength,S=G(n.edgeElasticity)?n.edgeElasticity(T):n.edgeElasticity,B=o.idToIndex[C.sourceId],D=o.idToIndex[C.targetId];if(o.indexToGraph[B]!=o.indexToGraph[D]){for(var _=Du(C.sourceId,C.targetId,o),A=o.graphSet[_],M=0,R=o.layoutNodes[B];-1===A.indexOf(R.id);)R=o.layoutNodes[o.idToIndex[R.parentId]],M++;for(R=o.layoutNodes[D];-1===A.indexOf(R.id);)R=o.layoutNodes[o.idToIndex[R.parentId]],M++;P*=M*n.nestingFactor}C.idealLength=P,C.elasticity=S,o.layoutEdges.push(C)}return o},Du=function(e,t,n){var r=_u(e,t,0,n);return 2>r.count?0:r.graph},_u=function(e,t,n,r){var a=r.graphSet[n];if(-1<a.indexOf(e)&&-1<a.indexOf(t))return{count:2,graph:n};for(var i=0,o=0;o<a.length;o++){var s=a[o],l=r.idToIndex[s],u=r.layoutNodes[l].children;if(0!==u.length){var c=r.indexToGraph[r.idToIndex[u[0]]],d=_u(e,t,c,r);if(0!==d.count){if(1!==d.count)return d;if(2===++i)break}}}return{count:i,graph:n}},Au=function(e,t){for(var n=e.clientWidth,r=e.clientHeight,a=0;a<e.nodeSize;a++){var i=e.layoutNodes[a];0!==i.children.length||i.isLocked||(i.positionX=Math.random()*n,i.positionY=Math.random()*r)}},Mu=function(e,t,n){var r=e.boundingBox,a={x1:1/0,x2:-1/0,y1:1/0,y2:-1/0};return t.boundingBox&&(n.forEach((function(t){var n=e.layoutNodes[e.idToIndex[t.data("id")]];a.x1=Math.min(a.x1,n.positionX),a.x2=Math.max(a.x2,n.positionX),a.y1=Math.min(a.y1,n.positionY),a.y2=Math.max(a.y2,n.positionY)})),a.w=a.x2-a.x1,a.h=a.y2-a.y1),function(n,i){var o=e.layoutNodes[e.idToIndex[n.data("id")]];if(t.boundingBox){var s=(o.positionX-a.x1)/a.w,l=(o.positionY-a.y1)/a.h;return{x:r.x1+s*r.w,y:r.y1+l*r.h}}return{x:o.positionX,y:o.positionY}}},Ru=function(e,t,n){var r=n.layout,a=n.eles.nodes(),i=Mu(e,n,a);a.positions(i),!0!==e.ready&&(e.ready=!0,r.one("layoutready",n.ready),r.emit({type:"layoutready",layout:this}))},Iu=function(e,t,n){Nu(e,t),Fu(e),ju(e,t),Xu(e),qu(e)},Nu=function(e,t){for(var n=0;n<e.graphSet.length;n++)for(var r=e.graphSet[n],a=r.length,i=0;i<a;i++)for(var o=e.layoutNodes[e.idToIndex[r[i]]],s=i+1;s<a;s++){var l=e.layoutNodes[e.idToIndex[r[s]]];zu(o,l,e,t)}},Lu=function(e){return 2*e*Math.random()-1},zu=function(e,t,n,r){if(e.cmptId===t.cmptId||n.isCompound){var a=t.positionX-e.positionX,i=t.positionY-e.positionY;0===a&&0===i&&(a=Lu(1),i=Lu(1));var o=Ou(e,t,a,i);if(o>0)var s=(u=r.nodeOverlap*o)*a/(g=Math.sqrt(a*a+i*i)),l=u*i/g;else{var u,c=Vu(e,a,i),d=Vu(t,-1*a,-1*i),h=d.x-c.x,f=d.y-c.y,p=h*h+f*f,g=Math.sqrt(p);s=(u=(e.nodeRepulsion+t.nodeRepulsion)/p)*h/g,l=u*f/g}e.isLocked||(e.offsetX-=s,e.offsetY-=l),t.isLocked||(t.offsetX+=s,t.offsetY+=l)}},Ou=function(e,t,n,r){if(n>0)var a=e.maxX-t.minX;else a=t.maxX-e.minX;if(r>0)var i=e.maxY-t.minY;else i=t.maxY-e.minY;return a>=0&&i>=0?Math.sqrt(a*a+i*i):0},Vu=function(e,t,n){var r=e.positionX,a=e.positionY,i=e.height||1,o=e.width||1,s=n/t,l=i/o,u={};return 0===t&&0<n||0===t&&0>n?(u.x=r,u.y=a+i/2,u):0<t&&-1*l<=s&&s<=l?(u.x=r+o/2,u.y=a+o*n/2/t,u):0>t&&-1*l<=s&&s<=l?(u.x=r-o/2,u.y=a-o*n/2/t,u):0<n&&(s<=-1*l||s>=l)?(u.x=r+i*t/2/n,u.y=a+i/2,u):0>n&&(s<=-1*l||s>=l)?(u.x=r-i*t/2/n,u.y=a-i/2,u):u},Fu=function(e,t){for(var n=0;n<e.edgeSize;n++){var r=e.layoutEdges[n],a=e.idToIndex[r.sourceId],i=e.layoutNodes[a],o=e.idToIndex[r.targetId],s=e.layoutNodes[o],l=s.positionX-i.positionX,u=s.positionY-i.positionY;if(0!==l||0!==u){var c=Vu(i,l,u),d=Vu(s,-1*l,-1*u),h=d.x-c.x,f=d.y-c.y,p=Math.sqrt(h*h+f*f),g=Math.pow(r.idealLength-p,2)/r.elasticity;if(0!==p)var v=g*h/p,y=g*f/p;else v=0,y=0;i.isLocked||(i.offsetX+=v,i.offsetY+=y),s.isLocked||(s.offsetX-=v,s.offsetY-=y)}}},ju=function(e,t){if(0!==t.gravity)for(var n=0;n<e.graphSet.length;n++){var r=e.graphSet[n],a=r.length;if(0===n)var i=e.clientHeight/2,o=e.clientWidth/2;else{var s=e.layoutNodes[e.idToIndex[r[0]]],l=e.layoutNodes[e.idToIndex[s.parentId]];i=l.positionX,o=l.positionY}for(var u=0;u<a;u++){var c=e.layoutNodes[e.idToIndex[r[u]]];if(!c.isLocked){var d=i-c.positionX,h=o-c.positionY,f=Math.sqrt(d*d+h*h);if(f>1){var p=t.gravity*d/f,g=t.gravity*h/f;c.offsetX+=p,c.offsetY+=g}}}}},Xu=function(e,t){var n=[],r=0,a=-1;for(n.push.apply(n,e.graphSet[0]),a+=e.graphSet[0].length;r<=a;){var i=n[r++],o=e.idToIndex[i],s=e.layoutNodes[o],l=s.children;if(0<l.length&&!s.isLocked){for(var u=s.offsetX,c=s.offsetY,d=0;d<l.length;d++){var h=e.layoutNodes[e.idToIndex[l[d]]];h.offsetX+=u,h.offsetY+=c,n[++a]=l[d]}s.offsetX=0,s.offsetY=0}}},qu=function(e,t){for(var n=0;n<e.nodeSize;n++){0<(a=e.layoutNodes[n]).children.length&&(a.maxX=void 0,a.minX=void 0,a.maxY=void 0,a.minY=void 0)}for(n=0;n<e.nodeSize;n++){if(!(0<(a=e.layoutNodes[n]).children.length||a.isLocked)){var r=Yu(a.offsetX,a.offsetY,e.temperature);a.positionX+=r.x,a.positionY+=r.y,a.offsetX=0,a.offsetY=0,a.minX=a.positionX-a.width,a.maxX=a.positionX+a.width,a.minY=a.positionY-a.height,a.maxY=a.positionY+a.height,Wu(a,e)}}for(n=0;n<e.nodeSize;n++){var a;0<(a=e.layoutNodes[n]).children.length&&!a.isLocked&&(a.positionX=(a.maxX+a.minX)/2,a.positionY=(a.maxY+a.minY)/2,a.width=a.maxX-a.minX,a.height=a.maxY-a.minY)}},Yu=function(e,t,n){var r=Math.sqrt(e*e+t*t);if(r>n)var a={x:n*e/r,y:n*t/r};else a={x:e,y:t};return a},Wu=function(e,t){var n=e.parentId;if(null!=n){var r=t.layoutNodes[t.idToIndex[n]],a=!1;return(null==r.maxX||e.maxX+r.padRight>r.maxX)&&(r.maxX=e.maxX+r.padRight,a=!0),(null==r.minX||e.minX-r.padLeft<r.minX)&&(r.minX=e.minX-r.padLeft,a=!0),(null==r.maxY||e.maxY+r.padBottom>r.maxY)&&(r.maxY=e.maxY+r.padBottom,a=!0),(null==r.minY||e.minY-r.padTop<r.minY)&&(r.minY=e.minY-r.padTop,a=!0),a?Wu(r,t):void 0}},Uu=function(e,t){for(var n=e.layoutNodes,r=[],a=0;a<n.length;a++){var i=n[a],o=i.cmptId;(r[o]=r[o]||[]).push(i)}var s=0;for(a=0;a<r.length;a++){if(g=r[a]){g.x1=1/0,g.x2=-1/0,g.y1=1/0,g.y2=-1/0;for(var l=0;l<g.length;l++){var u=g[l];g.x1=Math.min(g.x1,u.positionX-u.width/2),g.x2=Math.max(g.x2,u.positionX+u.width/2),g.y1=Math.min(g.y1,u.positionY-u.height/2),g.y2=Math.max(g.y2,u.positionY+u.height/2)}g.w=g.x2-g.x1,g.h=g.y2-g.y1,s+=g.w*g.h}}r.sort((function(e,t){return t.w*t.h-e.w*e.h}));var c=0,d=0,h=0,f=0,p=Math.sqrt(s)*e.clientWidth/e.clientHeight;for(a=0;a<r.length;a++){var g;if(g=r[a]){for(l=0;l<g.length;l++){(u=g[l]).isLocked||(u.positionX+=c-g.x1,u.positionY+=d-g.y1)}c+=g.w+t.componentSpacing,h+=g.w+t.componentSpacing,f=Math.max(f,g.h),h>p&&(d+=f+t.componentSpacing,c=0,h=0,f=0)}}},Hu={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,avoidOverlapPadding:10,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,condense:!1,rows:void 0,cols:void 0,position:function(e){},sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function Ku(e){this.options=me({},Hu,e)}Ku.prototype.run=function(){var e=this.options,t=e,n=e.cy,r=t.eles,a=r.nodes().not(":parent");t.sort&&(a=a.sort(t.sort));var i=Qt(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:n.width(),h:n.height()});if(0===i.h||0===i.w)r.nodes().layoutPositions(this,t,(function(e){return{x:i.x1,y:i.y1}}));else{var o=a.size(),s=Math.sqrt(o*i.h/i.w),l=Math.round(s),u=Math.round(i.w/i.h*s),c=function(e){if(null==e)return Math.min(l,u);Math.min(l,u)==l?l=e:u=e},d=function(e){if(null==e)return Math.max(l,u);Math.max(l,u)==l?l=e:u=e},h=t.rows,f=null!=t.cols?t.cols:t.columns;if(null!=h&&null!=f)l=h,u=f;else if(null!=h&&null==f)l=h,u=Math.ceil(o/l);else if(null==h&&null!=f)u=f,l=Math.ceil(o/u);else if(u*l>o){var p=c(),g=d();(p-1)*g>=o?c(p-1):(g-1)*p>=o&&d(g-1)}else for(;u*l<o;){var v=c(),y=d();(y+1)*v>=o?d(y+1):c(v+1)}var m=i.w/u,b=i.h/l;if(t.condense&&(m=0,b=0),t.avoidOverlap)for(var x=0;x<a.length;x++){var w=a[x],E=w._private.position;null!=E.x&&null!=E.y||(E.x=0,E.y=0);var k=w.layoutDimensions(t),T=t.avoidOverlapPadding,C=k.w+T,P=k.h+T;m=Math.max(m,C),b=Math.max(b,P)}for(var S={},B=function(e,t){return!!S["c-"+e+"-"+t]},D=function(e,t){S["c-"+e+"-"+t]=!0},_=0,A=0,M=function(){++A>=u&&(A=0,_++)},R={},I=0;I<a.length;I++){var N=a[I],L=t.position(N);if(L&&(void 0!==L.row||void 0!==L.col)){var z={row:L.row,col:L.col};if(void 0===z.col)for(z.col=0;B(z.row,z.col);)z.col++;else if(void 0===z.row)for(z.row=0;B(z.row,z.col);)z.row++;R[N.id()]=z,D(z.row,z.col)}}a.layoutPositions(this,t,(function(e,t){var n,r;if(e.locked()||e.isParent())return!1;var a=R[e.id()];if(a)n=a.col*m+m/2+i.x1,r=a.row*b+b/2+i.y1;else{for(;B(_,A);)M();n=A*m+m/2+i.x1,r=_*b+b/2+i.y1,D(_,A),M()}return{x:n,y:r}}))}return this};var Gu={ready:function(){},stop:function(){}};function Zu(e){this.options=me({},Gu,e)}Zu.prototype.run=function(){var e=this.options,t=e.eles,n=this;return e.cy,n.emit("layoutstart"),t.nodes().positions((function(){return{x:0,y:0}})),n.one("layoutready",e.ready),n.emit("layoutready"),n.one("layoutstop",e.stop),n.emit("layoutstop"),this},Zu.prototype.stop=function(){return this};var $u={positions:void 0,zoom:void 0,pan:void 0,fit:!0,padding:30,spacingFactor:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function Qu(e){this.options=me({},$u,e)}Qu.prototype.run=function(){var e=this.options,t=e.eles.nodes(),n=G(e.positions);return t.layoutPositions(this,e,(function(t,r){var a=function(t){if(null==e.positions)return function(e){return{x:e.x,y:e.y}}(t.position());if(n)return e.positions(t);var r=e.positions[t._private.data.id];return null==r?null:r}(t);return!t.locked()&&null!=a&&a})),this};var Ju={fit:!0,padding:30,boundingBox:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function ec(e){this.options=me({},Ju,e)}ec.prototype.run=function(){var e=this.options,t=e.cy,n=e.eles,r=Qt(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()});return n.nodes().layoutPositions(this,e,(function(e,t){return{x:r.x1+Math.round(Math.random()*r.w),y:r.y1+Math.round(Math.random()*r.h)}})),this};var tc=[{name:"breadthfirst",impl:xu},{name:"circle",impl:Eu},{name:"concentric",impl:Cu},{name:"cose",impl:Su},{name:"grid",impl:Ku},{name:"null",impl:Zu},{name:"preset",impl:Qu},{name:"random",impl:ec}];function nc(e){this.options=e,this.notifications=0}var rc=function(){},ac=function(){throw new Error("A headless instance can not render images")};nc.prototype={recalculateRenderedStyle:rc,notify:function(){this.notifications++},init:rc,isHeadless:function(){return!0},png:ac,jpg:ac};var ic={arrowShapeWidth:.3,registerArrowShapes:function(){var e=this.arrowShapes={},t=this,n=function(e,t,n,r,a,i,o){var s=a.x-n/2-o,l=a.x+n/2+o,u=a.y-n/2-o,c=a.y+n/2+o;return s<=e&&e<=l&&u<=t&&t<=c},r=function(e,t,n,r,a){var i=e*Math.cos(r)-t*Math.sin(r),o=(e*Math.sin(r)+t*Math.cos(r))*n;return{x:i*n+a.x,y:o+a.y}},a=function(e,t,n,a){for(var i=[],o=0;o<e.length;o+=2){var s=e[o],l=e[o+1];i.push(r(s,l,t,n,a))}return i},i=function(e){for(var t=[],n=0;n<e.length;n++){var r=e[n];t.push(r.x,r.y)}return t},o=function(e){return e.pstyle("width").pfValue*e.pstyle("arrow-scale").pfValue*2},s=function(r,s){K(s)&&(s=e[s]),e[r]=me({name:r,points:[-.15,-.3,.15,-.3,.15,.3,-.15,.3],collide:function(e,t,n,r,o,s){var l=i(a(this.points,n+2*s,r,o));return hn(e,t,l)},roughCollide:n,draw:function(e,n,r,i){var o=a(this.points,n,r,i);t.arrowShapeImpl("polygon")(e,o)},spacing:function(e){return 0},gap:o},s)};s("none",{collide:Je,roughCollide:Je,draw:tt,spacing:et,gap:et}),s("triangle",{points:[-.15,-.3,0,0,.15,-.3]}),s("arrow","triangle"),s("triangle-backcurve",{points:e.triangle.points,controlPoint:[0,-.15],roughCollide:n,draw:function(e,n,i,o,s){var l=a(this.points,n,i,o),u=this.controlPoint,c=r(u[0],u[1],n,i,o);t.arrowShapeImpl(this.name)(e,l,c)},gap:function(e){return.8*o(e)}}),s("triangle-tee",{points:[0,0,.15,-.3,-.15,-.3,0,0],pointsTee:[-.15,-.4,-.15,-.5,.15,-.5,.15,-.4],collide:function(e,t,n,r,o,s,l){var u=i(a(this.points,n+2*l,r,o)),c=i(a(this.pointsTee,n+2*l,r,o));return hn(e,t,u)||hn(e,t,c)},draw:function(e,n,r,i,o){var s=a(this.points,n,r,i),l=a(this.pointsTee,n,r,i);t.arrowShapeImpl(this.name)(e,s,l)}}),s("circle-triangle",{radius:.15,pointsTr:[0,-.15,.15,-.45,-.15,-.45,0,-.15],collide:function(e,t,n,r,o,s,l){var u=o,c=Math.pow(u.x-e,2)+Math.pow(u.y-t,2)<=Math.pow((n+2*l)*this.radius,2),d=i(a(this.points,n+2*l,r,o));return hn(e,t,d)||c},draw:function(e,n,r,i,o){var s=a(this.pointsTr,n,r,i);t.arrowShapeImpl(this.name)(e,s,i.x,i.y,this.radius*n)},spacing:function(e){return t.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.radius}}),s("triangle-cross",{points:[0,0,.15,-.3,-.15,-.3,0,0],baseCrossLinePts:[-.15,-.4,-.15,-.4,.15,-.4,.15,-.4],crossLinePts:function(e,t){var n=this.baseCrossLinePts.slice(),r=t/e;return n[3]=n[3]-r,n[5]=n[5]-r,n},collide:function(e,t,n,r,o,s,l){var u=i(a(this.points,n+2*l,r,o)),c=i(a(this.crossLinePts(n,s),n+2*l,r,o));return hn(e,t,u)||hn(e,t,c)},draw:function(e,n,r,i,o){var s=a(this.points,n,r,i),l=a(this.crossLinePts(n,o),n,r,i);t.arrowShapeImpl(this.name)(e,s,l)}}),s("vee",{points:[-.15,-.3,0,0,.15,-.3,0,-.15],gap:function(e){return.525*o(e)}}),s("circle",{radius:.15,collide:function(e,t,n,r,a,i,o){var s=a;return Math.pow(s.x-e,2)+Math.pow(s.y-t,2)<=Math.pow((n+2*o)*this.radius,2)},draw:function(e,n,r,a,i){t.arrowShapeImpl(this.name)(e,a.x,a.y,this.radius*n)},spacing:function(e){return t.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.radius}}),s("tee",{points:[-.15,0,-.15,-.1,.15,-.1,.15,0],spacing:function(e){return 1},gap:function(e){return 1}}),s("square",{points:[-.15,0,.15,0,.15,-.3,-.15,-.3]}),s("diamond",{points:[-.15,-.15,0,-.3,.15,-.15,0,0],gap:function(e){return e.pstyle("width").pfValue*e.pstyle("arrow-scale").value}}),s("chevron",{points:[0,0,-.15,-.15,-.1,-.2,0,-.1,.1,-.2,.15,-.15],gap:function(e){return.95*e.pstyle("width").pfValue*e.pstyle("arrow-scale").value}})}},oc={projectIntoViewport:function(e,t){var n=this.cy,r=this.findContainerClientCoords(),a=r[0],i=r[1],o=r[4],s=n.pan(),l=n.zoom();return[((e-a)/o-s.x)/l,((t-i)/o-s.y)/l]},findContainerClientCoords:function(){if(this.containerBB)return this.containerBB;var e=this.container,t=e.getBoundingClientRect(),n=this.cy.window().getComputedStyle(e),r=function(e){return parseFloat(n.getPropertyValue(e))},a=r("padding-left"),i=r("padding-right"),o=r("padding-top"),s=r("padding-bottom"),l=r("border-left-width"),u=r("border-right-width"),c=r("border-top-width"),d=(r("border-bottom-width"),e.clientWidth),h=e.clientHeight,f=a+i,p=o+s,g=l+u,v=t.width/(d+g),y=d-f,m=h-p,b=t.left+a+l,x=t.top+o+c;return this.containerBB=[b,x,y,m,v]},invalidateContainerClientCoordsCache:function(){this.containerBB=null},findNearestElement:function(e,t,n,r){return this.findNearestElements(e,t,n,r)[0]},findNearestElements:function(e,t,n,r){var a,i,o=this,s=this,l=s.getCachedZSortedEles(),u=[],c=s.cy.zoom(),d=s.cy.hasCompoundNodes(),h=(r?24:8)/c,f=(r?8:2)/c,p=(r?8:2)/c,g=1/0;function v(e,t){if(e.isNode()){if(i)return;i=e,u.push(e)}if(e.isEdge()&&(null==t||t<g))if(a){if(a.pstyle("z-compound-depth").value===e.pstyle("z-compound-depth").value&&a.pstyle("z-compound-depth").value===e.pstyle("z-compound-depth").value)for(var n=0;n<u.length;n++)if(u[n].isEdge()){u[n]=e,a=e,g=null!=t?t:g;break}}else u.push(e),a=e,g=null!=t?t:g}function y(n){var r=n.outerWidth()+2*f,a=n.outerHeight()+2*f,i=r/2,l=a/2,u=n.position(),c="auto"===n.pstyle("corner-radius").value?"auto":n.pstyle("corner-radius").pfValue,d=n._private.rscratch;if(u.x-i<=e&&e<=u.x+i&&u.y-l<=t&&t<=u.y+l&&s.nodeShapes[o.getNodeShape(n)].checkPoint(e,t,0,r,a,u.x,u.y,c,d))return v(n,0),!0}function m(n){var r,a=n._private,i=a.rscratch,l=n.pstyle("width").pfValue,c=n.pstyle("arrow-scale").value,f=l/2+h,p=f*f,g=2*f,m=a.source,b=a.target;if("segments"===i.edgeType||"straight"===i.edgeType||"haystack"===i.edgeType){for(var x=i.allpts,w=0;w+3<x.length;w+=2)if(ln(e,t,x[w],x[w+1],x[w+2],x[w+3],g)&&p>(r=dn(e,t,x[w],x[w+1],x[w+2],x[w+3])))return v(n,r),!0}else if("bezier"===i.edgeType||"multibezier"===i.edgeType||"self"===i.edgeType||"compound"===i.edgeType)for(x=i.allpts,w=0;w+5<i.allpts.length;w+=4)if(un(e,t,x[w],x[w+1],x[w+2],x[w+3],x[w+4],x[w+5],g)&&p>(r=cn(e,t,x[w],x[w+1],x[w+2],x[w+3],x[w+4],x[w+5])))return v(n,r),!0;m=m||a.source,b=b||a.target;var E=o.getArrowWidth(l,c),k=[{name:"source",x:i.arrowStartX,y:i.arrowStartY,angle:i.srcArrowAngle},{name:"target",x:i.arrowEndX,y:i.arrowEndY,angle:i.tgtArrowAngle},{name:"mid-source",x:i.midX,y:i.midY,angle:i.midsrcArrowAngle},{name:"mid-target",x:i.midX,y:i.midY,angle:i.midtgtArrowAngle}];for(w=0;w<k.length;w++){var T=k[w],C=s.arrowShapes[n.pstyle(T.name+"-arrow-shape").value],P=n.pstyle("width").pfValue;if(C.roughCollide(e,t,E,T.angle,{x:T.x,y:T.y},P,h)&&C.collide(e,t,E,T.angle,{x:T.x,y:T.y},P,h))return v(n),!0}d&&u.length>0&&(y(m),y(b))}function b(e,t,n){return ht(e,t,n)}function x(n,r){var a,i=n._private,o=p;a=r?r+"-":"",n.boundingBox();var s=i.labelBounds[r||"main"],l=n.pstyle(a+"label").value;if("yes"===n.pstyle("text-events").strValue&&l){var u=b(i.rscratch,"labelX",r),c=b(i.rscratch,"labelY",r),d=b(i.rscratch,"labelAngle",r),h=n.pstyle(a+"text-margin-x").pfValue,f=n.pstyle(a+"text-margin-y").pfValue,g=s.x1-o-h,y=s.x2+o-h,m=s.y1-o-f,x=s.y2+o-f;if(d){var w=Math.cos(d),E=Math.sin(d),k=function(e,t){return{x:(e-=u)*w-(t-=c)*E+u,y:e*E+t*w+c}},T=k(g,m),C=k(g,x),P=k(y,m),S=k(y,x),B=[T.x+h,T.y+f,P.x+h,P.y+f,S.x+h,S.y+f,C.x+h,C.y+f];if(hn(e,t,B))return v(n),!0}else if(on(s,e,t))return v(n),!0}}n&&(l=l.interactive);for(var w=l.length-1;w>=0;w--){var E=l[w];E.isNode()?y(E)||x(E):m(E)||x(E)||x(E,"source")||x(E,"target")}return u},getAllInBox:function(e,t,n,r){var a,i,o=this.getCachedZSortedEles().interactive,s=2/this.cy.zoom(),l=[],u=Math.min(e,n),c=Math.max(e,n),d=Math.min(t,r),h=Math.max(t,r),f=Qt({x1:e=u,y1:t=d,x2:n=c,y2:r=h});function p(e,t,n){return ht(e,t,n)}function g(e,t){var n=e._private,r=s;e.boundingBox();var a=n.labelBounds.main,i=p(n.rscratch,"labelX",t),o=p(n.rscratch,"labelY",t),l=p(n.rscratch,"labelAngle",t),u=e.pstyle("text-margin-x").pfValue,c=e.pstyle("text-margin-y").pfValue,d=a.x1-r-u,h=a.x2+r-u,f=a.y1-r-c,g=a.y2+r-c;if(l){var v=Math.cos(l),y=Math.sin(l),m=function(e,t){return{x:(e-=i)*v-(t-=o)*y+i,y:e*y+t*v+o}};return[m(d,f),m(h,f),m(h,g),m(d,g)]}return[{x:d,y:f},{x:h,y:f},{x:h,y:g},{x:d,y:g}]}for(var v=0;v<o.length;v++){var y=o[v];if(y.isNode()){var m=y,b="yes"===m.pstyle("text-events").strValue,x="yes"===m.pstyle("box-select-labels").strValue,w=m.boundingBox({includeNodes:!0,includeEdges:!1,includeLabels:x&&b});if(an(f,w))Bn(g(m),[{x:f.x1,y:f.y1},{x:f.x2,y:f.y1},{x:f.x2,y:f.y2},{x:f.x1,y:f.y2}])&&l.push(m)}else{var E=y,k=E._private,T=k.rscratch;if(null!=T.startX&&null!=T.startY&&!on(f,T.startX,T.startY))continue;if(null!=T.endX&&null!=T.endY&&!on(f,T.endX,T.endY))continue;if("bezier"===T.edgeType||"multibezier"===T.edgeType||"self"===T.edgeType||"compound"===T.edgeType||"segments"===T.edgeType||"haystack"===T.edgeType){for(var C=k.rstyle.bezierPts||k.rstyle.linePts||k.rstyle.haystackPts,P=!0,S=0;S<C.length;S++)if(a=f,i=C[S],!on(a,i.x,i.y)){P=!1;break}P&&l.push(E)}else"haystack"!==T.edgeType&&"straight"!==T.edgeType||l.push(E)}}return l}},sc={calculateArrowAngles:function(e){var t,n,r,a,i,o,s=e._private.rscratch,l="haystack"===s.edgeType,u="bezier"===s.edgeType,c="multibezier"===s.edgeType,d="segments"===s.edgeType,h="compound"===s.edgeType,f="self"===s.edgeType;if(l?(r=s.haystackPts[0],a=s.haystackPts[1],i=s.haystackPts[2],o=s.haystackPts[3]):(r=s.arrowStartX,a=s.arrowStartY,i=s.arrowEndX,o=s.arrowEndY),g=s.midX,v=s.midY,d)t=r-s.segpts[0],n=a-s.segpts[1];else if(c||h||f||u){var p=s.allpts;t=r-Gt(p[0],p[2],p[4],.1),n=a-Gt(p[1],p[3],p[5],.1)}else t=r-g,n=a-v;s.srcArrowAngle=qt(t,n);var g=s.midX,v=s.midY;if(l&&(g=(r+i)/2,v=(a+o)/2),t=i-r,n=o-a,d)if((p=s.allpts).length/2%2==0){var y=(C=p.length/2)-2;t=p[C]-p[y],n=p[C+1]-p[y+1]}else if(s.isRound)t=s.midVector[1],n=-s.midVector[0];else{y=(C=p.length/2-1)-2;t=p[C]-p[y],n=p[C+1]-p[y+1]}else if(c||h||f){var m,b,x,w,p=s.allpts;if(s.ctrlpts.length/2%2==0){var E=(k=(T=p.length/2-1)+2)+2;m=Gt(p[T],p[k],p[E],0),b=Gt(p[T+1],p[k+1],p[E+1],0),x=Gt(p[T],p[k],p[E],1e-4),w=Gt(p[T+1],p[k+1],p[E+1],1e-4)}else{var k,T;E=(k=p.length/2-1)+2;m=Gt(p[T=k-2],p[k],p[E],.4999),b=Gt(p[T+1],p[k+1],p[E+1],.4999),x=Gt(p[T],p[k],p[E],.5),w=Gt(p[T+1],p[k+1],p[E+1],.5)}t=x-m,n=w-b}if(s.midtgtArrowAngle=qt(t,n),s.midDispX=t,s.midDispY=n,t*=-1,n*=-1,d)if((p=s.allpts).length/2%2==0);else if(!s.isRound){var C,P=(C=p.length/2-1)+2;t=-(p[P]-p[C]),n=-(p[P+1]-p[C+1])}if(s.midsrcArrowAngle=qt(t,n),d)t=i-s.segpts[s.segpts.length-2],n=o-s.segpts[s.segpts.length-1];else if(c||h||f||u){var S=(p=s.allpts).length;t=i-Gt(p[S-6],p[S-4],p[S-2],.9),n=o-Gt(p[S-5],p[S-3],p[S-1],.9)}else t=i-g,n=o-v;s.tgtArrowAngle=qt(t,n)}};sc.getArrowWidth=sc.getArrowHeight=function(e,t){var n=this.arrowWidthCache=this.arrowWidthCache||{},r=n[e+", "+t];return r||(r=Math.max(Math.pow(13.37*e,.9),29)*t,n[e+", "+t]=r,r)};var lc,uc,cc,dc,hc,fc,pc,gc,vc,yc,mc,bc,xc,wc,Ec,kc,Tc,Cc={},Pc={},Sc=function(e,t,n){n.x=t.x-e.x,n.y=t.y-e.y,n.len=Math.sqrt(n.x*n.x+n.y*n.y),n.nx=n.x/n.len,n.ny=n.y/n.len,n.ang=Math.atan2(n.ny,n.nx)},Bc=function(e,t,n,r,a){var i,o;if(e!==Tc?Sc(t,e,Cc):((o=Cc).x=-1*(i=Pc).x,o.y=-1*i.y,o.nx=-1*i.nx,o.ny=-1*i.ny,o.ang=i.ang>0?-(Math.PI-i.ang):Math.PI+i.ang),Sc(t,n,Pc),cc=Cc.nx*Pc.ny-Cc.ny*Pc.nx,dc=Cc.nx*Pc.nx-Cc.ny*-Pc.ny,pc=Math.asin(Math.max(-1,Math.min(1,cc))),Math.abs(pc)<1e-6)return lc=t.x,uc=t.y,void(vc=mc=0);hc=1,fc=!1,dc<0?pc<0?pc=Math.PI+pc:(pc=Math.PI-pc,hc=-1,fc=!0):pc>0&&(hc=-1,fc=!0),mc=void 0!==t.radius?t.radius:r,gc=pc/2,bc=Math.min(Cc.len/2,Pc.len/2),a?(yc=Math.abs(Math.cos(gc)*mc/Math.sin(gc)))>bc?(yc=bc,vc=Math.abs(yc*Math.sin(gc)/Math.cos(gc))):vc=mc:(yc=Math.min(bc,mc),vc=Math.abs(yc*Math.sin(gc)/Math.cos(gc))),Ec=t.x+Pc.nx*yc,kc=t.y+Pc.ny*yc,lc=Ec-Pc.ny*vc*hc,uc=kc+Pc.nx*vc*hc,xc=t.x+Cc.nx*yc,wc=t.y+Cc.ny*yc,Tc=t};function Dc(e,t){0===t.radius?e.lineTo(t.cx,t.cy):e.arc(t.cx,t.cy,t.radius,t.startAngle,t.endAngle,t.counterClockwise)}function _c(e,t,n,r){var a=!(arguments.length>4&&void 0!==arguments[4])||arguments[4];return 0===r||0===t.radius?{cx:t.x,cy:t.y,radius:0,startX:t.x,startY:t.y,stopX:t.x,stopY:t.y,startAngle:void 0,endAngle:void 0,counterClockwise:void 0}:(Bc(e,t,n,r,a),{cx:lc,cy:uc,radius:vc,startX:xc,startY:wc,stopX:Ec,stopY:kc,startAngle:Cc.ang+Math.PI/2*hc,endAngle:Pc.ang-Math.PI/2*hc,counterClockwise:fc})}var Ac=.01,Mc=Math.sqrt(.02),Rc={};function Ic(e){var t=[];if(null!=e){for(var n=0;n<e.length;n+=2){var r=e[n],a=e[n+1];t.push({x:r,y:a})}return t}}Rc.findMidptPtsEtc=function(e,t){var n,r=t.posPts,a=t.intersectionPts,i=t.vectorNormInverse,o=e.pstyle("source-endpoint"),s=e.pstyle("target-endpoint"),u=null!=o.units&&null!=s.units;switch(e.pstyle("edge-distances").value){case"node-position":n=r;break;case"intersection":n=a;break;case"endpoints":if(u){var c=l(this.manualEndptToPx(e.source()[0],o),2),d=c[0],h=c[1],f=l(this.manualEndptToPx(e.target()[0],s),2),p=f[0],g=f[1],v={x1:d,y1:h,x2:p,y2:g};i=function(e,t,n,r){var a=r-t,i=n-e,o=Math.sqrt(i*i+a*a);return{x:-a/o,y:i/o}}(d,h,p,g),n=v}else at("Edge ".concat(e.id()," has edge-distances:endpoints specified without manual endpoints specified via source-endpoint and target-endpoint.  Falling back on edge-distances:intersection (default).")),n=a}return{midptPts:n,vectorNormInverse:i}},Rc.findHaystackPoints=function(e){for(var t=0;t<e.length;t++){var n=e[t],r=n._private,a=r.rscratch;if(!a.haystack){var i=2*Math.random()*Math.PI;a.source={x:Math.cos(i),y:Math.sin(i)},i=2*Math.random()*Math.PI,a.target={x:Math.cos(i),y:Math.sin(i)}}var o=r.source,s=r.target,l=o.position(),u=s.position(),c=o.width(),d=s.width(),h=o.height(),f=s.height(),p=n.pstyle("haystack-radius").value/2;a.haystackPts=a.allpts=[a.source.x*c*p+l.x,a.source.y*h*p+l.y,a.target.x*d*p+u.x,a.target.y*f*p+u.y],a.midX=(a.allpts[0]+a.allpts[2])/2,a.midY=(a.allpts[1]+a.allpts[3])/2,a.edgeType="haystack",a.haystack=!0,this.storeEdgeProjections(n),this.calculateArrowAngles(n),this.recalculateEdgeLabelProjections(n),this.calculateLabelAngles(n)}},Rc.findSegmentsPoints=function(e,t){var n=e._private.rscratch,r=e.pstyle("segment-weights"),a=e.pstyle("segment-distances"),i=e.pstyle("segment-radii"),o=e.pstyle("radius-type"),s=Math.min(r.pfValue.length,a.pfValue.length),l=i.pfValue[i.pfValue.length-1],u=o.pfValue[o.pfValue.length-1];n.edgeType="segments",n.segpts=[],n.radii=[],n.isArcRadius=[];for(var c=0;c<s;c++){var d=r.pfValue[c],h=a.pfValue[c],f=1-d,p=d,g=this.findMidptPtsEtc(e,t),v=g.midptPts,y=g.vectorNormInverse,m={x:v.x1*f+v.x2*p,y:v.y1*f+v.y2*p};n.segpts.push(m.x+y.x*h,m.y+y.y*h),n.radii.push(void 0!==i.pfValue[c]?i.pfValue[c]:l),n.isArcRadius.push("arc-radius"===(void 0!==o.pfValue[c]?o.pfValue[c]:u))}},Rc.findLoopPoints=function(e,t,n,r){var a=e._private.rscratch,i=t.dirCounts,o=t.srcPos,s=e.pstyle("control-point-distances"),l=s?s.pfValue[0]:void 0,u=e.pstyle("loop-direction").pfValue,c=e.pstyle("loop-sweep").pfValue,d=e.pstyle("control-point-step-size").pfValue;a.edgeType="self";var h=n,f=d;r&&(h=0,f=l);var p=u-Math.PI/2,g=p-c/2,v=p+c/2,y=String(u+"_"+c);h=void 0===i[y]?i[y]=0:++i[y],a.ctrlpts=[o.x+1.4*Math.cos(g)*f*(h/3+1),o.y+1.4*Math.sin(g)*f*(h/3+1),o.x+1.4*Math.cos(v)*f*(h/3+1),o.y+1.4*Math.sin(v)*f*(h/3+1)]},Rc.findCompoundLoopPoints=function(e,t,n,r){var a=e._private.rscratch;a.edgeType="compound";var i=t.srcPos,o=t.tgtPos,s=t.srcW,l=t.srcH,u=t.tgtW,c=t.tgtH,d=e.pstyle("control-point-step-size").pfValue,h=e.pstyle("control-point-distances"),f=h?h.pfValue[0]:void 0,p=n,g=d;r&&(p=0,g=f);var v={x:i.x-s/2,y:i.y-l/2},y={x:o.x-u/2,y:o.y-c/2},m={x:Math.min(v.x,y.x),y:Math.min(v.y,y.y)},b=Math.max(.5,Math.log(s*Ac)),x=Math.max(.5,Math.log(u*Ac));a.ctrlpts=[m.x,m.y-(1+Math.pow(50,1.12)/100)*g*(p/3+1)*b,m.x-(1+Math.pow(50,1.12)/100)*g*(p/3+1)*x,m.y]},Rc.findStraightEdgePoints=function(e){e._private.rscratch.edgeType="straight"},Rc.findBezierPoints=function(e,t,n,r,a){var i=e._private.rscratch,o=e.pstyle("control-point-step-size").pfValue,s=e.pstyle("control-point-distances"),l=e.pstyle("control-point-weights"),u=s&&l?Math.min(s.value.length,l.value.length):1,c=s?s.pfValue[0]:void 0,d=l.value[0],h=r;i.edgeType=h?"multibezier":"bezier",i.ctrlpts=[];for(var f=0;f<u;f++){var p=(.5-t.eles.length/2+n)*o*(a?-1:1),g=void 0,v=Wt(p);h&&(c=s?s.pfValue[f]:o,d=l.value[f]);var y=void 0!==(g=r?c:void 0!==c?v*c:void 0)?g:p,m=1-d,b=d,x=this.findMidptPtsEtc(e,t),w=x.midptPts,E=x.vectorNormInverse,k={x:w.x1*m+w.x2*b,y:w.y1*m+w.y2*b};i.ctrlpts.push(k.x+E.x*y,k.y+E.y*y)}},Rc.findTaxiPoints=function(e,t){var n=e._private.rscratch;n.edgeType="segments";var r="vertical",a="horizontal",i="leftward",o="rightward",s="downward",l="upward",u=t.posPts,c=t.srcW,d=t.srcH,h=t.tgtW,f=t.tgtH,p="node-position"!==e.pstyle("edge-distances").value,g=e.pstyle("taxi-direction").value,v=g,y=e.pstyle("taxi-turn"),m="%"===y.units,b=y.pfValue,x=b<0,w=e.pstyle("taxi-turn-min-distance").pfValue,E=p?(c+h)/2:0,k=p?(d+f)/2:0,T=u.x2-u.x1,C=u.y2-u.y1,P=function(e,t){return e>0?Math.max(e-t,0):Math.min(e+t,0)},S=P(T,E),B=P(C,k),D=!1;"auto"===v?g=Math.abs(S)>Math.abs(B)?a:r:v===l||v===s?(g=r,D=!0):v!==i&&v!==o||(g=a,D=!0);var _,A=g===r,M=A?B:S,R=A?C:T,I=Wt(R),N=!1;(D&&(m||x)||!(v===s&&R<0||v===l&&R>0||v===i&&R>0||v===o&&R<0)||(M=(I*=-1)*Math.abs(M),N=!0),m)?_=(b<0?1+b:b)*M:_=(b<0?M:0)+b*I;var L=function(e){return Math.abs(e)<w||Math.abs(e)>=Math.abs(M)},z=L(_),O=L(Math.abs(M)-Math.abs(_));if((z||O)&&!N)if(A){var V=Math.abs(R)<=d/2,F=Math.abs(T)<=h/2;if(V){var j=(u.x1+u.x2)/2,X=u.y1,q=u.y2;n.segpts=[j,X,j,q]}else if(F){var Y=(u.y1+u.y2)/2,W=u.x1,U=u.x2;n.segpts=[W,Y,U,Y]}else n.segpts=[u.x1,u.y2]}else{var H=Math.abs(R)<=c/2,K=Math.abs(C)<=f/2;if(H){var G=(u.y1+u.y2)/2,Z=u.x1,$=u.x2;n.segpts=[Z,G,$,G]}else if(K){var Q=(u.x1+u.x2)/2,J=u.y1,ee=u.y2;n.segpts=[Q,J,Q,ee]}else n.segpts=[u.x2,u.y1]}else if(A){var te=u.y1+_+(p?d/2*I:0),ne=u.x1,re=u.x2;n.segpts=[ne,te,re,te]}else{var ae=u.x1+_+(p?c/2*I:0),ie=u.y1,oe=u.y2;n.segpts=[ae,ie,ae,oe]}if(n.isRound){var se=e.pstyle("taxi-radius").value,le="arc-radius"===e.pstyle("radius-type").value[0];n.radii=new Array(n.segpts.length/2).fill(se),n.isArcRadius=new Array(n.segpts.length/2).fill(le)}},Rc.tryToCorrectInvalidPoints=function(e,t){var n=e._private.rscratch;if("bezier"===n.edgeType){var r=t.srcPos,a=t.tgtPos,i=t.srcW,o=t.srcH,s=t.tgtW,l=t.tgtH,u=t.srcShape,c=t.tgtShape,d=t.srcCornerRadius,h=t.tgtCornerRadius,f=t.srcRs,p=t.tgtRs,g=!Q(n.startX)||!Q(n.startY),v=!Q(n.arrowStartX)||!Q(n.arrowStartY),y=!Q(n.endX)||!Q(n.endY),m=!Q(n.arrowEndX)||!Q(n.arrowEndY),b=3*(this.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.arrowShapeWidth),x=Ut({x:n.ctrlpts[0],y:n.ctrlpts[1]},{x:n.startX,y:n.startY}),w=x<b,E=Ut({x:n.ctrlpts[0],y:n.ctrlpts[1]},{x:n.endX,y:n.endY}),k=E<b,T=!1;if(g||v||w){T=!0;var C={x:n.ctrlpts[0]-r.x,y:n.ctrlpts[1]-r.y},P=Math.sqrt(C.x*C.x+C.y*C.y),S={x:C.x/P,y:C.y/P},B=Math.max(i,o),D={x:n.ctrlpts[0]+2*S.x*B,y:n.ctrlpts[1]+2*S.y*B},_=u.intersectLine(r.x,r.y,i,o,D.x,D.y,0,d,f);w?(n.ctrlpts[0]=n.ctrlpts[0]+S.x*(b-x),n.ctrlpts[1]=n.ctrlpts[1]+S.y*(b-x)):(n.ctrlpts[0]=_[0]+S.x*b,n.ctrlpts[1]=_[1]+S.y*b)}if(y||m||k){T=!0;var A={x:n.ctrlpts[0]-a.x,y:n.ctrlpts[1]-a.y},M=Math.sqrt(A.x*A.x+A.y*A.y),R={x:A.x/M,y:A.y/M},I=Math.max(i,o),N={x:n.ctrlpts[0]+2*R.x*I,y:n.ctrlpts[1]+2*R.y*I},L=c.intersectLine(a.x,a.y,s,l,N.x,N.y,0,h,p);k?(n.ctrlpts[0]=n.ctrlpts[0]+R.x*(b-E),n.ctrlpts[1]=n.ctrlpts[1]+R.y*(b-E)):(n.ctrlpts[0]=L[0]+R.x*b,n.ctrlpts[1]=L[1]+R.y*b)}T&&this.findEndpoints(e)}},Rc.storeAllpts=function(e){var t=e._private.rscratch;if("multibezier"===t.edgeType||"bezier"===t.edgeType||"self"===t.edgeType||"compound"===t.edgeType){t.allpts=[],t.allpts.push(t.startX,t.startY);for(var n=0;n+1<t.ctrlpts.length;n+=2)t.allpts.push(t.ctrlpts[n],t.ctrlpts[n+1]),n+3<t.ctrlpts.length&&t.allpts.push((t.ctrlpts[n]+t.ctrlpts[n+2])/2,(t.ctrlpts[n+1]+t.ctrlpts[n+3])/2);var r;t.allpts.push(t.endX,t.endY),t.ctrlpts.length/2%2==0?(r=t.allpts.length/2-1,t.midX=t.allpts[r],t.midY=t.allpts[r+1]):(r=t.allpts.length/2-3,t.midX=Gt(t.allpts[r],t.allpts[r+2],t.allpts[r+4],.5),t.midY=Gt(t.allpts[r+1],t.allpts[r+3],t.allpts[r+5],.5))}else if("straight"===t.edgeType)t.allpts=[t.startX,t.startY,t.endX,t.endY],t.midX=(t.startX+t.endX+t.arrowStartX+t.arrowEndX)/4,t.midY=(t.startY+t.endY+t.arrowStartY+t.arrowEndY)/4;else if("segments"===t.edgeType){if(t.allpts=[],t.allpts.push(t.startX,t.startY),t.allpts.push.apply(t.allpts,t.segpts),t.allpts.push(t.endX,t.endY),t.isRound){t.roundCorners=[];for(var a=2;a+3<t.allpts.length;a+=2){var i=t.radii[a/2-1],o=t.isArcRadius[a/2-1];t.roundCorners.push(_c({x:t.allpts[a-2],y:t.allpts[a-1]},{x:t.allpts[a],y:t.allpts[a+1],radius:i},{x:t.allpts[a+2],y:t.allpts[a+3]},i,o))}}if(t.segpts.length%4==0){var s=t.segpts.length/2,l=s-2;t.midX=(t.segpts[l]+t.segpts[s])/2,t.midY=(t.segpts[l+1]+t.segpts[s+1])/2}else{var u=t.segpts.length/2-1;if(t.isRound){var c={x:t.segpts[u],y:t.segpts[u+1]},d=t.roundCorners[u/2];if(0===d.radius){var h={x:t.segpts[u+2],y:t.segpts[u+3]};t.midX=c.x,t.midY=c.y,t.midVector=[c.y-h.y,h.x-c.x]}else{var f=[c.x-d.cx,c.y-d.cy],p=d.radius/Math.sqrt(Math.pow(f[0],2)+Math.pow(f[1],2));f=f.map((function(e){return e*p})),t.midX=d.cx+f[0],t.midY=d.cy+f[1],t.midVector=f}}else t.midX=t.segpts[u],t.midY=t.segpts[u+1]}}},Rc.checkForInvalidEdgeWarning=function(e){var t=e[0]._private.rscratch;t.nodesOverlap||Q(t.startX)&&Q(t.startY)&&Q(t.endX)&&Q(t.endY)?t.loggedErr=!1:t.loggedErr||(t.loggedErr=!0,at("Edge `"+e.id()+"` has invalid endpoints and so it is impossible to draw.  Adjust your edge style (e.g. control points) accordingly or use an alternative edge type.  This is expected behaviour when the source node and the target node overlap."))},Rc.findEdgeControlPoints=function(e){var t=this;if(e&&0!==e.length){for(var n=this,r=n.cy.hasCompoundNodes(),a=new pt,i=function(e,t){return[].concat(u(e),[t?1:0]).join("-")},o=[],s=[],l=0;l<e.length;l++){var c=e[l],d=c._private,h=c.pstyle("curve-style").value;if(!c.removed()&&c.takesUpSpace())if("haystack"!==h){var f="unbundled-bezier"===h||h.endsWith("segments")||"straight"===h||"straight-triangle"===h||h.endsWith("taxi"),p="unbundled-bezier"===h||"bezier"===h,g=d.source,v=d.target,y=[g.poolIndex(),v.poolIndex()].sort(),m=i(y,f),b=a.get(m);null==b&&(b={eles:[]},o.push({pairId:y,edgeIsUnbundled:f}),a.set(m,b)),b.eles.push(c),f&&(b.hasUnbundled=!0),p&&(b.hasBezier=!0)}else s.push(c)}for(var x=function(){var e,s=o[w],l=s.pairId,u=s.edgeIsUnbundled,c=i(l,u),d=a.get(c);if(!d.hasUnbundled){var h=d.eles[0].parallelEdges().filter((function(e){return e.isBundledBezier()}));dt(d.eles),h.forEach((function(e){return d.eles.push(e)})),d.eles.sort((function(e,t){return e.poolIndex()-t.poolIndex()}))}var f=d.eles[0],p=f.source(),g=f.target();if(p.poolIndex()>g.poolIndex()){var v=p;p=g,g=v}var y=d.srcPos=p.position(),m=d.tgtPos=g.position(),b=d.srcW=p.outerWidth(),x=d.srcH=p.outerHeight(),E=d.tgtW=g.outerWidth(),k=d.tgtH=g.outerHeight(),T=d.srcShape=n.nodeShapes[t.getNodeShape(p)],C=d.tgtShape=n.nodeShapes[t.getNodeShape(g)],P=d.srcCornerRadius="auto"===p.pstyle("corner-radius").value?"auto":p.pstyle("corner-radius").pfValue,S=d.tgtCornerRadius="auto"===g.pstyle("corner-radius").value?"auto":g.pstyle("corner-radius").pfValue,B=d.tgtRs=g._private.rscratch,D=d.srcRs=p._private.rscratch;d.dirCounts={north:0,west:0,south:0,east:0,northwest:0,southwest:0,northeast:0,southeast:0};for(var _=0;_<d.eles.length;_++){var A=d.eles[_],M=A[0]._private.rscratch,R=A.pstyle("curve-style").value,I="unbundled-bezier"===R||R.endsWith("segments")||R.endsWith("taxi"),N=!p.same(A.source());if(!d.calculatedIntersection&&p!==g&&(d.hasBezier||d.hasUnbundled)){d.calculatedIntersection=!0;var L=T.intersectLine(y.x,y.y,b,x,m.x,m.y,0,P,D),z=d.srcIntn=L,O=C.intersectLine(m.x,m.y,E,k,y.x,y.y,0,S,B),V=d.tgtIntn=O,F=d.intersectionPts={x1:L[0],x2:O[0],y1:L[1],y2:O[1]},j=d.posPts={x1:y.x,x2:m.x,y1:y.y,y2:m.y},X=O[1]-L[1],q=O[0]-L[0],Y=Math.sqrt(q*q+X*X);Q(Y)&&Y>=Mc||(Y=Math.sqrt(Math.max(q*q,Ac)+Math.max(X*X,Ac)));var W=d.vector={x:q,y:X},U=d.vectorNorm={x:W.x/Y,y:W.y/Y},H={x:-U.y,y:U.x};d.nodesOverlap=!Q(Y)||C.checkPoint(L[0],L[1],0,E,k,m.x,m.y,S,B)||T.checkPoint(O[0],O[1],0,b,x,y.x,y.y,P,D),d.vectorNormInverse=H,e={nodesOverlap:d.nodesOverlap,dirCounts:d.dirCounts,calculatedIntersection:!0,hasBezier:d.hasBezier,hasUnbundled:d.hasUnbundled,eles:d.eles,srcPos:m,srcRs:B,tgtPos:y,tgtRs:D,srcW:E,srcH:k,tgtW:b,tgtH:x,srcIntn:V,tgtIntn:z,srcShape:C,tgtShape:T,posPts:{x1:j.x2,y1:j.y2,x2:j.x1,y2:j.y1},intersectionPts:{x1:F.x2,y1:F.y2,x2:F.x1,y2:F.y1},vector:{x:-W.x,y:-W.y},vectorNorm:{x:-U.x,y:-U.y},vectorNormInverse:{x:-H.x,y:-H.y}}}var K=N?e:d;M.nodesOverlap=K.nodesOverlap,M.srcIntn=K.srcIntn,M.tgtIntn=K.tgtIntn,M.isRound=R.startsWith("round"),r&&(p.isParent()||p.isChild()||g.isParent()||g.isChild())&&(p.parents().anySame(g)||g.parents().anySame(p)||p.same(g)&&p.isParent())?t.findCompoundLoopPoints(A,K,_,I):p===g?t.findLoopPoints(A,K,_,I):R.endsWith("segments")?t.findSegmentsPoints(A,K):R.endsWith("taxi")?t.findTaxiPoints(A,K):"straight"===R||!I&&d.eles.length%2==1&&_===Math.floor(d.eles.length/2)?t.findStraightEdgePoints(A):t.findBezierPoints(A,K,_,I,N),t.findEndpoints(A),t.tryToCorrectInvalidPoints(A,K),t.checkForInvalidEdgeWarning(A),t.storeAllpts(A),t.storeEdgeProjections(A),t.calculateArrowAngles(A),t.recalculateEdgeLabelProjections(A),t.calculateLabelAngles(A)}},w=0;w<o.length;w++)x();this.findHaystackPoints(s)}},Rc.getSegmentPoints=function(e){var t=e[0]._private.rscratch;if(this.recalculateRenderedStyle(e),"segments"===t.edgeType)return Ic(t.segpts)},Rc.getControlPoints=function(e){var t=e[0]._private.rscratch;this.recalculateRenderedStyle(e);var n=t.edgeType;if("bezier"===n||"multibezier"===n||"self"===n||"compound"===n)return Ic(t.ctrlpts)},Rc.getEdgeMidpoint=function(e){var t=e[0]._private.rscratch;return this.recalculateRenderedStyle(e),{x:t.midX,y:t.midY}};var Nc={manualEndptToPx:function(e,t){var n=e.position(),r=e.outerWidth(),a=e.outerHeight(),i=e._private.rscratch;if(2===t.value.length){var o=[t.pfValue[0],t.pfValue[1]];return"%"===t.units[0]&&(o[0]=o[0]*r),"%"===t.units[1]&&(o[1]=o[1]*a),o[0]+=n.x,o[1]+=n.y,o}var s=t.pfValue[0];s=-Math.PI/2+s;var l=2*Math.max(r,a),u=[n.x+Math.cos(s)*l,n.y+Math.sin(s)*l];return this.nodeShapes[this.getNodeShape(e)].intersectLine(n.x,n.y,r,a,u[0],u[1],0,"auto"===e.pstyle("corner-radius").value?"auto":e.pstyle("corner-radius").pfValue,i)},findEndpoints:function(e){var t,n,r,a,i,o=this,s=e.source()[0],l=e.target()[0],u=s.position(),c=l.position(),d=e.pstyle("target-arrow-shape").value,h=e.pstyle("source-arrow-shape").value,f=e.pstyle("target-distance-from-node").pfValue,p=e.pstyle("source-distance-from-node").pfValue,g=s._private.rscratch,v=l._private.rscratch,y=e.pstyle("curve-style").value,m=e._private.rscratch,b=m.edgeType,x="self"===b||"compound"===b,w="bezier"===b||"multibezier"===b||x,E="bezier"!==b,k="straight"===b||"segments"===b,T="segments"===b,C=w||E||k,P=x||"taxi"===y,S=e.pstyle("source-endpoint"),B=P?"outside-to-node":S.value,D="auto"===s.pstyle("corner-radius").value?"auto":s.pstyle("corner-radius").pfValue,_=e.pstyle("target-endpoint"),A=P?"outside-to-node":_.value,M="auto"===l.pstyle("corner-radius").value?"auto":l.pstyle("corner-radius").pfValue;if(m.srcManEndpt=S,m.tgtManEndpt=_,w){var R=[m.ctrlpts[0],m.ctrlpts[1]];n=E?[m.ctrlpts[m.ctrlpts.length-2],m.ctrlpts[m.ctrlpts.length-1]]:R,r=R}else if(k){var I=T?m.segpts.slice(0,2):[c.x,c.y];n=T?m.segpts.slice(m.segpts.length-2):[u.x,u.y],r=I}if("inside-to-node"===A)t=[c.x,c.y];else if(_.units)t=this.manualEndptToPx(l,_);else if("outside-to-line"===A)t=m.tgtIntn;else if("outside-to-node"===A||"outside-to-node-or-label"===A?a=n:"outside-to-line"!==A&&"outside-to-line-or-label"!==A||(a=[u.x,u.y]),t=o.nodeShapes[this.getNodeShape(l)].intersectLine(c.x,c.y,l.outerWidth(),l.outerHeight(),a[0],a[1],0,M,v),"outside-to-node-or-label"===A||"outside-to-line-or-label"===A){var N=l._private.rscratch,L=N.labelWidth,z=N.labelHeight,O=N.labelX,V=N.labelY,F=L/2,j=z/2,X=l.pstyle("text-valign").value;"top"===X?V-=j:"bottom"===X&&(V+=j);var q=l.pstyle("text-halign").value;"left"===q?O-=F:"right"===q&&(O+=F);var Y=xn(a[0],a[1],[O-F,V-j,O+F,V-j,O+F,V+j,O-F,V+j],c.x,c.y);if(Y.length>0){var W=u,U=Ht(W,Xt(t)),H=Ht(W,Xt(Y)),K=U;if(H<U&&(t=Y,K=H),Y.length>2)Ht(W,{x:Y[2],y:Y[3]})<K&&(t=[Y[2],Y[3]])}}var G=wn(t,n,o.arrowShapes[d].spacing(e)+f),Z=wn(t,n,o.arrowShapes[d].gap(e)+f);if(m.endX=Z[0],m.endY=Z[1],m.arrowEndX=G[0],m.arrowEndY=G[1],"inside-to-node"===B)t=[u.x,u.y];else if(S.units)t=this.manualEndptToPx(s,S);else if("outside-to-line"===B)t=m.srcIntn;else if("outside-to-node"===B||"outside-to-node-or-label"===B?i=r:"outside-to-line"!==B&&"outside-to-line-or-label"!==B||(i=[c.x,c.y]),t=o.nodeShapes[this.getNodeShape(s)].intersectLine(u.x,u.y,s.outerWidth(),s.outerHeight(),i[0],i[1],0,D,g),"outside-to-node-or-label"===B||"outside-to-line-or-label"===B){var $=s._private.rscratch,J=$.labelWidth,ee=$.labelHeight,te=$.labelX,ne=$.labelY,re=J/2,ae=ee/2,ie=s.pstyle("text-valign").value;"top"===ie?ne-=ae:"bottom"===ie&&(ne+=ae);var oe=s.pstyle("text-halign").value;"left"===oe?te-=re:"right"===oe&&(te+=re);var se=xn(i[0],i[1],[te-re,ne-ae,te+re,ne-ae,te+re,ne+ae,te-re,ne+ae],u.x,u.y);if(se.length>0){var le=c,ue=Ht(le,Xt(t)),ce=Ht(le,Xt(se)),de=ue;if(ce<ue&&(t=[se[0],se[1]],de=ce),se.length>2)Ht(le,{x:se[2],y:se[3]})<de&&(t=[se[2],se[3]])}}var he=wn(t,r,o.arrowShapes[h].spacing(e)+p),fe=wn(t,r,o.arrowShapes[h].gap(e)+p);m.startX=fe[0],m.startY=fe[1],m.arrowStartX=he[0],m.arrowStartY=he[1],C&&(Q(m.startX)&&Q(m.startY)&&Q(m.endX)&&Q(m.endY)?m.badLine=!1:m.badLine=!0)},getSourceEndpoint:function(e){var t=e[0]._private.rscratch;return this.recalculateRenderedStyle(e),"haystack"===t.edgeType?{x:t.haystackPts[0],y:t.haystackPts[1]}:{x:t.arrowStartX,y:t.arrowStartY}},getTargetEndpoint:function(e){var t=e[0]._private.rscratch;return this.recalculateRenderedStyle(e),"haystack"===t.edgeType?{x:t.haystackPts[2],y:t.haystackPts[3]}:{x:t.arrowEndX,y:t.arrowEndY}}},Lc={};function zc(e,t,n){for(var r=function(e,t,n,r){return Gt(e,t,n,r)},a=t._private.rstyle.bezierPts,i=0;i<e.bezierProjPcts.length;i++){var o=e.bezierProjPcts[i];a.push({x:r(n[0],n[2],n[4],o),y:r(n[1],n[3],n[5],o)})}}Lc.storeEdgeProjections=function(e){var t=e._private,n=t.rscratch,r=n.edgeType;if(t.rstyle.bezierPts=null,t.rstyle.linePts=null,t.rstyle.haystackPts=null,"multibezier"===r||"bezier"===r||"self"===r||"compound"===r){t.rstyle.bezierPts=[];for(var a=0;a+5<n.allpts.length;a+=4)zc(this,e,n.allpts.slice(a,a+6))}else if("segments"===r){var i=t.rstyle.linePts=[];for(a=0;a+1<n.allpts.length;a+=2)i.push({x:n.allpts[a],y:n.allpts[a+1]})}else if("haystack"===r){var o=n.haystackPts;t.rstyle.haystackPts=[{x:o[0],y:o[1]},{x:o[2],y:o[3]}]}t.rstyle.arrowWidth=this.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.arrowShapeWidth},Lc.recalculateEdgeProjections=function(e){this.findEdgeControlPoints(e)};var Oc={recalculateNodeLabelProjection:function(e){var t=e.pstyle("label").strValue;if(!ie(t)){var n,r,a=e._private,i=e.width(),o=e.height(),s=e.padding(),l=e.position(),u=e.pstyle("text-halign").strValue,c=e.pstyle("text-valign").strValue,d=a.rscratch,h=a.rstyle;switch(u){case"left":n=l.x-i/2-s;break;case"right":n=l.x+i/2+s;break;default:n=l.x}switch(c){case"top":r=l.y-o/2-s;break;case"bottom":r=l.y+o/2+s;break;default:r=l.y}d.labelX=n,d.labelY=r,h.labelX=n,h.labelY=r,this.calculateLabelAngles(e),this.applyLabelDimensions(e)}}},Vc=function(e,t){var n=Math.atan(t/e);return 0===e&&n<0&&(n*=-1),n},Fc=function(e,t){var n=t.x-e.x,r=t.y-e.y;return Vc(n,r)};Oc.recalculateEdgeLabelProjections=function(e){var t,n=e._private,r=n.rscratch,a=this,i={mid:e.pstyle("label").strValue,source:e.pstyle("source-label").strValue,target:e.pstyle("target-label").strValue};if(i.mid||i.source||i.target){t={x:r.midX,y:r.midY};var o=function(e,t,r){ft(n.rscratch,e,t,r),ft(n.rstyle,e,t,r)};o("labelX",null,t.x),o("labelY",null,t.y);var s=Vc(r.midDispX,r.midDispY);o("labelAutoAngle",null,s);var l=function(){if(l.cache)return l.cache;for(var e=[],t=0;t+5<r.allpts.length;t+=4){var i={x:r.allpts[t],y:r.allpts[t+1]},o={x:r.allpts[t+2],y:r.allpts[t+3]},s={x:r.allpts[t+4],y:r.allpts[t+5]};e.push({p0:i,p1:o,p2:s,startDist:0,length:0,segments:[]})}var u=n.rstyle.bezierPts,c=a.bezierProjPcts.length;function d(e,t,n,r,a){var i=Ut(t,n),o=e.segments[e.segments.length-1],s={p0:t,p1:n,t0:r,t1:a,startDist:o?o.startDist+o.length:0,length:i};e.segments.push(s),e.length+=i}for(var h=0;h<e.length;h++){var f=e[h],p=e[h-1];p&&(f.startDist=p.startDist+p.length),d(f,f.p0,u[h*c],0,a.bezierProjPcts[0]);for(var g=0;g<c-1;g++)d(f,u[h*c+g],u[h*c+g+1],a.bezierProjPcts[g],a.bezierProjPcts[g+1]);d(f,u[h*c+c-1],f.p2,a.bezierProjPcts[c-1],1)}return l.cache=e},u=function(n){var a,s="source"===n;if(i[n]){var u=e.pstyle(n+"-text-offset").pfValue;switch(r.edgeType){case"self":case"compound":case"bezier":case"multibezier":for(var c,d=l(),h=0,f=0,p=0;p<d.length;p++){for(var g=d[s?p:d.length-1-p],v=0;v<g.segments.length;v++){var y=g.segments[s?v:g.segments.length-1-v],m=p===d.length-1&&v===g.segments.length-1;if(h=f,(f+=y.length)>=u||m){c={cp:g,segment:y};break}}if(c)break}var b=c.cp,x=c.segment,w=(u-h)/x.length,E=x.t1-x.t0,k=s?x.t0+E*w:x.t1-E*w;k=$t(0,k,1),t=Zt(b.p0,b.p1,b.p2,k),a=function(e,t,n,r){var a=$t(0,r-.001,1),i=$t(0,r+.001,1),o=Zt(e,t,n,a),s=Zt(e,t,n,i);return Fc(o,s)}(b.p0,b.p1,b.p2,k);break;case"straight":case"segments":case"haystack":for(var T,C,P,S,B=0,D=r.allpts.length,_=0;_+3<D&&(s?(P={x:r.allpts[_],y:r.allpts[_+1]},S={x:r.allpts[_+2],y:r.allpts[_+3]}):(P={x:r.allpts[D-2-_],y:r.allpts[D-1-_]},S={x:r.allpts[D-4-_],y:r.allpts[D-3-_]}),C=B,!((B+=T=Ut(P,S))>=u));_+=2);var A=(u-C)/T;A=$t(0,A,1),t=function(e,t,n,r){var a=t.x-e.x,i=t.y-e.y,o=Ut(e,t),s=a/o,l=i/o;return n=null==n?0:n,r=null!=r?r:n*o,{x:e.x+s*r,y:e.y+l*r}}(P,S,A),a=Fc(P,S)}o("labelX",n,t.x),o("labelY",n,t.y),o("labelAutoAngle",n,a)}};u("source"),u("target"),this.applyLabelDimensions(e)}},Oc.applyLabelDimensions=function(e){this.applyPrefixedLabelDimensions(e),e.isEdge()&&(this.applyPrefixedLabelDimensions(e,"source"),this.applyPrefixedLabelDimensions(e,"target"))},Oc.applyPrefixedLabelDimensions=function(e,t){var n=e._private,r=this.getLabelText(e,t),a=We(r,e._private.labelDimsKey);if(ht(n.rscratch,"prefixedLabelDimsKey",t)!==a){ft(n.rscratch,"prefixedLabelDimsKey",t,a);var i=this.calculateLabelDimensions(e,r),o=e.pstyle("line-height").pfValue,s=e.pstyle("text-wrap").strValue,l=ht(n.rscratch,"labelWrapCachedLines",t)||[],u="wrap"!==s?1:Math.max(l.length,1),c=i.height/u,d=c*o,h=i.width,f=i.height+(u-1)*(o-1)*c;ft(n.rstyle,"labelWidth",t,h),ft(n.rscratch,"labelWidth",t,h),ft(n.rstyle,"labelHeight",t,f),ft(n.rscratch,"labelHeight",t,f),ft(n.rscratch,"labelLineHeight",t,d)}},Oc.getLabelText=function(e,t){var n=e._private,r=t?t+"-":"",a=e.pstyle(r+"label").strValue,i=e.pstyle("text-transform").value,s=function(e,r){return r?(ft(n.rscratch,e,t,r),r):ht(n.rscratch,e,t)};if(!a)return"";"none"==i||("uppercase"==i?a=a.toUpperCase():"lowercase"==i&&(a=a.toLowerCase()));var l=e.pstyle("text-wrap").value;if("wrap"===l){var u=s("labelKey");if(null!=u&&s("labelWrapKey")===u)return s("labelWrapCachedText");for(var c=a.split("\n"),d=e.pstyle("text-max-width").pfValue,h="anywhere"===e.pstyle("text-overflow-wrap").value,f=[],p=/[\s\u200b]+|$/g,g=0;g<c.length;g++){var v=c[g],y=this.calculateLabelDimensions(e,v).width;if(h){var m=v.split("").join("\u200b");v=m}if(y>d){var b,x="",w=0,E=o(v.matchAll(p));try{for(E.s();!(b=E.n()).done;){var k=b.value,T=k[0],C=v.substring(w,k.index);w=k.index+T.length;var P=0===x.length?C:x+C+T;this.calculateLabelDimensions(e,P).width<=d?x+=C+T:(x&&f.push(x),x=C+T)}}catch(A){E.e(A)}finally{E.f()}x.match(/^[\s\u200b]+$/)||f.push(x)}else f.push(v)}s("labelWrapCachedLines",f),a=s("labelWrapCachedText",f.join("\n")),s("labelWrapKey",u)}else if("ellipsis"===l){var S=e.pstyle("text-max-width").pfValue,B="",D=!1;if(this.calculateLabelDimensions(e,a).width<S)return a;for(var _=0;_<a.length;_++){if(this.calculateLabelDimensions(e,B+a[_]+"\u2026").width>S)break;B+=a[_],_===a.length-1&&(D=!0)}return D||(B+="\u2026"),B}return a},Oc.getLabelJustification=function(e){var t=e.pstyle("text-justification").strValue,n=e.pstyle("text-halign").strValue;if("auto"!==t)return t;if(!e.isNode())return"center";switch(n){case"left":return"right";case"right":return"left";default:return"center"}},Oc.calculateLabelDimensions=function(e,t){var n=this.cy.window().document,r=e.pstyle("font-style").strValue,a=e.pstyle("font-size").pfValue,i=e.pstyle("font-family").strValue,o=e.pstyle("font-weight").strValue,s=this.labelCalcCanvas,l=this.labelCalcCanvasContext;if(!s){s=this.labelCalcCanvas=n.createElement("canvas"),l=this.labelCalcCanvasContext=s.getContext("2d");var u=s.style;u.position="absolute",u.left="-9999px",u.top="-9999px",u.zIndex="-1",u.visibility="hidden",u.pointerEvents="none"}l.font="".concat(r," ").concat(o," ").concat(a,"px ").concat(i);for(var c=0,d=0,h=t.split("\n"),f=0;f<h.length;f++){var p=h[f],g=l.measureText(p),v=Math.ceil(g.width),y=a;c=Math.max(v,c),d+=y}return{width:c+=0,height:d+=0}},Oc.calculateLabelAngle=function(e,t){var n=e._private.rscratch,r=e.isEdge(),a=t?t+"-":"",i=e.pstyle(a+"text-rotation"),o=i.strValue;return"none"===o?0:r&&"autorotate"===o?n.labelAutoAngle:"autorotate"===o?0:i.pfValue},Oc.calculateLabelAngles=function(e){var t=this,n=e.isEdge(),r=e._private.rscratch;r.labelAngle=t.calculateLabelAngle(e),n&&(r.sourceLabelAngle=t.calculateLabelAngle(e,"source"),r.targetLabelAngle=t.calculateLabelAngle(e,"target"))};var jc={},Xc=!1;jc.getNodeShape=function(e){var t=e.pstyle("shape").value;if("cutrectangle"===t&&(e.width()<28||e.height()<28))return Xc||(at("The `cutrectangle` node shape can not be used at small sizes so `rectangle` is used instead"),Xc=!0),"rectangle";if(e.isParent())return"rectangle"===t||"roundrectangle"===t||"round-rectangle"===t||"cutrectangle"===t||"cut-rectangle"===t||"barrel"===t?t:"rectangle";if("polygon"===t){var n=e.pstyle("shape-polygon-points").value;return this.nodeShapes.makePolygon(n).name}return t};var qc={registerCalculationListeners:function(){var e=this.cy,t=e.collection(),n=this,r=function(e){var n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(t.merge(e),n)for(var r=0;r<e.length;r++){var a=e[r]._private.rstyle;a.clean=!1,a.cleanConnected=!1}};n.binder(e).on("bounds.* dirty.*",(function(e){var t=e.target;r(t)})).on("style.* background.*",(function(e){var t=e.target;r(t,!1)}));var a=function(a){if(a){var i=n.onUpdateEleCalcsFns;t.cleanStyle();for(var o=0;o<t.length;o++){var s=t[o],l=s._private.rstyle;s.isNode()&&!l.cleanConnected&&(r(s.connectedEdges()),l.cleanConnected=!0)}if(i)for(var u=0;u<i.length;u++){(0,i[u])(a,t)}n.recalculateRenderedStyle(t),t=e.collection()}};n.flushRenderedStyleQueue=function(){a(!0)},n.beforeRender(a,n.beforeRenderPriorities.eleCalcs)},onUpdateEleCalcs:function(e){(this.onUpdateEleCalcsFns=this.onUpdateEleCalcsFns||[]).push(e)},recalculateRenderedStyle:function(e,t){var n=function(e){return e._private.rstyle.cleanConnected};if(0!==e.length){var r=[],a=[];if(!this.destroyed){void 0===t&&(t=!0);for(var i=0;i<e.length;i++){var o=e[i],s=o._private,l=s.rstyle;!o.isEdge()||n(o.source())&&n(o.target())||(l.clean=!1),o.isEdge()&&o.isBundledBezier()&&o.parallelEdges().some((function(e){return!e._private.rstyle.clean&&e.isBundledBezier()}))&&(l.clean=!1),t&&l.clean||o.removed()||"none"!==o.pstyle("display").value&&("nodes"===s.group?a.push(o):r.push(o),l.clean=!0)}for(var u=0;u<a.length;u++){var c=a[u],d=c._private.rstyle,h=c.position();this.recalculateNodeLabelProjection(c),d.nodeX=h.x,d.nodeY=h.y,d.nodeW=c.pstyle("width").pfValue,d.nodeH=c.pstyle("height").pfValue}this.recalculateEdgeProjections(r);for(var f=0;f<r.length;f++){var p=r[f]._private,g=p.rstyle,v=p.rscratch;g.srcX=v.arrowStartX,g.srcY=v.arrowStartY,g.tgtX=v.arrowEndX,g.tgtY=v.arrowEndY,g.midX=v.midX,g.midY=v.midY,g.labelAngle=v.labelAngle,g.sourceLabelAngle=v.sourceLabelAngle,g.targetLabelAngle=v.targetLabelAngle}}}}},Yc={updateCachedGrabbedEles:function(){var e=this.cachedZSortedEles;if(e){e.drag=[],e.nondrag=[];for(var t=[],n=0;n<e.length;n++){var r=(a=e[n])._private.rscratch;a.grabbed()&&!a.isParent()?t.push(a):r.inDragLayer?e.drag.push(a):e.nondrag.push(a)}for(n=0;n<t.length;n++){var a=t[n];e.drag.push(a)}}},invalidateCachedZSortedEles:function(){this.cachedZSortedEles=null},getCachedZSortedEles:function(e){if(e||!this.cachedZSortedEles){var t=this.cy.mutableElements().toArray();t.sort(al),t.interactive=t.filter((function(e){return e.interactive()})),this.cachedZSortedEles=t,this.updateCachedGrabbedEles()}else t=this.cachedZSortedEles;return t}},Wc={};[oc,sc,Rc,Nc,Lc,Oc,jc,qc,Yc].forEach((function(e){me(Wc,e)}));var Uc={getCachedImage:function(e,t,n){var r=this.imageCache=this.imageCache||{},a=r[e];if(a)return a.image.complete||a.image.addEventListener("load",n),a.image;var i=(a=r[e]=r[e]||{}).image=new Image;i.addEventListener("load",n),i.addEventListener("error",(function(){i.error=!0}));var o="data:";return e.substring(0,5).toLowerCase()===o||(t="null"===t?null:t,i.crossOrigin=t),i.src=e,i}},Hc={registerBinding:function(e,t,n,r){var a=Array.prototype.slice.apply(arguments,[1]);if(Array.isArray(e)){for(var i=[],o=0;o<e.length;o++){var s=e[o];if(void 0!==s){var l=this.binder(s);i.push(l.on.apply(l,a))}}return i}return(l=this.binder(e)).on.apply(l,a)}};Hc.binder=function(e){var t,n=this,r=n.cy.window(),a=e===r||e===r.document||e===r.document.body||(t=e,"undefined"!=typeof HTMLElement&&t instanceof HTMLElement);if(null==n.supportsPassiveEvents){var i=!1;try{var o=Object.defineProperty({},"passive",{get:function(){return i=!0,!0}});r.addEventListener("test",null,o)}catch(l){}n.supportsPassiveEvents=i}var s=function(t,r,i){var o=Array.prototype.slice.call(arguments);return a&&n.supportsPassiveEvents&&(o[2]={capture:null!=i&&i,passive:!1,once:!1}),n.bindings.push({target:e,args:o}),(e.addEventListener||e.on).apply(e,o),this};return{on:s,addEventListener:s,addListener:s,bind:s}},Hc.nodeIsDraggable=function(e){return e&&e.isNode()&&!e.locked()&&e.grabbable()},Hc.nodeIsGrabbable=function(e){return this.nodeIsDraggable(e)&&e.interactive()},Hc.load=function(){var e=this,t=e.cy.window(),n=function(e){return e.selected()},r=function(t,n,r,a){null==t&&(t=e.cy);for(var i=0;i<n.length;i++){var o=n[i];t.emit({originalEvent:r,type:o,position:a})}},a=function(e){return e.shiftKey||e.metaKey||e.ctrlKey},i=function(t,n){var r=!0;if(e.cy.hasCompoundNodes()&&t&&t.pannable())for(var a=0;n&&a<n.length;a++){if((t=n[a]).isNode()&&t.isParent()&&!t.pannable()){r=!1;break}}else r=!0;return r},o=function(e){e[0]._private.rscratch.inDragLayer=!0},s=function(e){e[0]._private.rscratch.isGrabTarget=!0},l=function(e,t){var n=t.addToList;n.has(e)||!e.grabbable()||e.locked()||(n.merge(e),function(e){e[0]._private.grabbed=!0}(e))},u=function(t,n){n=n||{};var r=t.cy().hasCompoundNodes();n.inDragLayer&&(t.forEach(o),t.neighborhood().stdFilter((function(e){return!r||e.isEdge()})).forEach(o)),n.addToList&&t.forEach((function(e){l(e,n)})),function(e,t){if(e.cy().hasCompoundNodes()&&(null!=t.inDragLayer||null!=t.addToList)){var n=e.descendants();t.inDragLayer&&(n.forEach(o),n.connectedEdges().forEach(o)),t.addToList&&l(n,t)}}(t,n),h(t,{inDragLayer:n.inDragLayer}),e.updateCachedGrabbedEles()},c=u,d=function(t){t&&(e.getCachedZSortedEles().forEach((function(e){!function(e){e[0]._private.grabbed=!1}(e),function(e){e[0]._private.rscratch.inDragLayer=!1}(e),function(e){e[0]._private.rscratch.isGrabTarget=!1}(e)})),e.updateCachedGrabbedEles())},h=function(e,t){if((null!=t.inDragLayer||null!=t.addToList)&&e.cy().hasCompoundNodes()){var n=e.ancestors().orphans();if(!n.same(e)){var r=n.descendants().spawnSelf().merge(n).unmerge(e).unmerge(e.descendants()),a=r.connectedEdges();t.inDragLayer&&(a.forEach(o),r.forEach(o)),t.addToList&&r.forEach((function(e){l(e,t)}))}}},f=function(){null!=document.activeElement&&null!=document.activeElement.blur&&document.activeElement.blur()},p="undefined"!=typeof MutationObserver,g="undefined"!=typeof ResizeObserver;p?(e.removeObserver=new MutationObserver((function(t){for(var n=0;n<t.length;n++){var r=t[n].removedNodes;if(r)for(var a=0;a<r.length;a++){if(r[a]===e.container){e.destroy();break}}}})),e.container.parentNode&&e.removeObserver.observe(e.container.parentNode,{childList:!0})):e.registerBinding(e.container,"DOMNodeRemoved",(function(t){e.destroy()}));var v=Ae((function(){e.cy.resize()}),100);p&&(e.styleObserver=new MutationObserver(v),e.styleObserver.observe(e.container,{attributes:!0})),e.registerBinding(t,"resize",v),g&&(e.resizeObserver=new ResizeObserver(v),e.resizeObserver.observe(e.container));var y=function(){e.invalidateContainerClientCoordsCache()};!function(e,t){for(;null!=e;)t(e),e=e.parentNode}(e.container,(function(t){e.registerBinding(t,"transitionend",y),e.registerBinding(t,"animationend",y),e.registerBinding(t,"scroll",y)})),e.registerBinding(e.container,"contextmenu",(function(e){e.preventDefault()}));var m=function(t){for(var n=e.findContainerClientCoords(),r=n[0],a=n[1],i=n[2],o=n[3],s=t.touches?t.touches:[t],l=!1,u=0;u<s.length;u++){var c=s[u];if(r<=c.clientX&&c.clientX<=r+i&&a<=c.clientY&&c.clientY<=a+o){l=!0;break}}if(!l)return!1;for(var d=e.container,h=t.target.parentNode,f=!1;h;){if(h===d){f=!0;break}h=h.parentNode}return!!f};e.registerBinding(e.container,"mousedown",(function(t){if(m(t)&&(1!==e.hoverData.which||1===t.which)){t.preventDefault(),f(),e.hoverData.capture=!0,e.hoverData.which=t.which;var n=e.cy,a=[t.clientX,t.clientY],i=e.projectIntoViewport(a[0],a[1]),o=e.selection,l=e.findNearestElements(i[0],i[1],!0,!1),d=l[0],h=e.dragData.possibleDragElements;e.hoverData.mdownPos=i,e.hoverData.mdownGPos=a;if(3==t.which){e.hoverData.cxtStarted=!0;var p={originalEvent:t,type:"cxttapstart",position:{x:i[0],y:i[1]}};d?(d.activate(),d.emit(p),e.hoverData.down=d):n.emit(p),e.hoverData.downTime=(new Date).getTime(),e.hoverData.cxtDragged=!1}else if(1==t.which){if(d&&d.activate(),null!=d&&e.nodeIsGrabbable(d)){var g=function(e){return{originalEvent:t,type:e,position:{x:i[0],y:i[1]}}};if(s(d),d.selected()){h=e.dragData.possibleDragElements=n.collection();var v=n.$((function(t){return t.isNode()&&t.selected()&&e.nodeIsGrabbable(t)}));u(v,{addToList:h}),d.emit(g("grabon")),v.forEach((function(e){e.emit(g("grab"))}))}else h=e.dragData.possibleDragElements=n.collection(),c(d,{addToList:h}),d.emit(g("grabon")).emit(g("grab"));e.redrawHint("eles",!0),e.redrawHint("drag",!0)}e.hoverData.down=d,e.hoverData.downs=l,e.hoverData.downTime=(new Date).getTime(),r(d,["mousedown","tapstart","vmousedown"],t,{x:i[0],y:i[1]}),null==d?(o[4]=1,e.data.bgActivePosistion={x:i[0],y:i[1]},e.redrawHint("select",!0),e.redraw()):d.pannable()&&(o[4]=1),e.hoverData.tapholdCancelled=!1,clearTimeout(e.hoverData.tapholdTimeout),e.hoverData.tapholdTimeout=setTimeout((function(){if(!e.hoverData.tapholdCancelled){var r=e.hoverData.down;r?r.emit({originalEvent:t,type:"taphold",position:{x:i[0],y:i[1]}}):n.emit({originalEvent:t,type:"taphold",position:{x:i[0],y:i[1]}})}}),e.tapholdDuration)}o[0]=o[2]=i[0],o[1]=o[3]=i[1]}}),!1);var b,x,w,E=function(e){var t=e.getRootNode();if(t&&11===t.nodeType&&void 0!==t.host)return t}(e.container);e.registerBinding([t,E],"mousemove",(function(t){if(e.hoverData.capture||m(t)){var n=!1,o=e.cy,s=o.zoom(),l=[t.clientX,t.clientY],c=e.projectIntoViewport(l[0],l[1]),h=e.hoverData.mdownPos,f=e.hoverData.mdownGPos,p=e.selection,g=null;e.hoverData.draggingEles||e.hoverData.dragging||e.hoverData.selecting||(g=e.findNearestElement(c[0],c[1],!0,!1));var v,y=e.hoverData.last,b=e.hoverData.down,x=[c[0]-p[2],c[1]-p[3]],w=e.dragData.possibleDragElements;if(f){var E=l[0]-f[0],k=E*E,T=l[1]-f[1],C=k+T*T;e.hoverData.isOverThresholdDrag=v=C>=e.desktopTapThreshold2}var P=a(t);v&&(e.hoverData.tapholdCancelled=!0);n=!0,r(g,["mousemove","vmousemove","tapdrag"],t,{x:c[0],y:c[1]});var S=function(){e.data.bgActivePosistion=void 0,e.hoverData.selecting||o.emit({originalEvent:t,type:"boxstart",position:{x:c[0],y:c[1]}}),p[4]=1,e.hoverData.selecting=!0,e.redrawHint("select",!0),e.redraw()};if(3===e.hoverData.which){if(v){var B={originalEvent:t,type:"cxtdrag",position:{x:c[0],y:c[1]}};b?b.emit(B):o.emit(B),e.hoverData.cxtDragged=!0,e.hoverData.cxtOver&&g===e.hoverData.cxtOver||(e.hoverData.cxtOver&&e.hoverData.cxtOver.emit({originalEvent:t,type:"cxtdragout",position:{x:c[0],y:c[1]}}),e.hoverData.cxtOver=g,g&&g.emit({originalEvent:t,type:"cxtdragover",position:{x:c[0],y:c[1]}}))}}else if(e.hoverData.dragging){if(n=!0,o.panningEnabled()&&o.userPanningEnabled()){var D;if(e.hoverData.justStartedPan){var _=e.hoverData.mdownPos;D={x:(c[0]-_[0])*s,y:(c[1]-_[1])*s},e.hoverData.justStartedPan=!1}else D={x:x[0]*s,y:x[1]*s};o.panBy(D),o.emit("dragpan"),e.hoverData.dragged=!0}c=e.projectIntoViewport(t.clientX,t.clientY)}else if(1!=p[4]||null!=b&&!b.pannable()){if(b&&b.pannable()&&b.active()&&b.unactivate(),b&&b.grabbed()||g==y||(y&&r(y,["mouseout","tapdragout"],t,{x:c[0],y:c[1]}),g&&r(g,["mouseover","tapdragover"],t,{x:c[0],y:c[1]}),e.hoverData.last=g),b)if(v){if(o.boxSelectionEnabled()&&P)b&&b.grabbed()&&(d(w),b.emit("freeon"),w.emit("free"),e.dragData.didDrag&&(b.emit("dragfreeon"),w.emit("dragfree"))),S();else if(b&&b.grabbed()&&e.nodeIsDraggable(b)){var A=!e.dragData.didDrag;A&&e.redrawHint("eles",!0),e.dragData.didDrag=!0,e.hoverData.draggingEles||u(w,{inDragLayer:!0});var M={x:0,y:0};if(Q(x[0])&&Q(x[1])&&(M.x+=x[0],M.y+=x[1],A)){var R=e.hoverData.dragDelta;R&&Q(R[0])&&Q(R[1])&&(M.x+=R[0],M.y+=R[1])}e.hoverData.draggingEles=!0,w.silentShift(M).emit("position drag"),e.redrawHint("drag",!0),e.redraw()}}else!function(){var t=e.hoverData.dragDelta=e.hoverData.dragDelta||[];0===t.length?(t.push(x[0]),t.push(x[1])):(t[0]+=x[0],t[1]+=x[1])}();n=!0}else if(v){if(e.hoverData.dragging||!o.boxSelectionEnabled()||!P&&o.panningEnabled()&&o.userPanningEnabled()){if(!e.hoverData.selecting&&o.panningEnabled()&&o.userPanningEnabled()){i(b,e.hoverData.downs)&&(e.hoverData.dragging=!0,e.hoverData.justStartedPan=!0,p[4]=0,e.data.bgActivePosistion=Xt(h),e.redrawHint("select",!0),e.redraw())}}else S();b&&b.pannable()&&b.active()&&b.unactivate()}return p[2]=c[0],p[3]=c[1],n?(t.stopPropagation&&t.stopPropagation(),t.preventDefault&&t.preventDefault(),!1):void 0}}),!1),e.registerBinding(t,"mouseup",(function(t){if((1!==e.hoverData.which||1===t.which||!e.hoverData.capture)&&e.hoverData.capture){e.hoverData.capture=!1;var i=e.cy,o=e.projectIntoViewport(t.clientX,t.clientY),s=e.selection,l=e.findNearestElement(o[0],o[1],!0,!1),u=e.dragData.possibleDragElements,c=e.hoverData.down,h=a(t);if(e.data.bgActivePosistion&&(e.redrawHint("select",!0),e.redraw()),e.hoverData.tapholdCancelled=!0,e.data.bgActivePosistion=void 0,c&&c.unactivate(),3===e.hoverData.which){var f={originalEvent:t,type:"cxttapend",position:{x:o[0],y:o[1]}};if(c?c.emit(f):i.emit(f),!e.hoverData.cxtDragged){var p={originalEvent:t,type:"cxttap",position:{x:o[0],y:o[1]}};c?c.emit(p):i.emit(p)}e.hoverData.cxtDragged=!1,e.hoverData.which=null}else if(1===e.hoverData.which){if(r(l,["mouseup","tapend","vmouseup"],t,{x:o[0],y:o[1]}),e.dragData.didDrag||e.hoverData.dragged||e.hoverData.selecting||e.hoverData.isOverThresholdDrag||(r(c,["click","tap","vclick"],t,{x:o[0],y:o[1]}),x=!1,t.timeStamp-w<=i.multiClickDebounceTime()?(b&&clearTimeout(b),x=!0,w=null,r(c,["dblclick","dbltap","vdblclick"],t,{x:o[0],y:o[1]})):(b=setTimeout((function(){x||r(c,["oneclick","onetap","voneclick"],t,{x:o[0],y:o[1]})}),i.multiClickDebounceTime()),w=t.timeStamp)),null!=c||e.dragData.didDrag||e.hoverData.selecting||e.hoverData.dragged||a(t)||(i.$(n).unselect(["tapunselect"]),u.length>0&&e.redrawHint("eles",!0),e.dragData.possibleDragElements=u=i.collection()),l!=c||e.dragData.didDrag||e.hoverData.selecting||null!=l&&l._private.selectable&&(e.hoverData.dragging||("additive"===i.selectionType()||h?l.selected()?l.unselect(["tapunselect"]):l.select(["tapselect"]):h||(i.$(n).unmerge(l).unselect(["tapunselect"]),l.select(["tapselect"]))),e.redrawHint("eles",!0)),e.hoverData.selecting){var g=i.collection(e.getAllInBox(s[0],s[1],s[2],s[3]));e.redrawHint("select",!0),g.length>0&&e.redrawHint("eles",!0),i.emit({type:"boxend",originalEvent:t,position:{x:o[0],y:o[1]}});var v=function(e){return e.selectable()&&!e.selected()};"additive"===i.selectionType()||h||i.$(n).unmerge(g).unselect(),g.emit("box").stdFilter(v).select().emit("boxselect"),e.redraw()}if(e.hoverData.dragging&&(e.hoverData.dragging=!1,e.redrawHint("select",!0),e.redrawHint("eles",!0),e.redraw()),!s[4]){e.redrawHint("drag",!0),e.redrawHint("eles",!0);var y=c&&c.grabbed();d(u),y&&(c.emit("freeon"),u.emit("free"),e.dragData.didDrag&&(c.emit("dragfreeon"),u.emit("dragfree")))}}s[4]=0,e.hoverData.down=null,e.hoverData.cxtStarted=!1,e.hoverData.draggingEles=!1,e.hoverData.selecting=!1,e.hoverData.isOverThresholdDrag=!1,e.dragData.didDrag=!1,e.hoverData.dragged=!1,e.hoverData.dragDelta=[],e.hoverData.mdownPos=null,e.hoverData.mdownGPos=null,e.hoverData.which=null}}),!1);var k,T,C,P,S,B,D,_,A,M,R,I,N,L,z=[],O=1e5,V=function(t){var n=!1,r=t.deltaY;if(null==r&&(null!=t.wheelDeltaY?r=t.wheelDeltaY/4:null!=t.wheelDelta&&(r=t.wheelDelta/4)),null==k)if(z.length>=4){var a=z;if(k=function(e,t){for(var n=0;n<e.length;n++)if(e[n]%t!==0)return!1;return!0}(a,5),!k){var i=Math.abs(a[0]);k=function(e){for(var t=Math.abs(e[0]),n=1;n<e.length;n++)if(Math.abs(e[n])!==t)return!1;return!0}(a)&&i>5}if(k)for(var o=0;o<a.length;o++)O=Math.min(Math.abs(a[o]),O)}else z.push(r),n=!0;else k&&(O=Math.min(Math.abs(r),O));if(!e.scrollingPage){var s=e.cy,l=s.zoom(),u=s.pan(),c=e.projectIntoViewport(t.clientX,t.clientY),d=[c[0]*l+u.x,c[1]*l+u.y];if(e.hoverData.draggingEles||e.hoverData.dragging||e.hoverData.cxtStarted||0!==e.selection[4])t.preventDefault();else if(s.panningEnabled()&&s.userPanningEnabled()&&s.zoomingEnabled()&&s.userZoomingEnabled()){var h;t.preventDefault(),e.data.wheelZooming=!0,clearTimeout(e.data.wheelTimeout),e.data.wheelTimeout=setTimeout((function(){e.data.wheelZooming=!1,e.redrawHint("eles",!0),e.redraw()}),150),n&&Math.abs(r)>5&&(r=5*Wt(r)),h=r/-250,k&&(h/=O,h*=3),h*=e.wheelSensitivity,1===t.deltaMode&&(h*=33);var f=s.zoom()*Math.pow(10,h);"gesturechange"===t.type&&(f=e.gestureStartZoom*t.scale),s.zoom({level:f,renderedPosition:{x:d[0],y:d[1]}}),s.emit("gesturechange"===t.type?"pinchzoom":"scrollzoom")}}};e.registerBinding(e.container,"wheel",V,!0),e.registerBinding(t,"scroll",(function(t){e.scrollingPage=!0,clearTimeout(e.scrollingPageTimeout),e.scrollingPageTimeout=setTimeout((function(){e.scrollingPage=!1}),250)}),!0),e.registerBinding(e.container,"gesturestart",(function(t){e.gestureStartZoom=e.cy.zoom(),e.hasTouchStarted||t.preventDefault()}),!0),e.registerBinding(e.container,"gesturechange",(function(t){e.hasTouchStarted||V(t)}),!0),e.registerBinding(e.container,"mouseout",(function(t){var n=e.projectIntoViewport(t.clientX,t.clientY);e.cy.emit({originalEvent:t,type:"mouseout",position:{x:n[0],y:n[1]}})}),!1),e.registerBinding(e.container,"mouseover",(function(t){var n=e.projectIntoViewport(t.clientX,t.clientY);e.cy.emit({originalEvent:t,type:"mouseover",position:{x:n[0],y:n[1]}})}),!1);var F,j,X,q,Y,W,U,H=function(e,t,n,r){return Math.sqrt((n-e)*(n-e)+(r-t)*(r-t))},K=function(e,t,n,r){return(n-e)*(n-e)+(r-t)*(r-t)};if(e.registerBinding(e.container,"touchstart",F=function(t){if(e.hasTouchStarted=!0,m(t)){f(),e.touchData.capture=!0,e.data.bgActivePosistion=void 0;var n=e.cy,a=e.touchData.now,i=e.touchData.earlier;if(t.touches[0]){var o=e.projectIntoViewport(t.touches[0].clientX,t.touches[0].clientY);a[0]=o[0],a[1]=o[1]}if(t.touches[1]){o=e.projectIntoViewport(t.touches[1].clientX,t.touches[1].clientY);a[2]=o[0],a[3]=o[1]}if(t.touches[2]){o=e.projectIntoViewport(t.touches[2].clientX,t.touches[2].clientY);a[4]=o[0],a[5]=o[1]}if(t.touches[1]){e.touchData.singleTouchMoved=!0,d(e.dragData.touchDragEles);var l=e.findContainerClientCoords();M=l[0],R=l[1],I=l[2],N=l[3],T=t.touches[0].clientX-M,C=t.touches[0].clientY-R,P=t.touches[1].clientX-M,S=t.touches[1].clientY-R,L=0<=T&&T<=I&&0<=P&&P<=I&&0<=C&&C<=N&&0<=S&&S<=N;var h=n.pan(),p=n.zoom();B=H(T,C,P,S),D=K(T,C,P,S),A=[((_=[(T+P)/2,(C+S)/2])[0]-h.x)/p,(_[1]-h.y)/p];if(D<4e4&&!t.touches[2]){var g=e.findNearestElement(a[0],a[1],!0,!0),v=e.findNearestElement(a[2],a[3],!0,!0);return g&&g.isNode()?(g.activate().emit({originalEvent:t,type:"cxttapstart",position:{x:a[0],y:a[1]}}),e.touchData.start=g):v&&v.isNode()?(v.activate().emit({originalEvent:t,type:"cxttapstart",position:{x:a[0],y:a[1]}}),e.touchData.start=v):n.emit({originalEvent:t,type:"cxttapstart",position:{x:a[0],y:a[1]}}),e.touchData.start&&(e.touchData.start._private.grabbed=!1),e.touchData.cxt=!0,e.touchData.cxtDragged=!1,e.data.bgActivePosistion=void 0,void e.redraw()}}if(t.touches[2])n.boxSelectionEnabled()&&t.preventDefault();else if(t.touches[1]);else if(t.touches[0]){var y=e.findNearestElements(a[0],a[1],!0,!0),b=y[0];if(null!=b&&(b.activate(),e.touchData.start=b,e.touchData.starts=y,e.nodeIsGrabbable(b))){var x=e.dragData.touchDragEles=n.collection(),w=null;e.redrawHint("eles",!0),e.redrawHint("drag",!0),b.selected()?(w=n.$((function(t){return t.selected()&&e.nodeIsGrabbable(t)})),u(w,{addToList:x})):c(b,{addToList:x}),s(b);var E=function(e){return{originalEvent:t,type:e,position:{x:a[0],y:a[1]}}};b.emit(E("grabon")),w?w.forEach((function(e){e.emit(E("grab"))})):b.emit(E("grab"))}r(b,["touchstart","tapstart","vmousedown"],t,{x:a[0],y:a[1]}),null==b&&(e.data.bgActivePosistion={x:o[0],y:o[1]},e.redrawHint("select",!0),e.redraw()),e.touchData.singleTouchMoved=!1,e.touchData.singleTouchStartTime=+new Date,clearTimeout(e.touchData.tapholdTimeout),e.touchData.tapholdTimeout=setTimeout((function(){!1!==e.touchData.singleTouchMoved||e.pinching||e.touchData.selecting||r(e.touchData.start,["taphold"],t,{x:a[0],y:a[1]})}),e.tapholdDuration)}if(t.touches.length>=1){for(var k=e.touchData.startPosition=[null,null,null,null,null,null],z=0;z<a.length;z++)k[z]=i[z]=a[z];var O=t.touches[0];e.touchData.startGPosition=[O.clientX,O.clientY]}}},!1),e.registerBinding(t,"touchmove",j=function(t){var n=e.touchData.capture;if(n||m(t)){var a=e.selection,o=e.cy,s=e.touchData.now,l=e.touchData.earlier,c=o.zoom();if(t.touches[0]){var h=e.projectIntoViewport(t.touches[0].clientX,t.touches[0].clientY);s[0]=h[0],s[1]=h[1]}if(t.touches[1]){h=e.projectIntoViewport(t.touches[1].clientX,t.touches[1].clientY);s[2]=h[0],s[3]=h[1]}if(t.touches[2]){h=e.projectIntoViewport(t.touches[2].clientX,t.touches[2].clientY);s[4]=h[0],s[5]=h[1]}var f,p=e.touchData.startGPosition;if(n&&t.touches[0]&&p){for(var g=[],v=0;v<s.length;v++)g[v]=s[v]-l[v];var y=t.touches[0].clientX-p[0],b=y*y,x=t.touches[0].clientY-p[1];f=b+x*x>=e.touchTapThreshold2}if(n&&e.touchData.cxt){t.preventDefault();var w=t.touches[0].clientX-M,E=t.touches[0].clientY-R,k=t.touches[1].clientX-M,_=t.touches[1].clientY-R,I=K(w,E,k,_);if(I/D>=2.25||I>=22500){e.touchData.cxt=!1,e.data.bgActivePosistion=void 0,e.redrawHint("select",!0);var N={originalEvent:t,type:"cxttapend",position:{x:s[0],y:s[1]}};e.touchData.start?(e.touchData.start.unactivate().emit(N),e.touchData.start=null):o.emit(N)}}if(n&&e.touchData.cxt){N={originalEvent:t,type:"cxtdrag",position:{x:s[0],y:s[1]}};e.data.bgActivePosistion=void 0,e.redrawHint("select",!0),e.touchData.start?e.touchData.start.emit(N):o.emit(N),e.touchData.start&&(e.touchData.start._private.grabbed=!1),e.touchData.cxtDragged=!0;var z=e.findNearestElement(s[0],s[1],!0,!0);e.touchData.cxtOver&&z===e.touchData.cxtOver||(e.touchData.cxtOver&&e.touchData.cxtOver.emit({originalEvent:t,type:"cxtdragout",position:{x:s[0],y:s[1]}}),e.touchData.cxtOver=z,z&&z.emit({originalEvent:t,type:"cxtdragover",position:{x:s[0],y:s[1]}}))}else if(n&&t.touches[2]&&o.boxSelectionEnabled())t.preventDefault(),e.data.bgActivePosistion=void 0,this.lastThreeTouch=+new Date,e.touchData.selecting||o.emit({originalEvent:t,type:"boxstart",position:{x:s[0],y:s[1]}}),e.touchData.selecting=!0,e.touchData.didSelect=!0,a[4]=1,a&&0!==a.length&&void 0!==a[0]?(a[2]=(s[0]+s[2]+s[4])/3,a[3]=(s[1]+s[3]+s[5])/3):(a[0]=(s[0]+s[2]+s[4])/3,a[1]=(s[1]+s[3]+s[5])/3,a[2]=(s[0]+s[2]+s[4])/3+1,a[3]=(s[1]+s[3]+s[5])/3+1),e.redrawHint("select",!0),e.redraw();else if(n&&t.touches[1]&&!e.touchData.didSelect&&o.zoomingEnabled()&&o.panningEnabled()&&o.userZoomingEnabled()&&o.userPanningEnabled()){if(t.preventDefault(),e.data.bgActivePosistion=void 0,e.redrawHint("select",!0),ee=e.dragData.touchDragEles){e.redrawHint("drag",!0);for(var O=0;O<ee.length;O++){var V=ee[O]._private;V.grabbed=!1,V.rscratch.inDragLayer=!1}}var F=e.touchData.start,j=(w=t.touches[0].clientX-M,E=t.touches[0].clientY-R,k=t.touches[1].clientX-M,_=t.touches[1].clientY-R,H(w,E,k,_)),X=j/B;if(L){var q=(w-T+(k-P))/2,Y=(E-C+(_-S))/2,W=o.zoom(),U=W*X,G=o.pan(),Z=A[0]*W+G.x,$=A[1]*W+G.y,J={x:-U/W*(Z-G.x-q)+Z,y:-U/W*($-G.y-Y)+$};if(F&&F.active()){var ee=e.dragData.touchDragEles;d(ee),e.redrawHint("drag",!0),e.redrawHint("eles",!0),F.unactivate().emit("freeon"),ee.emit("free"),e.dragData.didDrag&&(F.emit("dragfreeon"),ee.emit("dragfree"))}o.viewport({zoom:U,pan:J,cancelOnFailedZoom:!0}),o.emit("pinchzoom"),B=j,T=w,C=E,P=k,S=_,e.pinching=!0}if(t.touches[0]){h=e.projectIntoViewport(t.touches[0].clientX,t.touches[0].clientY);s[0]=h[0],s[1]=h[1]}if(t.touches[1]){h=e.projectIntoViewport(t.touches[1].clientX,t.touches[1].clientY);s[2]=h[0],s[3]=h[1]}if(t.touches[2]){h=e.projectIntoViewport(t.touches[2].clientX,t.touches[2].clientY);s[4]=h[0],s[5]=h[1]}}else if(t.touches[0]&&!e.touchData.didSelect){var te=e.touchData.start,ne=e.touchData.last;if(e.hoverData.draggingEles||e.swipePanning||(z=e.findNearestElement(s[0],s[1],!0,!0)),n&&null!=te&&t.preventDefault(),n&&null!=te&&e.nodeIsDraggable(te))if(f){ee=e.dragData.touchDragEles;var re=!e.dragData.didDrag;re&&u(ee,{inDragLayer:!0}),e.dragData.didDrag=!0;var ae={x:0,y:0};if(Q(g[0])&&Q(g[1]))if(ae.x+=g[0],ae.y+=g[1],re)e.redrawHint("eles",!0),(ie=e.touchData.dragDelta)&&Q(ie[0])&&Q(ie[1])&&(ae.x+=ie[0],ae.y+=ie[1]);e.hoverData.draggingEles=!0,ee.silentShift(ae).emit("position drag"),e.redrawHint("drag",!0),e.touchData.startPosition[0]==l[0]&&e.touchData.startPosition[1]==l[1]&&e.redrawHint("eles",!0),e.redraw()}else{var ie;0===(ie=e.touchData.dragDelta=e.touchData.dragDelta||[]).length?(ie.push(g[0]),ie.push(g[1])):(ie[0]+=g[0],ie[1]+=g[1])}if(r(te||z,["touchmove","tapdrag","vmousemove"],t,{x:s[0],y:s[1]}),te&&te.grabbed()||z==ne||(ne&&ne.emit({originalEvent:t,type:"tapdragout",position:{x:s[0],y:s[1]}}),z&&z.emit({originalEvent:t,type:"tapdragover",position:{x:s[0],y:s[1]}})),e.touchData.last=z,n)for(O=0;O<s.length;O++)s[O]&&e.touchData.startPosition[O]&&f&&(e.touchData.singleTouchMoved=!0);if(n&&(null==te||te.pannable())&&o.panningEnabled()&&o.userPanningEnabled()){i(te,e.touchData.starts)&&(t.preventDefault(),e.data.bgActivePosistion||(e.data.bgActivePosistion=Xt(e.touchData.startPosition)),e.swipePanning?(o.panBy({x:g[0]*c,y:g[1]*c}),o.emit("dragpan")):f&&(e.swipePanning=!0,o.panBy({x:y*c,y:x*c}),o.emit("dragpan"),te&&(te.unactivate(),e.redrawHint("select",!0),e.touchData.start=null)));h=e.projectIntoViewport(t.touches[0].clientX,t.touches[0].clientY);s[0]=h[0],s[1]=h[1]}}for(v=0;v<s.length;v++)l[v]=s[v];n&&t.touches.length>0&&!e.hoverData.draggingEles&&!e.swipePanning&&null!=e.data.bgActivePosistion&&(e.data.bgActivePosistion=void 0,e.redrawHint("select",!0),e.redraw())}},!1),e.registerBinding(t,"touchcancel",X=function(t){var n=e.touchData.start;e.touchData.capture=!1,n&&n.unactivate()}),e.registerBinding(t,"touchend",q=function(t){var a=e.touchData.start;if(e.touchData.capture){0===t.touches.length&&(e.touchData.capture=!1),t.preventDefault();var i=e.selection;e.swipePanning=!1,e.hoverData.draggingEles=!1;var o,s=e.cy,l=s.zoom(),u=e.touchData.now,c=e.touchData.earlier;if(t.touches[0]){var h=e.projectIntoViewport(t.touches[0].clientX,t.touches[0].clientY);u[0]=h[0],u[1]=h[1]}if(t.touches[1]){h=e.projectIntoViewport(t.touches[1].clientX,t.touches[1].clientY);u[2]=h[0],u[3]=h[1]}if(t.touches[2]){h=e.projectIntoViewport(t.touches[2].clientX,t.touches[2].clientY);u[4]=h[0],u[5]=h[1]}if(a&&a.unactivate(),e.touchData.cxt){if(o={originalEvent:t,type:"cxttapend",position:{x:u[0],y:u[1]}},a?a.emit(o):s.emit(o),!e.touchData.cxtDragged){var f={originalEvent:t,type:"cxttap",position:{x:u[0],y:u[1]}};a?a.emit(f):s.emit(f)}return e.touchData.start&&(e.touchData.start._private.grabbed=!1),e.touchData.cxt=!1,e.touchData.start=null,void e.redraw()}if(!t.touches[2]&&s.boxSelectionEnabled()&&e.touchData.selecting){e.touchData.selecting=!1;var p=s.collection(e.getAllInBox(i[0],i[1],i[2],i[3]));i[0]=void 0,i[1]=void 0,i[2]=void 0,i[3]=void 0,i[4]=0,e.redrawHint("select",!0),s.emit({type:"boxend",originalEvent:t,position:{x:u[0],y:u[1]}});p.emit("box").stdFilter((function(e){return e.selectable()&&!e.selected()})).select().emit("boxselect"),p.nonempty()&&e.redrawHint("eles",!0),e.redraw()}if(null!=a&&a.unactivate(),t.touches[2])e.data.bgActivePosistion=void 0,e.redrawHint("select",!0);else if(t.touches[1]);else if(t.touches[0]);else if(!t.touches[0]){e.data.bgActivePosistion=void 0,e.redrawHint("select",!0);var g=e.dragData.touchDragEles;if(null!=a){var v=a._private.grabbed;d(g),e.redrawHint("drag",!0),e.redrawHint("eles",!0),v&&(a.emit("freeon"),g.emit("free"),e.dragData.didDrag&&(a.emit("dragfreeon"),g.emit("dragfree"))),r(a,["touchend","tapend","vmouseup","tapdragout"],t,{x:u[0],y:u[1]}),a.unactivate(),e.touchData.start=null}else{var y=e.findNearestElement(u[0],u[1],!0,!0);r(y,["touchend","tapend","vmouseup","tapdragout"],t,{x:u[0],y:u[1]})}var m=e.touchData.startPosition[0]-u[0],b=m*m,x=e.touchData.startPosition[1]-u[1],w=(b+x*x)*l*l;e.touchData.singleTouchMoved||(a||s.$(":selected").unselect(["tapunselect"]),r(a,["tap","vclick"],t,{x:u[0],y:u[1]}),Y=!1,t.timeStamp-U<=s.multiClickDebounceTime()?(W&&clearTimeout(W),Y=!0,U=null,r(a,["dbltap","vdblclick"],t,{x:u[0],y:u[1]})):(W=setTimeout((function(){Y||r(a,["onetap","voneclick"],t,{x:u[0],y:u[1]})}),s.multiClickDebounceTime()),U=t.timeStamp)),null!=a&&!e.dragData.didDrag&&a._private.selectable&&w<e.touchTapThreshold2&&!e.pinching&&("single"===s.selectionType()?(s.$(n).unmerge(a).unselect(["tapunselect"]),a.select(["tapselect"])):a.selected()?a.unselect(["tapunselect"]):a.select(["tapselect"]),e.redrawHint("eles",!0)),e.touchData.singleTouchMoved=!0}for(var E=0;E<u.length;E++)c[E]=u[E];e.dragData.didDrag=!1,0===t.touches.length&&(e.touchData.dragDelta=[],e.touchData.startPosition=[null,null,null,null,null,null],e.touchData.startGPosition=null,e.touchData.didSelect=!1),t.touches.length<2&&(1===t.touches.length&&(e.touchData.startGPosition=[t.touches[0].clientX,t.touches[0].clientY]),e.pinching=!1,e.redrawHint("eles",!0),e.redraw())}},!1),"undefined"==typeof TouchEvent){var G=[],Z=function(e){return{clientX:e.clientX,clientY:e.clientY,force:1,identifier:e.pointerId,pageX:e.pageX,pageY:e.pageY,radiusX:e.width/2,radiusY:e.height/2,screenX:e.screenX,screenY:e.screenY,target:e.target}},$=function(e){G.push(function(e){return{event:e,touch:Z(e)}}(e))},J=function(e){for(var t=0;t<G.length;t++){if(G[t].event.pointerId===e.pointerId)return void G.splice(t,1)}},ee=function(e){e.touches=G.map((function(e){return e.touch}))},te=function(e){return"mouse"===e.pointerType||4===e.pointerType};e.registerBinding(e.container,"pointerdown",(function(e){te(e)||(e.preventDefault(),$(e),ee(e),F(e))})),e.registerBinding(e.container,"pointerup",(function(e){te(e)||(J(e),ee(e),q(e))})),e.registerBinding(e.container,"pointercancel",(function(e){te(e)||(J(e),ee(e),X())})),e.registerBinding(e.container,"pointermove",(function(e){te(e)||(e.preventDefault(),function(e){var t=G.filter((function(t){return t.event.pointerId===e.pointerId}))[0];t.event=e,t.touch=Z(e)}(e),ee(e),j(e))}))}};var Kc={generatePolygon:function(e,t){return this.nodeShapes[e]={renderer:this,name:e,points:t,draw:function(e,t,n,r,a,i){this.renderer.nodeShapeImpl("polygon",e,t,n,r,a,this.points)},intersectLine:function(e,t,n,r,a,i,o,s){return xn(a,i,this.points,e,t,n/2,r/2,o)},checkPoint:function(e,t,n,r,a,i,o,s){return fn(e,t,this.points,i,o,r,a,[0,-1],n)}}}};Kc.generateEllipse=function(){return this.nodeShapes.ellipse={renderer:this,name:"ellipse",draw:function(e,t,n,r,a,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,a)},intersectLine:function(e,t,n,r,a,i,o,s){return function(e,t,n,r,a,i){var o=n-e,s=r-t;o/=a,s/=i;var l=Math.sqrt(o*o+s*s),u=l-1;if(u<0)return[];var c=u/l;return[(n-e)*c+e,(r-t)*c+t]}(a,i,e,t,n/2+o,r/2+o)},checkPoint:function(e,t,n,r,a,i,o,s){return vn(e,t,r,a,i,o,n)}}},Kc.generateRoundPolygon=function(e,t){return this.nodeShapes[e]={renderer:this,name:e,points:t,getOrCreateCorners:function(e,n,r,a,i,o,s){if(void 0!==o[s]&&o[s+"-cx"]===e&&o[s+"-cy"]===n)return o[s];o[s]=new Array(t.length/2),o[s+"-cx"]=e,o[s+"-cy"]=n;var l=r/2,u=a/2;i="auto"===i?Pn(r,a):i;for(var c=new Array(t.length/2),d=0;d<t.length/2;d++)c[d]={x:e+l*t[2*d],y:n+u*t[2*d+1]};var h,f,p,g,v=c.length;for(f=c[v-1],h=0;h<v;h++)p=c[h%v],g=c[(h+1)%v],o[s][h]=_c(f,p,g,i),f=p,p=g;return o[s]},draw:function(e,t,n,r,a,i,o){this.renderer.nodeShapeImpl("round-polygon",e,t,n,r,a,this.points,this.getOrCreateCorners(t,n,r,a,i,o,"drawCorners"))},intersectLine:function(e,t,n,r,a,i,o,s,l){return function(e,t,n,r,a,i,o,s,l){var u,c=[],d=new Array(2*n.length);l.forEach((function(n,i){0===i?(d[d.length-2]=n.startX,d[d.length-1]=n.startY):(d[4*i-2]=n.startX,d[4*i-1]=n.startY),d[4*i]=n.stopX,d[4*i+1]=n.stopY,0!==(u=yn(e,t,r,a,n.cx,n.cy,n.radius)).length&&c.push(u[0],u[1])}));for(var h=0;h<d.length/4;h++)0!==(u=bn(e,t,r,a,d[4*h],d[4*h+1],d[4*h+2],d[4*h+3],!1)).length&&c.push(u[0],u[1]);if(c.length>2){for(var f=[c[0],c[1]],p=Math.pow(f[0]-e,2)+Math.pow(f[1]-t,2),g=1;g<c.length/2;g++){var v=Math.pow(c[2*g]-e,2)+Math.pow(c[2*g+1]-t,2);v<=p&&(f[0]=c[2*g],f[1]=c[2*g+1],p=v)}return f}return c}(a,i,this.points,e,t,0,0,0,this.getOrCreateCorners(e,t,n,r,s,l,"corners"))},checkPoint:function(e,t,n,r,a,i,o,s,l){return function(e,t,n,r,a,i,o,s){for(var l=new Array(2*n.length),u=0;u<s.length;u++){var c=s[u];if(l[4*u+0]=c.startX,l[4*u+1]=c.startY,l[4*u+2]=c.stopX,l[4*u+3]=c.stopY,Math.pow(c.cx-e,2)+Math.pow(c.cy-t,2)<=Math.pow(c.radius,2))return!0}return hn(e,t,l)}(e,t,this.points,0,0,0,0,this.getOrCreateCorners(i,o,r,a,s,l,"corners"))}}},Kc.generateRoundRectangle=function(){return this.nodeShapes["round-rectangle"]=this.nodeShapes.roundrectangle={renderer:this,name:"round-rectangle",points:En(4,0),draw:function(e,t,n,r,a,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,a,this.points,i)},intersectLine:function(e,t,n,r,a,i,o,s){return sn(a,i,e,t,n,r,o,s)},checkPoint:function(e,t,n,r,a,i,o,s){var l=r/2,u=a/2;s="auto"===s?Cn(r,a):s;var c=2*(s=Math.min(l,u,s));return!!fn(e,t,this.points,i,o,r,a-c,[0,-1],n)||(!!fn(e,t,this.points,i,o,r-c,a,[0,-1],n)||(!!vn(e,t,c,c,i-l+s,o-u+s,n)||(!!vn(e,t,c,c,i+l-s,o-u+s,n)||(!!vn(e,t,c,c,i+l-s,o+u-s,n)||!!vn(e,t,c,c,i-l+s,o+u-s,n)))))}}},Kc.generateCutRectangle=function(){return this.nodeShapes["cut-rectangle"]=this.nodeShapes.cutrectangle={renderer:this,name:"cut-rectangle",cornerLength:8,points:En(4,0),draw:function(e,t,n,r,a,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,a,null,i)},generateCutTrianglePts:function(e,t,n,r,a){var i="auto"===a?this.cornerLength:a,o=t/2,s=e/2,l=n-s,u=n+s,c=r-o,d=r+o;return{topLeft:[l,c+i,l+i,c,l+i,c+i],topRight:[u-i,c,u,c+i,u-i,c+i],bottomRight:[u,d-i,u-i,d,u-i,d-i],bottomLeft:[l+i,d,l,d-i,l+i,d-i]}},intersectLine:function(e,t,n,r,a,i,o,s){var l=this.generateCutTrianglePts(n+2*o,r+2*o,e,t,s),u=[].concat.apply([],[l.topLeft.splice(0,4),l.topRight.splice(0,4),l.bottomRight.splice(0,4),l.bottomLeft.splice(0,4)]);return xn(a,i,u,e,t)},checkPoint:function(e,t,n,r,a,i,o,s){var l="auto"===s?this.cornerLength:s;if(fn(e,t,this.points,i,o,r,a-2*l,[0,-1],n))return!0;if(fn(e,t,this.points,i,o,r-2*l,a,[0,-1],n))return!0;var u=this.generateCutTrianglePts(r,a,i,o);return hn(e,t,u.topLeft)||hn(e,t,u.topRight)||hn(e,t,u.bottomRight)||hn(e,t,u.bottomLeft)}}},Kc.generateBarrel=function(){return this.nodeShapes.barrel={renderer:this,name:"barrel",points:En(4,0),draw:function(e,t,n,r,a,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,a)},intersectLine:function(e,t,n,r,a,i,o,s){var l=this.generateBarrelBezierPts(n+2*o,r+2*o,e,t),u=function(e){var t=Zt({x:e[0],y:e[1]},{x:e[2],y:e[3]},{x:e[4],y:e[5]},.15),n=Zt({x:e[0],y:e[1]},{x:e[2],y:e[3]},{x:e[4],y:e[5]},.5),r=Zt({x:e[0],y:e[1]},{x:e[2],y:e[3]},{x:e[4],y:e[5]},.85);return[e[0],e[1],t.x,t.y,n.x,n.y,r.x,r.y,e[4],e[5]]},c=[].concat(u(l.topLeft),u(l.topRight),u(l.bottomRight),u(l.bottomLeft));return xn(a,i,c,e,t)},generateBarrelBezierPts:function(e,t,n,r){var a=t/2,i=e/2,o=n-i,s=n+i,l=r-a,u=r+a,c=Sn(e,t),d=c.heightOffset,h=c.widthOffset,f=c.ctrlPtOffsetPct*e,p={topLeft:[o,l+d,o+f,l,o+h,l],topRight:[s-h,l,s-f,l,s,l+d],bottomRight:[s,u-d,s-f,u,s-h,u],bottomLeft:[o+h,u,o+f,u,o,u-d]};return p.topLeft.isTop=!0,p.topRight.isTop=!0,p.bottomLeft.isBottom=!0,p.bottomRight.isBottom=!0,p},checkPoint:function(e,t,n,r,a,i,o,s){var l=Sn(r,a),u=l.heightOffset,c=l.widthOffset;if(fn(e,t,this.points,i,o,r,a-2*u,[0,-1],n))return!0;if(fn(e,t,this.points,i,o,r-2*c,a,[0,-1],n))return!0;for(var d=this.generateBarrelBezierPts(r,a,i,o),h=function(e,t,n){var r,a,i=n[4],o=n[2],s=n[0],l=n[5],u=n[1],c=Math.min(i,s),d=Math.max(i,s),h=Math.min(l,u),f=Math.max(l,u);if(c<=e&&e<=d&&h<=t&&t<=f){var p=[(r=i)-2*(a=o)+s,2*(a-r),r],g=function(e,t,n,r){var a=t*t-4*e*(n-=r);if(a<0)return[];var i=Math.sqrt(a),o=2*e;return[(-t+i)/o,(-t-i)/o]}(p[0],p[1],p[2],e).filter((function(e){return 0<=e&&e<=1}));if(g.length>0)return g[0]}return null},f=Object.keys(d),p=0;p<f.length;p++){var g=d[f[p]],v=h(e,t,g);if(null!=v){var y=g[5],m=g[3],b=g[1],x=Gt(y,m,b,v);if(g.isTop&&x<=t)return!0;if(g.isBottom&&t<=x)return!0}}return!1}}},Kc.generateBottomRoundrectangle=function(){return this.nodeShapes["bottom-round-rectangle"]=this.nodeShapes.bottomroundrectangle={renderer:this,name:"bottom-round-rectangle",points:En(4,0),draw:function(e,t,n,r,a,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,a,this.points,i)},intersectLine:function(e,t,n,r,a,i,o,s){var l=t-(r/2+o),u=bn(a,i,e,t,e-(n/2+o),l,e+(n/2+o),l,!1);return u.length>0?u:sn(a,i,e,t,n,r,o,s)},checkPoint:function(e,t,n,r,a,i,o,s){var l=2*(s="auto"===s?Cn(r,a):s);if(fn(e,t,this.points,i,o,r,a-l,[0,-1],n))return!0;if(fn(e,t,this.points,i,o,r-l,a,[0,-1],n))return!0;var u=r/2+2*n,c=a/2+2*n;return!!hn(e,t,[i-u,o-c,i-u,o,i+u,o,i+u,o-c])||(!!vn(e,t,l,l,i+r/2-s,o+a/2-s,n)||!!vn(e,t,l,l,i-r/2+s,o+a/2-s,n))}}},Kc.registerNodeShapes=function(){var e=this.nodeShapes={},t=this;this.generateEllipse(),this.generatePolygon("triangle",En(3,0)),this.generateRoundPolygon("round-triangle",En(3,0)),this.generatePolygon("rectangle",En(4,0)),e.square=e.rectangle,this.generateRoundRectangle(),this.generateCutRectangle(),this.generateBarrel(),this.generateBottomRoundrectangle();var n=[0,1,1,0,0,-1,-1,0];this.generatePolygon("diamond",n),this.generateRoundPolygon("round-diamond",n),this.generatePolygon("pentagon",En(5,0)),this.generateRoundPolygon("round-pentagon",En(5,0)),this.generatePolygon("hexagon",En(6,0)),this.generateRoundPolygon("round-hexagon",En(6,0)),this.generatePolygon("heptagon",En(7,0)),this.generateRoundPolygon("round-heptagon",En(7,0)),this.generatePolygon("octagon",En(8,0)),this.generateRoundPolygon("round-octagon",En(8,0));var r=new Array(20),a=Tn(5,0),i=Tn(5,Math.PI/5),o=.5*(3-Math.sqrt(5));o*=1.57;for(var s=0;s<i.length/2;s++)i[2*s]*=o,i[2*s+1]*=o;for(s=0;s<5;s++)r[4*s]=a[2*s],r[4*s+1]=a[2*s+1],r[4*s+2]=i[2*s],r[4*s+3]=i[2*s+1];r=kn(r),this.generatePolygon("star",r),this.generatePolygon("vee",[-1,-1,0,-.333,1,-1,0,1]),this.generatePolygon("rhomboid",[-1,-1,.333,-1,1,1,-.333,1]),this.generatePolygon("right-rhomboid",[-.333,-1,1,-1,.333,1,-1,1]),this.nodeShapes.concavehexagon=this.generatePolygon("concave-hexagon",[-1,-.95,-.75,0,-1,.95,1,.95,.75,0,1,-.95]);var l=[-1,-1,.25,-1,1,0,.25,1,-1,1];this.generatePolygon("tag",l),this.generateRoundPolygon("round-tag",l),e.makePolygon=function(e){var n,r="polygon-"+e.join("$");return(n=this[r])?n:t.generatePolygon(r,e)}};var Gc={timeToRender:function(){return this.redrawTotalTime/this.redrawCount},redraw:function(e){e=e||lt();var t=this;void 0===t.averageRedrawTime&&(t.averageRedrawTime=0),void 0===t.lastRedrawTime&&(t.lastRedrawTime=0),void 0===t.lastDrawTime&&(t.lastDrawTime=0),t.requestedFrame=!0,t.renderOptions=e},beforeRender:function(e,t){if(!this.destroyed){null==t&&nt("Priority is not optional for beforeRender");var n=this.beforeRenderCallbacks;n.push({fn:e,priority:t}),n.sort((function(e,t){return t.priority-e.priority}))}}},Zc=function(e,t,n){for(var r=e.beforeRenderCallbacks,a=0;a<r.length;a++)r[a].fn(t,n)};Gc.startRenderLoop=function(){var e=this,t=e.cy;if(!e.renderLoopStarted){e.renderLoopStarted=!0;var n=function(r){if(!e.destroyed){if(t.batching());else if(e.requestedFrame&&!e.skipFrame){Zc(e,!0,r);var a=Le();e.render(e.renderOptions);var i=e.lastDrawTime=Le();void 0===e.averageRedrawTime&&(e.averageRedrawTime=i-a),void 0===e.redrawCount&&(e.redrawCount=0),e.redrawCount++,void 0===e.redrawTotalTime&&(e.redrawTotalTime=0);var o=i-a;e.redrawTotalTime+=o,e.lastRedrawTime=o,e.averageRedrawTime=e.averageRedrawTime/2+o/2,e.requestedFrame=!1}else Zc(e,!1,r);e.skipFrame=!1,Ne(n)}};Ne(n)}};var $c=function(e){this.init(e)},Qc=$c.prototype;Qc.clientFunctions=["redrawHint","render","renderTo","matchCanvasSize","nodeShapeImpl","arrowShapeImpl"],Qc.init=function(e){var t=this;t.options=e,t.cy=e.cy;var n=t.container=e.cy.container(),r=t.cy.window();if(r){var a=r.document,i=a.head,o="__________cytoscape_stylesheet",s="__________cytoscape_container",l=null!=a.getElementById(o);if(n.className.indexOf(s)<0&&(n.className=(n.className||"")+" "+s),!l){var u=a.createElement("style");u.id=o,u.textContent="."+s+" { position: relative; }",i.insertBefore(u,i.children[0])}"static"===r.getComputedStyle(n).getPropertyValue("position")&&at("A Cytoscape container has style position:static and so can not use UI extensions properly")}t.selection=[void 0,void 0,void 0,void 0,0],t.bezierProjPcts=[.05,.225,.4,.5,.6,.775,.95],t.hoverData={down:null,last:null,downTime:null,triggerMode:null,dragging:!1,initialPan:[null,null],capture:!1},t.dragData={possibleDragElements:[]},t.touchData={start:null,capture:!1,startPosition:[null,null,null,null,null,null],singleTouchStartTime:null,singleTouchMoved:!0,now:[null,null,null,null,null,null],earlier:[null,null,null,null,null,null]},t.redraws=0,t.showFps=e.showFps,t.debug=e.debug,t.webgl=e.webgl,t.hideEdgesOnViewport=e.hideEdgesOnViewport,t.textureOnViewport=e.textureOnViewport,t.wheelSensitivity=e.wheelSensitivity,t.motionBlurEnabled=e.motionBlur,t.forcedPixelRatio=Q(e.pixelRatio)?e.pixelRatio:null,t.motionBlur=e.motionBlur,t.motionBlurOpacity=e.motionBlurOpacity,t.motionBlurTransparency=1-t.motionBlurOpacity,t.motionBlurPxRatio=1,t.mbPxRBlurry=1,t.minMbLowQualFrames=4,t.fullQualityMb=!1,t.clearedForMotionBlur=[],t.desktopTapThreshold=e.desktopTapThreshold,t.desktopTapThreshold2=e.desktopTapThreshold*e.desktopTapThreshold,t.touchTapThreshold=e.touchTapThreshold,t.touchTapThreshold2=e.touchTapThreshold*e.touchTapThreshold,t.tapholdDuration=500,t.bindings=[],t.beforeRenderCallbacks=[],t.beforeRenderPriorities={animations:400,eleCalcs:300,eleTxrDeq:200,lyrTxrDeq:150,lyrTxrSkip:100},t.registerNodeShapes(),t.registerArrowShapes(),t.registerCalculationListeners()},Qc.notify=function(e,t){var n=this,r=n.cy;this.destroyed||("init"!==e?"destroy"!==e?(("add"===e||"remove"===e||"move"===e&&r.hasCompoundNodes()||"load"===e||"zorder"===e||"mount"===e)&&n.invalidateCachedZSortedEles(),"viewport"===e&&n.redrawHint("select",!0),"gc"===e&&n.redrawHint("gc",!0),"load"!==e&&"resize"!==e&&"mount"!==e||(n.invalidateContainerClientCoordsCache(),n.matchCanvasSize(n.container)),n.redrawHint("eles",!0),n.redrawHint("drag",!0),this.startRenderLoop(),this.redraw()):n.destroy():n.load())},Qc.destroy=function(){var e=this;e.destroyed=!0,e.cy.stopAnimationLoop();for(var t=0;t<e.bindings.length;t++){var n=e.bindings[t],r=n.target;(r.off||r.removeEventListener).apply(r,n.args)}if(e.bindings=[],e.beforeRenderCallbacks=[],e.onUpdateEleCalcsFns=[],e.removeObserver&&e.removeObserver.disconnect(),e.styleObserver&&e.styleObserver.disconnect(),e.resizeObserver&&e.resizeObserver.disconnect(),e.labelCalcDiv)try{document.body.removeChild(e.labelCalcDiv)}catch(a){}},Qc.isHeadless=function(){return!1},[ic,Wc,Uc,Hc,Kc,Gc].forEach((function(e){me(Qc,e)}));var Jc=1e3/60,ed=function(e){return function(){var t=this,n=this.renderer;if(!t.dequeueingSetup){t.dequeueingSetup=!0;var r=Ae((function(){n.redrawHint("eles",!0),n.redrawHint("drag",!0),n.redraw()}),e.deqRedrawThreshold),a=e.priority||tt;n.beforeRender((function(a,i){var o=Le(),s=n.averageRedrawTime,l=n.lastRedrawTime,u=[],c=n.cy.extent(),d=n.getPixelRatio();for(a||n.flushRenderedStyleQueue();;){var h=Le(),f=h-o,p=h-i;if(l<Jc){var g=Jc-(a?s:0);if(p>=e.deqFastCost*g)break}else if(a){if(f>=e.deqCost*l||f>=e.deqAvgCost*s)break}else if(p>=e.deqNoDrawCost*Jc)break;var v=e.deq(t,d,c);if(!(v.length>0))break;for(var y=0;y<v.length;y++)u.push(v[y])}u.length>0&&(e.onDeqd(t,u),!a&&e.shouldRedraw(t,u,d,c)&&r())}),a(t))}}},td=function(){return i((function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Je;a(this,e),this.idsByKey=new pt,this.keyForId=new pt,this.cachesByLvl=new pt,this.lvls=[],this.getKey=t,this.doesEleInvalidateKey=n}),[{key:"getIdsFor",value:function(e){null==e&&nt("Can not get id list for null key");var t=this.idsByKey,n=this.idsByKey.get(e);return n||(n=new vt,t.set(e,n)),n}},{key:"addIdForKey",value:function(e,t){null!=e&&this.getIdsFor(e).add(t)}},{key:"deleteIdForKey",value:function(e,t){null!=e&&this.getIdsFor(e).delete(t)}},{key:"getNumberOfIdsForKey",value:function(e){return null==e?0:this.getIdsFor(e).size}},{key:"updateKeyMappingFor",value:function(e){var t=e.id(),n=this.keyForId.get(t),r=this.getKey(e);this.deleteIdForKey(n,t),this.addIdForKey(r,t),this.keyForId.set(t,r)}},{key:"deleteKeyMappingFor",value:function(e){var t=e.id(),n=this.keyForId.get(t);this.deleteIdForKey(n,t),this.keyForId.delete(t)}},{key:"keyHasChangedFor",value:function(e){var t=e.id();return this.keyForId.get(t)!==this.getKey(e)}},{key:"isInvalid",value:function(e){return this.keyHasChangedFor(e)||this.doesEleInvalidateKey(e)}},{key:"getCachesAt",value:function(e){var t=this.cachesByLvl,n=this.lvls,r=t.get(e);return r||(r=new pt,t.set(e,r),n.push(e)),r}},{key:"getCache",value:function(e,t){return this.getCachesAt(t).get(e)}},{key:"get",value:function(e,t){var n=this.getKey(e),r=this.getCache(n,t);return null!=r&&this.updateKeyMappingFor(e),r}},{key:"getForCachedKey",value:function(e,t){var n=this.keyForId.get(e.id());return this.getCache(n,t)}},{key:"hasCache",value:function(e,t){return this.getCachesAt(t).has(e)}},{key:"has",value:function(e,t){var n=this.getKey(e);return this.hasCache(n,t)}},{key:"setCache",value:function(e,t,n){n.key=e,this.getCachesAt(t).set(e,n)}},{key:"set",value:function(e,t,n){var r=this.getKey(e);this.setCache(r,t,n),this.updateKeyMappingFor(e)}},{key:"deleteCache",value:function(e,t){this.getCachesAt(t).delete(e)}},{key:"delete",value:function(e,t){var n=this.getKey(e);this.deleteCache(n,t)}},{key:"invalidateKey",value:function(e){var t=this;this.lvls.forEach((function(n){return t.deleteCache(e,n)}))}},{key:"invalidate",value:function(e){var t=e.id(),n=this.keyForId.get(t);this.deleteKeyMappingFor(e);var r=this.doesEleInvalidateKey(e);return r&&this.invalidateKey(n),r||0===this.getNumberOfIdsForKey(n)}}])}(),nd=7.99,rd={dequeue:"dequeue",downscale:"downscale",highQuality:"highQuality"},ad=ut({getKey:null,doesEleInvalidateKey:Je,drawElement:null,getBoundingBox:null,getRotationPoint:null,getRotationOffset:null,isVisible:Qe,allowEdgeTxrCaching:!0,allowParentTxrCaching:!0}),id=function(e,t){var n=this;n.renderer=e,n.onDequeues=[];var r=ad(t);me(n,r),n.lookup=new td(r.getKey,r.doesEleInvalidateKey),n.setupDequeueing()},od=id.prototype;od.reasons=rd,od.getTextureQueue=function(e){var t=this;return t.eleImgCaches=t.eleImgCaches||{},t.eleImgCaches[e]=t.eleImgCaches[e]||[]},od.getRetiredTextureQueue=function(e){var t=this.eleImgCaches.retired=this.eleImgCaches.retired||{};return t[e]=t[e]||[]},od.getElementQueue=function(){return this.eleCacheQueue=this.eleCacheQueue||new Pt((function(e,t){return t.reqs-e.reqs}))},od.getElementKeyToQueue=function(){return this.eleKeyToCacheQueue=this.eleKeyToCacheQueue||{}},od.getElement=function(e,t,n,r,a){var i=this,o=this.renderer,s=o.cy.zoom(),l=this.lookup;if(!t||0===t.w||0===t.h||isNaN(t.w)||isNaN(t.h)||!e.visible()||e.removed())return null;if(!i.allowEdgeTxrCaching&&e.isEdge()||!i.allowParentTxrCaching&&e.isParent())return null;if(null==r&&(r=Math.ceil(Yt(s*n))),r<-4)r=-4;else if(s>=7.99||r>3)return null;var u=Math.pow(2,r),c=t.h*u,d=t.w*u,h=o.eleTextBiggerThanMin(e,u);if(!this.isVisible(e,h))return null;var f,p=l.get(e,r);if(p&&p.invalidated&&(p.invalidated=!1,p.texture.invalidatedWidth-=p.width),p)return p;if(f=c<=25?25:c<=50?50:50*Math.ceil(c/50),c>1024||d>1024)return null;var g=i.getTextureQueue(f),v=g[g.length-2],y=function(){return i.recycleTexture(f,d)||i.addTexture(f,d)};v||(v=g[g.length-1]),v||(v=y()),v.width-v.usedWidth<d&&(v=y());for(var m,b=function(e){return e&&e.scaledLabelShown===h},x=a&&a===rd.dequeue,w=a&&a===rd.highQuality,E=a&&a===rd.downscale,k=r+1;k<=3;k++){var T=l.get(e,k);if(T){m=T;break}}var C=m&&m.level===r+1?m:null,P=function(){v.context.drawImage(C.texture.canvas,C.x,0,C.width,C.height,v.usedWidth,0,d,c)};if(v.context.setTransform(1,0,0,1,0,0),v.context.clearRect(v.usedWidth,0,d,f),b(C))P();else if(b(m)){if(!w)return i.queueElement(e,m.level-1),m;for(var S=m.level;S>r;S--)C=i.getElement(e,t,n,S,rd.downscale);P()}else{var B;if(!x&&!w&&!E)for(var D=r-1;D>=-4;D--){var _=l.get(e,D);if(_){B=_;break}}if(b(B))return i.queueElement(e,r),B;v.context.translate(v.usedWidth,0),v.context.scale(u,u),this.drawElement(v.context,e,t,h,!1),v.context.scale(1/u,1/u),v.context.translate(-v.usedWidth,0)}return p={x:v.usedWidth,texture:v,level:r,scale:u,width:d,height:c,scaledLabelShown:h},v.usedWidth+=Math.ceil(d+8),v.eleCaches.push(p),l.set(e,r,p),i.checkTextureFullness(v),p},od.invalidateElements=function(e){for(var t=0;t<e.length;t++)this.invalidateElement(e[t])},od.invalidateElement=function(e){var t=this,n=t.lookup,r=[];if(n.isInvalid(e)){for(var a=-4;a<=3;a++){var i=n.getForCachedKey(e,a);i&&r.push(i)}if(n.invalidate(e))for(var o=0;o<r.length;o++){var s=r[o],l=s.texture;l.invalidatedWidth+=s.width,s.invalidated=!0,t.checkTextureUtility(l)}t.removeFromQueue(e)}},od.checkTextureUtility=function(e){e.invalidatedWidth>=.2*e.width&&this.retireTexture(e)},od.checkTextureFullness=function(e){var t=this.getTextureQueue(e.height);e.usedWidth/e.width>.8&&e.fullnessChecks>=10?ct(t,e):e.fullnessChecks++},od.retireTexture=function(e){var t=e.height,n=this.getTextureQueue(t),r=this.lookup;ct(n,e),e.retired=!0;for(var a=e.eleCaches,i=0;i<a.length;i++){var o=a[i];r.deleteCache(o.key,o.level)}dt(a),this.getRetiredTextureQueue(t).push(e)},od.addTexture=function(e,t){var n={};return this.getTextureQueue(e).push(n),n.eleCaches=[],n.height=e,n.width=Math.max(1024,t),n.usedWidth=0,n.invalidatedWidth=0,n.fullnessChecks=0,n.canvas=this.renderer.makeOffscreenCanvas(n.width,n.height),n.context=n.canvas.getContext("2d"),n},od.recycleTexture=function(e,t){for(var n=this.getTextureQueue(e),r=this.getRetiredTextureQueue(e),a=0;a<r.length;a++){var i=r[a];if(i.width>=t)return i.retired=!1,i.usedWidth=0,i.invalidatedWidth=0,i.fullnessChecks=0,dt(i.eleCaches),i.context.setTransform(1,0,0,1,0,0),i.context.clearRect(0,0,i.width,i.height),ct(r,i),n.push(i),i}},od.queueElement=function(e,t){var n=this.getElementQueue(),r=this.getElementKeyToQueue(),a=this.getKey(e),i=r[a];if(i)i.level=Math.max(i.level,t),i.eles.merge(e),i.reqs++,n.updateItem(i);else{var o={eles:e.spawn().merge(e),level:t,reqs:1,key:a};n.push(o),r[a]=o}},od.dequeue=function(e){for(var t=this,n=t.getElementQueue(),r=t.getElementKeyToQueue(),a=[],i=t.lookup,o=0;o<1&&n.size()>0;o++){var s=n.pop(),l=s.key,u=s.eles[0],c=i.hasCache(u,s.level);if(r[l]=null,!c){a.push(s);var d=t.getBoundingBox(u);t.getElement(u,d,e,s.level,rd.dequeue)}}return a},od.removeFromQueue=function(e){var t=this.getElementQueue(),n=this.getElementKeyToQueue(),r=this.getKey(e),a=n[r];null!=a&&(1===a.eles.length?(a.reqs=$e,t.updateItem(a),t.pop(),n[r]=null):a.eles.unmerge(e))},od.onDequeue=function(e){this.onDequeues.push(e)},od.offDequeue=function(e){ct(this.onDequeues,e)},od.setupDequeueing=ed({deqRedrawThreshold:100,deqCost:.15,deqAvgCost:.1,deqNoDrawCost:.9,deqFastCost:.9,deq:function(e,t,n){return e.dequeue(t,n)},onDeqd:function(e,t){for(var n=0;n<e.onDequeues.length;n++){(0,e.onDequeues[n])(t)}},shouldRedraw:function(e,t,n,r){for(var a=0;a<t.length;a++)for(var i=t[a].eles,o=0;o<i.length;o++){var s=i[o].boundingBox();if(an(s,r))return!0}return!1},priority:function(e){return e.renderer.beforeRenderPriorities.eleTxrDeq}});var sd=function(e){var t=this,n=t.renderer=e,r=n.cy;t.layersByLevel={},t.firstGet=!0,t.lastInvalidationTime=Le()-500,t.skipping=!1,t.eleTxrDeqs=r.collection(),t.scheduleElementRefinement=Ae((function(){t.refineElementTextures(t.eleTxrDeqs),t.eleTxrDeqs.unmerge(t.eleTxrDeqs)}),50),n.beforeRender((function(e,n){n-t.lastInvalidationTime<=250?t.skipping=!0:t.skipping=!1}),n.beforeRenderPriorities.lyrTxrSkip);t.layersQueue=new Pt((function(e,t){return t.reqs-e.reqs})),t.setupDequeueing()},ld=sd.prototype,ud=0,cd=Math.pow(2,53)-1;ld.makeLayer=function(e,t){var n=Math.pow(2,t),r=Math.ceil(e.w*n),a=Math.ceil(e.h*n),i=this.renderer.makeOffscreenCanvas(r,a),o={id:ud=++ud%cd,bb:e,level:t,width:r,height:a,canvas:i,context:i.getContext("2d"),eles:[],elesQueue:[],reqs:0},s=o.context,l=-o.bb.x1,u=-o.bb.y1;return s.scale(n,n),s.translate(l,u),o},ld.getLayers=function(e,t,n){var r=this,a=r.renderer.cy.zoom(),i=r.firstGet;if(r.firstGet=!1,null==n)if((n=Math.ceil(Yt(a*t)))<-4)n=-4;else if(a>=3.99||n>2)return null;r.validateLayersElesOrdering(n,e);var o,s,l=r.layersByLevel,u=Math.pow(2,n),c=l[n]=l[n]||[];if(r.levelIsComplete(n,e))return c;!function(){var t=function(t){if(r.validateLayersElesOrdering(t,e),r.levelIsComplete(t,e))return s=l[t],!0},a=function(e){if(!s)for(var r=n+e;-4<=r&&r<=2&&!t(r);r+=e);};a(1),a(-1);for(var i=c.length-1;i>=0;i--){var o=c[i];o.invalid&&ct(c,o)}}();var d=function(t){var a=(t=t||{}).after;!function(){if(!o){o=Qt();for(var t=0;t<e.length;t++)Jt(o,e[t].boundingBox())}}();var i=Math.ceil(o.w*u),s=Math.ceil(o.h*u);if(i>32767||s>32767)return null;if(i*s>16e6)return null;var l=r.makeLayer(o,n);if(null!=a){var d=c.indexOf(a)+1;c.splice(d,0,l)}else(void 0===t.insert||t.insert)&&c.unshift(l);return l};if(r.skipping&&!i)return null;for(var h,f,p=null,g=e.length/1,v=!i,y=0;y<e.length;y++){var m=e[y],b=m._private.rscratch,x=b.imgLayerCaches=b.imgLayerCaches||{},w=x[n];if(w)p=w;else{if((!p||p.eles.length>=g||(h=p.bb,f=m.boundingBox(),!on(h,f.x1,f.y1)||!on(h,f.x2,f.y2)))&&!(p=d({insert:!0,after:p})))return null;s||v?r.queueLayer(p,m):r.drawEleInLayer(p,m,n,t),p.eles.push(m),x[n]=p}}return s||(v?null:c)},ld.getEleLevelForLayerLevel=function(e,t){return e},ld.drawEleInLayer=function(e,t,n,r){var a=this.renderer,i=e.context,o=t.boundingBox();0!==o.w&&0!==o.h&&t.visible()&&(n=this.getEleLevelForLayerLevel(n,r),a.setImgSmoothing(i,!1),a.drawCachedElement(i,t,null,null,n,true),a.setImgSmoothing(i,!0))},ld.levelIsComplete=function(e,t){var n=this.layersByLevel[e];if(!n||0===n.length)return!1;for(var r=0,a=0;a<n.length;a++){var i=n[a];if(i.reqs>0)return!1;if(i.invalid)return!1;r+=i.eles.length}return r===t.length},ld.validateLayersElesOrdering=function(e,t){var n=this.layersByLevel[e];if(n)for(var r=0;r<n.length;r++){for(var a=n[r],i=-1,o=0;o<t.length;o++)if(a.eles[0]===t[o]){i=o;break}if(i<0)this.invalidateLayer(a);else{var s=i;for(o=0;o<a.eles.length;o++)if(a.eles[o]!==t[s+o]){this.invalidateLayer(a);break}}}},ld.updateElementsInLayers=function(e,t){for(var n=te(e[0]),r=0;r<e.length;r++)for(var a=n?null:e[r],i=n?e[r]:e[r].ele,o=i._private.rscratch,s=o.imgLayerCaches=o.imgLayerCaches||{},l=-4;l<=2;l++){var u=s[l];u&&(a&&this.getEleLevelForLayerLevel(u.level)!==a.level||t(u,i,a))}},ld.haveLayers=function(){for(var e=!1,t=-4;t<=2;t++){var n=this.layersByLevel[t];if(n&&n.length>0){e=!0;break}}return e},ld.invalidateElements=function(e){var t=this;0!==e.length&&(t.lastInvalidationTime=Le(),0!==e.length&&t.haveLayers()&&t.updateElementsInLayers(e,(function(e,n,r){t.invalidateLayer(e)})))},ld.invalidateLayer=function(e){if(this.lastInvalidationTime=Le(),!e.invalid){var t=e.level,n=e.eles,r=this.layersByLevel[t];ct(r,e),e.elesQueue=[],e.invalid=!0,e.replacement&&(e.replacement.invalid=!0);for(var a=0;a<n.length;a++){var i=n[a]._private.rscratch.imgLayerCaches;i&&(i[t]=null)}}},ld.refineElementTextures=function(e){var t=this;t.updateElementsInLayers(e,(function(e,n,r){var a=e.replacement;if(a||((a=e.replacement=t.makeLayer(e.bb,e.level)).replaces=e,a.eles=e.eles),!a.reqs)for(var i=0;i<a.eles.length;i++)t.queueLayer(a,a.eles[i])}))},ld.enqueueElementRefinement=function(e){this.eleTxrDeqs.merge(e),this.scheduleElementRefinement()},ld.queueLayer=function(e,t){var n=this.layersQueue,r=e.elesQueue,a=r.hasId=r.hasId||{};if(!e.replacement){if(t){if(a[t.id()])return;r.push(t),a[t.id()]=!0}e.reqs?(e.reqs++,n.updateItem(e)):(e.reqs=1,n.push(e))}},ld.dequeue=function(e){for(var t=this,n=t.layersQueue,r=[],a=0;a<1&&0!==n.size();){var i=n.peek();if(i.replacement)n.pop();else if(i.replaces&&i!==i.replaces.replacement)n.pop();else if(i.invalid)n.pop();else{var o=i.elesQueue.shift();o&&(t.drawEleInLayer(i,o,i.level,e),a++),0===r.length&&r.push(!0),0===i.elesQueue.length&&(n.pop(),i.reqs=0,i.replaces&&t.applyLayerReplacement(i),t.requestRedraw())}}return r},ld.applyLayerReplacement=function(e){var t=this.layersByLevel[e.level],n=e.replaces,r=t.indexOf(n);if(!(r<0||n.invalid)){t[r]=e;for(var a=0;a<e.eles.length;a++){var i=e.eles[a]._private,o=i.imgLayerCaches=i.imgLayerCaches||{};o&&(o[e.level]=e)}this.requestRedraw()}},ld.requestRedraw=Ae((function(){var e=this.renderer;e.redrawHint("eles",!0),e.redrawHint("drag",!0),e.redraw()}),100),ld.setupDequeueing=ed({deqRedrawThreshold:50,deqCost:.15,deqAvgCost:.1,deqNoDrawCost:.9,deqFastCost:.9,deq:function(e,t){return e.dequeue(t)},onDeqd:tt,shouldRedraw:Qe,priority:function(e){return e.renderer.beforeRenderPriorities.lyrTxrDeq}});var dd,hd={};function fd(e,t){for(var n=0;n<t.length;n++){var r=t[n];e.lineTo(r.x,r.y)}}function pd(e,t,n){for(var r,a=0;a<t.length;a++){var i=t[a];0===a&&(r=i),e.lineTo(i.x,i.y)}e.quadraticCurveTo(n.x,n.y,r.x,r.y)}function gd(e,t,n){e.beginPath&&e.beginPath();for(var r=t,a=0;a<r.length;a++){var i=r[a];e.lineTo(i.x,i.y)}var o=n,s=n[0];e.moveTo(s.x,s.y);for(a=1;a<o.length;a++){i=o[a];e.lineTo(i.x,i.y)}e.closePath&&e.closePath()}function vd(e,t,n,r,a){e.beginPath&&e.beginPath(),e.arc(n,r,a,0,2*Math.PI,!1);var i=t,o=i[0];e.moveTo(o.x,o.y);for(var s=0;s<i.length;s++){var l=i[s];e.lineTo(l.x,l.y)}e.closePath&&e.closePath()}function yd(e,t,n,r){e.arc(t,n,r,0,2*Math.PI,!1)}hd.arrowShapeImpl=function(e){return(dd||(dd={polygon:fd,"triangle-backcurve":pd,"triangle-tee":gd,"circle-triangle":vd,"triangle-cross":gd,circle:yd}))[e]};var md={drawElement:function(e,t,n,r,a,i){t.isNode()?this.drawNode(e,t,n,r,a,i):this.drawEdge(e,t,n,r,a,i)},drawElementOverlay:function(e,t){t.isNode()?this.drawNodeOverlay(e,t):this.drawEdgeOverlay(e,t)},drawElementUnderlay:function(e,t){t.isNode()?this.drawNodeUnderlay(e,t):this.drawEdgeUnderlay(e,t)},drawCachedElementPortion:function(e,t,n,r,a,i,o,s){var l=this,u=n.getBoundingBox(t);if(0!==u.w&&0!==u.h){var c=n.getElement(t,u,r,a,i);if(null!=c){var d=s(l,t);if(0===d)return;var h,f,p,g,v,y,m=o(l,t),b=u.x1,x=u.y1,w=u.w,E=u.h;if(0!==m){var k=n.getRotationPoint(t);p=k.x,g=k.y,e.translate(p,g),e.rotate(m),(v=l.getImgSmoothing(e))||l.setImgSmoothing(e,!0);var T=n.getRotationOffset(t);h=T.x,f=T.y}else h=b,f=x;1!==d&&(y=e.globalAlpha,e.globalAlpha=y*d),e.drawImage(c.texture.canvas,c.x,0,c.width,c.height,h,f,w,E),1!==d&&(e.globalAlpha=y),0!==m&&(e.rotate(-m),e.translate(-p,-g),v||l.setImgSmoothing(e,!1))}else n.drawElement(e,t)}}},bd=function(){return 0},xd=function(e,t){return e.getTextAngle(t,null)},wd=function(e,t){return e.getTextAngle(t,"source")},Ed=function(e,t){return e.getTextAngle(t,"target")},kd=function(e,t){return t.effectiveOpacity()},Td=function(e,t){return t.pstyle("text-opacity").pfValue*t.effectiveOpacity()};md.drawCachedElement=function(e,t,n,r,a,i){var o=this,s=o.data,l=s.eleTxrCache,u=s.lblTxrCache,c=s.slbTxrCache,d=s.tlbTxrCache,h=t.boundingBox(),f=!0===i?l.reasons.highQuality:null;if(0!==h.w&&0!==h.h&&t.visible()&&(!r||an(h,r))){var p=t.isEdge(),g=t.element()._private.rscratch.badLine;o.drawElementUnderlay(e,t),o.drawCachedElementPortion(e,t,l,n,a,f,bd,kd),p&&g||o.drawCachedElementPortion(e,t,u,n,a,f,xd,Td),p&&!g&&(o.drawCachedElementPortion(e,t,c,n,a,f,wd,Td),o.drawCachedElementPortion(e,t,d,n,a,f,Ed,Td)),o.drawElementOverlay(e,t)}},md.drawElements=function(e,t){for(var n=0;n<t.length;n++){var r=t[n];this.drawElement(e,r)}},md.drawCachedElements=function(e,t,n,r){for(var a=0;a<t.length;a++){var i=t[a];this.drawCachedElement(e,i,n,r)}},md.drawCachedNodes=function(e,t,n,r){for(var a=0;a<t.length;a++){var i=t[a];i.isNode()&&this.drawCachedElement(e,i,n,r)}},md.drawLayeredElements=function(e,t,n,r){var a=this.data.lyrTxrCache.getLayers(t,n);if(a)for(var i=0;i<a.length;i++){var o=a[i],s=o.bb;0!==s.w&&0!==s.h&&e.drawImage(o.canvas,s.x1,s.y1,s.w,s.h)}else this.drawCachedElements(e,t,n,r)};var Cd={drawEdge:function(e,t,n){var r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],a=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],i=!(arguments.length>5&&void 0!==arguments[5])||arguments[5],o=this,s=t._private.rscratch;if((!i||t.visible())&&!s.badLine&&null!=s.allpts&&!isNaN(s.allpts[0])){var l;n&&(l=n,e.translate(-l.x1,-l.y1));var u=i?t.pstyle("opacity").value:1,c=i?t.pstyle("line-opacity").value:1,d=t.pstyle("curve-style").value,h=t.pstyle("line-style").value,f=t.pstyle("width").pfValue,p=t.pstyle("line-cap").value,g=t.pstyle("line-outline-width").value,v=t.pstyle("line-outline-color").value,y=u*c,m=u*c,b=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:y;"straight-triangle"===d?(o.eleStrokeStyle(e,t,n),o.drawEdgeTrianglePath(t,e,s.allpts)):(e.lineWidth=f,e.lineCap=p,o.eleStrokeStyle(e,t,n),o.drawEdgePath(t,e,s.allpts,h),e.lineCap="butt")},x=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m;o.drawArrowheads(e,t,n)};if(e.lineJoin="round","yes"===t.pstyle("ghost").value){var w=t.pstyle("ghost-offset-x").pfValue,E=t.pstyle("ghost-offset-y").pfValue,k=t.pstyle("ghost-opacity").value,T=y*k;e.translate(w,E),b(T),x(T),e.translate(-w,-E)}else!function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:y;e.lineWidth=f+g,e.lineCap=p,g>0?(o.colorStrokeStyle(e,v[0],v[1],v[2],n),"straight-triangle"===d?o.drawEdgeTrianglePath(t,e,s.allpts):(o.drawEdgePath(t,e,s.allpts,h),e.lineCap="butt")):e.lineCap="butt"}();a&&o.drawEdgeUnderlay(e,t),b(),x(),a&&o.drawEdgeOverlay(e,t),o.drawElementText(e,t,null,r),n&&e.translate(l.x1,l.y1)}}},Pd=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(t,n){if(n.visible()){var r=n.pstyle("".concat(e,"-opacity")).value;if(0!==r){var a=this,i=a.usePaths(),o=n._private.rscratch,s=2*n.pstyle("".concat(e,"-padding")).pfValue,l=n.pstyle("".concat(e,"-color")).value;t.lineWidth=s,"self"!==o.edgeType||i?t.lineCap="round":t.lineCap="butt",a.colorStrokeStyle(t,l[0],l[1],l[2],r),a.drawEdgePath(n,t,o.allpts,"solid")}}}};Cd.drawEdgeOverlay=Pd("overlay"),Cd.drawEdgeUnderlay=Pd("underlay"),Cd.drawEdgePath=function(e,t,n,r){var a,i=e._private.rscratch,s=t,l=!1,u=this.usePaths(),c=e.pstyle("line-dash-pattern").pfValue,d=e.pstyle("line-dash-offset").pfValue;if(u){var h=n.join("$");i.pathCacheKey&&i.pathCacheKey===h?(a=t=i.pathCache,l=!0):(a=t=new Path2D,i.pathCacheKey=h,i.pathCache=a)}if(s.setLineDash)switch(r){case"dotted":s.setLineDash([1,1]);break;case"dashed":s.setLineDash(c),s.lineDashOffset=d;break;case"solid":s.setLineDash([])}if(!l&&!i.badLine)switch(t.beginPath&&t.beginPath(),t.moveTo(n[0],n[1]),i.edgeType){case"bezier":case"self":case"compound":case"multibezier":for(var f=2;f+3<n.length;f+=4)t.quadraticCurveTo(n[f],n[f+1],n[f+2],n[f+3]);break;case"straight":case"haystack":for(var p=2;p+1<n.length;p+=2)t.lineTo(n[p],n[p+1]);break;case"segments":if(i.isRound){var g,v=o(i.roundCorners);try{for(v.s();!(g=v.n()).done;){Dc(t,g.value)}}catch(m){v.e(m)}finally{v.f()}t.lineTo(n[n.length-2],n[n.length-1])}else for(var y=2;y+1<n.length;y+=2)t.lineTo(n[y],n[y+1])}t=s,u?t.stroke(a):t.stroke(),t.setLineDash&&t.setLineDash([])},Cd.drawEdgeTrianglePath=function(e,t,n){t.fillStyle=t.strokeStyle;for(var r=e.pstyle("width").pfValue,a=0;a+1<n.length;a+=2){var i=[n[a+2]-n[a],n[a+3]-n[a+1]],o=Math.sqrt(i[0]*i[0]+i[1]*i[1]),s=[i[1]/o,-i[0]/o],l=[s[0]*r/2,s[1]*r/2];t.beginPath(),t.moveTo(n[a]-l[0],n[a+1]-l[1]),t.lineTo(n[a]+l[0],n[a+1]+l[1]),t.lineTo(n[a+2],n[a+3]),t.closePath(),t.fill()}},Cd.drawArrowheads=function(e,t,n){var r=t._private.rscratch,a="haystack"===r.edgeType;a||this.drawArrowhead(e,t,"source",r.arrowStartX,r.arrowStartY,r.srcArrowAngle,n),this.drawArrowhead(e,t,"mid-target",r.midX,r.midY,r.midtgtArrowAngle,n),this.drawArrowhead(e,t,"mid-source",r.midX,r.midY,r.midsrcArrowAngle,n),a||this.drawArrowhead(e,t,"target",r.arrowEndX,r.arrowEndY,r.tgtArrowAngle,n)},Cd.drawArrowhead=function(e,t,n,r,a,i,o){if(!(isNaN(r)||null==r||isNaN(a)||null==a||isNaN(i)||null==i)){var s=this,l=t.pstyle(n+"-arrow-shape").value;if("none"!==l){var u="hollow"===t.pstyle(n+"-arrow-fill").value?"both":"filled",c=t.pstyle(n+"-arrow-fill").value,d=t.pstyle("width").pfValue,h=t.pstyle(n+"-arrow-width"),f="match-line"===h.value?d:h.pfValue;"%"===h.units&&(f*=d);var p=t.pstyle("opacity").value;void 0===o&&(o=p);var g=e.globalCompositeOperation;1===o&&"hollow"!==c||(e.globalCompositeOperation="destination-out",s.colorFillStyle(e,255,255,255,1),s.colorStrokeStyle(e,255,255,255,1),s.drawArrowShape(t,e,u,d,l,f,r,a,i),e.globalCompositeOperation=g);var v=t.pstyle(n+"-arrow-color").value;s.colorFillStyle(e,v[0],v[1],v[2],o),s.colorStrokeStyle(e,v[0],v[1],v[2],o),s.drawArrowShape(t,e,c,d,l,f,r,a,i)}}},Cd.drawArrowShape=function(e,t,n,r,a,i,o,s,l){var u,c=this,d=this.usePaths()&&"triangle-cross"!==a,h=!1,f=t,p={x:o,y:s},g=e.pstyle("arrow-scale").value,v=this.getArrowWidth(r,g),y=c.arrowShapes[a];if(d){var m=c.arrowPathCache=c.arrowPathCache||[],b=We(a),x=m[b];null!=x?(u=t=x,h=!0):(u=t=new Path2D,m[b]=u)}h||(t.beginPath&&t.beginPath(),d?y.draw(t,1,0,{x:0,y:0},1):y.draw(t,v,l,p,r),t.closePath&&t.closePath()),t=f,d&&(t.translate(o,s),t.rotate(l),t.scale(v,v)),"filled"!==n&&"both"!==n||(d?t.fill(u):t.fill()),"hollow"!==n&&"both"!==n||(t.lineWidth=i/(d?v:1),t.lineJoin="miter",d?t.stroke(u):t.stroke()),d&&(t.scale(1/v,1/v),t.rotate(-l),t.translate(-o,-s))};var Sd={safeDrawImage:function(e,t,n,r,a,i,o,s,l,u){if(!(a<=0||i<=0||l<=0||u<=0))try{e.drawImage(t,n,r,a,i,o,s,l,u)}catch(c){at(c)}},drawInscribedImage:function(e,t,n,r,a){var i=this,o=n.position(),s=o.x,l=o.y,u=n.cy().style(),c=u.getIndexedStyle.bind(u),d=c(n,"background-fit","value",r),h=c(n,"background-repeat","value",r),f=n.width(),p=n.height(),g=2*n.padding(),v=f+("inner"===c(n,"background-width-relative-to","value",r)?0:g),y=p+("inner"===c(n,"background-height-relative-to","value",r)?0:g),m=n._private.rscratch,b="node"===c(n,"background-clip","value",r),x=c(n,"background-image-opacity","value",r)*a,w=c(n,"background-image-smoothing","value",r),E=n.pstyle("corner-radius").value;"auto"!==E&&(E=n.pstyle("corner-radius").pfValue);var k=t.width||t.cachedW,T=t.height||t.cachedH;null!=k&&null!=T||(document.body.appendChild(t),k=t.cachedW=t.width||t.offsetWidth,T=t.cachedH=t.height||t.offsetHeight,document.body.removeChild(t));var C=k,P=T;if("auto"!==c(n,"background-width","value",r)&&(C="%"===c(n,"background-width","units",r)?c(n,"background-width","pfValue",r)*v:c(n,"background-width","pfValue",r)),"auto"!==c(n,"background-height","value",r)&&(P="%"===c(n,"background-height","units",r)?c(n,"background-height","pfValue",r)*y:c(n,"background-height","pfValue",r)),0!==C&&0!==P){if("contain"===d)C*=S=Math.min(v/C,y/P),P*=S;else if("cover"===d){var S;C*=S=Math.max(v/C,y/P),P*=S}var B=s-v/2,D=c(n,"background-position-x","units",r),_=c(n,"background-position-x","pfValue",r);B+="%"===D?(v-C)*_:_;var A=c(n,"background-offset-x","units",r),M=c(n,"background-offset-x","pfValue",r);B+="%"===A?(v-C)*M:M;var R=l-y/2,I=c(n,"background-position-y","units",r),N=c(n,"background-position-y","pfValue",r);R+="%"===I?(y-P)*N:N;var L=c(n,"background-offset-y","units",r),z=c(n,"background-offset-y","pfValue",r);R+="%"===L?(y-P)*z:z,m.pathCache&&(B-=s,R-=l,s=0,l=0);var O=e.globalAlpha;e.globalAlpha=x;var V=i.getImgSmoothing(e),F=!1;if("no"===w&&V?(i.setImgSmoothing(e,!1),F=!0):"yes"!==w||V||(i.setImgSmoothing(e,!0),F=!0),"no-repeat"===h)b&&(e.save(),m.pathCache?e.clip(m.pathCache):(i.nodeShapes[i.getNodeShape(n)].draw(e,s,l,v,y,E,m),e.clip())),i.safeDrawImage(e,t,0,0,k,T,B,R,C,P),b&&e.restore();else{var j=e.createPattern(t,h);e.fillStyle=j,i.nodeShapes[i.getNodeShape(n)].draw(e,s,l,v,y,E,m),e.translate(B,R),e.fill(),e.translate(-B,-R)}e.globalAlpha=O,F&&i.setImgSmoothing(e,V)}}},Bd={};function Dd(e,t,n,r,a){var i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:5,o=arguments.length>6?arguments[6]:void 0;e.beginPath(),e.moveTo(t+i,n),e.lineTo(t+r-i,n),e.quadraticCurveTo(t+r,n,t+r,n+i),e.lineTo(t+r,n+a-i),e.quadraticCurveTo(t+r,n+a,t+r-i,n+a),e.lineTo(t+i,n+a),e.quadraticCurveTo(t,n+a,t,n+a-i),e.lineTo(t,n+i),e.quadraticCurveTo(t,n,t+i,n),e.closePath(),o?e.stroke():e.fill()}Bd.eleTextBiggerThanMin=function(e,t){if(!t){var n=e.cy().zoom(),r=this.getPixelRatio(),a=Math.ceil(Yt(n*r));t=Math.pow(2,a)}return!(e.pstyle("font-size").pfValue*t<e.pstyle("min-zoomed-font-size").pfValue)},Bd.drawElementText=function(e,t,n,r,a){var i=!(arguments.length>5&&void 0!==arguments[5])||arguments[5],o=this;if(null==r){if(i&&!o.eleTextBiggerThanMin(t))return}else if(!1===r)return;if(t.isNode()){var s=t.pstyle("label");if(!s||!s.value)return;var l=o.getLabelJustification(t);e.textAlign=l,e.textBaseline="bottom"}else{var u=t.element()._private.rscratch.badLine,c=t.pstyle("label"),d=t.pstyle("source-label"),h=t.pstyle("target-label");if(u||(!c||!c.value)&&(!d||!d.value)&&(!h||!h.value))return;e.textAlign="center",e.textBaseline="bottom"}var f,p=!n;n&&(f=n,e.translate(-f.x1,-f.y1)),null==a?(o.drawText(e,t,null,p,i),t.isEdge()&&(o.drawText(e,t,"source",p,i),o.drawText(e,t,"target",p,i))):o.drawText(e,t,a,p,i),n&&e.translate(f.x1,f.y1)},Bd.getFontCache=function(e){var t;this.fontCaches=this.fontCaches||[];for(var n=0;n<this.fontCaches.length;n++)if((t=this.fontCaches[n]).context===e)return t;return t={context:e},this.fontCaches.push(t),t},Bd.setupTextStyle=function(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=t.pstyle("font-style").strValue,a=t.pstyle("font-size").pfValue+"px",i=t.pstyle("font-family").strValue,o=t.pstyle("font-weight").strValue,s=n?t.effectiveOpacity()*t.pstyle("text-opacity").value:1,l=t.pstyle("text-outline-opacity").value*s,u=t.pstyle("color").value,c=t.pstyle("text-outline-color").value;e.font=r+" "+o+" "+a+" "+i,e.lineJoin="round",this.colorFillStyle(e,u[0],u[1],u[2],s),this.colorStrokeStyle(e,c[0],c[1],c[2],l)},Bd.getTextAngle=function(e,t){var n,r=e._private.rscratch,a=t?t+"-":"",i=e.pstyle(a+"text-rotation");if("autorotate"===i.strValue){var o=ht(r,"labelAngle",t);n=e.isEdge()?o:0}else n="none"===i.strValue?0:i.pfValue;return n},Bd.drawText=function(e,t,n){var r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],a=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],i=t._private.rscratch,o=a?t.effectiveOpacity():1;if(!a||0!==o&&0!==t.pstyle("text-opacity").value){"main"===n&&(n=null);var s,l,u=ht(i,"labelX",n),c=ht(i,"labelY",n),d=this.getLabelText(t,n);if(null!=d&&""!==d&&!isNaN(u)&&!isNaN(c)){this.setupTextStyle(e,t,a);var h,f=n?n+"-":"",p=ht(i,"labelWidth",n),g=ht(i,"labelHeight",n),v=t.pstyle(f+"text-margin-x").pfValue,y=t.pstyle(f+"text-margin-y").pfValue,m=t.isEdge(),b=t.pstyle("text-halign").value,x=t.pstyle("text-valign").value;switch(m&&(b="center",x="center"),u+=v,c+=y,0!==(h=r?this.getTextAngle(t,n):0)&&(s=u,l=c,e.translate(s,l),e.rotate(h),u=0,c=0),x){case"top":break;case"center":c+=g/2;break;case"bottom":c+=g}var w=t.pstyle("text-background-opacity").value,E=t.pstyle("text-border-opacity").value,k=t.pstyle("text-border-width").pfValue,T=t.pstyle("text-background-padding").pfValue,C=0===t.pstyle("text-background-shape").strValue.indexOf("round");if(w>0||k>0&&E>0){var P=u-T;switch(b){case"left":P-=p;break;case"center":P-=p/2}var S=c-g-T,B=p+2*T,D=g+2*T;if(w>0){var _=e.fillStyle,A=t.pstyle("text-background-color").value;e.fillStyle="rgba("+A[0]+","+A[1]+","+A[2]+","+w*o+")",C?Dd(e,P,S,B,D,2):e.fillRect(P,S,B,D),e.fillStyle=_}if(k>0&&E>0){var M=e.strokeStyle,R=e.lineWidth,I=t.pstyle("text-border-color").value,N=t.pstyle("text-border-style").value;if(e.strokeStyle="rgba("+I[0]+","+I[1]+","+I[2]+","+E*o+")",e.lineWidth=k,e.setLineDash)switch(N){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash([4,2]);break;case"double":e.lineWidth=k/4,e.setLineDash([]);break;case"solid":e.setLineDash([])}if(C?Dd(e,P,S,B,D,2,"stroke"):e.strokeRect(P,S,B,D),"double"===N){var L=k/2;C?Dd(e,P+L,S+L,B-2*L,D-2*L,2,"stroke"):e.strokeRect(P+L,S+L,B-2*L,D-2*L)}e.setLineDash&&e.setLineDash([]),e.lineWidth=R,e.strokeStyle=M}}var z=2*t.pstyle("text-outline-width").pfValue;if(z>0&&(e.lineWidth=z),"wrap"===t.pstyle("text-wrap").value){var O=ht(i,"labelWrapCachedLines",n),V=ht(i,"labelLineHeight",n),F=p/2,j=this.getLabelJustification(t);switch("auto"===j||("left"===b?"left"===j?u+=-p:"center"===j&&(u+=-F):"center"===b?"left"===j?u+=-F:"right"===j&&(u+=F):"right"===b&&("center"===j?u+=F:"right"===j&&(u+=p))),x){case"top":case"center":case"bottom":c-=(O.length-1)*V}for(var X=0;X<O.length;X++)z>0&&e.strokeText(O[X],u,c),e.fillText(O[X],u,c),c+=V}else z>0&&e.strokeText(d,u,c),e.fillText(d,u,c);0!==h&&(e.rotate(-h),e.translate(-s,-l))}}};var _d={drawNode:function(e,t,n){var r,a,i=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],o=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],s=!(arguments.length>5&&void 0!==arguments[5])||arguments[5],l=this,u=t._private,c=u.rscratch,d=t.position();if(Q(d.x)&&Q(d.y)&&(!s||t.visible())){var h,f,p=s?t.effectiveOpacity():1,g=l.usePaths(),v=!1,y=t.padding();r=t.width()+2*y,a=t.height()+2*y,n&&(f=n,e.translate(-f.x1,-f.y1));for(var m=t.pstyle("background-image").value,b=new Array(m.length),x=new Array(m.length),w=0,E=0;E<m.length;E++){var k=m[E];if(b[E]=null!=k&&"none"!==k){var T=t.cy().style().getIndexedStyle(t,"background-image-crossorigin","value",E);w++,x[E]=l.getCachedImage(k,T,(function(){u.backgroundTimestamp=Date.now(),t.emitAndNotify("background")}))}}var C=t.pstyle("background-blacken").value,P=t.pstyle("border-width").pfValue,S=t.pstyle("background-opacity").value*p,B=t.pstyle("border-color").value,D=t.pstyle("border-style").value,_=t.pstyle("border-join").value,A=t.pstyle("border-cap").value,M=t.pstyle("border-position").value,R=t.pstyle("border-dash-pattern").pfValue,I=t.pstyle("border-dash-offset").pfValue,N=t.pstyle("border-opacity").value*p,L=t.pstyle("outline-width").pfValue,z=t.pstyle("outline-color").value,O=t.pstyle("outline-style").value,V=t.pstyle("outline-opacity").value*p,F=t.pstyle("outline-offset").value,j=t.pstyle("corner-radius").value;"auto"!==j&&(j=t.pstyle("corner-radius").pfValue);var X=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:S;l.eleFillStyle(e,t,n)},q=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:N;l.colorStrokeStyle(e,B[0],B[1],B[2],t)},Y=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:V;l.colorStrokeStyle(e,z[0],z[1],z[2],t)},W=function(e,t,n,r){var a,i=l.nodePathCache=l.nodePathCache||[],o=Ue("polygon"===n?n+","+r.join(","):n,""+t,""+e,""+j),s=i[o],u=!1;return null!=s?(a=s,u=!0,c.pathCache=a):(a=new Path2D,i[o]=c.pathCache=a),{path:a,cacheHit:u}},U=t.pstyle("shape").strValue,H=t.pstyle("shape-polygon-points").pfValue;if(g){e.translate(d.x,d.y);var K=W(r,a,U,H);h=K.path,v=K.cacheHit}var G=function(){if(!v){var n=d;g&&(n={x:0,y:0}),l.nodeShapes[l.getNodeShape(t)].draw(h||e,n.x,n.y,r,a,j,c)}g?e.fill(h):e.fill()},Z=function(){for(var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:p,r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],a=u.backgrounding,i=0,o=0;o<x.length;o++){var s=t.cy().style().getIndexedStyle(t,"background-image-containment","value",o);r&&"over"===s||!r&&"inside"===s?i++:b[o]&&x[o].complete&&!x[o].error&&(i++,l.drawInscribedImage(e,x[o],t,o,n))}u.backgrounding=!(i===w),a!==u.backgrounding&&t.updateStyle(!1)},$=function(){var n=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p;l.hasPie(t)&&(l.drawPie(e,t,i),n&&(g||l.nodeShapes[l.getNodeShape(t)].draw(e,d.x,d.y,r,a,j,c)))},J=function(){var n=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p;l.hasStripe(t)&&(e.save(),g?e.clip(c.pathCache):(l.nodeShapes[l.getNodeShape(t)].draw(e,d.x,d.y,r,a,j,c),e.clip()),l.drawStripe(e,t,i),e.restore(),n&&(g||l.nodeShapes[l.getNodeShape(t)].draw(e,d.x,d.y,r,a,j,c)))},ee=function(){var t=(C>0?C:-C)*(arguments.length>0&&void 0!==arguments[0]?arguments[0]:p),n=C>0?0:255;0!==C&&(l.colorFillStyle(e,n,n,n,t),g?e.fill(h):e.fill())},te=function(){if(P>0){if(e.lineWidth=P,e.lineCap=A,e.lineJoin=_,e.setLineDash)switch(D){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash(R),e.lineDashOffset=I;break;case"solid":case"double":e.setLineDash([])}if("center"!==M){if(e.save(),e.lineWidth*=2,"inside"===M)g?e.clip(h):e.clip();else{var t=new Path2D;t.rect(-r/2-P,-a/2-P,r+2*P,a+2*P),t.addPath(h),e.clip(t,"evenodd")}g?e.stroke(h):e.stroke(),e.restore()}else g?e.stroke(h):e.stroke();if("double"===D){e.lineWidth=P/3;var n=e.globalCompositeOperation;e.globalCompositeOperation="destination-out",g?e.stroke(h):e.stroke(),e.globalCompositeOperation=n}e.setLineDash&&e.setLineDash([])}},ne=function(){if(L>0){if(e.lineWidth=L,e.lineCap="butt",e.setLineDash)switch(O){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash([4,2]);break;case"solid":case"double":e.setLineDash([])}var n=d;g&&(n={x:0,y:0});var i=l.getNodeShape(t),o=P;"inside"===M&&(o=0),"outside"===M&&(o*=2);var s,u=(r+o+(L+F))/r,c=(a+o+(L+F))/a,h=r*u,f=a*c,p=l.nodeShapes[i].points;if(g)s=W(h,f,i,p).path;if("ellipse"===i)l.drawEllipsePath(s||e,n.x,n.y,h,f);else if(["round-diamond","round-heptagon","round-hexagon","round-octagon","round-pentagon","round-polygon","round-triangle","round-tag"].includes(i)){var v=0,y=0,m=0;"round-diamond"===i?v=1.4*(o+F+L):"round-heptagon"===i?(v=1.075*(o+F+L),m=-(o/2+F+L)/35):"round-hexagon"===i?v=1.12*(o+F+L):"round-pentagon"===i?(v=1.13*(o+F+L),m=-(o/2+F+L)/15):"round-tag"===i?(v=1.12*(o+F+L),y=.07*(o/2+L+F)):"round-triangle"===i&&(v=(o+F+L)*(Math.PI/2),m=-(o+F/2+L)/Math.PI),0!==v&&(h=r*(u=(r+v)/r),["round-hexagon","round-tag"].includes(i)||(f=a*(c=(a+v)/a)));for(var b=h/2,x=f/2,w=(j="auto"===j?Pn(h,f):j)+(o+L+F)/2,E=new Array(p.length/2),k=new Array(p.length/2),T=0;T<p.length/2;T++)E[T]={x:n.x+y+b*p[2*T],y:n.y+m+x*p[2*T+1]};var C,S,B,D,_=E.length;for(S=E[_-1],C=0;C<_;C++)B=E[C%_],D=E[(C+1)%_],k[C]=_c(S,B,D,w),S=B,B=D;l.drawRoundPolygonPath(s||e,n.x+y,n.y+m,r*u,a*c,p,k)}else if(["roundrectangle","round-rectangle"].includes(i))j="auto"===j?Cn(h,f):j,l.drawRoundRectanglePath(s||e,n.x,n.y,h,f,j+(o+L+F)/2);else if(["cutrectangle","cut-rectangle"].includes(i))j="auto"===j?8:j,l.drawCutRectanglePath(s||e,n.x,n.y,h,f,null,j+(o+L+F)/4);else if(["bottomroundrectangle","bottom-round-rectangle"].includes(i))j="auto"===j?Cn(h,f):j,l.drawBottomRoundRectanglePath(s||e,n.x,n.y,h,f,j+(o+L+F)/2);else if("barrel"===i)l.drawBarrelPath(s||e,n.x,n.y,h,f);else if(i.startsWith("polygon")||["rhomboid","right-rhomboid","round-tag","tag","vee"].includes(i)){p=pn(gn(p,(o+L+F)/r)),l.drawPolygonPath(s||e,n.x,n.y,r,a,p)}else{p=pn(gn(p,-((o+L+F)/r))),l.drawPolygonPath(s||e,n.x,n.y,r,a,p)}if(g?e.stroke(s):e.stroke(),"double"===O){e.lineWidth=o/3;var A=e.globalCompositeOperation;e.globalCompositeOperation="destination-out",g?e.stroke(s):e.stroke(),e.globalCompositeOperation=A}e.setLineDash&&e.setLineDash([])}};if("yes"===t.pstyle("ghost").value){var re=t.pstyle("ghost-offset-x").pfValue,ae=t.pstyle("ghost-offset-y").pfValue,ie=t.pstyle("ghost-opacity").value,oe=ie*p;e.translate(re,ae),Y(),ne(),X(ie*S),G(),Z(oe,!0),q(ie*N),te(),$(0!==C||0!==P),J(0!==C||0!==P),Z(oe,!1),ee(oe),e.translate(-re,-ae)}g&&e.translate(-d.x,-d.y),o&&l.drawNodeUnderlay(e,t,d,r,a),g&&e.translate(d.x,d.y),Y(),ne(),X(),G(),Z(p,!0),q(),te(),$(0!==C||0!==P),J(0!==C||0!==P),Z(p,!1),ee(),g&&e.translate(-d.x,-d.y),l.drawElementText(e,t,null,i),o&&l.drawNodeOverlay(e,t,d,r,a),n&&e.translate(f.x1,f.y1)}}},Ad=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(t,n,r,a,i){if(n.visible()){var o=n.pstyle("".concat(e,"-padding")).pfValue,s=n.pstyle("".concat(e,"-opacity")).value,l=n.pstyle("".concat(e,"-color")).value,u=n.pstyle("".concat(e,"-shape")).value,c=n.pstyle("".concat(e,"-corner-radius")).value;if(s>0){if(r=r||n.position(),null==a||null==i){var d=n.padding();a=n.width()+2*d,i=n.height()+2*d}this.colorFillStyle(t,l[0],l[1],l[2],s),this.nodeShapes[u].draw(t,r.x,r.y,a+2*o,i+2*o,c),t.fill()}}}};_d.drawNodeOverlay=Ad("overlay"),_d.drawNodeUnderlay=Ad("underlay"),_d.hasPie=function(e){return(e=e[0])._private.hasPie},_d.hasStripe=function(e){return(e=e[0])._private.hasStripe},_d.drawPie=function(e,t,n,r){t=t[0],r=r||t.position();var a,i=t.cy().style(),o=t.pstyle("pie-size"),s=t.pstyle("pie-hole"),l=t.pstyle("pie-start-angle").pfValue,u=r.x,c=r.y,d=t.width(),h=t.height(),f=Math.min(d,h)/2,p=0;if(this.usePaths()&&(u=0,c=0),"%"===o.units?f*=o.pfValue:void 0!==o.pfValue&&(f=o.pfValue/2),"%"===s.units?a=f*s.pfValue:void 0!==s.pfValue&&(a=s.pfValue/2),!(a>=f))for(var g=1;g<=i.pieBackgroundN;g++){var v=t.pstyle("pie-"+g+"-background-size").value,y=t.pstyle("pie-"+g+"-background-color").value,m=t.pstyle("pie-"+g+"-background-opacity").value*n,b=v/100;b+p>1&&(b=1-p);var x=1.5*Math.PI+2*Math.PI*p,w=(x+=l)+2*Math.PI*b;0===v||p>=1||p+b>1||(0===a?(e.beginPath(),e.moveTo(u,c),e.arc(u,c,f,x,w),e.closePath()):(e.beginPath(),e.arc(u,c,f,x,w),e.arc(u,c,a,w,x,!0),e.closePath()),this.colorFillStyle(e,y[0],y[1],y[2],m),e.fill(),p+=b)}},_d.drawStripe=function(e,t,n,r){t=t[0],r=r||t.position();var a=t.cy().style(),i=r.x,o=r.y,s=t.width(),l=t.height(),u=0,c=this.usePaths();e.save();var d=t.pstyle("stripe-direction").value,h=t.pstyle("stripe-size");switch(d){case"vertical":break;case"righward":e.rotate(-Math.PI/2)}var f=s,p=l;"%"===h.units?(f*=h.pfValue,p*=h.pfValue):void 0!==h.pfValue&&(f=h.pfValue,p=h.pfValue),c&&(i=0,o=0),o-=f/2,i-=p/2;for(var g=1;g<=a.stripeBackgroundN;g++){var v=t.pstyle("stripe-"+g+"-background-size").value,y=t.pstyle("stripe-"+g+"-background-color").value,m=t.pstyle("stripe-"+g+"-background-opacity").value*n,b=v/100;b+u>1&&(b=1-u),0===v||u>=1||u+b>1||(e.beginPath(),e.rect(i,o+p*u,f,p*b),e.closePath(),this.colorFillStyle(e,y[0],y[1],y[2],m),e.fill(),u+=b)}e.restore()};var Md,Rd={};function Id(e,t,n){var r=e.createShader(t);if(e.shaderSource(r,n),e.compileShader(r),!e.getShaderParameter(r,e.COMPILE_STATUS))throw new Error(e.getShaderInfoLog(r));return r}function Nd(e,t,n){void 0===n&&(n=t);var r=e.makeOffscreenCanvas(t,n),a=r.context=r.getContext("2d");return r.clear=function(){return a.clearRect(0,0,r.width,r.height)},r.clear(),r}function Ld(e){var t=e.pixelRatio,n=e.cy.zoom(),r=e.cy.pan();return{zoom:n*t,pan:{x:r.x*t,y:r.y*t}}}function zd(e){return"solid"===e.pstyle("background-fill").value&&("none"===e.pstyle("background-image").strValue&&(0===e.pstyle("border-width").value||(0===e.pstyle("border-opacity").value||"solid"===e.pstyle("border-style").value)))}function Od(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function Vd(e,t,n){var r=e[0]/255,a=e[1]/255,i=e[2]/255,o=t,s=n||new Array(4);return s[0]=r*o,s[1]=a*o,s[2]=i*o,s[3]=o,s}function Fd(e,t){var n=t||new Array(4);return n[0]=(255&e)/255,n[1]=(e>>8&255)/255,n[2]=(e>>16&255)/255,n[3]=(e>>24&255)/255,n}function jd(e){return e[0]+(e[1]<<8)+(e[2]<<16)+(e[3]<<24)}function Xd(e,t){switch(t){case"float":return[1,e.FLOAT,4];case"vec2":return[2,e.FLOAT,4];case"vec3":return[3,e.FLOAT,4];case"vec4":return[4,e.FLOAT,4];case"int":return[1,e.INT,4];case"ivec2":return[2,e.INT,4]}}function qd(e,t,n){switch(t){case e.FLOAT:return new Float32Array(n);case e.INT:return new Int32Array(n)}}function Yd(e,t,n,r,a,i){switch(t){case e.FLOAT:return new Float32Array(n.buffer,i*r,a);case e.INT:return new Int32Array(n.buffer,i*r,a)}}function Wd(e,t,n,r){var a=l(Xd(e,n),3),i=a[0],o=a[1],s=a[2],u=qd(e,o,t*i),c=i*s,d=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,d),e.bufferData(e.ARRAY_BUFFER,t*c,e.DYNAMIC_DRAW),e.enableVertexAttribArray(r),o===e.FLOAT?e.vertexAttribPointer(r,i,o,!1,c,0):o===e.INT&&e.vertexAttribIPointer(r,i,o,c,0),e.vertexAttribDivisor(r,1),e.bindBuffer(e.ARRAY_BUFFER,null);for(var h=new Array(t),f=0;f<t;f++)h[f]=Yd(e,o,u,c,i,f);return d.dataArray=u,d.stride=c,d.size=i,d.getView=function(e){return h[e]},d.setPoint=function(e,t,n){var r=h[e];r[0]=t,r[1]=n},d.bufferSubData=function(t){e.bindBuffer(e.ARRAY_BUFFER,d),t?e.bufferSubData(e.ARRAY_BUFFER,0,u,0,t*i):e.bufferSubData(e.ARRAY_BUFFER,0,u)},d}Rd.getPixelRatio=function(){var e=this.data.contexts[0];if(null!=this.forcedPixelRatio)return this.forcedPixelRatio;var t=this.cy.window(),n=e.backingStorePixelRatio||e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1;return(t.devicePixelRatio||1)/n},Rd.paintCache=function(e){for(var t,n=this.paintCaches=this.paintCaches||[],r=!0,a=0;a<n.length;a++)if((t=n[a]).context===e){r=!1;break}return r&&(t={context:e},n.push(t)),t},Rd.createGradientStyleFor=function(e,t,n,r,a){var i,o=this.usePaths(),s=n.pstyle(t+"-gradient-stop-colors").value,l=n.pstyle(t+"-gradient-stop-positions").pfValue;if("radial-gradient"===r)if(n.isEdge()){var u=n.sourceEndpoint(),c=n.targetEndpoint(),d=n.midpoint(),h=Ut(u,d),f=Ut(c,d);i=e.createRadialGradient(d.x,d.y,0,d.x,d.y,Math.max(h,f))}else{var p=o?{x:0,y:0}:n.position(),g=n.paddedWidth(),v=n.paddedHeight();i=e.createRadialGradient(p.x,p.y,0,p.x,p.y,Math.max(g,v))}else if(n.isEdge()){var y=n.sourceEndpoint(),m=n.targetEndpoint();i=e.createLinearGradient(y.x,y.y,m.x,m.y)}else{var b=o?{x:0,y:0}:n.position(),x=n.paddedWidth()/2,w=n.paddedHeight()/2;switch(n.pstyle("background-gradient-direction").value){case"to-bottom":i=e.createLinearGradient(b.x,b.y-w,b.x,b.y+w);break;case"to-top":i=e.createLinearGradient(b.x,b.y+w,b.x,b.y-w);break;case"to-left":i=e.createLinearGradient(b.x+x,b.y,b.x-x,b.y);break;case"to-right":i=e.createLinearGradient(b.x-x,b.y,b.x+x,b.y);break;case"to-bottom-right":case"to-right-bottom":i=e.createLinearGradient(b.x-x,b.y-w,b.x+x,b.y+w);break;case"to-top-right":case"to-right-top":i=e.createLinearGradient(b.x-x,b.y+w,b.x+x,b.y-w);break;case"to-bottom-left":case"to-left-bottom":i=e.createLinearGradient(b.x+x,b.y-w,b.x-x,b.y+w);break;case"to-top-left":case"to-left-top":i=e.createLinearGradient(b.x+x,b.y+w,b.x-x,b.y-w)}}if(!i)return null;for(var E=l.length===s.length,k=s.length,T=0;T<k;T++)i.addColorStop(E?l[T]:T/(k-1),"rgba("+s[T][0]+","+s[T][1]+","+s[T][2]+","+a+")");return i},Rd.gradientFillStyle=function(e,t,n,r){var a=this.createGradientStyleFor(e,"background",t,n,r);if(!a)return null;e.fillStyle=a},Rd.colorFillStyle=function(e,t,n,r,a){e.fillStyle="rgba("+t+","+n+","+r+","+a+")"},Rd.eleFillStyle=function(e,t,n){var r=t.pstyle("background-fill").value;if("linear-gradient"===r||"radial-gradient"===r)this.gradientFillStyle(e,t,r,n);else{var a=t.pstyle("background-color").value;this.colorFillStyle(e,a[0],a[1],a[2],n)}},Rd.gradientStrokeStyle=function(e,t,n,r){var a=this.createGradientStyleFor(e,"line",t,n,r);if(!a)return null;e.strokeStyle=a},Rd.colorStrokeStyle=function(e,t,n,r,a){e.strokeStyle="rgba("+t+","+n+","+r+","+a+")"},Rd.eleStrokeStyle=function(e,t,n){var r=t.pstyle("line-fill").value;if("linear-gradient"===r||"radial-gradient"===r)this.gradientStrokeStyle(e,t,r,n);else{var a=t.pstyle("line-color").value;this.colorStrokeStyle(e,a[0],a[1],a[2],n)}},Rd.matchCanvasSize=function(e){var t=this,n=t.data,r=t.findContainerClientCoords(),a=r[2],i=r[3],o=t.getPixelRatio(),s=t.motionBlurPxRatio;e!==t.data.bufferCanvases[t.MOTIONBLUR_BUFFER_NODE]&&e!==t.data.bufferCanvases[t.MOTIONBLUR_BUFFER_DRAG]||(o=s);var l,u=a*o,c=i*o;if(u!==t.canvasWidth||c!==t.canvasHeight){t.fontCaches=null;var d=n.canvasContainer;d.style.width=a+"px",d.style.height=i+"px";for(var h=0;h<t.CANVAS_LAYERS;h++)(l=n.canvases[h]).width=u,l.height=c,l.style.width=a+"px",l.style.height=i+"px";for(h=0;h<t.BUFFER_COUNT;h++)(l=n.bufferCanvases[h]).width=u,l.height=c,l.style.width=a+"px",l.style.height=i+"px";t.textureMult=1,o<=1&&(l=n.bufferCanvases[t.TEXTURE_BUFFER],t.textureMult=2,l.width=u*t.textureMult,l.height=c*t.textureMult),t.canvasWidth=u,t.canvasHeight=c,t.pixelRatio=o}},Rd.renderTo=function(e,t,n,r){this.render({forcedContext:e,forcedZoom:t,forcedPan:n,drawAllLayers:!0,forcedPxRatio:r})},Rd.clearCanvas=function(){var e=this,t=e.data;function n(t){t.clearRect(0,0,e.canvasWidth,e.canvasHeight)}n(t.contexts[e.NODE]),n(t.contexts[e.DRAG])},Rd.render=function(e){var t=this;e=e||lt();var n=t.cy,r=e.forcedContext,a=e.drawAllLayers,i=e.drawOnlyNodeLayer,o=e.forcedZoom,s=e.forcedPan,l=void 0===e.forcedPxRatio?this.getPixelRatio():e.forcedPxRatio,u=t.data,c=u.canvasNeedsRedraw,d=t.textureOnViewport&&!r&&(t.pinching||t.hoverData.dragging||t.swipePanning||t.data.wheelZooming),h=void 0!==e.motionBlur?e.motionBlur:t.motionBlur,f=t.motionBlurPxRatio,p=n.hasCompoundNodes(),g=t.hoverData.draggingEles,v=!(!t.hoverData.selecting&&!t.touchData.selecting),y=h=h&&!r&&t.motionBlurEnabled&&!v;r||(t.prevPxRatio!==l&&(t.invalidateContainerClientCoordsCache(),t.matchCanvasSize(t.container),t.redrawHint("eles",!0),t.redrawHint("drag",!0)),t.prevPxRatio=l),!r&&t.motionBlurTimeout&&clearTimeout(t.motionBlurTimeout),h&&(null==t.mbFrames&&(t.mbFrames=0),t.mbFrames++,t.mbFrames<3&&(y=!1),t.mbFrames>t.minMbLowQualFrames&&(t.motionBlurPxRatio=t.mbPxRBlurry)),t.clearingMotionBlur&&(t.motionBlurPxRatio=1),t.textureDrawLastFrame&&!d&&(c[t.NODE]=!0,c[t.SELECT_BOX]=!0);var m=n.style(),b=n.zoom(),x=void 0!==o?o:b,w=n.pan(),E={x:w.x,y:w.y},k={zoom:b,pan:{x:w.x,y:w.y}},T=t.prevViewport;void 0===T||k.zoom!==T.zoom||k.pan.x!==T.pan.x||k.pan.y!==T.pan.y||g&&!p||(t.motionBlurPxRatio=1),s&&(E=s),x*=l,E.x*=l,E.y*=l;var C=t.getCachedZSortedEles();function P(e,n,r,a,i){var o=e.globalCompositeOperation;e.globalCompositeOperation="destination-out",t.colorFillStyle(e,255,255,255,t.motionBlurTransparency),e.fillRect(n,r,a,i),e.globalCompositeOperation=o}function S(e,n){var i,l,c,d;t.clearingMotionBlur||e!==u.bufferContexts[t.MOTIONBLUR_BUFFER_NODE]&&e!==u.bufferContexts[t.MOTIONBLUR_BUFFER_DRAG]?(i=E,l=x,c=t.canvasWidth,d=t.canvasHeight):(i={x:w.x*f,y:w.y*f},l=b*f,c=t.canvasWidth*f,d=t.canvasHeight*f),e.setTransform(1,0,0,1,0,0),"motionBlur"===n?P(e,0,0,c,d):r||void 0!==n&&!n||e.clearRect(0,0,c,d),a||(e.translate(i.x,i.y),e.scale(l,l)),s&&e.translate(s.x,s.y),o&&e.scale(o,o)}if(d||(t.textureDrawLastFrame=!1),d){if(t.textureDrawLastFrame=!0,!t.textureCache){t.textureCache={},t.textureCache.bb=n.mutableElements().boundingBox(),t.textureCache.texture=t.data.bufferCanvases[t.TEXTURE_BUFFER];var B=t.data.bufferContexts[t.TEXTURE_BUFFER];B.setTransform(1,0,0,1,0,0),B.clearRect(0,0,t.canvasWidth*t.textureMult,t.canvasHeight*t.textureMult),t.render({forcedContext:B,drawOnlyNodeLayer:!0,forcedPxRatio:l*t.textureMult}),(k=t.textureCache.viewport={zoom:n.zoom(),pan:n.pan(),width:t.canvasWidth,height:t.canvasHeight}).mpan={x:(0-k.pan.x)/k.zoom,y:(0-k.pan.y)/k.zoom}}c[t.DRAG]=!1,c[t.NODE]=!1;var D=u.contexts[t.NODE],_=t.textureCache.texture;k=t.textureCache.viewport;D.setTransform(1,0,0,1,0,0),h?P(D,0,0,k.width,k.height):D.clearRect(0,0,k.width,k.height);var A=m.core("outside-texture-bg-color").value,M=m.core("outside-texture-bg-opacity").value;t.colorFillStyle(D,A[0],A[1],A[2],M),D.fillRect(0,0,k.width,k.height);b=n.zoom();S(D,!1),D.clearRect(k.mpan.x,k.mpan.y,k.width/k.zoom/l,k.height/k.zoom/l),D.drawImage(_,k.mpan.x,k.mpan.y,k.width/k.zoom/l,k.height/k.zoom/l)}else t.textureOnViewport&&!r&&(t.textureCache=null);var R=n.extent(),I=t.pinching||t.hoverData.dragging||t.swipePanning||t.data.wheelZooming||t.hoverData.draggingEles||t.cy.animated(),N=t.hideEdgesOnViewport&&I,L=[];if(L[t.NODE]=!c[t.NODE]&&h&&!t.clearedForMotionBlur[t.NODE]||t.clearingMotionBlur,L[t.NODE]&&(t.clearedForMotionBlur[t.NODE]=!0),L[t.DRAG]=!c[t.DRAG]&&h&&!t.clearedForMotionBlur[t.DRAG]||t.clearingMotionBlur,L[t.DRAG]&&(t.clearedForMotionBlur[t.DRAG]=!0),c[t.NODE]||a||i||L[t.NODE]){var z=h&&!L[t.NODE]&&1!==f;S(D=r||(z?t.data.bufferContexts[t.MOTIONBLUR_BUFFER_NODE]:u.contexts[t.NODE]),h&&!z?"motionBlur":void 0),N?t.drawCachedNodes(D,C.nondrag,l,R):t.drawLayeredElements(D,C.nondrag,l,R),t.debug&&t.drawDebugPoints(D,C.nondrag),a||h||(c[t.NODE]=!1)}if(!i&&(c[t.DRAG]||a||L[t.DRAG])){z=h&&!L[t.DRAG]&&1!==f;S(D=r||(z?t.data.bufferContexts[t.MOTIONBLUR_BUFFER_DRAG]:u.contexts[t.DRAG]),h&&!z?"motionBlur":void 0),N?t.drawCachedNodes(D,C.drag,l,R):t.drawCachedElements(D,C.drag,l,R),t.debug&&t.drawDebugPoints(D,C.drag),a||h||(c[t.DRAG]=!1)}if(this.drawSelectionRectangle(e,S),h&&1!==f){var O=u.contexts[t.NODE],V=t.data.bufferCanvases[t.MOTIONBLUR_BUFFER_NODE],F=u.contexts[t.DRAG],j=t.data.bufferCanvases[t.MOTIONBLUR_BUFFER_DRAG],X=function(e,n,r){e.setTransform(1,0,0,1,0,0),r||!y?e.clearRect(0,0,t.canvasWidth,t.canvasHeight):P(e,0,0,t.canvasWidth,t.canvasHeight);var a=f;e.drawImage(n,0,0,t.canvasWidth*a,t.canvasHeight*a,0,0,t.canvasWidth,t.canvasHeight)};(c[t.NODE]||L[t.NODE])&&(X(O,V,L[t.NODE]),c[t.NODE]=!1),(c[t.DRAG]||L[t.DRAG])&&(X(F,j,L[t.DRAG]),c[t.DRAG]=!1)}t.prevViewport=k,t.clearingMotionBlur&&(t.clearingMotionBlur=!1,t.motionBlurCleared=!0,t.motionBlur=!0),h&&(t.motionBlurTimeout=setTimeout((function(){t.motionBlurTimeout=null,t.clearedForMotionBlur[t.NODE]=!1,t.clearedForMotionBlur[t.DRAG]=!1,t.motionBlur=!1,t.clearingMotionBlur=!d,t.mbFrames=0,c[t.NODE]=!0,c[t.DRAG]=!0,t.redraw()}),100)),r||n.emit("render")},Rd.drawSelectionRectangle=function(e,t){var n=this,r=n.cy,a=n.data,i=r.style(),o=e.drawOnlyNodeLayer,s=e.drawAllLayers,l=a.canvasNeedsRedraw,u=e.forcedContext;if(n.showFps||!o&&l[n.SELECT_BOX]&&!s){var c=u||a.contexts[n.SELECT_BOX];if(t(c),1==n.selection[4]&&(n.hoverData.selecting||n.touchData.selecting)){var d=n.cy.zoom(),h=i.core("selection-box-border-width").value/d;c.lineWidth=h,c.fillStyle="rgba("+i.core("selection-box-color").value[0]+","+i.core("selection-box-color").value[1]+","+i.core("selection-box-color").value[2]+","+i.core("selection-box-opacity").value+")",c.fillRect(n.selection[0],n.selection[1],n.selection[2]-n.selection[0],n.selection[3]-n.selection[1]),h>0&&(c.strokeStyle="rgba("+i.core("selection-box-border-color").value[0]+","+i.core("selection-box-border-color").value[1]+","+i.core("selection-box-border-color").value[2]+","+i.core("selection-box-opacity").value+")",c.strokeRect(n.selection[0],n.selection[1],n.selection[2]-n.selection[0],n.selection[3]-n.selection[1]))}if(a.bgActivePosistion&&!n.hoverData.selecting){d=n.cy.zoom();var f=a.bgActivePosistion;c.fillStyle="rgba("+i.core("active-bg-color").value[0]+","+i.core("active-bg-color").value[1]+","+i.core("active-bg-color").value[2]+","+i.core("active-bg-opacity").value+")",c.beginPath(),c.arc(f.x,f.y,i.core("active-bg-size").pfValue/d,0,2*Math.PI),c.fill()}var p=n.lastRedrawTime;if(n.showFps&&p){p=Math.round(p);var g=Math.round(1e3/p),v="1 frame = "+p+" ms = "+g+" fps";if(c.setTransform(1,0,0,1,0,0),c.fillStyle="rgba(255, 0, 0, 0.75)",c.strokeStyle="rgba(255, 0, 0, 0.75)",c.font="30px Arial",!Md){var y=c.measureText(v);Md=y.actualBoundingBoxAscent}c.fillText(v,0,Md);c.strokeRect(0,Md+10,250,20),c.fillRect(0,Md+10,250*Math.min(g/60,1),20)}s||(l[n.SELECT_BOX]=!1)}};var Ud="undefined"!=typeof Float32Array?Float32Array:Array;function Hd(){var e=new Ud(9);return Ud!=Float32Array&&(e[1]=0,e[2]=0,e[3]=0,e[5]=0,e[6]=0,e[7]=0),e[0]=1,e[4]=1,e[8]=1,e}function Kd(e){return e[0]=1,e[1]=0,e[2]=0,e[3]=0,e[4]=1,e[5]=0,e[6]=0,e[7]=0,e[8]=1,e}function Gd(e,t,n){var r=t[0],a=t[1],i=t[2],o=t[3],s=t[4],l=t[5],u=t[6],c=t[7],d=t[8],h=n[0],f=n[1];return e[0]=r,e[1]=a,e[2]=i,e[3]=o,e[4]=s,e[5]=l,e[6]=h*r+f*o+u,e[7]=h*a+f*s+c,e[8]=h*i+f*l+d,e}function Zd(e,t,n){var r=t[0],a=t[1],i=t[2],o=t[3],s=t[4],l=t[5],u=t[6],c=t[7],d=t[8],h=Math.sin(n),f=Math.cos(n);return e[0]=f*r+h*o,e[1]=f*a+h*s,e[2]=f*i+h*l,e[3]=f*o-h*r,e[4]=f*s-h*a,e[5]=f*l-h*i,e[6]=u,e[7]=c,e[8]=d,e}function $d(e,t,n){var r=n[0],a=n[1];return e[0]=r*t[0],e[1]=r*t[1],e[2]=r*t[2],e[3]=a*t[3],e[4]=a*t[4],e[5]=a*t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],e}Math.hypot||(Math.hypot=function(){for(var e=0,t=arguments.length;t--;)e+=arguments[t]*arguments[t];return Math.sqrt(e)});var Qd=function(){return i((function e(t,n,r,i){a(this,e),this.debugID=Math.floor(1e4*Math.random()),this.r=t,this.texSize=n,this.texRows=r,this.texHeight=Math.floor(n/r),this.enableWrapping=!0,this.locked=!1,this.texture=null,this.needsBuffer=!0,this.freePointer={x:0,row:0},this.keyToLocation=new Map,this.canvas=i(t,n,n),this.scratch=i(t,n,this.texHeight,"scratch")}),[{key:"lock",value:function(){this.locked=!0}},{key:"getKeys",value:function(){return new Set(this.keyToLocation.keys())}},{key:"getScale",value:function(e){var t=e.w,n=e.h,r=this.texHeight,a=this.texSize,i=r/n,o=t*i,s=n*i;return o>a&&(o=t*(i=a/t),s=n*i),{scale:i,texW:o,texH:s}}},{key:"draw",value:function(e,t,n){var r=this;if(this.locked)throw new Error("can't draw, atlas is locked");var a=this.texSize,i=this.texRows,o=this.texHeight,s=this.getScale(t),l=s.scale,u=s.texW,c=s.texH,d=function(e,r){if(n&&r){var a=r.context,i=e.x,s=e.row,u=i,c=o*s;a.save(),a.translate(u,c),a.scale(l,l),n(a,t),a.restore()}},h=[null,null],f=function(){d(r.freePointer,r.canvas),h[0]={x:r.freePointer.x,y:r.freePointer.row*o,w:u,h:c},h[1]={x:r.freePointer.x+u,y:r.freePointer.row*o,w:0,h:c},r.freePointer.x+=u,r.freePointer.x==a&&(r.freePointer.x=0,r.freePointer.row++)},p=function(){r.freePointer.x=0,r.freePointer.row++};if(this.freePointer.x+u<=a)f();else{if(this.freePointer.row>=i-1)return!1;this.freePointer.x===a?(p(),f()):this.enableWrapping?function(){var e=r.scratch,t=r.canvas;e.clear(),d({x:0,row:0},e);var n=a-r.freePointer.x,i=u-n,s=o,l=r.freePointer.x,f=r.freePointer.row*o,p=n;t.context.drawImage(e,0,0,p,s,l,f,p,s),h[0]={x:l,y:f,w:p,h:c};var g=n,v=(r.freePointer.row+1)*o,y=i;t&&t.context.drawImage(e,g,0,y,s,0,v,y,s),h[1]={x:0,y:v,w:y,h:c},r.freePointer.x=i,r.freePointer.row++}():(p(),f())}return this.keyToLocation.set(e,h),this.needsBuffer=!0,h}},{key:"getOffsets",value:function(e){return this.keyToLocation.get(e)}},{key:"isEmpty",value:function(){return 0===this.freePointer.x&&0===this.freePointer.row}},{key:"canFit",value:function(e){if(this.locked)return!1;var t=this.texSize,n=this.texRows,r=this.getScale(e).texW;return!(this.freePointer.x+r>t)||this.freePointer.row<n-1}},{key:"bufferIfNeeded",value:function(e){this.texture||(this.texture=function(e){var t=e.createTexture();return t.buffer=function(n){e.bindTexture(e.TEXTURE_2D,t),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.LINEAR_MIPMAP_NEAREST),e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,n),e.generateMipmap(e.TEXTURE_2D),e.bindTexture(e.TEXTURE_2D,null)},t.deleteTexture=function(){e.deleteTexture(t)},t}(e,this.debugID)),this.needsBuffer&&(this.texture.buffer(this.canvas),this.needsBuffer=!1,this.locked&&(this.canvas=null,this.scratch=null))}},{key:"dispose",value:function(){this.texture&&(this.texture.deleteTexture(),this.texture=null),this.canvas=null,this.scratch=null,this.locked=!0}}])}(),Jd=function(){return i((function e(t,n,r,i){a(this,e),this.r=t,this.texSize=n,this.texRows=r,this.createTextureCanvas=i,this.atlases=[],this.styleKeyToAtlas=new Map,this.markedKeys=new Set}),[{key:"getKeys",value:function(){return new Set(this.styleKeyToAtlas.keys())}},{key:"_createAtlas",value:function(){var e=this.r,t=this.texSize,n=this.texRows,r=this.createTextureCanvas;return new Qd(e,t,n,r)}},{key:"_getScratchCanvas",value:function(){if(!this.scratch){var e=this.r,t=this.texSize,n=this.texRows,r=this.createTextureCanvas,a=Math.floor(t/n);this.scratch=r(e,t,a,"scratch")}return this.scratch}},{key:"draw",value:function(e,t,n){var r=this.styleKeyToAtlas.get(e);return r||((r=this.atlases[this.atlases.length-1])&&r.canFit(t)||(r&&r.lock(),r=this._createAtlas(),this.atlases.push(r)),r.draw(e,t,n),this.styleKeyToAtlas.set(e,r)),r}},{key:"getAtlas",value:function(e){return this.styleKeyToAtlas.get(e)}},{key:"hasAtlas",value:function(e){return this.styleKeyToAtlas.has(e)}},{key:"markKeyForGC",value:function(e){this.markedKeys.add(e)}},{key:"gc",value:function(){var e=this,t=this.markedKeys;if(0!==t.size){var n,r=[],a=new Map,i=null,s=o(this.atlases);try{var c=function(){var s,c,d=n.value,h=d.getKeys(),f=(c=h,(s=t).intersection?s.intersection(c):new Set(u(s).filter((function(e){return c.has(e)}))));if(0===f.size)return r.push(d),h.forEach((function(e){return a.set(e,d)})),1;i||(i=e._createAtlas(),r.push(i));var p,g=o(h);try{for(g.s();!(p=g.n()).done;){var v=p.value;if(!f.has(v)){var y=l(d.getOffsets(v),2),m=y[0],b=y[1];i.canFit({w:m.w+b.w,h:m.h})||(i.lock(),i=e._createAtlas(),r.push(i)),d.canvas&&(e._copyTextureToNewAtlas(v,d,i),a.set(v,i))}}}catch(x){g.e(x)}finally{g.f()}d.dispose()};for(s.s();!(n=s.n()).done;)c()}catch(d){s.e(d)}finally{s.f()}this.atlases=r,this.styleKeyToAtlas=a,this.markedKeys=new Set}else console.log("nothing to garbage collect")}},{key:"_copyTextureToNewAtlas",value:function(e,t,n){var r=l(t.getOffsets(e),2),a=r[0],i=r[1];if(0===i.w)n.draw(e,a,(function(e){e.drawImage(t.canvas,a.x,a.y,a.w,a.h,0,0,a.w,a.h)}));else{var o=this._getScratchCanvas();o.clear(),o.context.drawImage(t.canvas,a.x,a.y,a.w,a.h,0,0,a.w,a.h),o.context.drawImage(t.canvas,i.x,i.y,i.w,i.h,a.w,0,i.w,i.h);var s=a.w+i.w,u=a.h;n.draw(e,{w:s,h:u},(function(e){e.drawImage(o,0,0,s,u,0,0,s,u)}))}}},{key:"getCounts",value:function(){return{keyCount:this.styleKeyToAtlas.size,atlasCount:new Set(this.styleKeyToAtlas.values()).size}}}])}();var eh=function(){return i((function e(t,n){a(this,e),this.r=t,this.globalOptions=n,this.atlasSize=n.webglTexSize,this.maxAtlasesPerBatch=n.webglTexPerBatch,this.renderTypes=new Map,this.collections=new Map,this.typeAndIdToKey=new Map}),[{key:"getAtlasSize",value:function(){return this.atlasSize}},{key:"addAtlasCollection",value:function(e,t){var n=this.globalOptions,r=n.webglTexSize,a=n.createTextureCanvas,i=t.texRows,o=this._cacheScratchCanvas(a),s=new Jd(this.r,r,i,o);this.collections.set(e,s)}},{key:"addRenderType",value:function(e,t){var n=t.collection;if(!this.collections.has(n))throw new Error("invalid atlas collection name '".concat(n,"'"));var r=this.collections.get(n),a=me({type:e,atlasCollection:r},t);this.renderTypes.set(e,a)}},{key:"getRenderTypeOpts",value:function(e){return this.renderTypes.get(e)}},{key:"getAtlasCollection",value:function(e){return this.collections.get(e)}},{key:"_cacheScratchCanvas",value:function(e){var t=-1,n=-1,r=null;return function(a,i,o,s){return s?(r&&i==t&&o==n||(t=i,n=o,r=e(a,i,o)),r):e(a,i,o)}}},{key:"_key",value:function(e,t){return"".concat(e,"-").concat(t)}},{key:"invalidate",value:function(e){var t,n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=r.forceRedraw,i=void 0!==a&&a,s=r.filterEle,l=void 0===s?function(){return!0}:s,u=r.filterType,c=void 0===u?function(){return!0}:u,d=!1,h=!1,f=o(e);try{for(f.s();!(t=f.n()).done;){var p=t.value;if(l(p)){var g,v=o(this.renderTypes.values());try{var y=function(){var e=g.value,t=e.type;if(c(t)){var r=n.collections.get(e.collection),a=e.getKey(p),o=Array.isArray(a)?a:[a];if(i)o.forEach((function(e){return r.markKeyForGC(e)})),h=!0;else{var s=e.getID?e.getID(p):p.id(),l=n._key(t,s),u=n.typeAndIdToKey.get(l);void 0===u||Od(o,u)||(d=!0,n.typeAndIdToKey.delete(l),u.forEach((function(e){return r.markKeyForGC(e)})))}}};for(v.s();!(g=v.n()).done;)y()}catch(m){v.e(m)}finally{v.f()}}}}catch(m){f.e(m)}finally{f.f()}return h&&(this.gc(),d=!1),d}},{key:"gc",value:function(){var e,t=o(this.collections.values());try{for(t.s();!(e=t.n()).done;){e.value.gc()}}catch(n){t.e(n)}finally{t.f()}}},{key:"getOrCreateAtlas",value:function(e,t,n,r){var a=this.renderTypes.get(t),i=this.collections.get(a.collection),o=!1,s=i.draw(r,n,(function(t){a.drawClipped?(t.save(),t.beginPath(),t.rect(0,0,n.w,n.h),t.clip(),a.drawElement(t,e,n,!0,!0),t.restore()):a.drawElement(t,e,n,!0,!0),o=!0}));if(o){var l=a.getID?a.getID(e):e.id(),u=this._key(t,l);this.typeAndIdToKey.has(u)?this.typeAndIdToKey.get(u).push(r):this.typeAndIdToKey.set(u,[r])}return s}},{key:"getAtlasInfo",value:function(e,t){var n=this,r=this.renderTypes.get(t),a=r.getKey(e);return(Array.isArray(a)?a:[a]).map((function(a){var i=r.getBoundingBox(e,a),o=n.getOrCreateAtlas(e,t,i,a),s=l(o.getOffsets(a),2),u=s[0];return{atlas:o,tex:u,tex1:u,tex2:s[1],bb:i}}))}},{key:"getDebugInfo",value:function(){var e,t=[],n=o(this.collections);try{for(n.s();!(e=n.n()).done;){var r=l(e.value,2),a=r[0],i=r[1].getCounts(),s=i.keyCount,u=i.atlasCount;t.push({type:a,keyCount:s,atlasCount:u})}}catch(c){n.e(c)}finally{n.f()}return t}}])}(),th=function(){return i((function e(t){a(this,e),this.globalOptions=t,this.atlasSize=t.webglTexSize,this.maxAtlasesPerBatch=t.webglTexPerBatch,this.batchAtlases=[]}),[{key:"getMaxAtlasesPerBatch",value:function(){return this.maxAtlasesPerBatch}},{key:"getAtlasSize",value:function(){return this.atlasSize}},{key:"getIndexArray",value:function(){return Array.from({length:this.maxAtlasesPerBatch},(function(e,t){return t}))}},{key:"startBatch",value:function(){this.batchAtlases=[]}},{key:"getAtlasCount",value:function(){return this.batchAtlases.length}},{key:"getAtlases",value:function(){return this.batchAtlases}},{key:"canAddToCurrentBatch",value:function(e){return this.batchAtlases.length!==this.maxAtlasesPerBatch||this.batchAtlases.includes(e)}},{key:"getAtlasIndexForBatch",value:function(e){var t=this.batchAtlases.indexOf(e);if(t<0){if(this.batchAtlases.length===this.maxAtlasesPerBatch)throw new Error("cannot add more atlases to batch");this.batchAtlases.push(e),t=this.batchAtlases.length-1}return t}}])}(),nh={SCREEN:{name:"screen",screen:!0},PICKING:{name:"picking",picking:!0}},rh=1,ah=2,ih=function(){return i((function e(t,n,r){a(this,e),this.r=t,this.gl=n,this.maxInstances=r.webglBatchSize,this.atlasSize=r.webglTexSize,this.bgColor=r.bgColor,this.debug=r.webglDebug,this.batchDebugInfo=[],r.enableWrapping=!0,r.createTextureCanvas=Nd,this.atlasManager=new eh(t,r),this.batchManager=new th(r),this.simpleShapeOptions=new Map,this.program=this._createShaderProgram(nh.SCREEN),this.pickingProgram=this._createShaderProgram(nh.PICKING),this.vao=this._createVAO()}),[{key:"addAtlasCollection",value:function(e,t){this.atlasManager.addAtlasCollection(e,t)}},{key:"addTextureAtlasRenderType",value:function(e,t){this.atlasManager.addRenderType(e,t)}},{key:"addSimpleShapeRenderType",value:function(e,t){this.simpleShapeOptions.set(e,t)}},{key:"invalidate",value:function(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).type,n=this.atlasManager;return t?n.invalidate(e,{filterType:function(e){return e===t},forceRedraw:!0}):n.invalidate(e)}},{key:"gc",value:function(){this.atlasManager.gc()}},{key:"_createShaderProgram",value:function(e){var t=this.gl,n="#version 300 es\n      precision highp float;\n\n      uniform mat3 uPanZoomMatrix;\n      uniform int  uAtlasSize;\n      \n      // instanced\n      in vec2 aPosition; // a vertex from the unit square\n      \n      in mat3 aTransform; // used to transform verticies, eg into a bounding box\n      in int aVertType; // the type of thing we are rendering\n\n      // the z-index that is output when using picking mode\n      in vec4 aIndex;\n      \n      // For textures\n      in int aAtlasId; // which shader unit/atlas to use\n      in vec4 aTex; // x/y/w/h of texture in atlas\n\n      // for edges\n      in vec4 aPointAPointB;\n      in vec4 aPointCPointD;\n      in vec2 aLineWidth; // also used for node border width\n\n      // simple shapes\n      in vec4 aCornerRadius; // for round-rectangle [top-right, bottom-right, top-left, bottom-left]\n      in vec4 aColor; // also used for edges\n      in vec4 aBorderColor; // aLineWidth is used for border width\n\n      // output values passed to the fragment shader\n      out vec2 vTexCoord;\n      out vec4 vColor;\n      out vec2 vPosition;\n      // flat values are not interpolated\n      flat out int vAtlasId; \n      flat out int vVertType;\n      flat out vec2 vTopRight;\n      flat out vec2 vBotLeft;\n      flat out vec4 vCornerRadius;\n      flat out vec4 vBorderColor;\n      flat out vec2 vBorderWidth;\n      flat out vec4 vIndex;\n      \n      void main(void) {\n        int vid = gl_VertexID;\n        vec2 position = aPosition; // TODO make this a vec3, simplifies some code below\n\n        if(aVertType == ".concat(0,") {\n          float texX = aTex.x; // texture coordinates\n          float texY = aTex.y;\n          float texW = aTex.z;\n          float texH = aTex.w;\n\n          if(vid == 1 || vid == 2 || vid == 4) {\n            texX += texW;\n          }\n          if(vid == 2 || vid == 4 || vid == 5) {\n            texY += texH;\n          }\n\n          float d = float(uAtlasSize);\n          vTexCoord = vec2(texX / d, texY / d); // tex coords must be between 0 and 1\n\n          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);\n        }\n        else if(aVertType == ").concat(4," || aVertType == ").concat(7," \n             || aVertType == ").concat(5," || aVertType == ").concat(6,") { // simple shapes\n\n          // the bounding box is needed by the fragment shader\n          vBotLeft  = (aTransform * vec3(0, 0, 1)).xy; // flat\n          vTopRight = (aTransform * vec3(1, 1, 1)).xy; // flat\n          vPosition = (aTransform * vec3(position, 1)).xy; // will be interpolated\n\n          // calculations are done in the fragment shader, just pass these along\n          vColor = aColor;\n          vCornerRadius = aCornerRadius;\n          vBorderColor = aBorderColor;\n          vBorderWidth = aLineWidth;\n\n          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);\n        }\n        else if(aVertType == ").concat(1,") {\n          vec2 source = aPointAPointB.xy;\n          vec2 target = aPointAPointB.zw;\n\n          // adjust the geometry so that the line is centered on the edge\n          position.y = position.y - 0.5;\n\n          // stretch the unit square into a long skinny rectangle\n          vec2 xBasis = target - source;\n          vec2 yBasis = normalize(vec2(-xBasis.y, xBasis.x));\n          vec2 point = source + xBasis * position.x + yBasis * aLineWidth[0] * position.y;\n\n          gl_Position = vec4(uPanZoomMatrix * vec3(point, 1.0), 1.0);\n          vColor = aColor;\n        } \n        else if(aVertType == ").concat(2,") {\n          vec2 pointA = aPointAPointB.xy;\n          vec2 pointB = aPointAPointB.zw;\n          vec2 pointC = aPointCPointD.xy;\n          vec2 pointD = aPointCPointD.zw;\n\n          // adjust the geometry so that the line is centered on the edge\n          position.y = position.y - 0.5;\n\n          vec2 p0, p1, p2, pos;\n          if(position.x == 0.0) { // The left side of the unit square\n            p0 = pointA;\n            p1 = pointB;\n            p2 = pointC;\n            pos = position;\n          } else { // The right side of the unit square, use same approach but flip the geometry upside down\n            p0 = pointD;\n            p1 = pointC;\n            p2 = pointB;\n            pos = vec2(0.0, -position.y);\n          }\n\n          vec2 p01 = p1 - p0;\n          vec2 p12 = p2 - p1;\n          vec2 p21 = p1 - p2;\n\n          // Find the normal vector.\n          vec2 tangent = normalize(normalize(p12) + normalize(p01));\n          vec2 normal = vec2(-tangent.y, tangent.x);\n\n          // Find the vector perpendicular to p0 -> p1.\n          vec2 p01Norm = normalize(vec2(-p01.y, p01.x));\n\n          // Determine the bend direction.\n          float sigma = sign(dot(p01 + p21, normal));\n          float width = aLineWidth[0];\n\n          if(sign(pos.y) == -sigma) {\n            // This is an intersecting vertex. Adjust the position so that there's no overlap.\n            vec2 point = 0.5 * width * normal * -sigma / dot(normal, p01Norm);\n            gl_Position = vec4(uPanZoomMatrix * vec3(p1 + point, 1.0), 1.0);\n          } else {\n            // This is a non-intersecting vertex. Treat it like a mitre join.\n            vec2 point = 0.5 * width * normal * sigma * dot(normal, p01Norm);\n            gl_Position = vec4(uPanZoomMatrix * vec3(p1 + point, 1.0), 1.0);\n          }\n\n          vColor = aColor;\n        } \n        else if(aVertType == ").concat(3," && vid < 3) {\n          // massage the first triangle into an edge arrow\n          if(vid == 0)\n            position = vec2(-0.15, -0.3);\n          if(vid == 1)\n            position = vec2(  0.0,  0.0);\n          if(vid == 2)\n            position = vec2( 0.15, -0.3);\n\n          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);\n          vColor = aColor;\n        }\n        else {\n          gl_Position = vec4(2.0, 0.0, 0.0, 1.0); // discard vertex by putting it outside webgl clip space\n        }\n\n        vAtlasId = aAtlasId;\n        vVertType = aVertType;\n        vIndex = aIndex;\n      }\n    "),r=this.batchManager.getIndexArray(),a="#version 300 es\n      precision highp float;\n\n      // declare texture unit for each texture atlas in the batch\n      ".concat(r.map((function(e){return"uniform sampler2D uTexture".concat(e,";")})).join("\n\t"),"\n\n      uniform vec4 uBGColor;\n      uniform float uZoom;\n\n      in vec2 vTexCoord;\n      in vec4 vColor;\n      in vec2 vPosition; // model coordinates\n\n      flat in int vAtlasId;\n      flat in vec4 vIndex;\n      flat in int vVertType;\n      flat in vec2 vTopRight;\n      flat in vec2 vBotLeft;\n      flat in vec4 vCornerRadius;\n      flat in vec4 vBorderColor;\n      flat in vec2 vBorderWidth;\n\n      out vec4 outColor;\n\n      ").concat("\n  float circleSD(vec2 p, float r) {\n    return distance(vec2(0), p) - r; // signed distance\n  }\n","\n      ").concat("\n  float rectangleSD(vec2 p, vec2 b) {\n    vec2 d = abs(p)-b;\n    return distance(vec2(0),max(d,0.0)) + min(max(d.x,d.y),0.0);\n  }\n","\n      ").concat("\n  float roundRectangleSD(vec2 p, vec2 b, vec4 cr) {\n    cr.xy = (p.x > 0.0) ? cr.xy : cr.zw;\n    cr.x  = (p.y > 0.0) ? cr.x  : cr.y;\n    vec2 q = abs(p) - b + cr.x;\n    return min(max(q.x, q.y), 0.0) + distance(vec2(0), max(q, 0.0)) - cr.x;\n  }\n","\n      ").concat("\n  float ellipseSD(vec2 p, vec2 ab) {\n    p = abs( p ); // symmetry\n\n    // find root with Newton solver\n    vec2 q = ab*(p-ab);\n    float w = (q.x<q.y)? 1.570796327 : 0.0;\n    for( int i=0; i<5; i++ ) {\n      vec2 cs = vec2(cos(w),sin(w));\n      vec2 u = ab*vec2( cs.x,cs.y);\n      vec2 v = ab*vec2(-cs.y,cs.x);\n      w = w + dot(p-u,v)/(dot(p-u,u)+dot(v,v));\n    }\n    \n    // compute final point and distance\n    float d = length(p-ab*vec2(cos(w),sin(w)));\n    \n    // return signed distance\n    return (dot(p/ab,p/ab)>1.0) ? d : -d;\n  }\n","\n\n      vec4 blend(vec4 top, vec4 bot) { // blend colors with premultiplied alpha\n        return vec4( \n          top.rgb + (bot.rgb * (1.0 - top.a)),\n          top.a   + (bot.a   * (1.0 - top.a)) \n        );\n      }\n\n      vec4 distInterp(vec4 cA, vec4 cB, float d) { // interpolate color using Signed Distance\n        // scale to the zoom level so that borders don't look blurry when zoomed in\n        // note 1.5 is an aribitrary value chosen because it looks good\n        return mix(cA, cB, 1.0 - smoothstep(0.0, 1.5 / uZoom, abs(d))); \n      }\n\n      void main(void) {\n        if(vVertType == ").concat(0,") {\n          // look up the texel from the texture unit\n          ").concat(r.map((function(e){return"if(vAtlasId == ".concat(e,") outColor = texture(uTexture").concat(e,", vTexCoord);")})).join("\n\telse "),"\n        } \n        else if(vVertType == ").concat(3,") {\n          // mimics how canvas renderer uses context.globalCompositeOperation = 'destination-out';\n          outColor = blend(vColor, uBGColor);\n          outColor.a = 1.0; // make opaque, masks out line under arrow\n        }\n        else if(vVertType == ").concat(4," && vBorderWidth == vec2(0.0)) { // simple rectangle with no border\n          outColor = vColor; // unit square is already transformed to the rectangle, nothing else needs to be done\n        }\n        else if(vVertType == ").concat(4," || vVertType == ").concat(7," \n          || vVertType == ").concat(5," || vVertType == ").concat(6,") { // use SDF\n\n          float outerBorder = vBorderWidth[0];\n          float innerBorder = vBorderWidth[1];\n          float borderPadding = outerBorder * 2.0;\n          float w = vTopRight.x - vBotLeft.x - borderPadding;\n          float h = vTopRight.y - vBotLeft.y - borderPadding;\n          vec2 b = vec2(w/2.0, h/2.0); // half width, half height\n          vec2 p = vPosition - vec2(vTopRight.x - b[0] - outerBorder, vTopRight.y - b[1] - outerBorder); // translate to center\n\n          float d; // signed distance\n          if(vVertType == ").concat(4,") {\n            d = rectangleSD(p, b);\n          } else if(vVertType == ").concat(7," && w == h) {\n            d = circleSD(p, b.x); // faster than ellipse\n          } else if(vVertType == ").concat(7,") {\n            d = ellipseSD(p, b);\n          } else {\n            d = roundRectangleSD(p, b, vCornerRadius.wzyx);\n          }\n\n          // use the distance to interpolate a color to smooth the edges of the shape, doesn't need multisampling\n          // we must smooth colors inwards, because we can't change pixels outside the shape's bounding box\n          if(d > 0.0) {\n            if(d > outerBorder) {\n              discard;\n            } else {\n              outColor = distInterp(vBorderColor, vec4(0), d - outerBorder);\n            }\n          } else {\n            if(d > innerBorder) {\n              vec4 outerColor = outerBorder == 0.0 ? vec4(0) : vBorderColor;\n              vec4 innerBorderColor = blend(vBorderColor, vColor);\n              outColor = distInterp(innerBorderColor, outerColor, d);\n            } \n            else {\n              vec4 outerColor;\n              if(innerBorder == 0.0 && outerBorder == 0.0) {\n                outerColor = vec4(0);\n              } else if(innerBorder == 0.0) {\n                outerColor = vBorderColor;\n              } else {\n                outerColor = blend(vBorderColor, vColor);\n              }\n              outColor = distInterp(vColor, outerColor, d - innerBorder);\n            }\n          }\n        }\n        else {\n          outColor = vColor;\n        }\n\n        ").concat(e.picking?"if(outColor.a == 0.0) discard;\n             else outColor = vIndex;":"","\n      }\n    "),i=function(e,t,n){var r=Id(e,e.VERTEX_SHADER,t),a=Id(e,e.FRAGMENT_SHADER,n),i=e.createProgram();if(e.attachShader(i,r),e.attachShader(i,a),e.linkProgram(i),!e.getProgramParameter(i,e.LINK_STATUS))throw new Error("Could not initialize shaders");return i}(t,n,a);i.aPosition=t.getAttribLocation(i,"aPosition"),i.aIndex=t.getAttribLocation(i,"aIndex"),i.aVertType=t.getAttribLocation(i,"aVertType"),i.aTransform=t.getAttribLocation(i,"aTransform"),i.aAtlasId=t.getAttribLocation(i,"aAtlasId"),i.aTex=t.getAttribLocation(i,"aTex"),i.aPointAPointB=t.getAttribLocation(i,"aPointAPointB"),i.aPointCPointD=t.getAttribLocation(i,"aPointCPointD"),i.aLineWidth=t.getAttribLocation(i,"aLineWidth"),i.aColor=t.getAttribLocation(i,"aColor"),i.aCornerRadius=t.getAttribLocation(i,"aCornerRadius"),i.aBorderColor=t.getAttribLocation(i,"aBorderColor"),i.uPanZoomMatrix=t.getUniformLocation(i,"uPanZoomMatrix"),i.uAtlasSize=t.getUniformLocation(i,"uAtlasSize"),i.uBGColor=t.getUniformLocation(i,"uBGColor"),i.uZoom=t.getUniformLocation(i,"uZoom"),i.uTextures=[];for(var o=0;o<this.batchManager.getMaxAtlasesPerBatch();o++)i.uTextures.push(t.getUniformLocation(i,"uTexture".concat(o)));return i}},{key:"_createVAO",value:function(){var e=[0,0,1,0,1,1,0,0,1,1,0,1];this.vertexCount=e.length/2;var t=this.maxInstances,n=this.gl,r=this.program,a=n.createVertexArray();return n.bindVertexArray(a),function(e,t,n,r){var a=l(Xd(e,t),2),i=a[0],o=a[1],s=qd(e,o,r),u=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,u),e.bufferData(e.ARRAY_BUFFER,s,e.STATIC_DRAW),o===e.FLOAT?e.vertexAttribPointer(n,i,o,!1,0,0):o===e.INT&&e.vertexAttribIPointer(n,i,o,0,0),e.enableVertexAttribArray(n),e.bindBuffer(e.ARRAY_BUFFER,null)}(n,"vec2",r.aPosition,e),this.transformBuffer=function(e,t,n){for(var r=new Float32Array(9*t),a=new Array(t),i=0;i<t;i++){var o=9*i*4;a[i]=new Float32Array(r.buffer,o,9)}var s=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,s),e.bufferData(e.ARRAY_BUFFER,r.byteLength,e.DYNAMIC_DRAW);for(var l=0;l<3;l++){var u=n+l;e.enableVertexAttribArray(u),e.vertexAttribPointer(u,3,e.FLOAT,!1,36,12*l),e.vertexAttribDivisor(u,1)}return e.bindBuffer(e.ARRAY_BUFFER,null),s.getMatrixView=function(e){return a[e]},s.setData=function(e,t){a[t].set(e,0)},s.bufferSubData=function(){e.bindBuffer(e.ARRAY_BUFFER,s),e.bufferSubData(e.ARRAY_BUFFER,0,r)},s}(n,t,r.aTransform),this.indexBuffer=Wd(n,t,"vec4",r.aIndex),this.vertTypeBuffer=Wd(n,t,"int",r.aVertType),this.atlasIdBuffer=Wd(n,t,"int",r.aAtlasId),this.texBuffer=Wd(n,t,"vec4",r.aTex),this.pointAPointBBuffer=Wd(n,t,"vec4",r.aPointAPointB),this.pointCPointDBuffer=Wd(n,t,"vec4",r.aPointCPointD),this.lineWidthBuffer=Wd(n,t,"vec2",r.aLineWidth),this.colorBuffer=Wd(n,t,"vec4",r.aColor),this.cornerRadiusBuffer=Wd(n,t,"vec4",r.aCornerRadius),this.borderColorBuffer=Wd(n,t,"vec4",r.aBorderColor),n.bindVertexArray(null),a}},{key:"buffers",get:function(){var e=this;return this._buffers||(this._buffers=Object.keys(this).filter((function(e){return e.endsWith("Buffer")})).map((function(t){return e[t]}))),this._buffers}},{key:"startFrame",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:nh.SCREEN;this.panZoomMatrix=e,this.renderTarget=t,this.batchDebugInfo=[],this.wrappedCount=0,this.simpleCount=0,this.startBatch()}},{key:"startBatch",value:function(){this.instanceCount=0,this.batchManager.startBatch()}},{key:"endFrame",value:function(){this.endBatch()}},{key:"_isVisible",value:function(e,t){return!!e.visible()&&(!t||!t.isVisible||t.isVisible(e))}},{key:"drawTexture",value:function(e,t,n){var r=this.atlasManager,a=this.batchManager,i=r.getRenderTypeOpts(n);if(this._isVisible(e,i)){if(this.renderTarget.picking&&i.getTexPickingMode){var s=i.getTexPickingMode(e);if(s===rh)return;if(s==ah)return void this.drawPickingRectangle(e,t,n)}var u,c=o(r.getAtlasInfo(e,n));try{for(c.s();!(u=c.n()).done;){var d=u.value,h=d.atlas,f=d.tex1,p=d.tex2;a.canAddToCurrentBatch(h)||this.endBatch();for(var g=a.getAtlasIndexForBatch(h),v=0,y=[[f,!0],[p,!1]];v<y.length;v++){var m=l(y[v],2),b=m[0],x=m[1];if(0!=b.w){var w=this.instanceCount;this.vertTypeBuffer.getView(w)[0]=0,Fd(t,this.indexBuffer.getView(w)),this.atlasIdBuffer.getView(w)[0]=g;var E=this.texBuffer.getView(w);E[0]=b.x,E[1]=b.y,E[2]=b.w,E[3]=b.h;var k=this.transformBuffer.getMatrixView(w);this.setTransformMatrix(e,k,i,d,x),this.instanceCount++,x||this.wrappedCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}}}catch(T){c.e(T)}finally{c.f()}}}},{key:"setTransformMatrix",value:function(e,t,n,r){var a=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],i=0;if(n.shapeProps&&n.shapeProps.padding&&(i=e.pstyle(n.shapeProps.padding).pfValue),r){var o=r.bb,s=r.tex1,l=r.tex2,u=s.w/(s.w+l.w);a||(u=1-u);var c=this._getAdjustedBB(o,i,a,u);this._applyTransformMatrix(t,c,n,e)}else{var d=n.getBoundingBox(e),h=this._getAdjustedBB(d,i,!0,1);this._applyTransformMatrix(t,h,n,e)}}},{key:"_applyTransformMatrix",value:function(e,t,n,r){var a,i;Kd(e);var o=n.getRotation?n.getRotation(r):0;if(0!==o){var s=n.getRotationPoint(r);Gd(e,e,[s.x,s.y]),Zd(e,e,o);var l=n.getRotationOffset(r);a=l.x+(t.xOffset||0),i=l.y+(t.yOffset||0)}else a=t.x1,i=t.y1;Gd(e,e,[a,i]),$d(e,e,[t.w,t.h])}},{key:"_getAdjustedBB",value:function(e,t,n,r){var a=e.x1,i=e.y1,o=e.w,s=e.h;t&&(a-=t,i-=t,o+=2*t,s+=2*t);var l=0,u=o*r;return n&&r<1?o=u:!n&&r<1&&(a+=l=o-u,o=u),{x1:a,y1:i,w:o,h:s,xOffset:l,yOffset:e.yOffset}}},{key:"drawPickingRectangle",value:function(e,t,n){var r=this.atlasManager.getRenderTypeOpts(n),a=this.instanceCount;this.vertTypeBuffer.getView(a)[0]=4,Fd(t,this.indexBuffer.getView(a)),Vd([0,0,0],1,this.colorBuffer.getView(a));var i=this.transformBuffer.getMatrixView(a);this.setTransformMatrix(e,i,r),this.simpleCount++,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}},{key:"drawNode",value:function(e,t,n){var r=this.simpleShapeOptions.get(n);if(this._isVisible(e,r)){var a=r.shapeProps,i=this._getVertTypeForShape(e,a.shape);if(void 0===i||r.isSimple&&!r.isSimple(e))this.drawTexture(e,t,n);else{var o=this.instanceCount;if(this.vertTypeBuffer.getView(o)[0]=i,5===i||6===i){var s=r.getBoundingBox(e),l=this._getCornerRadius(e,a.radius,s),u=this.cornerRadiusBuffer.getView(o);u[0]=l,u[1]=l,u[2]=l,u[3]=l,6===i&&(u[0]=0,u[2]=0)}Fd(t,this.indexBuffer.getView(o)),Vd(e.pstyle(a.color).value,e.pstyle(a.opacity).value,this.colorBuffer.getView(o));var c=this.lineWidthBuffer.getView(o);if(c[0]=0,c[1]=0,a.border){var d=e.pstyle("border-width").value;if(d>0){Vd(e.pstyle("border-color").value,e.pstyle("border-opacity").value,this.borderColorBuffer.getView(o));var h=e.pstyle("border-position").value;if("inside"===h)c[0]=0,c[1]=-d;else if("outside"===h)c[0]=d,c[1]=0;else{var f=d/2;c[0]=f,c[1]=-f}}}var p=this.transformBuffer.getMatrixView(o);this.setTransformMatrix(e,p,r),this.simpleCount++,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}}},{key:"_getVertTypeForShape",value:function(e,t){switch(e.pstyle(t).value){case"rectangle":return 4;case"ellipse":return 7;case"roundrectangle":case"round-rectangle":return 5;case"bottom-round-rectangle":return 6;default:return}}},{key:"_getCornerRadius",value:function(e,t,n){var r=n.w,a=n.h;if("auto"===e.pstyle(t).value)return Cn(r,a);var i=e.pstyle(t).pfValue,o=r/2,s=a/2;return Math.min(i,s,o)}},{key:"drawEdgeArrow",value:function(e,t,n){if(e.visible()){var r,a,i,o=e._private.rscratch;if("source"===n?(r=o.arrowStartX,a=o.arrowStartY,i=o.srcArrowAngle):(r=o.arrowEndX,a=o.arrowEndY,i=o.tgtArrowAngle),!(isNaN(r)||null==r||isNaN(a)||null==a||isNaN(i)||null==i))if("none"!==e.pstyle(n+"-arrow-shape").value){var s=e.pstyle(n+"-arrow-color").value,l=e.pstyle("opacity").value*e.pstyle("line-opacity").value,u=e.pstyle("width").pfValue,c=e.pstyle("arrow-scale").value,d=this.r.getArrowWidth(u,c),h=this.instanceCount,f=this.transformBuffer.getMatrixView(h);Kd(f),Gd(f,f,[r,a]),$d(f,f,[d,d]),Zd(f,f,i),this.vertTypeBuffer.getView(h)[0]=3,Fd(t,this.indexBuffer.getView(h)),Vd(s,l,this.colorBuffer.getView(h)),this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}}},{key:"drawEdgeLine",value:function(e,t){if(e.visible()){var n=this._getEdgePoints(e);if(n){var r=e.pstyle("opacity").value,a=e.pstyle("line-opacity").value,i=e.pstyle("width").pfValue,o=e.pstyle("line-color").value,s=r*a;if(n.length/2+this.instanceCount>this.maxInstances&&this.endBatch(),4==n.length){var l=this.instanceCount;this.vertTypeBuffer.getView(l)[0]=1,Fd(t,this.indexBuffer.getView(l)),Vd(o,s,this.colorBuffer.getView(l)),this.lineWidthBuffer.getView(l)[0]=i;var u=this.pointAPointBBuffer.getView(l);u[0]=n[0],u[1]=n[1],u[2]=n[2],u[3]=n[3],this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}else for(var c=0;c<n.length-2;c+=2){var d=this.instanceCount;this.vertTypeBuffer.getView(d)[0]=2,Fd(t,this.indexBuffer.getView(d)),Vd(o,s,this.colorBuffer.getView(d)),this.lineWidthBuffer.getView(d)[0]=i;var h=n[c-2],f=n[c-1],p=n[c],g=n[c+1],v=n[c+2],y=n[c+3],m=n[c+4],b=n[c+5];0==c&&(h=2*p-v+.001,f=2*g-y+.001),c==n.length-4&&(m=2*v-p+.001,b=2*y-g+.001);var x=this.pointAPointBBuffer.getView(d);x[0]=h,x[1]=f,x[2]=p,x[3]=g;var w=this.pointCPointDBuffer.getView(d);w[0]=v,w[1]=y,w[2]=m,w[3]=b,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}}}},{key:"_getEdgePoints",value:function(e){var t=e._private.rscratch;if(!t.badLine&&null!=t.allpts&&!isNaN(t.allpts[0])){var n=t.allpts;if(4==n.length)return n;var r=this._getNumSegments(e);return this._getCurveSegmentPoints(n,r)}}},{key:"_getNumSegments",value:function(e){return Math.min(Math.max(15,5),this.maxInstances)}},{key:"_getCurveSegmentPoints",value:function(e,t){if(4==e.length)return e;for(var n=Array(2*(t+1)),r=0;r<=t;r++)if(0==r)n[0]=e[0],n[1]=e[1];else if(r==t)n[2*r]=e[e.length-2],n[2*r+1]=e[e.length-1];else{var a=r/t;this._setCurvePoint(e,a,n,2*r)}return n}},{key:"_setCurvePoint",value:function(e,t,n,r){if(!(e.length<=2)){for(var a=Array(e.length-2),i=0;i<a.length;i+=2){var o=(1-t)*e[i]+t*e[i+2],s=(1-t)*e[i+1]+t*e[i+3];a[i]=o,a[i+1]=s}return this._setCurvePoint(a,t,n,r)}n[r]=e[0],n[r+1]=e[1]}},{key:"endBatch",value:function(){var e=this.gl,t=this.vao,n=this.vertexCount,r=this.instanceCount;if(0!==r){var a=this.renderTarget.picking?this.pickingProgram:this.program;e.useProgram(a),e.bindVertexArray(t);var i,s=o(this.buffers);try{for(s.s();!(i=s.n()).done;){i.value.bufferSubData(r)}}catch(p){s.e(p)}finally{s.f()}for(var l,u,c=this.batchManager.getAtlases(),d=0;d<c.length;d++)c[d].bufferIfNeeded(e);for(var h=0;h<c.length;h++)e.activeTexture(e.TEXTURE0+h),e.bindTexture(e.TEXTURE_2D,c[h].texture),e.uniform1i(a.uTextures[h],h);e.uniform1f(a.uZoom,(l=this.r,u=l.pixelRatio,l.cy.zoom()*u)),e.uniformMatrix3fv(a.uPanZoomMatrix,!1,this.panZoomMatrix),e.uniform1i(a.uAtlasSize,this.batchManager.getAtlasSize());var f=Vd(this.bgColor,1);e.uniform4fv(a.uBGColor,f),e.drawArraysInstanced(e.TRIANGLES,0,n,r),e.bindVertexArray(null),e.bindTexture(e.TEXTURE_2D,null),this.debug&&this.batchDebugInfo.push({count:r,atlasCount:c.length}),this.startBatch()}}},{key:"getDebugInfo",value:function(){var e=this.atlasManager.getDebugInfo(),t=e.reduce((function(e,t){return e+t.atlasCount}),0),n=this.batchDebugInfo,r=n.reduce((function(e,t){return e+t.count}),0);return{atlasInfo:e,totalAtlases:t,wrappedCount:this.wrappedCount,simpleCount:this.simpleCount,batchCount:n.length,batchInfo:n,totalInstances:r}}}])}(),oh={};function sh(e,t){var n=e._private.rscratch;return ht(n,"labelWrapCachedLines",t)||[]}oh.initWebgl=function(e,t){var n=this,r=n.data.contexts[n.WEBGL];e.bgColor=function(e){var t=e.cy.container(),n=t&&t.style&&t.style.backgroundColor||"white";return be(n)}(n),e.webglTexSize=Math.min(e.webglTexSize,r.getParameter(r.MAX_TEXTURE_SIZE)),e.webglTexRows=Math.min(e.webglTexRows,54),e.webglTexRowsNodes=Math.min(e.webglTexRowsNodes,54),e.webglBatchSize=Math.min(e.webglBatchSize,16384),e.webglTexPerBatch=Math.min(e.webglTexPerBatch,r.getParameter(r.MAX_TEXTURE_IMAGE_UNITS)),n.webglDebug=e.webglDebug,n.webglDebugShowAtlases=e.webglDebugShowAtlases,n.pickingFrameBuffer=function(e){var t=e.createFramebuffer();e.bindFramebuffer(e.FRAMEBUFFER,t);var n=e.createTexture();return e.bindTexture(e.TEXTURE_2D,n),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,n,0),e.bindFramebuffer(e.FRAMEBUFFER,null),t.setFramebufferAttachmentSizes=function(t,r){e.bindTexture(e.TEXTURE_2D,n),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,t,r,0,e.RGBA,e.UNSIGNED_BYTE,null)},t}(r),n.pickingFrameBuffer.needsDraw=!0,n.drawing=new ih(n,r,e);var a=function(e){return function(t){return n.getTextAngle(t,e)}},i=function(e){return function(t){var n=t.pstyle(e);return n&&n.value}},s=function(e){return function(t){return t.pstyle("".concat(e,"-opacity")).value>0}},u=function(e){return"yes"===e.pstyle("text-events").strValue?ah:rh},c=function(e){var t=e.position(),n=t.x,r=t.y,a=e.outerWidth(),i=e.outerHeight();return{w:a,h:i,x1:n-a/2,y1:r-i/2}};n.drawing.addAtlasCollection("node",{texRows:e.webglTexRowsNodes}),n.drawing.addAtlasCollection("label",{texRows:e.webglTexRows}),n.drawing.addTextureAtlasRenderType("node-body",{collection:"node",getKey:t.getStyleKey,getBoundingBox:t.getElementBox,drawElement:t.drawElement}),n.drawing.addSimpleShapeRenderType("node-body",{getBoundingBox:c,isSimple:zd,shapeProps:{shape:"shape",color:"background-color",opacity:"background-opacity",radius:"corner-radius",border:!0}}),n.drawing.addSimpleShapeRenderType("node-overlay",{getBoundingBox:c,isVisible:s("overlay"),shapeProps:{shape:"overlay-shape",color:"overlay-color",opacity:"overlay-opacity",padding:"overlay-padding",radius:"overlay-corner-radius"}}),n.drawing.addSimpleShapeRenderType("node-underlay",{getBoundingBox:c,isVisible:s("underlay"),shapeProps:{shape:"underlay-shape",color:"underlay-color",opacity:"underlay-opacity",padding:"underlay-padding",radius:"underlay-corner-radius"}}),n.drawing.addTextureAtlasRenderType("label",{collection:"label",getTexPickingMode:u,getKey:lh(t.getLabelKey,null),getBoundingBox:uh(t.getLabelBox,null),drawClipped:!0,drawElement:t.drawLabel,getRotation:a(null),getRotationPoint:t.getLabelRotationPoint,getRotationOffset:t.getLabelRotationOffset,isVisible:i("label")}),n.drawing.addTextureAtlasRenderType("edge-source-label",{collection:"label",getTexPickingMode:u,getKey:lh(t.getSourceLabelKey,"source"),getBoundingBox:uh(t.getSourceLabelBox,"source"),drawClipped:!0,drawElement:t.drawSourceLabel,getRotation:a("source"),getRotationPoint:t.getSourceLabelRotationPoint,getRotationOffset:t.getSourceLabelRotationOffset,isVisible:i("source-label")}),n.drawing.addTextureAtlasRenderType("edge-target-label",{collection:"label",getTexPickingMode:u,getKey:lh(t.getTargetLabelKey,"target"),getBoundingBox:uh(t.getTargetLabelBox,"target"),drawClipped:!0,drawElement:t.drawTargetLabel,getRotation:a("target"),getRotationPoint:t.getTargetLabelRotationPoint,getRotationOffset:t.getTargetLabelRotationOffset,isVisible:i("target-label")});var d=Ae((function(){console.log("garbage collect flag set"),n.data.gc=!0}),1e4);n.onUpdateEleCalcs((function(e,t){var r=!1;t&&t.length>0&&(r|=n.drawing.invalidate(t)),r&&d()})),function(e){var t=e.render;e.render=function(n){n=n||{};var r=e.cy;e.webgl&&(r.zoom()>nd?(!function(e){var t=e.data.contexts[e.WEBGL];t.clear(t.COLOR_BUFFER_BIT|t.DEPTH_BUFFER_BIT)}(e),t.call(e,n)):(!function(e){var t=function(t){t.save(),t.setTransform(1,0,0,1,0,0),t.clearRect(0,0,e.canvasWidth,e.canvasHeight),t.restore()};t(e.data.contexts[e.NODE]),t(e.data.contexts[e.DRAG])}(e),hh(e,n,nh.SCREEN)))};var n=e.matchCanvasSize;e.matchCanvasSize=function(t){n.call(e,t),e.pickingFrameBuffer.setFramebufferAttachmentSizes(e.canvasWidth,e.canvasHeight),e.pickingFrameBuffer.needsDraw=!0},e.findNearestElements=function(t,n,r,a){return function(e,t,n){var r,a,i,s=function(e,t,n){var r,a,i,o,s=Ld(e),u=s.pan,c=s.zoom,d=function(e,t,n,r,a){var i=r*n+t.x,o=a*n+t.y;return[i,o=Math.round(e.canvasHeight-o)]}(e,u,c,t,n),h=l(d,2),f=h[0],p=h[1],g=6;if(r=f-g/2,a=p-g/2,o=g,0===(i=g)||0===o)return[];var v=e.data.contexts[e.WEBGL];v.bindFramebuffer(v.FRAMEBUFFER,e.pickingFrameBuffer),e.pickingFrameBuffer.needsDraw&&(v.viewport(0,0,v.canvas.width,v.canvas.height),hh(e,null,nh.PICKING),e.pickingFrameBuffer.needsDraw=!1);var y=i*o,m=new Uint8Array(4*y);v.readPixels(r,a,i,o,v.RGBA,v.UNSIGNED_BYTE,m),v.bindFramebuffer(v.FRAMEBUFFER,null);for(var b=new Set,x=0;x<y;x++){var w=jd(m.slice(4*x,4*x+4))-1;w>=0&&b.add(w)}return b}(e,t,n),u=e.getCachedZSortedEles(),c=o(s);try{for(c.s();!(i=c.n()).done;){var d=u[i.value];if(!r&&d.isNode()&&(r=d),!a&&d.isEdge()&&(a=d),r&&a)break}}catch(h){c.e(h)}finally{c.f()}return[r,a].filter(Boolean)}(e,t,n)};var r=e.invalidateCachedZSortedEles;e.invalidateCachedZSortedEles=function(){r.call(e),e.pickingFrameBuffer.needsDraw=!0};var a=e.notify;e.notify=function(t,n){a.call(e,t,n),"viewport"===t||"bounds"===t?e.pickingFrameBuffer.needsDraw=!0:"background"===t&&e.drawing.invalidate(n,{type:"node-body"})}}(n)};var lh=function(e,t){return function(n){var r=e(n),a=sh(n,t);return a.length>1?a.map((function(e,t){return"".concat(r,"_").concat(t)})):r}},uh=function(e,t){return function(n,r){var a=e(n);if("string"==typeof r){var i=r.indexOf("_");if(i>0){var o=Number(r.substring(i+1)),s=sh(n,t),l=a.h/s.length,u=l*o,c=a.y1+u;return{x1:a.x1,w:a.w,y1:c,h:l,yOffset:u}}}return a}};function ch(e,t){var n=e.canvasWidth,r=e.canvasHeight,a=Ld(e),i=a.pan,o=a.zoom;t.setTransform(1,0,0,1,0,0),t.clearRect(0,0,n,r),t.translate(i.x,i.y),t.scale(o,o)}function dh(e,t,n){var r=e.drawing;t+=1,n.isNode()?(r.drawNode(n,t,"node-underlay"),r.drawNode(n,t,"node-body"),r.drawTexture(n,t,"label"),r.drawNode(n,t,"node-overlay")):(r.drawEdgeLine(n,t),r.drawEdgeArrow(n,t,"source"),r.drawEdgeArrow(n,t,"target"),r.drawTexture(n,t,"label"),r.drawTexture(n,t,"edge-source-label"),r.drawTexture(n,t,"edge-target-label"))}function hh(e,t,n){var r;e.webglDebug&&(r=performance.now());var a=e.drawing,i=0;if(n.screen&&e.data.canvasNeedsRedraw[e.SELECT_BOX]&&function(e,t){e.drawSelectionRectangle(t,(function(t){return ch(e,t)}))}(e,t),e.data.canvasNeedsRedraw[e.NODE]||n.picking){var s=e.data.contexts[e.WEBGL];n.screen?(s.clearColor(0,0,0,0),s.enable(s.BLEND),s.blendFunc(s.ONE,s.ONE_MINUS_SRC_ALPHA)):s.disable(s.BLEND),s.clear(s.COLOR_BUFFER_BIT|s.DEPTH_BUFFER_BIT),s.viewport(0,0,s.canvas.width,s.canvas.height);var l=function(e){var t=e.canvasWidth,n=e.canvasHeight,r=Ld(e),a=r.pan,i=r.zoom,o=Hd();Gd(o,o,[a.x,a.y]),$d(o,o,[i,i]);var s=Hd();!function(e,t,n){e[0]=2/t,e[1]=0,e[2]=0,e[3]=0,e[4]=-2/n,e[5]=0,e[6]=-1,e[7]=1,e[8]=1}(s,t,n);var l,u,c,d,h,f,p,g,v,y,m,b,x,w,E,k,T,C,P,S,B,D=Hd();return l=D,c=o,d=(u=s)[0],h=u[1],f=u[2],p=u[3],g=u[4],v=u[5],y=u[6],m=u[7],b=u[8],x=c[0],w=c[1],E=c[2],k=c[3],T=c[4],C=c[5],P=c[6],S=c[7],B=c[8],l[0]=x*d+w*p+E*y,l[1]=x*h+w*g+E*m,l[2]=x*f+w*v+E*b,l[3]=k*d+T*p+C*y,l[4]=k*h+T*g+C*m,l[5]=k*f+T*v+C*b,l[6]=P*d+S*p+B*y,l[7]=P*h+S*g+B*m,l[8]=P*f+S*v+B*b,D}(e),u=e.getCachedZSortedEles();if(i=u.length,a.startFrame(l,n),n.screen){for(var c=0;c<u.nondrag.length;c++)dh(e,c,u.nondrag[c]);for(var d=0;d<u.drag.length;d++)dh(e,d,u.drag[d])}else if(n.picking)for(var h=0;h<u.length;h++)dh(e,h,u[h]);a.endFrame(),n.screen&&e.webglDebugShowAtlases&&(function(e){var t=e.data.contexts[e.NODE];t.save(),ch(e,t),t.strokeStyle="rgba(0, 0, 0, 0.3)",t.beginPath(),t.moveTo(-1e3,0),t.lineTo(1e3,0),t.stroke(),t.beginPath(),t.moveTo(0,-1e3),t.lineTo(0,1e3),t.stroke(),t.restore()}(e),function(e){var t=function(t,n,r){for(var a=t.atlasManager.getAtlasCollection(n),i=e.data.contexts[e.NODE],o=a.atlases,s=0;s<o.length;s++){var l=o[s].canvas;if(l){var u=l.width,c=l.height,d=u*s,h=l.height*r;i.save(),i.scale(.4,.4),i.drawImage(l,d,h),i.strokeStyle="black",i.rect(d,h,u,c),i.stroke(),i.restore()}}},n=0;t(e.drawing,"node",n++),t(e.drawing,"label",n++)}(e)),e.data.canvasNeedsRedraw[e.NODE]=!1,e.data.canvasNeedsRedraw[e.DRAG]=!1}if(e.webglDebug){var f=performance.now(),p=Math.ceil(f-r),g=a.getDebugInfo(),v=["".concat(i," elements"),"".concat(g.totalInstances," instances"),"".concat(g.batchCount," batches"),"".concat(g.totalAtlases," atlases"),"".concat(g.wrappedCount," wrapped textures"),"".concat(g.simpleCount," simple shapes")].join(", ");console.log("WebGL (".concat(n.name,") - frame time ").concat(p,"ms")),console.log("Totals:"),console.log("  ".concat(v)),console.log("Texture Atlases Used:");var y,m=o(g.atlasInfo);try{for(m.s();!(y=m.n()).done;){var b=y.value;console.log("  ".concat(b.type,": ").concat(b.keyCount," keys, ").concat(b.atlasCount," atlases"))}}catch(x){m.e(x)}finally{m.f()}console.log("")}e.data.gc&&(console.log("Garbage Collect!"),e.data.gc=!1,a.gc())}for(var fh={drawPolygonPath:function(e,t,n,r,a,i){var o=r/2,s=a/2;e.beginPath&&e.beginPath(),e.moveTo(t+o*i[0],n+s*i[1]);for(var l=1;l<i.length/2;l++)e.lineTo(t+o*i[2*l],n+s*i[2*l+1]);e.closePath()},drawRoundPolygonPath:function(e,t,n,r,a,i,o){o.forEach((function(t){return Dc(e,t)})),e.closePath()},drawRoundRectanglePath:function(e,t,n,r,a,i){var o=r/2,s=a/2,l="auto"===i?Cn(r,a):Math.min(i,s,o);e.beginPath&&e.beginPath(),e.moveTo(t,n-s),e.arcTo(t+o,n-s,t+o,n,l),e.arcTo(t+o,n+s,t,n+s,l),e.arcTo(t-o,n+s,t-o,n,l),e.arcTo(t-o,n-s,t,n-s,l),e.lineTo(t,n-s),e.closePath()},drawBottomRoundRectanglePath:function(e,t,n,r,a,i){var o=r/2,s=a/2,l="auto"===i?Cn(r,a):i;e.beginPath&&e.beginPath(),e.moveTo(t,n-s),e.lineTo(t+o,n-s),e.lineTo(t+o,n),e.arcTo(t+o,n+s,t,n+s,l),e.arcTo(t-o,n+s,t-o,n,l),e.lineTo(t-o,n-s),e.lineTo(t,n-s),e.closePath()},drawCutRectanglePath:function(e,t,n,r,a,i,o){var s=r/2,l=a/2,u="auto"===o?8:o;e.beginPath&&e.beginPath(),e.moveTo(t-s+u,n-l),e.lineTo(t+s-u,n-l),e.lineTo(t+s,n-l+u),e.lineTo(t+s,n+l-u),e.lineTo(t+s-u,n+l),e.lineTo(t-s+u,n+l),e.lineTo(t-s,n+l-u),e.lineTo(t-s,n-l+u),e.closePath()},drawBarrelPath:function(e,t,n,r,a){var i=r/2,o=a/2,s=t-i,l=t+i,u=n-o,c=n+o,d=Sn(r,a),h=d.widthOffset,f=d.heightOffset,p=d.ctrlPtOffsetPct*h;e.beginPath&&e.beginPath(),e.moveTo(s,u+f),e.lineTo(s,c-f),e.quadraticCurveTo(s+p,c,s+h,c),e.lineTo(l-h,c),e.quadraticCurveTo(l-p,c,l,c-f),e.lineTo(l,u+f),e.quadraticCurveTo(l-p,u,l-h,u),e.lineTo(s+h,u),e.quadraticCurveTo(s+p,u,s,u+f),e.closePath()}},ph=Math.sin(0),gh=Math.cos(0),vh={},yh={},mh=Math.PI/40,bh=0*Math.PI;bh<2*Math.PI;bh+=mh)vh[bh]=Math.sin(bh),yh[bh]=Math.cos(bh);fh.drawEllipsePath=function(e,t,n,r,a){if(e.beginPath&&e.beginPath(),e.ellipse)e.ellipse(t,n,r/2,a/2,0,0,2*Math.PI);else for(var i,o,s=r/2,l=a/2,u=0*Math.PI;u<2*Math.PI;u+=mh)i=t-s*vh[u]*ph+s*yh[u]*gh,o=n+l*yh[u]*ph+l*vh[u]*gh,0===u?e.moveTo(i,o):e.lineTo(i,o);e.closePath()};var xh={};function wh(e){var t=e.indexOf(",");return e.substr(t+1)}function Eh(e,t,n){var r=function(){return t.toDataURL(n,e.quality)};switch(e.output){case"blob-promise":return new jr((function(r,a){try{t.toBlob((function(e){null!=e?r(e):a(new Error("`canvas.toBlob()` sent a null value in its callback"))}),n,e.quality)}catch(i){a(i)}}));case"blob":return function(e,t){for(var n=atob(e),r=new ArrayBuffer(n.length),a=new Uint8Array(r),i=0;i<n.length;i++)a[i]=n.charCodeAt(i);return new Blob([r],{type:t})}(wh(r()),n);case"base64":return wh(r());default:return r()}}xh.createBuffer=function(e,t){var n=document.createElement("canvas");return n.width=e,n.height=t,[n,n.getContext("2d")]},xh.bufferCanvasImage=function(e){var t=this.cy,n=t.mutableElements().boundingBox(),r=this.findContainerClientCoords(),a=e.full?Math.ceil(n.w):r[2],i=e.full?Math.ceil(n.h):r[3],o=Q(e.maxWidth)||Q(e.maxHeight),s=this.getPixelRatio(),l=1;if(void 0!==e.scale)a*=e.scale,i*=e.scale,l=e.scale;else if(o){var u=1/0,c=1/0;Q(e.maxWidth)&&(u=l*e.maxWidth/a),Q(e.maxHeight)&&(c=l*e.maxHeight/i),a*=l=Math.min(u,c),i*=l}o||(a*=s,i*=s,l*=s);var d=document.createElement("canvas");d.width=a,d.height=i,d.style.width=a+"px",d.style.height=i+"px";var h=d.getContext("2d");if(a>0&&i>0){h.clearRect(0,0,a,i),h.globalCompositeOperation="source-over";var f=this.getCachedZSortedEles();if(e.full)h.translate(-n.x1*l,-n.y1*l),h.scale(l,l),this.drawElements(h,f),h.scale(1/l,1/l),h.translate(n.x1*l,n.y1*l);else{var p=t.pan(),g={x:p.x*l,y:p.y*l};l*=t.zoom(),h.translate(g.x,g.y),h.scale(l,l),this.drawElements(h,f),h.scale(1/l,1/l),h.translate(-g.x,-g.y)}e.bg&&(h.globalCompositeOperation="destination-over",h.fillStyle=e.bg,h.rect(0,0,a,i),h.fill())}return d},xh.png=function(e){return Eh(e,this.bufferCanvasImage(e),"image/png")},xh.jpg=function(e){return Eh(e,this.bufferCanvasImage(e),"image/jpeg")};var kh={nodeShapeImpl:function(e,t,n,r,a,i,o,s){switch(e){case"ellipse":return this.drawEllipsePath(t,n,r,a,i);case"polygon":return this.drawPolygonPath(t,n,r,a,i,o);case"round-polygon":return this.drawRoundPolygonPath(t,n,r,a,i,o,s);case"roundrectangle":case"round-rectangle":return this.drawRoundRectanglePath(t,n,r,a,i,s);case"cutrectangle":case"cut-rectangle":return this.drawCutRectanglePath(t,n,r,a,i,o,s);case"bottomroundrectangle":case"bottom-round-rectangle":return this.drawBottomRoundRectanglePath(t,n,r,a,i,s);case"barrel":return this.drawBarrelPath(t,n,r,a,i)}}},Th=Ph,Ch=Ph.prototype;function Ph(e){var t=this,n=t.cy.window().document;e.webgl&&(Ch.CANVAS_LAYERS=t.CANVAS_LAYERS=4,console.log("webgl rendering enabled")),t.data={canvases:new Array(Ch.CANVAS_LAYERS),contexts:new Array(Ch.CANVAS_LAYERS),canvasNeedsRedraw:new Array(Ch.CANVAS_LAYERS),bufferCanvases:new Array(Ch.BUFFER_COUNT),bufferContexts:new Array(Ch.CANVAS_LAYERS)};var r="-webkit-tap-highlight-color",a="rgba(0,0,0,0)";t.data.canvasContainer=n.createElement("div");var i=t.data.canvasContainer.style;t.data.canvasContainer.style[r]=a,i.position="relative",i.zIndex="0",i.overflow="hidden";var o=e.cy.container();o.appendChild(t.data.canvasContainer),o.style[r]=a;var s={"-webkit-user-select":"none","-moz-user-select":"-moz-none","user-select":"none","-webkit-tap-highlight-color":"rgba(0,0,0,0)","outline-style":"none"};p&&p.userAgent.match(/msie|trident|edge/i)&&(s["-ms-touch-action"]="none",s["touch-action"]="none");for(var l=0;l<Ch.CANVAS_LAYERS;l++){var u=t.data.canvases[l]=n.createElement("canvas"),c=Ch.CANVAS_TYPES[l];t.data.contexts[l]=u.getContext(c),t.data.contexts[l]||nt("Could not create canvas of type "+c),Object.keys(s).forEach((function(e){u.style[e]=s[e]})),u.style.position="absolute",u.setAttribute("data-id","layer"+l),u.style.zIndex=String(Ch.CANVAS_LAYERS-l),t.data.canvasContainer.appendChild(u),t.data.canvasNeedsRedraw[l]=!1}t.data.topCanvas=t.data.canvases[0],t.data.canvases[Ch.NODE].setAttribute("data-id","layer"+Ch.NODE+"-node"),t.data.canvases[Ch.SELECT_BOX].setAttribute("data-id","layer"+Ch.SELECT_BOX+"-selectbox"),t.data.canvases[Ch.DRAG].setAttribute("data-id","layer"+Ch.DRAG+"-drag"),t.data.canvases[Ch.WEBGL]&&t.data.canvases[Ch.WEBGL].setAttribute("data-id","layer"+Ch.WEBGL+"-webgl");for(l=0;l<Ch.BUFFER_COUNT;l++)t.data.bufferCanvases[l]=n.createElement("canvas"),t.data.bufferContexts[l]=t.data.bufferCanvases[l].getContext("2d"),t.data.bufferCanvases[l].style.position="absolute",t.data.bufferCanvases[l].setAttribute("data-id","buffer"+l),t.data.bufferCanvases[l].style.zIndex=String(-l-1),t.data.bufferCanvases[l].style.visibility="hidden";t.pathsEnabled=!0;var d=Qt(),h=function(e){return{x:-e.w/2,y:-e.h/2}},f=function(e){return e[0]._private.nodeKey},g=function(e){return e[0]._private.labelStyleKey},v=function(e){return e[0]._private.sourceLabelStyleKey},y=function(e){return e[0]._private.targetLabelStyleKey},m=function(e,n,r,a,i){return t.drawElement(e,n,r,!1,!1,i)},b=function(e,n,r,a,i){return t.drawElementText(e,n,r,a,"main",i)},x=function(e,n,r,a,i){return t.drawElementText(e,n,r,a,"source",i)},w=function(e,n,r,a,i){return t.drawElementText(e,n,r,a,"target",i)},E=function(e){return e.boundingBox(),e[0]._private.bodyBounds},k=function(e){return e.boundingBox(),e[0]._private.labelBounds.main||d},T=function(e){return e.boundingBox(),e[0]._private.labelBounds.source||d},C=function(e){return e.boundingBox(),e[0]._private.labelBounds.target||d},P=function(e,t){return t},S=function(e){return{x:((t=E(e)).x1+t.x2)/2,y:(t.y1+t.y2)/2};var t},B=function(e,t,n){var r=e?e+"-":"";return{x:t.x+n.pstyle(r+"text-margin-x").pfValue,y:t.y+n.pstyle(r+"text-margin-y").pfValue}},D=function(e,t,n){var r=e[0]._private.rscratch;return{x:r[t],y:r[n]}},_=function(e){return B("",D(e,"labelX","labelY"),e)},A=function(e){return B("source",D(e,"sourceLabelX","sourceLabelY"),e)},M=function(e){return B("target",D(e,"targetLabelX","targetLabelY"),e)},R=function(e){return h(E(e))},I=function(e){return h(T(e))},N=function(e){return h(C(e))},L=function(e){var t=k(e),n=h(k(e));if(e.isNode()){switch(e.pstyle("text-halign").value){case"left":n.x=-t.w-(t.leftPad||0);break;case"right":n.x=-(t.rightPad||0)}switch(e.pstyle("text-valign").value){case"top":n.y=-t.h-(t.topPad||0);break;case"bottom":n.y=-(t.botPad||0)}}return n},z=t.data.eleTxrCache=new id(t,{getKey:f,doesEleInvalidateKey:function(e){var t=e[0]._private;return!(t.oldBackgroundTimestamp===t.backgroundTimestamp)},drawElement:m,getBoundingBox:E,getRotationPoint:S,getRotationOffset:R,allowEdgeTxrCaching:!1,allowParentTxrCaching:!1}),O=t.data.lblTxrCache=new id(t,{getKey:g,drawElement:b,getBoundingBox:k,getRotationPoint:_,getRotationOffset:L,isVisible:P}),V=t.data.slbTxrCache=new id(t,{getKey:v,drawElement:x,getBoundingBox:T,getRotationPoint:A,getRotationOffset:I,isVisible:P}),F=t.data.tlbTxrCache=new id(t,{getKey:y,drawElement:w,getBoundingBox:C,getRotationPoint:M,getRotationOffset:N,isVisible:P}),j=t.data.lyrTxrCache=new sd(t);t.onUpdateEleCalcs((function(e,t){z.invalidateElements(t),O.invalidateElements(t),V.invalidateElements(t),F.invalidateElements(t),j.invalidateElements(t);for(var n=0;n<t.length;n++){var r=t[n]._private;r.oldBackgroundTimestamp=r.backgroundTimestamp}}));var X=function(e){for(var t=0;t<e.length;t++)j.enqueueElementRefinement(e[t].ele)};z.onDequeue(X),O.onDequeue(X),V.onDequeue(X),F.onDequeue(X),e.webgl&&t.initWebgl(e,{getStyleKey:f,getLabelKey:g,getSourceLabelKey:v,getTargetLabelKey:y,drawElement:m,drawLabel:b,drawSourceLabel:x,drawTargetLabel:w,getElementBox:E,getLabelBox:k,getSourceLabelBox:T,getTargetLabelBox:C,getElementRotationPoint:S,getElementRotationOffset:R,getLabelRotationPoint:_,getSourceLabelRotationPoint:A,getTargetLabelRotationPoint:M,getLabelRotationOffset:L,getSourceLabelRotationOffset:I,getTargetLabelRotationOffset:N})}Ch.CANVAS_LAYERS=3,Ch.SELECT_BOX=0,Ch.DRAG=1,Ch.NODE=2,Ch.WEBGL=3,Ch.CANVAS_TYPES=["2d","2d","2d","webgl2"],Ch.BUFFER_COUNT=3,Ch.TEXTURE_BUFFER=0,Ch.MOTIONBLUR_BUFFER_NODE=1,Ch.MOTIONBLUR_BUFFER_DRAG=2,Ch.redrawHint=function(e,t){var n=this;switch(e){case"eles":n.data.canvasNeedsRedraw[Ch.NODE]=t;break;case"drag":n.data.canvasNeedsRedraw[Ch.DRAG]=t;break;case"select":n.data.canvasNeedsRedraw[Ch.SELECT_BOX]=t;break;case"gc":n.data.gc=!0}};var Sh="undefined"!=typeof Path2D;Ch.path2dEnabled=function(e){if(void 0===e)return this.pathsEnabled;this.pathsEnabled=!!e},Ch.usePaths=function(){return Sh&&this.pathsEnabled},Ch.setImgSmoothing=function(e,t){null!=e.imageSmoothingEnabled?e.imageSmoothingEnabled=t:(e.webkitImageSmoothingEnabled=t,e.mozImageSmoothingEnabled=t,e.msImageSmoothingEnabled=t)},Ch.getImgSmoothing=function(e){return null!=e.imageSmoothingEnabled?e.imageSmoothingEnabled:e.webkitImageSmoothingEnabled||e.mozImageSmoothingEnabled||e.msImageSmoothingEnabled},Ch.makeOffscreenCanvas=function(e,t){var n;"undefined"!==("undefined"==typeof OffscreenCanvas?"undefined":d(OffscreenCanvas))?n=new OffscreenCanvas(e,t):((n=this.cy.window().document.createElement("canvas")).width=e,n.height=t);return n},[hd,md,Cd,Sd,Bd,_d,Rd,oh,fh,xh,kh].forEach((function(e){me(Ch,e)}));var Bh=[{type:"layout",extensions:tc},{type:"renderer",extensions:[{name:"null",impl:nc},{name:"base",impl:$c},{name:"canvas",impl:Th}]}],Dh={},_h={};function Ah(e,t,n){var r=n,a=function(n){at("Can not register `"+t+"` for `"+e+"` since `"+n+"` already exists in the prototype and can not be overridden")};if("core"===e){if(pu.prototype[t])return a(t);pu.prototype[t]=n}else if("collection"===e){if(_l.prototype[t])return a(t);_l.prototype[t]=n}else if("layout"===e){for(var i=function(e){this.options=e,n.call(this,e),$(this._private)||(this._private={}),this._private.cy=e.cy,this._private.listeners=[],this.createEmitter()},o=i.prototype=Object.create(n.prototype),s=[],l=0;l<s.length;l++){var u=s[l];o[u]=o[u]||function(){return this}}o.start&&!o.run?o.run=function(){return this.start(),this}:!o.start&&o.run&&(o.start=function(){return this.run(),this});var c=n.prototype.stop;o.stop=function(){var e=this.options;if(e&&e.animate){var t=this.animations;if(t)for(var n=0;n<t.length;n++)t[n].stop()}return c?c.call(this):this.emit("layoutstop"),this},o.destroy||(o.destroy=function(){return this}),o.cy=function(){return this._private.cy};var d=function(e){return e._private.cy},h={addEventFields:function(e,t){t.layout=e,t.cy=d(e),t.target=e},bubble:function(){return!0},parent:function(e){return d(e)}};me(o,{createEmitter:function(){return this._private.emitter=new Ks(h,this),this},emitter:function(){return this._private.emitter},on:function(e,t){return this.emitter().on(e,t),this},one:function(e,t){return this.emitter().one(e,t),this},once:function(e,t){return this.emitter().one(e,t),this},removeListener:function(e,t){return this.emitter().removeListener(e,t),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},emit:function(e,t){return this.emitter().emit(e,t),this}}),po.eventAliasesOn(o),r=i}else if("renderer"===e&&"null"!==t&&"base"!==t){var f=Mh("renderer","base"),p=f.prototype,g=n,v=n.prototype,y=function(){f.apply(this,arguments),g.apply(this,arguments)},m=y.prototype;for(var b in p){var x=p[b];if(null!=v[b])return a(b);m[b]=x}for(var w in v)m[w]=v[w];p.clientFunctions.forEach((function(e){m[e]=m[e]||function(){nt("Renderer does not implement `renderer."+e+"()` on its prototype")}})),r=y}else if("__proto__"===e||"constructor"===e||"prototype"===e)return nt(e+" is an illegal type to be registered, possibly lead to prototype pollutions");return we({map:Dh,keys:[e,t],value:r})}function Mh(e,t){return Ee({map:Dh,keys:[e,t]})}function Rh(e,t,n,r,a){return we({map:_h,keys:[e,t,n,r],value:a})}function Ih(e,t,n,r){return Ee({map:_h,keys:[e,t,n,r]})}var Nh=function(){return 2===arguments.length?Mh.apply(null,arguments):3===arguments.length?Ah.apply(null,arguments):4===arguments.length?Ih.apply(null,arguments):5===arguments.length?Rh.apply(null,arguments):void nt("Invalid extension access syntax")};pu.prototype.extension=Nh,Bh.forEach((function(e){e.extensions.forEach((function(t){Ah(e.type,t.name,t.impl)}))}));var Lh=function(){if(!(this instanceof Lh))return new Lh;this.length=0},zh=Lh.prototype;zh.instanceString=function(){return"stylesheet"},zh.selector=function(e){return this[this.length++]={selector:e,properties:[]},this},zh.css=function(e,t){var n=this.length-1;if(K(e))this[n].properties.push({name:e,value:t});else if($(e))for(var r=e,a=Object.keys(r),i=0;i<a.length;i++){var o=a[i],s=r[o];if(null!=s){var l=uu.properties[o]||uu.properties[ue(o)];if(null!=l){var u=l.name,c=s;this[n].properties.push({name:u,value:c})}}}return this},zh.style=zh.css,zh.generateStyle=function(e){var t=new uu(e);return this.appendToStyle(t)},zh.appendToStyle=function(e){for(var t=0;t<this.length;t++){var n=this[t],r=n.selector,a=n.properties;e.selector(r);for(var i=0;i<a.length;i++){var o=a[i];e.css(o.name,o.value)}}return e};var Oh=function(e){return void 0===e&&(e={}),$(e)?new pu(e):K(e)?Nh.apply(Nh,arguments):void 0};Oh.use=function(e){var t=Array.prototype.slice.call(arguments,1);return t.unshift(Oh),e.apply(null,t),this},Oh.warnings=function(e){return rt(e)},Oh.version="3.32.0",Oh.stylesheet=Oh.Stylesheet=Lh}}]);