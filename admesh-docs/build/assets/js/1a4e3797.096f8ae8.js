(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[2138],{317:e=>{"use strict";e.exports=function(e){return e&&Object.keys(e).length>0}},849:e=>{"use strict";e.exports=function(e,t){if(Array.isArray(e))for(var r=0;r<e.length;r++)if(t(e[r]))return e[r]}},917:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>W});var i=r(6540),n=r(4164),s=r(4103),a=r.n(s);function c(e){let t;const r=`algolia-client-js-${e.key}`;function i(){return void 0===t&&(t=e.localStorage||window.localStorage),t}function n(){return JSON.parse(i().getItem(r)||"{}")}function s(e){i().setItem(r,JSON.stringify(e))}return{get:(t,r,i={miss:()=>Promise.resolve()})=>Promise.resolve().then((()=>(function(){const t=e.timeToLive?1e3*e.timeToLive:null,r=n(),i=Object.fromEntries(Object.entries(r).filter((([,e])=>void 0!==e.timestamp)));if(s(i),!t)return;s(Object.fromEntries(Object.entries(i).filter((([,e])=>{const r=(new Date).getTime();return!(e.timestamp+t<r)}))))}(),n()[JSON.stringify(t)]))).then((e=>Promise.all([e?e.value:r(),void 0!==e]))).then((([e,t])=>Promise.all([e,t||i.miss(e)]))).then((([e])=>e)),set:(e,t)=>Promise.resolve().then((()=>{const s=n();return s[JSON.stringify(e)]={timestamp:(new Date).getTime(),value:t},i().setItem(r,JSON.stringify(s)),t})),delete:e=>Promise.resolve().then((()=>{const t=n();delete t[JSON.stringify(e)],i().setItem(r,JSON.stringify(t))})),clear:()=>Promise.resolve().then((()=>{i().removeItem(r)}))}}function o(e){const t=[...e.caches],r=t.shift();return void 0===r?{get:(e,t,r={miss:()=>Promise.resolve()})=>t().then((e=>Promise.all([e,r.miss(e)]))).then((([e])=>e)),set:(e,t)=>Promise.resolve(t),delete:e=>Promise.resolve(),clear:()=>Promise.resolve()}:{get:(e,i,n={miss:()=>Promise.resolve()})=>r.get(e,i,n).catch((()=>o({caches:t}).get(e,i,n))),set:(e,i)=>r.set(e,i).catch((()=>o({caches:t}).set(e,i))),delete:e=>r.delete(e).catch((()=>o({caches:t}).delete(e))),clear:()=>r.clear().catch((()=>o({caches:t}).clear()))}}function u(e={serializable:!0}){let t={};return{get(r,i,n={miss:()=>Promise.resolve()}){const s=JSON.stringify(r);if(s in t)return Promise.resolve(e.serializable?JSON.parse(t[s]):t[s]);const a=i();return a.then((e=>n.miss(e))).then((()=>a))},set:(r,i)=>(t[JSON.stringify(r)]=e.serializable?JSON.stringify(i):i,Promise.resolve(i)),delete:e=>(delete t[JSON.stringify(e)],Promise.resolve()),clear:()=>(t={},Promise.resolve())}}function h({algoliaAgents:e,client:t,version:r}){const i=function(e){const t={value:`Algolia for JavaScript (${e})`,add(e){const r=`; ${e.segment}${void 0!==e.version?` (${e.version})`:""}`;return-1===t.value.indexOf(r)&&(t.value=`${t.value}${r}`),t}};return t}(r).add({segment:t,version:r});return e.forEach((e=>i.add(e))),i}var l=12e4;function f(e,t="up"){const r=Date.now();return{...e,status:t,lastUpdate:r,isUp:function(){return"up"===t||Date.now()-r>l},isTimedOut:function(){return"timed out"===t&&Date.now()-r<=l}}}var m=class extends Error{name="AlgoliaError";constructor(e,t){super(e),t&&(this.name=t)}},d=class extends m{stackTrace;constructor(e,t,r){super(e,r),this.stackTrace=t}},p=class extends d{constructor(e){super("Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.",e,"RetryError")}},g=class extends d{status;constructor(e,t,r,i="ApiError"){super(e,r,i),this.status=t}},v=class extends m{response;constructor(e,t){super(e,"DeserializationError"),this.response=t}},y=class extends g{error;constructor(e,t,r,i){super(e,t,i,"DetailedApiError"),this.error=r}};function R(e,t,r){const i=(n=r,Object.keys(n).filter((e=>void 0!==n[e])).sort().map((e=>`${e}=${encodeURIComponent("[object Array]"===Object.prototype.toString.call(n[e])?n[e].join(","):n[e]).replace(/\+/g,"%20")}`)).join("&"));var n;let s=`${e.protocol}://${e.url}${e.port?`:${e.port}`:""}/${"/"===t.charAt(0)?t.substring(1):t}`;return i.length&&(s+=`?${i}`),s}function F(e){const t=e.request.headers["x-algolia-api-key"]?{"x-algolia-api-key":"*****"}:{};return{...e,request:{...e.request,headers:{...e.request.headers,...t}}}}function _({hosts:e,hostsCache:t,baseHeaders:r,logger:i,baseQueryParameters:n,algoliaAgent:s,timeouts:a,requester:c,requestsCache:o,responsesCache:u}){async function h(o,u,h=!0){const l=[],m=function(e,t){if("GET"===e.method||void 0===e.data&&void 0===t.data)return;const r=Array.isArray(e.data)?e.data:{...e.data,...t.data};return JSON.stringify(r)}(o,u),d=function(e,t,r){const i={Accept:"application/json",...e,...t,...r},n={};return Object.keys(i).forEach((e=>{const t=i[e];n[e.toLowerCase()]=t})),n}(r,o.headers,u.headers),_="GET"===o.method?{...o.data,...u.data}:{},b={...n,...o.queryParameters,..._};if(s.value&&(b["x-algolia-agent"]=s.value),u&&u.queryParameters)for(const e of Object.keys(u.queryParameters))u.queryParameters[e]&&"[object Object]"!==Object.prototype.toString.call(u.queryParameters[e])?b[e]=u.queryParameters[e].toString():b[e]=u.queryParameters[e];let P=0;const j=async(e,r)=>{const n=e.pop();if(void 0===n)throw new p(function(e){return e.map((e=>F(e)))}(l));const s={...a,...u.timeouts},_={data:m,headers:d,method:o.method,url:R(n,o.path,b),connectTimeout:r(P,s.connect),responseTimeout:r(P,h?s.read:s.write)},x=t=>{const r={request:_,response:t,host:n,triesLeft:e.length};return l.push(r),r},E=await c.send(_);if(function({isTimedOut:e,status:t}){return e||function({isTimedOut:e,status:t}){return!e&&0===~~t}({isTimedOut:e,status:t})||2!=~~(t/100)&&4!=~~(t/100)}(E)){const s=x(E);return E.isTimedOut&&P++,i.info("Retryable failure",F(s)),await t.set(n,f(n,E.isTimedOut?"timed out":"down")),j(e,r)}if(function({status:e}){return 2==~~(e/100)}(E))return function(e){try{return JSON.parse(e.content)}catch(t){throw new v(t.message,e)}}(E);throw x(E),function({content:e,status:t},r){try{const i=JSON.parse(e);return"error"in i?new y(i.message,t,i.error,r):new g(i.message,t,r)}catch{}return new g(e,t,r)}(E,l)},x=e.filter((e=>"readWrite"===e.accept||(h?"read"===e.accept:"write"===e.accept))),E=await async function(e){const r=await Promise.all(e.map((e=>t.get(e,(()=>Promise.resolve(f(e))))))),i=r.filter((e=>e.isUp())),n=r.filter((e=>e.isTimedOut())),s=[...i,...n];return{hosts:s.length>0?s:e,getTimeout:(e,t)=>(0===n.length&&0===e?1:n.length+3+e)*t}}(x);return j([...E.hosts].reverse(),E.getTimeout)}return{hostsCache:t,requester:c,timeouts:a,logger:i,algoliaAgent:s,baseHeaders:r,baseQueryParameters:n,hosts:e,request:function(e,t={}){const i=e.useReadTransporter||"GET"===e.method;if(!i)return h(e,t,i);const s=()=>h(e,t);if(!0!==(t.cacheable||e.cacheable))return s();const a={request:e,requestOptions:t,transporter:{queryParameters:n,headers:r}};return u.get(a,(()=>o.get(a,(()=>o.set(a,s()).then((e=>Promise.all([o.delete(a),e])),(e=>Promise.all([o.delete(a),Promise.reject(e)]))).then((([e,t])=>t))))),{miss:e=>u.set(a,e)})},requestsCache:o,responsesCache:u}}var b="5.27.0";function P(e){return[{url:`${e}-dsn.algolia.net`,accept:"read",protocol:"https"},{url:`${e}.algolia.net`,accept:"write",protocol:"https"}].concat(function(e){const t=e;for(let r=e.length-1;r>0;r--){const i=Math.floor(Math.random()*(r+1)),n=e[r];t[r]=e[i],t[i]=n}return t}([{url:`${e}-1.algolianet.com`,accept:"readWrite",protocol:"https"},{url:`${e}-2.algolianet.com`,accept:"readWrite",protocol:"https"},{url:`${e}-3.algolianet.com`,accept:"readWrite",protocol:"https"}]))}function j(e,t,r){if(!e||"string"!=typeof e)throw new Error("`appId` is missing.");if(!t||"string"!=typeof t)throw new Error("`apiKey` is missing.");return function({appId:e,apiKey:t,authMode:r,algoliaAgents:i,...n}){const s=function(e,t,r="WithinHeaders"){const i={"x-algolia-api-key":t,"x-algolia-application-id":e};return{headers:()=>"WithinHeaders"===r?i:{},queryParameters:()=>"WithinQueryParameters"===r?i:{}}}(e,t,r),a=_({hosts:P(e),...n,algoliaAgent:h({algoliaAgents:i,client:"Lite",version:b}),baseHeaders:{"content-type":"text/plain",...s.headers(),...n.baseHeaders},baseQueryParameters:{...s.queryParameters(),...n.baseQueryParameters}});return{transporter:a,appId:e,apiKey:t,clearCache:()=>Promise.all([a.requestsCache.clear(),a.responsesCache.clear()]).then((()=>{})),get _ua(){return a.algoliaAgent.value},addAlgoliaAgent(e,t){a.algoliaAgent.add({segment:e,version:t})},setClientApiKey({apiKey:e}){r&&"WithinHeaders"!==r?a.baseQueryParameters["x-algolia-api-key"]=e:a.baseHeaders["x-algolia-api-key"]=e},searchForHits(e,t){return this.search(e,t)},searchForFacets(e,t){return this.search(e,t)},customPost({path:e,parameters:t,body:r},i){if(!e)throw new Error("Parameter `path` is required when calling `customPost`.");const n={method:"POST",path:"/{path}".replace("{path}",e),queryParameters:t||{},headers:{},data:r||{}};return a.request(n,i)},getRecommendations(e,t){if(e&&Array.isArray(e)&&(e={requests:e}),!e)throw new Error("Parameter `getRecommendationsParams` is required when calling `getRecommendations`.");if(!e.requests)throw new Error("Parameter `getRecommendationsParams.requests` is required when calling `getRecommendations`.");const r={method:"POST",path:"/1/indexes/*/recommendations",queryParameters:{},headers:{},data:e,useReadTransporter:!0,cacheable:!0};return a.request(r,t)},search(e,t){if(e&&Array.isArray(e)){const t={requests:e.map((({params:e,...t})=>"facet"===t.type?{...t,...e,type:"facet"}:{...t,...e,facet:void 0,maxFacetHits:void 0,facetQuery:void 0}))};e=t}if(!e)throw new Error("Parameter `searchMethodParams` is required when calling `search`.");if(!e.requests)throw new Error("Parameter `searchMethodParams.requests` is required when calling `search`.");const r={method:"POST",path:"/1/indexes/*/queries",queryParameters:{},headers:{},data:e,useReadTransporter:!0,cacheable:!0};return a.request(r,t)}}}({appId:e,apiKey:t,timeouts:{connect:1e3,read:2e3,write:3e4},logger:{debug:(e,t)=>Promise.resolve(),info:(e,t)=>Promise.resolve(),error:(e,t)=>Promise.resolve()},requester:{send:function(e){return new Promise((t=>{let r=new XMLHttpRequest;r.open(e.method,e.url,!0),Object.keys(e.headers).forEach((t=>r.setRequestHeader(t,e.headers[t])));let i,n=(e,i)=>setTimeout((()=>{r.abort(),t({status:0,content:i,isTimedOut:!0})}),e),s=n(e.connectTimeout,"Connection timeout");r.onreadystatechange=()=>{r.readyState>r.OPENED&&void 0===i&&(clearTimeout(s),i=n(e.responseTimeout,"Socket timeout"))},r.onerror=()=>{0===r.status&&(clearTimeout(s),clearTimeout(i),t({content:r.responseText||"Network request failed",status:r.status,isTimedOut:!1}))},r.onload=()=>{clearTimeout(s),clearTimeout(i),t({content:r.responseText,status:r.status,isTimedOut:!1})},r.send(e.data)}))}},algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:u(),requestsCache:u({serializable:!1}),hostsCache:o({caches:[c({key:`${b}-${e}`}),u()]}),...r})}var x=r(8193),E=r(5260),w=r(8774),O=r(4070),A=r(4586);const S=["zero","one","two","few","many","other"];function H(e){return S.filter((t=>e.includes(t)))}const Q={locale:"en",pluralForms:H(["one","other"]),select:e=>1===e?"one":"other"};function T(){const{i18n:{currentLocale:e}}=(0,A.A)();return(0,i.useMemo)((()=>{try{return function(e){const t=new Intl.PluralRules(e);return{locale:e,pluralForms:H(t.resolvedOptions().pluralCategories),select:e=>t.select(e)}}(e)}catch(t){return console.error(`Failed to use Intl.PluralRules for locale "${e}".\nDocusaurus will fallback to the default (English) implementation.\nError: ${t.message}\n`),Q}}),[e])}function N(){const e=T();return{selectMessage:(t,r)=>function(e,t,r){const i=e.split("|");if(1===i.length)return i[0];i.length>r.pluralForms.length&&console.error(`For locale=${r.locale}, a maximum of ${r.pluralForms.length} plural forms are expected (${r.pluralForms.join(",")}), but the message contains ${i.length}: ${e}`);const n=r.select(t),s=r.pluralForms.indexOf(n);return i[Math.min(s,i.length-1)]}(r,t,e)}}var C=r(4255),I=r(9532),D=r(5500),k=r(1312),q=r(8126),V=r(1062),L=r(1330),$=r(1107);const B={searchQueryInput:"searchQueryInput_u2C7",searchVersionInput:"searchVersionInput_m0Ui",searchResultsColumn:"searchResultsColumn_JPFH",algoliaLogo:"algoliaLogo_rT1R",algoliaLogoPathFill:"algoliaLogoPathFill_WdUC",searchResultItem:"searchResultItem_Tv2o",searchResultItemHeading:"searchResultItemHeading_KbCB",searchResultItemPath:"searchResultItemPath_lhe1",searchResultItemSummary:"searchResultItemSummary_AEaO",searchQueryColumn:"searchQueryColumn_RTkw",searchVersionColumn:"searchVersionColumn_ypXd",searchLogoColumn:"searchLogoColumn_rJIA",loadingSpinner:"loadingSpinner_XVxU","loading-spin":"loading-spin_vzvp",loader:"loader_vvXV"};var M=r(4848);function z({docsSearchVersionsHelpers:e}){const t=Object.entries(e.allDocsData).filter((([,e])=>e.versions.length>1));return(0,M.jsx)("div",{className:(0,n.A)("col","col--3","padding-left--none",B.searchVersionColumn),children:t.map((([r,i])=>{const n=t.length>1?`${r}: `:"";return(0,M.jsx)("select",{onChange:t=>e.setSearchVersion(r,t.target.value),defaultValue:e.searchVersions[r],className:B.searchVersionInput,children:i.versions.map(((e,t)=>(0,M.jsx)("option",{label:`${n}${e.label}`,value:e.name},t)))},r)}))})}function J(){const{i18n:{currentLocale:e}}=(0,A.A)(),{algolia:{appId:t,apiKey:r,indexName:s,contextualSearch:c}}=(0,q.c)(),o=(0,V.C)(),u=function(){const{selectMessage:e}=N();return t=>e(t,(0,k.T)({id:"theme.SearchPage.documentsFound.plurals",description:'Pluralized label for "{count} documents found". Use as much plural forms (separated by "|") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)',message:"One document found|{count} documents found"},{count:t}))}(),h=function(){const e=(0,O.Gy)(),[t,r]=(0,i.useState)((()=>Object.entries(e).reduce(((e,[t,r])=>({...e,[t]:r.versions[0].name})),{}))),n=Object.values(e).some((e=>e.versions.length>1));return{allDocsData:e,versioningEnabled:n,searchVersions:t,setSearchVersion:(e,t)=>r((r=>({...r,[e]:t})))}}(),[l,f]=(0,C.b)(),m=function(e){return e?(0,k.T)({id:"theme.SearchPage.existingResultsTitle",message:'Search results for "{query}"',description:"The search page title for non-empty query"},{query:e}):(0,k.T)({id:"theme.SearchPage.emptyResultsTitle",message:"Search the documentation",description:"The search page title for empty query"})}(l),d={items:[],query:null,totalResults:null,totalPages:null,lastPage:null,hasMore:null,loading:null},[p,g]=(0,i.useReducer)(((e,t)=>{switch(t.type){case"reset":return d;case"loading":return{...e,loading:!0};case"update":return l!==t.value.query?e:{...t.value,items:0===t.value.lastPage?t.value.items:e.items.concat(t.value.items)};case"advance":{const t=e.totalPages>e.lastPage+1;return{...e,lastPage:t?e.lastPage+1:e.lastPage,hasMore:t}}default:return e}}),d),v=c?["language","docusaurus_tag"]:[],y=j(t,r),R=a()(y,s,{hitsPerPage:15,advancedSyntax:!0,disjunctiveFacets:v});R.on("result",(({results:{query:e,hits:t,page:r,nbHits:i,nbPages:n}})=>{if(""===e||!Array.isArray(t))return void g({type:"reset"});const s=e=>e.replace(/algolia-docsearch-suggestion--highlight/g,"search-result-match"),a=t.map((({url:e,_highlightResult:{hierarchy:t},_snippetResult:r={}})=>{const i=Object.keys(t).map((e=>s(t[e].value)));return{title:i.pop(),url:o(e),summary:r.content?`${s(r.content.value)}...`:"",breadcrumbs:i}}));g({type:"update",value:{items:a,query:e,totalResults:i,totalPages:n,lastPage:r,hasMore:n>r+1,loading:!1}})}));const[F,_]=(0,i.useState)(null),b=(0,i.useRef)(0),P=(0,i.useRef)(x.A.canUseIntersectionObserver&&new IntersectionObserver((e=>{const{isIntersecting:t,boundingClientRect:{y:r}}=e[0];t&&b.current>r&&g({type:"advance"}),b.current=r}),{threshold:1})),S=(0,I._q)(((t=0)=>{c&&(R.addDisjunctiveFacetRefinement("docusaurus_tag","default"),R.addDisjunctiveFacetRefinement("language",e),Object.entries(h.searchVersions).forEach((([e,t])=>{R.addDisjunctiveFacetRefinement("docusaurus_tag",`docs-${e}-${t}`)}))),R.setQuery(l).setPage(t).search()}));return(0,i.useEffect)((()=>{if(!F)return;const e=P.current;return e?(e.observe(F),()=>e.unobserve(F)):()=>!0}),[F]),(0,i.useEffect)((()=>{g({type:"reset"}),l&&(g({type:"loading"}),setTimeout((()=>{S()}),300))}),[l,h.searchVersions,S]),(0,i.useEffect)((()=>{p.lastPage&&0!==p.lastPage&&S(p.lastPage)}),[S,p.lastPage]),(0,M.jsxs)(L.A,{children:[(0,M.jsx)(D.be,{title:m}),(0,M.jsx)(E.A,{children:(0,M.jsx)("meta",{property:"robots",content:"noindex, follow"})}),(0,M.jsxs)("div",{className:"container margin-vert--lg",children:[(0,M.jsx)($.A,{as:"h1",children:m}),(0,M.jsxs)("form",{className:"row",onSubmit:e=>e.preventDefault(),children:[(0,M.jsx)("div",{className:(0,n.A)("col",B.searchQueryColumn,{"col--9":h.versioningEnabled,"col--12":!h.versioningEnabled}),children:(0,M.jsx)("input",{type:"search",name:"q",className:B.searchQueryInput,placeholder:(0,k.T)({id:"theme.SearchPage.inputPlaceholder",message:"Type your search here",description:"The placeholder for search page input"}),"aria-label":(0,k.T)({id:"theme.SearchPage.inputLabel",message:"Search",description:"The ARIA label for search page input"}),onChange:e=>f(e.target.value),value:l,autoComplete:"off",autoFocus:!0})}),c&&h.versioningEnabled&&(0,M.jsx)(z,{docsSearchVersionsHelpers:h})]}),(0,M.jsxs)("div",{className:"row",children:[(0,M.jsx)("div",{className:(0,n.A)("col","col--8",B.searchResultsColumn),children:!!p.totalResults&&u(p.totalResults)}),(0,M.jsx)("div",{className:(0,n.A)("col","col--4","text--right",B.searchLogoColumn),children:(0,M.jsx)(w.A,{to:"https://www.algolia.com/","aria-label":(0,k.T)({id:"theme.SearchPage.algoliaLabel",message:"Search by Algolia",description:"The ARIA label for Algolia mention"}),children:(0,M.jsx)("svg",{viewBox:"0 0 168 24",className:B.algoliaLogo,children:(0,M.jsxs)("g",{fill:"none",children:[(0,M.jsx)("path",{className:B.algoliaLogoPathFill,d:"M120.925 18.804c-4.386.02-4.386-3.54-4.386-4.106l-.007-13.336 2.675-.424v13.254c0 .322 0 2.358 1.718 2.364v2.248zm-10.846-2.18c.821 0 1.43-.047 1.855-.129v-2.719a6.334 6.334 0 0 0-1.574-.199 5.7 5.7 0 0 0-.897.069 2.699 2.699 0 0 0-.814.24c-.24.116-.439.28-.582.491-.15.212-.219.335-.219.656 0 .628.219.991.616 1.23s.938.362 1.615.362zm-.233-9.7c.883 0 1.629.109 2.231.328.602.218 1.088.525 1.444.915.363.396.609.922.76 1.483.157.56.232 1.175.232 1.85v6.874a32.5 32.5 0 0 1-1.868.314c-.834.123-1.772.185-2.813.185-.69 0-1.327-.069-1.895-.198a4.001 4.001 0 0 1-1.471-.636 3.085 3.085 0 0 1-.951-1.134c-.226-.465-.343-1.12-.343-1.803 0-.656.13-1.073.384-1.525a3.24 3.24 0 0 1 1.047-1.106c.445-.287.95-.492 1.532-.615a8.8 8.8 0 0 1 1.82-.185 8.404 8.404 0 0 1 1.972.24v-.438c0-.307-.035-.6-.11-.874a1.88 1.88 0 0 0-.384-.73 1.784 1.784 0 0 0-.724-.493 3.164 3.164 0 0 0-1.143-.205c-.616 0-1.177.075-1.69.164a7.735 7.735 0 0 0-1.26.307l-.321-2.192c.335-.117.834-.233 1.478-.349a10.98 10.98 0 0 1 2.073-.178zm52.842 9.626c.822 0 1.43-.048 1.854-.13V13.7a6.347 6.347 0 0 0-1.574-.199c-.294 0-.595.021-.896.069a2.7 2.7 0 0 0-.814.24 1.46 1.46 0 0 0-.582.491c-.15.212-.218.335-.218.656 0 .628.218.991.615 1.23.404.245.938.362 1.615.362zm-.226-9.694c.883 0 1.629.108 2.231.327.602.219 1.088.526 1.444.915.355.39.609.923.759 1.483a6.8 6.8 0 0 1 .233 1.852v6.873c-.41.088-1.034.19-1.868.314-.834.123-1.772.184-2.813.184-.69 0-1.327-.068-1.895-.198a4.001 4.001 0 0 1-1.471-.635 3.085 3.085 0 0 1-.951-1.134c-.226-.465-.343-1.12-.343-1.804 0-.656.13-1.073.384-1.524.26-.45.608-.82 1.047-1.107.445-.286.95-.491 1.532-.614a8.803 8.803 0 0 1 2.751-.13c.329.034.671.096 1.04.185v-.437a3.3 3.3 0 0 0-.109-.875 1.873 1.873 0 0 0-.384-.731 1.784 1.784 0 0 0-.724-.492 3.165 3.165 0 0 0-1.143-.205c-.616 0-1.177.075-1.69.164a7.75 7.75 0 0 0-1.26.307l-.321-2.193c.335-.116.834-.232 1.478-.348a11.633 11.633 0 0 1 2.073-.177zm-8.034-1.271a1.626 1.626 0 0 1-1.628-1.62c0-.895.725-1.62 1.628-1.62.904 0 1.63.725 1.63 1.62 0 .895-.733 1.62-1.63 1.62zm1.348 13.22h-2.689V7.27l2.69-.423v11.956zm-4.714 0c-4.386.02-4.386-3.54-4.386-4.107l-.008-13.336 2.676-.424v13.254c0 .322 0 2.358 1.718 2.364v2.248zm-8.698-5.903c0-1.156-.253-2.119-.746-2.788-.493-.677-1.183-1.01-2.067-1.01-.882 0-1.574.333-2.065 1.01-.493.676-.733 1.632-.733 2.788 0 1.168.246 1.953.74 2.63.492.683 1.183 1.018 2.066 1.018.882 0 1.574-.342 2.067-1.019.492-.683.738-1.46.738-2.63zm2.737-.007c0 .902-.13 1.584-.397 2.33a5.52 5.52 0 0 1-1.128 1.906 4.986 4.986 0 0 1-1.752 1.223c-.685.286-1.739.45-2.265.45-.528-.006-1.574-.157-2.252-.45a5.096 5.096 0 0 1-1.744-1.223c-.487-.527-.863-1.162-1.137-1.906a6.345 6.345 0 0 1-.41-2.33c0-.902.123-1.77.397-2.508a5.554 5.554 0 0 1 1.15-1.892 5.133 5.133 0 0 1 1.75-1.216c.679-.287 1.425-.423 2.232-.423.808 0 1.553.142 2.237.423a4.88 4.88 0 0 1 1.753 1.216 5.644 5.644 0 0 1 1.135 1.892c.287.738.431 1.606.431 2.508zm-20.138 0c0 1.12.246 2.363.738 2.882.493.52 1.13.78 1.91.78.424 0 .828-.062 1.204-.178.377-.116.677-.253.917-.417V9.33a10.476 10.476 0 0 0-1.766-.226c-.971-.028-1.71.37-2.23 1.004-.513.636-.773 1.75-.773 2.788zm7.438 5.274c0 1.824-.466 3.156-1.404 4.004-.936.846-2.367 1.27-4.296 1.27-.705 0-2.17-.137-3.34-.396l.431-2.118c.98.205 2.272.26 2.95.26 1.074 0 1.84-.219 2.299-.656.459-.437.684-1.086.684-1.948v-.437a8.07 8.07 0 0 1-1.047.397c-.43.13-.93.198-1.492.198-.739 0-1.41-.116-2.018-.349a4.206 4.206 0 0 1-1.567-1.025c-.431-.45-.774-1.017-1.013-1.694-.24-.677-.363-1.885-.363-2.773 0-.834.13-1.88.384-2.577.26-.696.629-1.298 1.129-1.796.493-.498 1.095-.881 1.8-1.162a6.605 6.605 0 0 1 2.428-.457c.87 0 1.67.109 2.45.24.78.129 1.444.265 1.985.415V18.17zM6.972 6.677v1.627c-.712-.446-1.52-.67-2.425-.67-.585 0-1.045.13-1.38.391a1.24 1.24 0 0 0-.502 1.03c0 .425.164.765.494 1.02.33.256.835.532 1.516.83.447.192.795.356 1.045.495.25.138.537.332.862.582.324.25.563.548.718.894.154.345.23.741.23 1.188 0 .947-.334 1.691-1.004 2.234-.67.542-1.537.814-2.601.814-1.18 0-2.16-.229-2.936-.686v-1.708c.84.628 1.814.942 2.92.942.585 0 1.048-.136 1.388-.407.34-.271.51-.646.51-1.125 0-.287-.1-.55-.302-.79-.203-.24-.42-.42-.655-.542-.234-.123-.585-.29-1.053-.503a61.27 61.27 0 0 1-.582-.271 13.67 13.67 0 0 1-.55-.287 4.275 4.275 0 0 1-.567-.351 6.92 6.92 0 0 1-.455-.4c-.18-.17-.31-.34-.39-.51-.08-.17-.155-.37-.224-.598a2.553 2.553 0 0 1-.104-.742c0-.915.333-1.638.998-2.17.664-.532 1.523-.798 2.576-.798.968 0 1.793.17 2.473.51zm7.468 5.696v-.287c-.022-.607-.187-1.088-.495-1.444-.309-.357-.75-.535-1.324-.535-.532 0-.99.194-1.373.583-.382.388-.622.949-.717 1.683h3.909zm1.005 2.792v1.404c-.596.34-1.383.51-2.362.51-1.255 0-2.255-.377-3-1.132-.744-.755-1.116-1.744-1.116-2.968 0-1.297.34-2.316 1.021-3.055.68-.74 1.548-1.11 2.6-1.11 1.033 0 1.852.323 2.458.966.606.644.91 1.572.91 2.784 0 .33-.033.676-.096 1.038h-5.314c.107.702.405 1.239.894 1.611.49.372 1.106.558 1.85.558.862 0 1.58-.202 2.155-.606zm6.605-1.77h-1.212c-.596 0-1.045.116-1.349.35-.303.234-.454.532-.454.894 0 .372.117.664.35.877.235.213.575.32 1.022.32.51 0 .912-.142 1.204-.424.293-.281.44-.651.44-1.108v-.91zm-4.068-2.554V9.325c.627-.361 1.457-.542 2.489-.542 2.116 0 3.175 1.026 3.175 3.08V17h-1.548v-.957c-.415.68-1.143 1.02-2.186 1.02-.766 0-1.38-.22-1.843-.661-.462-.442-.694-1.003-.694-1.684 0-.776.293-1.38.878-1.81.585-.431 1.404-.647 2.457-.647h1.34V11.8c0-.554-.133-.971-.399-1.253-.266-.282-.707-.423-1.324-.423a4.07 4.07 0 0 0-2.345.718zm9.333-1.93v1.42c.394-1 1.101-1.5 2.123-1.5.148 0 .313.016.494.048v1.531a1.885 1.885 0 0 0-.75-.143c-.542 0-.989.24-1.34.718-.351.479-.527 1.048-.527 1.707V17h-1.563V8.91h1.563zm5.01 4.084c.022.82.272 1.492.75 2.019.479.526 1.15.79 2.01.79.639 0 1.235-.176 1.788-.527v1.404c-.521.319-1.186.479-1.995.479-1.265 0-2.276-.4-3.031-1.197-.755-.798-1.133-1.792-1.133-2.984 0-1.16.38-2.151 1.14-2.975.761-.825 1.79-1.237 3.088-1.237.702 0 1.346.149 1.93.447v1.436a3.242 3.242 0 0 0-1.77-.495c-.84 0-1.513.266-2.019.798-.505.532-.758 1.213-.758 2.042zM40.24 5.72v4.579c.458-1 1.293-1.5 2.505-1.5.787 0 1.42.245 1.899.734.479.49.718 1.17.718 2.042V17h-1.564v-5.106c0-.553-.14-.98-.422-1.284-.282-.303-.652-.455-1.11-.455-.531 0-1.002.202-1.411.606-.41.405-.615 1.022-.615 1.851V17h-1.563V5.72h1.563zm14.966 10.02c.596 0 1.096-.253 1.5-.758.404-.506.606-1.157.606-1.955 0-.915-.202-1.62-.606-2.114-.404-.495-.92-.742-1.548-.742-.553 0-1.05.224-1.491.67-.442.447-.662 1.133-.662 2.058 0 .958.212 1.67.638 2.138.425.469.946.703 1.563.703zM53.004 5.72v4.42c.574-.894 1.388-1.341 2.44-1.341 1.022 0 1.857.383 2.506 1.149.649.766.973 1.781.973 3.047 0 1.138-.309 2.109-.925 2.912-.617.803-1.463 1.205-2.537 1.205-1.075 0-1.894-.447-2.457-1.34V17h-1.58V5.72h1.58zm9.908 11.104l-3.223-7.913h1.739l1.005 2.632 1.26 3.415c.096-.32.48-1.458 1.15-3.415l.909-2.632h1.66l-2.92 7.866c-.777 2.074-1.963 3.11-3.559 3.11a2.92 2.92 0 0 1-.734-.079v-1.34c.17.042.351.064.543.064 1.032 0 1.755-.57 2.17-1.708z"}),(0,M.jsx)("path",{fill:"#5468FF",d:"M78.988.938h16.594a2.968 2.968 0 0 1 2.966 2.966V20.5a2.967 2.967 0 0 1-2.966 2.964H78.988a2.967 2.967 0 0 1-2.966-2.964V3.897A2.961 2.961 0 0 1 78.988.938z"}),(0,M.jsx)("path",{fill:"white",d:"M89.632 5.967v-.772a.978.978 0 0 0-.978-.977h-2.28a.978.978 0 0 0-.978.977v.793c0 .088.082.15.171.13a7.127 7.127 0 0 1 1.984-.28c.65 0 1.295.088 1.917.259.082.02.164-.04.164-.13m-6.248 1.01l-.39-.389a.977.977 0 0 0-1.382 0l-.465.465a.973.973 0 0 0 0 1.38l.383.383c.062.061.15.047.205-.014.226-.307.472-.601.746-.874.281-.28.568-.526.883-.751.068-.042.075-.137.02-.2m4.16 2.453v3.341c0 .096.104.165.192.117l2.97-1.537c.068-.034.089-.117.055-.184a3.695 3.695 0 0 0-3.08-1.866c-.068 0-.136.054-.136.13m0 8.048a4.489 4.489 0 0 1-4.49-4.482 4.488 4.488 0 0 1 4.49-4.482 4.488 4.488 0 0 1 4.489 4.482 4.484 4.484 0 0 1-4.49 4.482m0-10.85a6.363 6.363 0 1 0 0 12.729 6.37 6.37 0 0 0 6.372-6.368 6.358 6.358 0 0 0-6.371-6.36"})]})})})})]}),p.items.length>0?(0,M.jsx)("main",{children:p.items.map((({title:e,url:t,summary:r,breadcrumbs:i},s)=>(0,M.jsxs)("article",{className:B.searchResultItem,children:[(0,M.jsx)($.A,{as:"h2",className:B.searchResultItemHeading,children:(0,M.jsx)(w.A,{to:t,dangerouslySetInnerHTML:{__html:e}})}),i.length>0&&(0,M.jsx)("nav",{"aria-label":"breadcrumbs",children:(0,M.jsx)("ul",{className:(0,n.A)("breadcrumbs",B.searchResultItemPath),children:i.map(((e,t)=>(0,M.jsx)("li",{className:"breadcrumbs__item",dangerouslySetInnerHTML:{__html:e}},t)))})}),r&&(0,M.jsx)("p",{className:B.searchResultItemSummary,dangerouslySetInnerHTML:{__html:r}})]},s)))}):[l&&!p.loading&&(0,M.jsx)("p",{children:(0,M.jsx)(k.A,{id:"theme.SearchPage.noResultsText",description:"The paragraph for empty search result",children:"No results were found"})},"no-results"),!!p.loading&&(0,M.jsx)("div",{className:B.loadingSpinner},"spinner")],p.hasMore&&(0,M.jsx)("div",{className:B.loader,ref:_,children:(0,M.jsx)(k.A,{id:"theme.SearchPage.fetchingNewResults",description:"The paragraph for fetching new search results",children:"Fetching new results..."})})]})]})}function W(){return(0,M.jsx)(D.e3,{className:"search-page-wrapper",children:(0,M.jsx)(J,{})})}},1383:e=>{"use strict";e.exports=function(e,t){if(null===e)return{};var r,i,n={},s=Object.keys(e);for(i=0;i<s.length;i++)r=s[i],t.indexOf(r)>=0||(n[r]=e[r]);return n}},1673:(e,t,r)=>{"use strict";var i=r(9110),n=r(317),s=r(1383),a={addRefinement:function(e,t,r){if(a.isRefined(e,t,r))return e;var n=""+r,s=e[t]?e[t].concat(n):[n],c={};return c[t]=s,i(c,e)},removeRefinement:function(e,t,r){if(void 0===r)return a.clearRefinement(e,(function(e,r){return t===r}));var i=""+r;return a.clearRefinement(e,(function(e,r){return t===r&&i===e}))},toggleRefinement:function(e,t,r){if(void 0===r)throw new Error("toggleRefinement should be used with a value");return a.isRefined(e,t,r)?a.removeRefinement(e,t,r):a.addRefinement(e,t,r)},clearRefinement:function(e,t,r){if(void 0===t)return n(e)?{}:e;if("string"==typeof t)return s(e,[t]);if("function"==typeof t){var i=!1,a=Object.keys(e).reduce((function(n,s){var a=e[s]||[],c=a.filter((function(e){return!t(e,s,r)}));return c.length!==a.length&&(i=!0),n[s]=c,n}),{});return i?a:e}},isRefined:function(e,t,r){var i=Boolean(e[t])&&e[t].length>0;if(void 0===r||!i)return i;var n=""+r;return-1!==e[t].indexOf(n)}};e.exports=a},2183:e=>{"use strict";e.exports=function(){return Array.prototype.slice.call(arguments).reduceRight((function(e,t){return Object.keys(Object(t)).forEach((function(r){var i="number"==typeof e[r]?e[r]:0,n=t[r];void 0!==n&&n>=i&&(void 0!==e[r]&&delete e[r],e[r]=n)})),e}),{})}},2208:e=>{"use strict";e.exports=function(e){return null!==e&&/^[a-zA-Z0-9_-]{1,64}$/.test(e)}},2223:e=>{"use strict";function t(e,t){this._state=e,this._rawResults={};var r=this;e.params.forEach((function(e){var i=e.$$id;r[i]=t[i],r._rawResults[i]=t[i]}))}t.prototype={constructor:t},e.exports=t},2733:e=>{function t(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}function r(e){return"function"==typeof e}function i(e){return"object"==typeof e&&null!==e}function n(e){return void 0===e}e.exports=t,t.prototype._events=void 0,t.prototype._maxListeners=void 0,t.defaultMaxListeners=10,t.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||isNaN(e))throw TypeError("n must be a positive number");return this._maxListeners=e,this},t.prototype.emit=function(e){var t,s,a,c,o,u;if(this._events||(this._events={}),"error"===e&&(!this._events.error||i(this._events.error)&&!this._events.error.length)){if((t=arguments[1])instanceof Error)throw t;var h=new Error('Uncaught, unspecified "error" event. ('+t+")");throw h.context=t,h}if(n(s=this._events[e]))return!1;if(r(s))switch(arguments.length){case 1:s.call(this);break;case 2:s.call(this,arguments[1]);break;case 3:s.call(this,arguments[1],arguments[2]);break;default:c=Array.prototype.slice.call(arguments,1),s.apply(this,c)}else if(i(s))for(c=Array.prototype.slice.call(arguments,1),a=(u=s.slice()).length,o=0;o<a;o++)u[o].apply(this,c);return!0},t.prototype.addListener=function(e,s){var a;if(!r(s))throw TypeError("listener must be a function");return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",e,r(s.listener)?s.listener:s),this._events[e]?i(this._events[e])?this._events[e].push(s):this._events[e]=[this._events[e],s]:this._events[e]=s,i(this._events[e])&&!this._events[e].warned&&(a=n(this._maxListeners)?t.defaultMaxListeners:this._maxListeners)&&a>0&&this._events[e].length>a&&(this._events[e].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[e].length),"function"==typeof console.trace&&console.trace()),this},t.prototype.on=t.prototype.addListener,t.prototype.once=function(e,t){if(!r(t))throw TypeError("listener must be a function");var i=!1;function n(){this.removeListener(e,n),i||(i=!0,t.apply(this,arguments))}return n.listener=t,this.on(e,n),this},t.prototype.removeListener=function(e,t){var n,s,a,c;if(!r(t))throw TypeError("listener must be a function");if(!this._events||!this._events[e])return this;if(a=(n=this._events[e]).length,s=-1,n===t||r(n.listener)&&n.listener===t)delete this._events[e],this._events.removeListener&&this.emit("removeListener",e,t);else if(i(n)){for(c=a;c-- >0;)if(n[c]===t||n[c].listener&&n[c].listener===t){s=c;break}if(s<0)return this;1===n.length?(n.length=0,delete this._events[e]):n.splice(s,1),this._events.removeListener&&this.emit("removeListener",e,t)}return this},t.prototype.removeAllListeners=function(e){var t,i;if(!this._events)return this;if(!this._events.removeListener)return 0===arguments.length?this._events={}:this._events[e]&&delete this._events[e],this;if(0===arguments.length){for(t in this._events)"removeListener"!==t&&this.removeAllListeners(t);return this.removeAllListeners("removeListener"),this._events={},this}if(r(i=this._events[e]))this.removeListener(e,i);else if(i)for(;i.length;)this.removeListener(e,i[i.length-1]);return delete this._events[e],this},t.prototype.listeners=function(e){return this._events&&this._events[e]?r(this._events[e])?[this._events[e]]:this._events[e].slice():[]},t.prototype.listenerCount=function(e){if(this._events){var t=this._events[e];if(r(t))return 1;if(t)return t.length}return 0},t.listenerCount=function(e,t){return e.listenerCount(t)}},2909:e=>{"use strict";e.exports={escapeFacetValue:function(e){return"string"!=typeof e?e:String(e).replace(/^-/,"\\-")},unescapeFacetValue:function(e){return"string"!=typeof e?e:e.replace(/^\\-/,"-")}}},3014:e=>{"use strict";e.exports=function(e,t){e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}},3371:(e,t,r)=>{"use strict";var i=r(9110),n=r(849),s=r(4843),a=r(4728),c=r(317),o=r(1383),u=r(7507),h=r(2208),l=r(1673);function f(e,t){return Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&e.every((function(e,r){return f(t[r],e)})):e===t}function m(e){var t=e?m._parseNumbers(e):{};void 0===t.userToken||h(t.userToken)||console.warn("[algoliasearch-helper] The `userToken` parameter is invalid. This can lead to wrong analytics.\n  - Format: [a-zA-Z0-9_-]{1,64}"),this.facets=t.facets||[],this.disjunctiveFacets=t.disjunctiveFacets||[],this.hierarchicalFacets=t.hierarchicalFacets||[],this.facetsRefinements=t.facetsRefinements||{},this.facetsExcludes=t.facetsExcludes||{},this.disjunctiveFacetsRefinements=t.disjunctiveFacetsRefinements||{},this.numericRefinements=t.numericRefinements||{},this.tagRefinements=t.tagRefinements||[],this.hierarchicalFacetsRefinements=t.hierarchicalFacetsRefinements||{};var r=this;Object.keys(t).forEach((function(e){var i=-1!==m.PARAMETERS.indexOf(e),n=void 0!==t[e];!i&&n&&(r[e]=t[e])}))}m.PARAMETERS=Object.keys(new m),m._parseNumbers=function(e){if(e instanceof m)return e;var t={};if(["aroundPrecision","aroundRadius","getRankingInfo","minWordSizefor2Typos","minWordSizefor1Typo","page","maxValuesPerFacet","distinct","minimumAroundRadius","hitsPerPage","minProximity"].forEach((function(r){var i=e[r];if("string"==typeof i){var n=parseFloat(i);t[r]=isNaN(n)?i:n}})),Array.isArray(e.insideBoundingBox)&&(t.insideBoundingBox=e.insideBoundingBox.map((function(e){return Array.isArray(e)?e.map((function(e){return parseFloat(e)})):e}))),e.numericRefinements){var r={};Object.keys(e.numericRefinements).forEach((function(t){var i=e.numericRefinements[t]||{};r[t]={},Object.keys(i).forEach((function(e){var n=i[e].map((function(e){return Array.isArray(e)?e.map((function(e){return"string"==typeof e?parseFloat(e):e})):"string"==typeof e?parseFloat(e):e}));r[t][e]=n}))})),t.numericRefinements=r}return a(e,t)},m.make=function(e){var t=new m(e);return(e.hierarchicalFacets||[]).forEach((function(e){if(e.rootPath){var r=t.getHierarchicalRefinement(e.name);r.length>0&&0!==r[0].indexOf(e.rootPath)&&(t=t.clearRefinements(e.name)),0===(r=t.getHierarchicalRefinement(e.name)).length&&(t=t.toggleHierarchicalFacetRefinement(e.name,e.rootPath))}})),t},m.validate=function(e,t){var r=t||{};return e.tagFilters&&r.tagRefinements&&r.tagRefinements.length>0?new Error("[Tags] Cannot switch from the managed tag API to the advanced API. It is probably an error, if it is really what you want, you should first clear the tags with clearTags method."):e.tagRefinements.length>0&&r.tagFilters?new Error("[Tags] Cannot switch from the advanced tag API to the managed API. It is probably an error, if it is not, you should first clear the tags with clearTags method."):e.numericFilters&&r.numericRefinements&&c(r.numericRefinements)?new Error("[Numeric filters] Can't switch from the advanced to the managed API. It is probably an error, if this is really what you want, you have to first clear the numeric filters."):c(e.numericRefinements)&&r.numericFilters?new Error("[Numeric filters] Can't switch from the managed API to the advanced. It is probably an error, if this is really what you want, you have to first clear the numeric filters."):null},m.prototype={constructor:m,clearRefinements:function(e){var t={numericRefinements:this._clearNumericRefinements(e),facetsRefinements:l.clearRefinement(this.facetsRefinements,e,"conjunctiveFacet"),facetsExcludes:l.clearRefinement(this.facetsExcludes,e,"exclude"),disjunctiveFacetsRefinements:l.clearRefinement(this.disjunctiveFacetsRefinements,e,"disjunctiveFacet"),hierarchicalFacetsRefinements:l.clearRefinement(this.hierarchicalFacetsRefinements,e,"hierarchicalFacet")};return t.numericRefinements===this.numericRefinements&&t.facetsRefinements===this.facetsRefinements&&t.facetsExcludes===this.facetsExcludes&&t.disjunctiveFacetsRefinements===this.disjunctiveFacetsRefinements&&t.hierarchicalFacetsRefinements===this.hierarchicalFacetsRefinements?this:this.setQueryParameters(t)},clearTags:function(){return void 0===this.tagFilters&&0===this.tagRefinements.length?this:this.setQueryParameters({tagFilters:void 0,tagRefinements:[]})},setIndex:function(e){return e===this.index?this:this.setQueryParameters({index:e})},setQuery:function(e){return e===this.query?this:this.setQueryParameters({query:e})},setPage:function(e){return e===this.page?this:this.setQueryParameters({page:e})},setFacets:function(e){return this.setQueryParameters({facets:e})},setDisjunctiveFacets:function(e){return this.setQueryParameters({disjunctiveFacets:e})},setHitsPerPage:function(e){return this.hitsPerPage===e?this:this.setQueryParameters({hitsPerPage:e})},setTypoTolerance:function(e){return this.typoTolerance===e?this:this.setQueryParameters({typoTolerance:e})},addNumericRefinement:function(e,t,r){var i=u(r);if(this.isNumericRefined(e,t,i))return this;var n=a({},this.numericRefinements);return n[e]=a({},n[e]),n[e][t]?(n[e][t]=n[e][t].slice(),n[e][t].push(i)):n[e][t]=[i],this.setQueryParameters({numericRefinements:n})},getConjunctiveRefinements:function(e){return this.isConjunctiveFacet(e)&&this.facetsRefinements[e]||[]},getDisjunctiveRefinements:function(e){return this.isDisjunctiveFacet(e)&&this.disjunctiveFacetsRefinements[e]||[]},getHierarchicalRefinement:function(e){return this.hierarchicalFacetsRefinements[e]||[]},getExcludeRefinements:function(e){return this.isConjunctiveFacet(e)&&this.facetsExcludes[e]||[]},removeNumericRefinement:function(e,t,r){var i=r;return void 0!==i?this.isNumericRefined(e,t,i)?this.setQueryParameters({numericRefinements:this._clearNumericRefinements((function(r,n){return n===e&&r.op===t&&f(r.val,u(i))}))}):this:void 0!==t?this.isNumericRefined(e,t)?this.setQueryParameters({numericRefinements:this._clearNumericRefinements((function(r,i){return i===e&&r.op===t}))}):this:this.isNumericRefined(e)?this.setQueryParameters({numericRefinements:this._clearNumericRefinements((function(t,r){return r===e}))}):this},getNumericRefinements:function(e){return this.numericRefinements[e]||{}},getNumericRefinement:function(e,t){return this.numericRefinements[e]&&this.numericRefinements[e][t]},_clearNumericRefinements:function(e){if(void 0===e)return c(this.numericRefinements)?{}:this.numericRefinements;if("string"==typeof e)return o(this.numericRefinements,[e]);if("function"==typeof e){var t=!1,r=this.numericRefinements,i=Object.keys(r).reduce((function(i,n){var s=r[n],a={};return s=s||{},Object.keys(s).forEach((function(r){var i=s[r]||[],c=[];i.forEach((function(t){e({val:t,op:r},n,"numeric")||c.push(t)})),c.length!==i.length&&(t=!0),a[r]=c})),i[n]=a,i}),{});return t?i:this.numericRefinements}},addFacet:function(e){return this.isConjunctiveFacet(e)?this:this.setQueryParameters({facets:this.facets.concat([e])})},addDisjunctiveFacet:function(e){return this.isDisjunctiveFacet(e)?this:this.setQueryParameters({disjunctiveFacets:this.disjunctiveFacets.concat([e])})},addHierarchicalFacet:function(e){if(this.isHierarchicalFacet(e.name))throw new Error("Cannot declare two hierarchical facets with the same name: `"+e.name+"`");return this.setQueryParameters({hierarchicalFacets:this.hierarchicalFacets.concat([e])})},addFacetRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw new Error(e+" is not defined in the facets attribute of the helper configuration");return l.isRefined(this.facetsRefinements,e,t)?this:this.setQueryParameters({facetsRefinements:l.addRefinement(this.facetsRefinements,e,t)})},addExcludeRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw new Error(e+" is not defined in the facets attribute of the helper configuration");return l.isRefined(this.facetsExcludes,e,t)?this:this.setQueryParameters({facetsExcludes:l.addRefinement(this.facetsExcludes,e,t)})},addDisjunctiveFacetRefinement:function(e,t){if(!this.isDisjunctiveFacet(e))throw new Error(e+" is not defined in the disjunctiveFacets attribute of the helper configuration");return l.isRefined(this.disjunctiveFacetsRefinements,e,t)?this:this.setQueryParameters({disjunctiveFacetsRefinements:l.addRefinement(this.disjunctiveFacetsRefinements,e,t)})},addTagRefinement:function(e){if(this.isTagRefined(e))return this;var t={tagRefinements:this.tagRefinements.concat(e)};return this.setQueryParameters(t)},removeFacet:function(e){return this.isConjunctiveFacet(e)?this.clearRefinements(e).setQueryParameters({facets:this.facets.filter((function(t){return t!==e}))}):this},removeDisjunctiveFacet:function(e){return this.isDisjunctiveFacet(e)?this.clearRefinements(e).setQueryParameters({disjunctiveFacets:this.disjunctiveFacets.filter((function(t){return t!==e}))}):this},removeHierarchicalFacet:function(e){return this.isHierarchicalFacet(e)?this.clearRefinements(e).setQueryParameters({hierarchicalFacets:this.hierarchicalFacets.filter((function(t){return t.name!==e}))}):this},removeFacetRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw new Error(e+" is not defined in the facets attribute of the helper configuration");return l.isRefined(this.facetsRefinements,e,t)?this.setQueryParameters({facetsRefinements:l.removeRefinement(this.facetsRefinements,e,t)}):this},removeExcludeRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw new Error(e+" is not defined in the facets attribute of the helper configuration");return l.isRefined(this.facetsExcludes,e,t)?this.setQueryParameters({facetsExcludes:l.removeRefinement(this.facetsExcludes,e,t)}):this},removeDisjunctiveFacetRefinement:function(e,t){if(!this.isDisjunctiveFacet(e))throw new Error(e+" is not defined in the disjunctiveFacets attribute of the helper configuration");return l.isRefined(this.disjunctiveFacetsRefinements,e,t)?this.setQueryParameters({disjunctiveFacetsRefinements:l.removeRefinement(this.disjunctiveFacetsRefinements,e,t)}):this},removeTagRefinement:function(e){if(!this.isTagRefined(e))return this;var t={tagRefinements:this.tagRefinements.filter((function(t){return t!==e}))};return this.setQueryParameters(t)},toggleRefinement:function(e,t){return this.toggleFacetRefinement(e,t)},toggleFacetRefinement:function(e,t){if(this.isHierarchicalFacet(e))return this.toggleHierarchicalFacetRefinement(e,t);if(this.isConjunctiveFacet(e))return this.toggleConjunctiveFacetRefinement(e,t);if(this.isDisjunctiveFacet(e))return this.toggleDisjunctiveFacetRefinement(e,t);throw new Error("Cannot refine the undeclared facet "+e+"; it should be added to the helper options facets, disjunctiveFacets or hierarchicalFacets")},toggleConjunctiveFacetRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw new Error(e+" is not defined in the facets attribute of the helper configuration");return this.setQueryParameters({facetsRefinements:l.toggleRefinement(this.facetsRefinements,e,t)})},toggleExcludeFacetRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw new Error(e+" is not defined in the facets attribute of the helper configuration");return this.setQueryParameters({facetsExcludes:l.toggleRefinement(this.facetsExcludes,e,t)})},toggleDisjunctiveFacetRefinement:function(e,t){if(!this.isDisjunctiveFacet(e))throw new Error(e+" is not defined in the disjunctiveFacets attribute of the helper configuration");return this.setQueryParameters({disjunctiveFacetsRefinements:l.toggleRefinement(this.disjunctiveFacetsRefinements,e,t)})},toggleHierarchicalFacetRefinement:function(e,t){if(!this.isHierarchicalFacet(e))throw new Error(e+" is not defined in the hierarchicalFacets attribute of the helper configuration");var r=this._getHierarchicalFacetSeparator(this.getHierarchicalFacetByName(e)),n={};return void 0!==this.hierarchicalFacetsRefinements[e]&&this.hierarchicalFacetsRefinements[e].length>0&&(this.hierarchicalFacetsRefinements[e][0]===t||0===this.hierarchicalFacetsRefinements[e][0].indexOf(t+r))?-1===t.indexOf(r)?n[e]=[]:n[e]=[t.slice(0,t.lastIndexOf(r))]:n[e]=[t],this.setQueryParameters({hierarchicalFacetsRefinements:i(n,this.hierarchicalFacetsRefinements)})},addHierarchicalFacetRefinement:function(e,t){if(this.isHierarchicalFacetRefined(e))throw new Error(e+" is already refined.");if(!this.isHierarchicalFacet(e))throw new Error(e+" is not defined in the hierarchicalFacets attribute of the helper configuration.");var r={};return r[e]=[t],this.setQueryParameters({hierarchicalFacetsRefinements:i(r,this.hierarchicalFacetsRefinements)})},removeHierarchicalFacetRefinement:function(e){if(!this.isHierarchicalFacetRefined(e))return this;var t={};return t[e]=[],this.setQueryParameters({hierarchicalFacetsRefinements:i(t,this.hierarchicalFacetsRefinements)})},toggleTagRefinement:function(e){return this.isTagRefined(e)?this.removeTagRefinement(e):this.addTagRefinement(e)},isDisjunctiveFacet:function(e){return this.disjunctiveFacets.indexOf(e)>-1},isHierarchicalFacet:function(e){return void 0!==this.getHierarchicalFacetByName(e)},isConjunctiveFacet:function(e){return this.facets.indexOf(e)>-1},isFacetRefined:function(e,t){return!!this.isConjunctiveFacet(e)&&l.isRefined(this.facetsRefinements,e,t)},isExcludeRefined:function(e,t){return!!this.isConjunctiveFacet(e)&&l.isRefined(this.facetsExcludes,e,t)},isDisjunctiveFacetRefined:function(e,t){return!!this.isDisjunctiveFacet(e)&&l.isRefined(this.disjunctiveFacetsRefinements,e,t)},isHierarchicalFacetRefined:function(e,t){if(!this.isHierarchicalFacet(e))return!1;var r=this.getHierarchicalRefinement(e);return t?-1!==r.indexOf(t):r.length>0},isNumericRefined:function(e,t,r){if(void 0===r&&void 0===t)return Boolean(this.numericRefinements[e]);var i=this.numericRefinements[e]&&void 0!==this.numericRefinements[e][t];if(void 0===r||!i)return i;var s,a,c=u(r),o=void 0!==(s=this.numericRefinements[e][t],a=c,n(s,(function(e){return f(e,a)})));return i&&o},isTagRefined:function(e){return-1!==this.tagRefinements.indexOf(e)},getRefinedDisjunctiveFacets:function(){var e=this,t=s(Object.keys(this.numericRefinements).filter((function(t){return Object.keys(e.numericRefinements[t]).length>0})),this.disjunctiveFacets);return Object.keys(this.disjunctiveFacetsRefinements).filter((function(t){return e.disjunctiveFacetsRefinements[t].length>0})).concat(t).concat(this.getRefinedHierarchicalFacets()).sort()},getRefinedHierarchicalFacets:function(){var e=this;return s(this.hierarchicalFacets.map((function(e){return e.name})),Object.keys(this.hierarchicalFacetsRefinements).filter((function(t){return e.hierarchicalFacetsRefinements[t].length>0}))).sort()},getUnrefinedDisjunctiveFacets:function(){var e=this.getRefinedDisjunctiveFacets();return this.disjunctiveFacets.filter((function(t){return-1===e.indexOf(t)}))},managedParameters:["index","facets","disjunctiveFacets","facetsRefinements","hierarchicalFacets","facetsExcludes","disjunctiveFacetsRefinements","numericRefinements","tagRefinements","hierarchicalFacetsRefinements"],getQueryParams:function(){var e=this.managedParameters,t={},r=this;return Object.keys(this).forEach((function(i){var n=r[i];-1===e.indexOf(i)&&void 0!==n&&(t[i]=n)})),t},setQueryParameter:function(e,t){if(this[e]===t)return this;var r={};return r[e]=t,this.setQueryParameters(r)},setQueryParameters:function(e){if(!e)return this;var t=m.validate(this,e);if(t)throw t;var r=this,i=m._parseNumbers(e),n=Object.keys(this).reduce((function(e,t){return e[t]=r[t],e}),{}),s=Object.keys(i).reduce((function(e,t){var r=void 0!==e[t],n=void 0!==i[t];return r&&!n?o(e,[t]):(n&&(e[t]=i[t]),e)}),n);return new this.constructor(s)},resetPage:function(){return void 0===this.page?this:this.setPage(0)},_getHierarchicalFacetSortBy:function(e){return e.sortBy||["isRefined:desc","name:asc"]},_getHierarchicalFacetSeparator:function(e){return e.separator||" > "},_getHierarchicalRootPath:function(e){return e.rootPath||null},_getHierarchicalShowParentLevel:function(e){return"boolean"!=typeof e.showParentLevel||e.showParentLevel},getHierarchicalFacetByName:function(e){return n(this.hierarchicalFacets,(function(t){return t.name===e}))},getHierarchicalFacetBreadcrumb:function(e){if(!this.isHierarchicalFacet(e))return[];var t=this.getHierarchicalRefinement(e)[0];if(!t)return[];var r=this._getHierarchicalFacetSeparator(this.getHierarchicalFacetByName(e));return t.split(r).map((function(e){return e.trim()}))},toString:function(){return JSON.stringify(this,null,2)}},e.exports=m},3917:e=>{"use strict";e.exports=function(e,t){if(!Array.isArray(e))return-1;for(var r=0;r<e.length;r++)if(t(e[r]))return r;return-1}},4103:(e,t,r)=>{"use strict";var i=r(6571),n=r(9127),s=r(2223),a=r(3371),c=r(7691);function o(e,t,r,n){return new i(e,t,r,n)}o.version=r(6938),o.AlgoliaSearchHelper=i,o.SearchParameters=a,o.RecommendParameters=n,o.SearchResults=c,o.RecommendResults=s,e.exports=o},4728:e=>{"use strict";function t(e){return"function"==typeof e||Array.isArray(e)||"[object Object]"===Object.prototype.toString.call(e)}function r(e,i){if(e===i)return e;for(var n in i)if(Object.prototype.hasOwnProperty.call(i,n)&&"__proto__"!==n&&"constructor"!==n){var s=i[n],a=e[n];void 0!==a&&void 0===s||(t(a)&&t(s)?e[n]=r(a,s):e[n]="object"==typeof(c=s)&&null!==c?r(Array.isArray(c)?[]:{},c):c)}var c;return e}e.exports=function(e){t(e)||(e={});for(var i=1,n=arguments.length;i<n;i++){var s=arguments[i];t(s)&&r(e,s)}return e}},4843:e=>{"use strict";e.exports=function(e,t){return e.filter((function(r,i){return t.indexOf(r)>-1&&e.indexOf(r)===i}))}},6571:(e,t,r)=>{"use strict";var i=r(2733),n=r(6732),s=r(2909).escapeFacetValue,a=r(3014),c=r(4728),o=r(317),u=r(1383),h=r(9127),l=r(2223),f=r(9228),m=r(3371),d=r(7691),p=r(7749),g=r(6938);function v(e,t,r,i){"function"==typeof e.addAlgoliaAgent&&e.addAlgoliaAgent("JS Helper ("+g+")"),this.setClient(e);var n=r||{};n.index=t,this.state=m.make(n),this.recommendState=new h({params:n.recommendState}),this.lastResults=null,this.lastRecommendResults=null,this._queryId=0,this._recommendQueryId=0,this._lastQueryIdReceived=-1,this._lastRecommendQueryIdReceived=-1,this.derivedHelpers=[],this._currentNbQueries=0,this._currentNbRecommendQueries=0,this._searchResultsOptions=i,this._recommendCache={}}function y(e){if(e<0)throw new Error("Page requested below 0.");return this._change({state:this.state.setPage(e),isPageReset:!1}),this}function R(){return this.state.page}a(v,i),v.prototype.search=function(){return this._search({onlyWithDerivedHelpers:!1}),this},v.prototype.searchOnlyWithDerivedHelpers=function(){return this._search({onlyWithDerivedHelpers:!0}),this},v.prototype.searchWithComposition=function(){return this._runComposition({onlyWithDerivedHelpers:!0}),this},v.prototype.recommend=function(){return this._recommend(),this},v.prototype.getQuery=function(){var e=this.state;return f._getHitsSearchParams(e)},v.prototype.searchOnce=function(e,t){var r=e?this.state.setQueryParameters(e):this.state,i=f._getQueries(r.index,r),n=this;if(this._currentNbQueries++,this.emit("searchOnce",{state:r}),!t)return this.client.search(i).then((function(e){return n._currentNbQueries--,0===n._currentNbQueries&&n.emit("searchQueueEmpty"),{content:new d(r,e.results),state:r,_originalResponse:e}}),(function(e){throw n._currentNbQueries--,0===n._currentNbQueries&&n.emit("searchQueueEmpty"),e}));this.client.search(i).then((function(e){n._currentNbQueries--,0===n._currentNbQueries&&n.emit("searchQueueEmpty"),t(null,new d(r,e.results),r)})).catch((function(e){n._currentNbQueries--,0===n._currentNbQueries&&n.emit("searchQueueEmpty"),t(e,null,r)}))},v.prototype.findAnswers=function(e){console.warn("[algoliasearch-helper] answers is no longer supported");var t=this.state,r=this.derivedHelpers[0];if(!r)return Promise.resolve([]);var i=r.getModifiedState(t),n=c({attributesForPrediction:e.attributesForPrediction,nbHits:e.nbHits},{params:u(f._getHitsSearchParams(i),["attributesToSnippet","hitsPerPage","restrictSearchableAttributes","snippetEllipsisText"])}),s="search for answers was called, but this client does not have a function client.initIndex(index).findAnswers";if("function"!=typeof this.client.initIndex)throw new Error(s);var a=this.client.initIndex(i.index);if("function"!=typeof a.findAnswers)throw new Error(s);return a.findAnswers(i.query,e.queryLanguages,n)},v.prototype.searchForFacetValues=function(e,t,r,i){var n="function"==typeof this.client.searchForFacetValues&&"function"!=typeof this.client.searchForFacets,a="function"==typeof this.client.initIndex;if(!n&&!a&&"function"!=typeof this.client.search)throw new Error("search for facet values (searchable) was called, but this client does not have a function client.searchForFacetValues or client.initIndex(index).searchForFacetValues");var c=this.state.setQueryParameters(i||{}),o=c.isDisjunctiveFacet(e),u=f.getSearchForFacetQuery(e,t,r,c);this._currentNbQueries++;var h,l=this;return n?h=this.client.searchForFacetValues([{indexName:c.index,params:u}]):a?h=this.client.initIndex(c.index).searchForFacetValues(u):(delete u.facetName,h=this.client.search([{type:"facet",facet:e,indexName:c.index,params:u}]).then((function(e){return e.results[0]}))),this.emit("searchForFacetValues",{state:c,facet:e,query:t}),h.then((function(t){return l._currentNbQueries--,0===l._currentNbQueries&&l.emit("searchQueueEmpty"),(t=Array.isArray(t)?t[0]:t).facetHits.forEach((function(t){t.escapedValue=s(t.value),t.isRefined=o?c.isDisjunctiveFacetRefined(e,t.escapedValue):c.isFacetRefined(e,t.escapedValue)})),t}),(function(e){throw l._currentNbQueries--,0===l._currentNbQueries&&l.emit("searchQueueEmpty"),e}))},v.prototype.searchForCompositionFacetValues=function(e,t,r,i){if("function"!=typeof this.client.searchForFacetValues)throw new Error("search for facet values (searchable) was called, but this client does not have a function client.searchForFacetValues");var n=this.state.setQueryParameters(i||{}),a=n.isDisjunctiveFacet(e);this._currentNbQueries++;var c,o=this;return c=this.client.searchForFacetValues({compositionID:n.index,facetName:e,searchForFacetValuesRequest:{params:{query:t,maxFacetHits:r,searchQuery:f._getCompositionHitsSearchParams(n)}}}),this.emit("searchForFacetValues",{state:n,facet:e,query:t}),c.then((function(t){return o._currentNbQueries--,0===o._currentNbQueries&&o.emit("searchQueueEmpty"),(t=t.results[0]).facetHits.forEach((function(t){t.escapedValue=s(t.value),t.isRefined=a?n.isDisjunctiveFacetRefined(e,t.escapedValue):n.isFacetRefined(e,t.escapedValue)})),t}),(function(e){throw o._currentNbQueries--,0===o._currentNbQueries&&o.emit("searchQueueEmpty"),e}))},v.prototype.setQuery=function(e){return this._change({state:this.state.resetPage().setQuery(e),isPageReset:!0}),this},v.prototype.clearRefinements=function(e){return this._change({state:this.state.resetPage().clearRefinements(e),isPageReset:!0}),this},v.prototype.clearTags=function(){return this._change({state:this.state.resetPage().clearTags(),isPageReset:!0}),this},v.prototype.addDisjunctiveFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().addDisjunctiveFacetRefinement(e,t),isPageReset:!0}),this},v.prototype.addDisjunctiveRefine=function(){return this.addDisjunctiveFacetRefinement.apply(this,arguments)},v.prototype.addHierarchicalFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().addHierarchicalFacetRefinement(e,t),isPageReset:!0}),this},v.prototype.addNumericRefinement=function(e,t,r){return this._change({state:this.state.resetPage().addNumericRefinement(e,t,r),isPageReset:!0}),this},v.prototype.addFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().addFacetRefinement(e,t),isPageReset:!0}),this},v.prototype.addRefine=function(){return this.addFacetRefinement.apply(this,arguments)},v.prototype.addFacetExclusion=function(e,t){return this._change({state:this.state.resetPage().addExcludeRefinement(e,t),isPageReset:!0}),this},v.prototype.addExclude=function(){return this.addFacetExclusion.apply(this,arguments)},v.prototype.addTag=function(e){return this._change({state:this.state.resetPage().addTagRefinement(e),isPageReset:!0}),this},v.prototype.addFrequentlyBoughtTogether=function(e){return this._recommendChange({state:this.recommendState.addFrequentlyBoughtTogether(e)}),this},v.prototype.addRelatedProducts=function(e){return this._recommendChange({state:this.recommendState.addRelatedProducts(e)}),this},v.prototype.addTrendingItems=function(e){return this._recommendChange({state:this.recommendState.addTrendingItems(e)}),this},v.prototype.addTrendingFacets=function(e){return this._recommendChange({state:this.recommendState.addTrendingFacets(e)}),this},v.prototype.addLookingSimilar=function(e){return this._recommendChange({state:this.recommendState.addLookingSimilar(e)}),this},v.prototype.removeNumericRefinement=function(e,t,r){return this._change({state:this.state.resetPage().removeNumericRefinement(e,t,r),isPageReset:!0}),this},v.prototype.removeDisjunctiveFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().removeDisjunctiveFacetRefinement(e,t),isPageReset:!0}),this},v.prototype.removeDisjunctiveRefine=function(){return this.removeDisjunctiveFacetRefinement.apply(this,arguments)},v.prototype.removeHierarchicalFacetRefinement=function(e){return this._change({state:this.state.resetPage().removeHierarchicalFacetRefinement(e),isPageReset:!0}),this},v.prototype.removeFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().removeFacetRefinement(e,t),isPageReset:!0}),this},v.prototype.removeRefine=function(){return this.removeFacetRefinement.apply(this,arguments)},v.prototype.removeFacetExclusion=function(e,t){return this._change({state:this.state.resetPage().removeExcludeRefinement(e,t),isPageReset:!0}),this},v.prototype.removeExclude=function(){return this.removeFacetExclusion.apply(this,arguments)},v.prototype.removeTag=function(e){return this._change({state:this.state.resetPage().removeTagRefinement(e),isPageReset:!0}),this},v.prototype.removeFrequentlyBoughtTogether=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},v.prototype.removeRelatedProducts=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},v.prototype.removeTrendingItems=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},v.prototype.removeTrendingFacets=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},v.prototype.removeLookingSimilar=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},v.prototype.toggleFacetExclusion=function(e,t){return this._change({state:this.state.resetPage().toggleExcludeFacetRefinement(e,t),isPageReset:!0}),this},v.prototype.toggleExclude=function(){return this.toggleFacetExclusion.apply(this,arguments)},v.prototype.toggleRefinement=function(e,t){return this.toggleFacetRefinement(e,t)},v.prototype.toggleFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().toggleFacetRefinement(e,t),isPageReset:!0}),this},v.prototype.toggleRefine=function(){return this.toggleFacetRefinement.apply(this,arguments)},v.prototype.toggleTag=function(e){return this._change({state:this.state.resetPage().toggleTagRefinement(e),isPageReset:!0}),this},v.prototype.nextPage=function(){var e=this.state.page||0;return this.setPage(e+1)},v.prototype.previousPage=function(){var e=this.state.page||0;return this.setPage(e-1)},v.prototype.setCurrentPage=y,v.prototype.setPage=y,v.prototype.setIndex=function(e){return this._change({state:this.state.resetPage().setIndex(e),isPageReset:!0}),this},v.prototype.setQueryParameter=function(e,t){return this._change({state:this.state.resetPage().setQueryParameter(e,t),isPageReset:!0}),this},v.prototype.setState=function(e){return this._change({state:m.make(e),isPageReset:!1}),this},v.prototype.overrideStateWithoutTriggeringChangeEvent=function(e){return this.state=new m(e),this},v.prototype.hasRefinements=function(e){return!!o(this.state.getNumericRefinements(e))||(this.state.isConjunctiveFacet(e)?this.state.isFacetRefined(e):this.state.isDisjunctiveFacet(e)?this.state.isDisjunctiveFacetRefined(e):!!this.state.isHierarchicalFacet(e)&&this.state.isHierarchicalFacetRefined(e))},v.prototype.isExcluded=function(e,t){return this.state.isExcludeRefined(e,t)},v.prototype.isDisjunctiveRefined=function(e,t){return this.state.isDisjunctiveFacetRefined(e,t)},v.prototype.hasTag=function(e){return this.state.isTagRefined(e)},v.prototype.isTagRefined=function(){return this.hasTagRefinements.apply(this,arguments)},v.prototype.getIndex=function(){return this.state.index},v.prototype.getCurrentPage=R,v.prototype.getPage=R,v.prototype.getTags=function(){return this.state.tagRefinements},v.prototype.getRefinements=function(e){var t=[];if(this.state.isConjunctiveFacet(e))this.state.getConjunctiveRefinements(e).forEach((function(e){t.push({value:e,type:"conjunctive"})})),this.state.getExcludeRefinements(e).forEach((function(e){t.push({value:e,type:"exclude"})}));else if(this.state.isDisjunctiveFacet(e)){this.state.getDisjunctiveRefinements(e).forEach((function(e){t.push({value:e,type:"disjunctive"})}))}var r=this.state.getNumericRefinements(e);return Object.keys(r).forEach((function(e){var i=r[e];t.push({value:i,operator:e,type:"numeric"})})),t},v.prototype.getNumericRefinement=function(e,t){return this.state.getNumericRefinement(e,t)},v.prototype.getHierarchicalFacetBreadcrumb=function(e){return this.state.getHierarchicalFacetBreadcrumb(e)},v.prototype._search=function(e){var t=this.state,r=[],i=[];e.onlyWithDerivedHelpers||(i=f._getQueries(t.index,t),r.push({state:t,queriesCount:i.length,helper:this}),this.emit("search",{state:t,results:this.lastResults}));var n=this.derivedHelpers.map((function(e){var i=e.getModifiedState(t),n=i.index?f._getQueries(i.index,i):[];return r.push({state:i,queriesCount:n.length,helper:e}),e.emit("search",{state:i,results:e.lastResults}),n})),s=Array.prototype.concat.apply(i,n),a=this._queryId++;if(this._currentNbQueries++,!s.length)return Promise.resolve({results:[]}).then(this._dispatchAlgoliaResponse.bind(this,r,a));try{this.client.search(s).then(this._dispatchAlgoliaResponse.bind(this,r,a)).catch(this._dispatchAlgoliaError.bind(this,a))}catch(c){this.emit("error",{error:c})}},v.prototype._runComposition=function(){var e=this.state,t=[],r=this.derivedHelpers.map((function(r){var i=r.getModifiedState(e),n=f._getCompositionQueries(i);return t.push({state:i,queriesCount:n.length,helper:r}),r.emit("search",{state:i,results:r.lastResults}),n})),i=Array.prototype.concat.apply([],r),n=this._queryId++;if(this._currentNbQueries++,!i.length)return Promise.resolve({results:[]}).then(this._dispatchAlgoliaResponse.bind(this,t,n));if(i.length>1)throw new Error("Only one query is allowed when using a composition.");var s=i[0];try{this.client.search(s).then(this._dispatchAlgoliaResponse.bind(this,t,n)).catch(this._dispatchAlgoliaError.bind(this,n))}catch(a){this.emit("error",{error:a})}},v.prototype._recommend=function(){var e=this.state,t=this.recommendState,r=this.getIndex(),i=[{state:t,index:r,helper:this}],n=t.params.map((function(e){return e.$$id}));this.emit("fetch",{recommend:{state:t,results:this.lastRecommendResults}});var s=this._recommendCache,a=this.derivedHelpers.map((function(t){var r=t.getModifiedState(e).index;if(!r)return[];var a=t.getModifiedRecommendState(new h);return i.push({state:a,index:r,helper:t}),n=Array.prototype.concat.apply(n,a.params.map((function(e){return e.$$id}))),t.emit("fetch",{recommend:{state:a,results:t.lastRecommendResults}}),a._buildQueries(r,s)})),c=Array.prototype.concat.apply(this.recommendState._buildQueries(r,s),a);if(0!==c.length)if(c.length>0&&void 0===this.client.getRecommendations)console.warn("Please update algoliasearch/lite to the latest version in order to use recommend widgets.");else{var o=this._recommendQueryId++;this._currentNbRecommendQueries++;try{this.client.getRecommendations(c).then(this._dispatchRecommendResponse.bind(this,o,i,n)).catch(this._dispatchRecommendError.bind(this,o))}catch(u){this.emit("error",{error:u})}}},v.prototype._dispatchAlgoliaResponse=function(e,t,r){var i=this;if(!(t<this._lastQueryIdReceived)){this._currentNbQueries-=t-this._lastQueryIdReceived,this._lastQueryIdReceived=t,0===this._currentNbQueries&&this.emit("searchQueueEmpty");var n=r.results.slice(),s=Object.keys(r).reduce((function(e,t){return"results"!==t&&(e[t]=r[t]),e}),{});Object.keys(s).length<=0&&(s=void 0),e.forEach((function(e){var t=e.state,r=e.queriesCount,a=e.helper,c=n.splice(0,r);t.index?(a.lastResults=new d(t,c,i._searchResultsOptions),void 0!==s&&(a.lastResults._rawContent=s),a.emit("result",{results:a.lastResults,state:t})):a.emit("result",{results:null,state:t})}))}},v.prototype._dispatchRecommendResponse=function(e,t,r,i){if(!(e<this._lastRecommendQueryIdReceived)){this._currentNbRecommendQueries-=e-this._lastRecommendQueryIdReceived,this._lastRecommendQueryIdReceived=e,0===this._currentNbRecommendQueries&&this.emit("recommendQueueEmpty");var n=this._recommendCache,s={};r.filter((function(e){return void 0===n[e]})).forEach((function(e,t){s[e]||(s[e]=[]),s[e].push(t)})),Object.keys(s).forEach((function(e){var t=s[e],a=i.results[t[0]];1!==t.length?n[e]=Object.assign({},a,{hits:p(r,t.map((function(e){return i.results[e].hits})))}):n[e]=a}));var a={};r.forEach((function(e){a[e]=n[e]})),t.forEach((function(e){var t=e.state,r=e.helper;e.index?(r.lastRecommendResults=new l(t,a),r.emit("recommend:result",{recommend:{results:r.lastRecommendResults,state:t}})):r.emit("recommend:result",{results:null,state:t})}))}},v.prototype._dispatchAlgoliaError=function(e,t){e<this._lastQueryIdReceived||(this._currentNbQueries-=e-this._lastQueryIdReceived,this._lastQueryIdReceived=e,this.emit("error",{error:t}),0===this._currentNbQueries&&this.emit("searchQueueEmpty"))},v.prototype._dispatchRecommendError=function(e,t){e<this._lastRecommendQueryIdReceived||(this._currentNbRecommendQueries-=e-this._lastRecommendQueryIdReceived,this._lastRecommendQueryIdReceived=e,this.emit("error",{error:t}),0===this._currentNbRecommendQueries&&this.emit("recommendQueueEmpty"))},v.prototype.containsRefinement=function(e,t,r,i){return e||0!==t.length||0!==r.length||0!==i.length},v.prototype._hasDisjunctiveRefinements=function(e){return this.state.disjunctiveRefinements[e]&&this.state.disjunctiveRefinements[e].length>0},v.prototype._change=function(e){var t=e.state,r=e.isPageReset;t!==this.state&&(this.state=t,this.emit("change",{state:this.state,results:this.lastResults,isPageReset:r}))},v.prototype._recommendChange=function(e){var t=e.state;t!==this.recommendState&&(this.recommendState=t,this.emit("recommend:change",{search:{results:this.lastResults,state:this.state},recommend:{results:this.lastRecommendResults,state:this.recommendState}}))},v.prototype.clearCache=function(){return this.client.clearCache&&this.client.clearCache(),this},v.prototype.setClient=function(e){return this.client===e||("function"==typeof e.addAlgoliaAgent&&e.addAlgoliaAgent("JS Helper ("+g+")"),this.client=e),this},v.prototype.getClient=function(){return this.client},v.prototype.derive=function(e,t){var r=new n(this,e,t);return this.derivedHelpers.push(r),r},v.prototype.detachDerivedHelper=function(e){var t=this.derivedHelpers.indexOf(e);if(-1===t)throw new Error("Derived helper already detached");this.derivedHelpers.splice(t,1)},v.prototype.hasPendingRequests=function(){return this._currentNbQueries>0},e.exports=v},6673:(e,t,r)=>{"use strict";e.exports=function(e){return function(t,r){var i=e.hierarchicalFacets[r],u=e.hierarchicalFacetsRefinements[i.name]&&e.hierarchicalFacetsRefinements[i.name][0]||"",h=e._getHierarchicalFacetSeparator(i),l=e._getHierarchicalRootPath(i),f=e._getHierarchicalShowParentLevel(i),m=s(e._getHierarchicalFacetSortBy(i)),d=t.every((function(e){return e.exhaustive})),p=function(e,t,r,i,s){return function(u,h,l){var f=u;if(l>0){var m=0;for(f=u;m<l;){var d=f&&Array.isArray(f.data)?f.data:[];f=n(d,(function(e){return e.isRefined})),m++}}if(f){var p=Object.keys(h.data).map((function(e){return[e,h.data[e]]})).filter((function(e){return function(e,t,r,i,n,s){if(n&&(0!==e.indexOf(n)||n===e))return!1;return!n&&-1===e.indexOf(i)||n&&e.split(i).length-n.split(i).length===1||-1===e.indexOf(i)&&-1===r.indexOf(i)||0===r.indexOf(e)||0===e.indexOf(t+i)&&(s||0===e.indexOf(r))}(e[0],f.path||r,s,t,r,i)}));f.data=a(p.map((function(e){var r=e[0];return function(e,t,r,i,n){var s=t.split(r);return{name:s[s.length-1].trim(),path:t,escapedValue:c(t),count:e,isRefined:i===t||0===i.indexOf(t+r),exhaustive:n,data:null}}(e[1],r,t,o(s),h.exhaustive)})),e[0],e[1])}return u}}(m,h,l,f,u),g=t;return l&&(g=t.slice(l.split(h).length)),g.reduce(p,{name:e.hierarchicalFacets[r].name,count:null,isRefined:!0,path:null,escapedValue:null,exhaustive:d,data:null})}};var i=r(2909),n=r(849),s=r(7577),a=r(8601),c=i.escapeFacetValue,o=i.unescapeFacetValue},6732:(e,t,r)=>{"use strict";var i=r(2733);function n(e,t,r){this.main=e,this.fn=t,this.recommendFn=r,this.lastResults=null,this.lastRecommendResults=null}r(3014)(n,i),n.prototype.detach=function(){this.removeAllListeners(),this.main.detachDerivedHelper(this)},n.prototype.getModifiedState=function(e){return this.fn(e)},n.prototype.getModifiedRecommendState=function(e){return this.recommendFn(e)},e.exports=n},6938:e=>{"use strict";e.exports="3.25.0"},7507:e=>{"use strict";e.exports=function e(t){if("number"==typeof t)return t;if("string"==typeof t)return parseFloat(t);if(Array.isArray(t))return t.map(e);throw new Error("The value should be a number, a parsable string or an array of those.")}},7577:(e,t,r)=>{"use strict";var i=r(849);e.exports=function(e,t){var r=(t||[]).map((function(e){return e.split(":")}));return e.reduce((function(e,t){var n=t.split(":"),s=i(r,(function(e){return e[0]===n[0]}));return n.length>1||!s?(e[0].push(n[0]),e[1].push(n[1]),e):(e[0].push(s[0]),e[1].push(s[1]),e)}),[[],[]])}},7691:(e,t,r)=>{"use strict";var i=r(8965),n=r(9110),s=r(2909),a=r(849),c=r(3917),o=r(7577),u=r(2183),h=r(8601),l=s.escapeFacetValue,f=s.unescapeFacetValue,m=r(6673);function d(e){var t={};return e.forEach((function(e,r){t[e]=r})),t}function p(e,t,r){t&&t[r]&&(e.stats=t[r])}function g(e,t,r){var s=t[0]||{};this._rawResults=t;var o=this;Object.keys(s).forEach((function(e){o[e]=s[e]}));var h=n(r,{persistHierarchicalRootCount:!1});Object.keys(h).forEach((function(e){o[e]=h[e]})),this.processingTimeMS=t.reduce((function(e,t){return void 0===t.processingTimeMS?e:e+t.processingTimeMS}),0),this.disjunctiveFacets=[],this.hierarchicalFacets=e.hierarchicalFacets.map((function(){return[]})),this.facets=[];var l=e.getRefinedDisjunctiveFacets(),g=d(e.facets),v=d(e.disjunctiveFacets),y=1,R=s.facets||{};Object.keys(R).forEach((function(t){var r,i,n=R[t],u=(r=e.hierarchicalFacets,i=t,a(r,(function(e){return(e.attributes||[]).indexOf(i)>-1})));if(u){var h=u.attributes.indexOf(t),l=c(e.hierarchicalFacets,(function(e){return e.name===u.name}));o.hierarchicalFacets[l][h]={attribute:t,data:n,exhaustive:s.exhaustiveFacetsCount}}else{var f,m=-1!==e.disjunctiveFacets.indexOf(t),d=-1!==e.facets.indexOf(t);m&&(f=v[t],o.disjunctiveFacets[f]={name:t,data:n,exhaustive:s.exhaustiveFacetsCount},p(o.disjunctiveFacets[f],s.facets_stats,t)),d&&(f=g[t],o.facets[f]={name:t,data:n,exhaustive:s.exhaustiveFacetsCount},p(o.facets[f],s.facets_stats,t))}})),this.hierarchicalFacets=i(this.hierarchicalFacets),l.forEach((function(r){var i=t[y],a=i&&i.facets?i.facets:{},h=e.getHierarchicalFacetByName(r);Object.keys(a).forEach((function(t){var r,l=a[t];if(h){r=c(e.hierarchicalFacets,(function(e){return e.name===h.name}));var m=c(o.hierarchicalFacets[r],(function(e){return e.attribute===t}));if(-1===m)return;o.hierarchicalFacets[r][m].data=o.persistHierarchicalRootCount?u(o.hierarchicalFacets[r][m].data,l):n(l,o.hierarchicalFacets[r][m].data)}else{r=v[t];var d=s.facets&&s.facets[t]||{};o.disjunctiveFacets[r]={name:t,data:u(d,l),exhaustive:i.exhaustiveFacetsCount},p(o.disjunctiveFacets[r],i.facets_stats,t),e.disjunctiveFacetsRefinements[t]&&e.disjunctiveFacetsRefinements[t].forEach((function(i){!o.disjunctiveFacets[r].data[i]&&e.disjunctiveFacetsRefinements[t].indexOf(f(i))>-1&&(o.disjunctiveFacets[r].data[i]=0)}))}})),y++})),e.getRefinedHierarchicalFacets().forEach((function(r){var i=e.getHierarchicalFacetByName(r),s=e._getHierarchicalFacetSeparator(i),a=e.getHierarchicalRefinement(r);0===a.length||a[0].split(s).length<2||t.slice(y).forEach((function(t){var r=t&&t.facets?t.facets:{};Object.keys(r).forEach((function(t){var u=r[t],h=c(e.hierarchicalFacets,(function(e){return e.name===i.name})),l=c(o.hierarchicalFacets[h],(function(e){return e.attribute===t}));if(-1!==l){var f={};if(a.length>0&&!o.persistHierarchicalRootCount){var m=a[0].split(s)[0];f[m]=o.hierarchicalFacets[h][l].data[m]}o.hierarchicalFacets[h][l].data=n(f,u,o.hierarchicalFacets[h][l].data)}})),y++}))})),Object.keys(e.facetsExcludes).forEach((function(t){var r=e.facetsExcludes[t],i=g[t];o.facets[i]={name:t,data:R[t],exhaustive:s.exhaustiveFacetsCount},r.forEach((function(e){o.facets[i]=o.facets[i]||{name:t},o.facets[i].data=o.facets[i].data||{},o.facets[i].data[e]=0}))})),this.hierarchicalFacets=this.hierarchicalFacets.map(m(e)),this.facets=i(this.facets),this.disjunctiveFacets=i(this.disjunctiveFacets),this._state=e}function v(e,t){function r(e){return e.name===t}if(e._state.isConjunctiveFacet(t)){var i=a(e.facets,r);return i?Object.keys(i.data).map((function(r){var n=l(r);return{name:r,escapedValue:n,count:i.data[r],isRefined:e._state.isFacetRefined(t,n),isExcluded:e._state.isExcludeRefined(t,r)}})):[]}if(e._state.isDisjunctiveFacet(t)){var n=a(e.disjunctiveFacets,r);return n?Object.keys(n.data).map((function(r){var i=l(r);return{name:r,escapedValue:i,count:n.data[r],isRefined:e._state.isDisjunctiveFacetRefined(t,i)}})):[]}if(e._state.isHierarchicalFacet(t)){var s=a(e.hierarchicalFacets,r);if(!s)return s;var c=e._state.getHierarchicalFacetByName(t),o=e._state._getHierarchicalFacetSeparator(c),u=f(e._state.getHierarchicalRefinement(t)[0]||"");0===u.indexOf(c.rootPath)&&(u=u.replace(c.rootPath+o,""));var h=u.split(o);return h.unshift(t),y(s,h,0),s}}function y(e,t,r){e.isRefined=e.name===(t[r]&&t[r].trim()),e.data&&e.data.forEach((function(e){y(e,t,r+1)}))}function R(e,t,r,i){if(i=i||0,Array.isArray(t))return e(t,r[i]);if(!t.data||0===t.data.length)return t;var s=t.data.map((function(t){return R(e,t,r,i+1)})),a=e(s,r[i]);return n({data:a},t)}function F(e,t){var r=a(e,(function(e){return e.name===t}));return r&&r.stats}function _(e,t,r,i,n){var s=a(n,(function(e){return e.name===r})),c=s&&s.data&&s.data[i]?s.data[i]:0,o=s&&s.exhaustive||!1;return{type:t,attributeName:r,name:i,count:c,exhaustive:o}}g.prototype.getFacetByName=function(e){function t(t){return t.name===e}return a(this.facets,t)||a(this.disjunctiveFacets,t)||a(this.hierarchicalFacets,t)},g.DEFAULT_SORT=["isRefined:desc","count:desc","name:asc"],g.prototype.getFacetValues=function(e,t){var r=v(this,e);if(r){var i,s=n(t,{sortBy:g.DEFAULT_SORT,facetOrdering:!(t&&t.sortBy)}),a=this;if(Array.isArray(r))i=[e];else i=a._state.getHierarchicalFacetByName(r.name).attributes;return R((function(e,t){if(s.facetOrdering){var r=function(e,t){return e.renderingContent&&e.renderingContent.facetOrdering&&e.renderingContent.facetOrdering.values&&e.renderingContent.facetOrdering.values[t]}(a,t);if(r)return function(e,t){var r=[],i=[],n=t.hide||[],s=(t.order||[]).reduce((function(e,t,r){return e[t]=r,e}),{});e.forEach((function(e){var t=e.path||e.name,a=n.indexOf(t)>-1;a||void 0===s[t]?a||i.push(e):r[s[t]]=e})),r=r.filter((function(e){return e}));var a,c=t.sortRemainingBy;return"hidden"===c?r:(a="alpha"===c?[["path","name"],["asc","asc"]]:[["count"],["desc"]],r.concat(h(i,a[0],a[1])))}(e,r)}if(Array.isArray(s.sortBy)){var i=o(s.sortBy,g.DEFAULT_SORT);return h(e,i[0],i[1])}if("function"==typeof s.sortBy)return function(e,t){return t.sort(e)}(s.sortBy,e);throw new Error("options.sortBy is optional but if defined it must be either an array of string (predicates) or a sorting function")}),r,i)}},g.prototype.getFacetStats=function(e){return this._state.isConjunctiveFacet(e)?F(this.facets,e):this._state.isDisjunctiveFacet(e)?F(this.disjunctiveFacets,e):void 0},g.prototype.getRefinements=function(){var e=this._state,t=this,r=[];return Object.keys(e.facetsRefinements).forEach((function(i){e.facetsRefinements[i].forEach((function(n){r.push(_(e,"facet",i,n,t.facets))}))})),Object.keys(e.facetsExcludes).forEach((function(i){e.facetsExcludes[i].forEach((function(n){r.push(_(e,"exclude",i,n,t.facets))}))})),Object.keys(e.disjunctiveFacetsRefinements).forEach((function(i){e.disjunctiveFacetsRefinements[i].forEach((function(n){r.push(_(e,"disjunctive",i,n,t.disjunctiveFacets))}))})),Object.keys(e.hierarchicalFacetsRefinements).forEach((function(i){e.hierarchicalFacetsRefinements[i].forEach((function(n){r.push(function(e,t,r,i){var n=e.getHierarchicalFacetByName(t),s=e._getHierarchicalFacetSeparator(n),c=r.split(s),o=a(i,(function(e){return e.name===t})),u=c.reduce((function(e,t){var r=e&&a(e.data,(function(e){return e.name===t}));return void 0!==r?r:e}),o),h=u&&u.count||0,l=u&&u.exhaustive||!1,f=u&&u.path||"";return{type:"hierarchical",attributeName:t,name:f,count:h,exhaustive:l}}(e,i,n,t.hierarchicalFacets))}))})),Object.keys(e.numericRefinements).forEach((function(t){var i=e.numericRefinements[t];Object.keys(i).forEach((function(e){i[e].forEach((function(i){r.push({type:"numeric",attributeName:t,name:i,numericValue:i,operator:e})}))}))})),e.tagRefinements.forEach((function(e){r.push({type:"tag",attributeName:"_tags",name:e})})),r},e.exports=g},7749:(e,t,r)=>{"use strict";var i=r(849),n=r(8657);e.exports=function(e,t){var r={};return t.forEach((function(t){t.forEach((function(t,i){e.includes(t.objectID)||(r[t.objectID]?r[t.objectID]={indexSum:r[t.objectID].indexSum+i,count:r[t.objectID].count+1}:r[t.objectID]={indexSum:i,count:1})}))})),function(e,t){var r=[];return Object.keys(e).forEach((function(i){e[i].count<2&&(e[i].indexSum+=100),r.push({objectID:i,avgOfIndices:e[i].indexSum/t})})),r.sort((function(e,t){return e.avgOfIndices>t.avgOfIndices?1:-1}))}(r,t.length).reduce((function(e,r){var s=i(n(t),(function(e){return e.objectID===r.objectID}));return s?e.concat(s):e}),[])}},8601:e=>{"use strict";function t(e,t){if(e!==t){var r=void 0!==e,i=null===e,n=void 0!==t,s=null===t;if(!s&&e>t||i&&n||!r)return 1;if(!i&&e<t||s&&r||!n)return-1}return 0}e.exports=function(e,r,i){if(!Array.isArray(e))return[];Array.isArray(i)||(i=[]);var n=e.map((function(e,t){return{criteria:r.map((function(t){return e[t]})),index:t,value:e}}));return n.sort((function(e,r){for(var n=-1;++n<e.criteria.length;){var s=t(e.criteria[n],r.criteria[n]);if(s)return n>=i.length?s:"desc"===i[n]?-s:s}return e.index-r.index})),n.map((function(e){return e.value}))}},8657:e=>{e.exports=function(e){return e.reduce((function(e,t){return e.concat(t)}),[])}},8965:e=>{"use strict";e.exports=function(e){return Array.isArray(e)?e.filter(Boolean):[]}},9110:e=>{"use strict";e.exports=function(){return Array.prototype.slice.call(arguments).reduceRight((function(e,t){return Object.keys(Object(t)).forEach((function(r){void 0!==t[r]&&(void 0!==e[r]&&delete e[r],e[r]=t[r])})),e}),{})}},9127:e=>{"use strict";function t(e){e=e||{},this.params=e.params||[]}t.prototype={constructor:t,addParams:function(e){var r=this.params.slice();return r.push(e),new t({params:r})},removeParams:function(e){return new t({params:this.params.filter((function(t){return t.$$id!==e}))})},addFrequentlyBoughtTogether:function(e){return this.addParams(Object.assign({},e,{model:"bought-together"}))},addRelatedProducts:function(e){return this.addParams(Object.assign({},e,{model:"related-products"}))},addTrendingItems:function(e){return this.addParams(Object.assign({},e,{model:"trending-items"}))},addTrendingFacets:function(e){return this.addParams(Object.assign({},e,{model:"trending-facets"}))},addLookingSimilar:function(e){return this.addParams(Object.assign({},e,{model:"looking-similar"}))},_buildQueries:function(e,t){return this.params.filter((function(e){return void 0===t[e.$$id]})).map((function(t){var r=Object.assign({},t,{indexName:e,threshold:t.threshold||0});return delete r.$$id,r}))}},e.exports=t},9228:(e,t,r)=>{"use strict";var i=r(4728);function n(e){return Object.keys(e).sort().reduce((function(t,r){return t[r]=e[r],t}),{})}var s={_getQueries:function(e,t){var r=[];return r.push({indexName:e,params:s._getHitsSearchParams(t)}),t.getRefinedDisjunctiveFacets().forEach((function(i){r.push({indexName:e,params:s._getDisjunctiveFacetSearchParams(t,i)})})),t.getRefinedHierarchicalFacets().forEach((function(i){var n=t.getHierarchicalFacetByName(i),a=t.getHierarchicalRefinement(i),c=t._getHierarchicalFacetSeparator(n);if(a.length>0&&a[0].split(c).length>1){var o=a[0].split(c).slice(0,-1).reduce((function(e,t,r){return e.concat({attribute:n.attributes[r],value:0===r?t:[e[e.length-1].value,t].join(c)})}),[]);o.forEach((function(i,a){var c=s._getDisjunctiveFacetSearchParams(t,i.attribute,0===a);function u(e){return n.attributes.some((function(t){return t===e.split(":")[0]}))}var h=(c.facetFilters||[]).reduce((function(e,t){if(Array.isArray(t)){var r=t.filter((function(e){return!u(e)}));r.length>0&&e.push(r)}return"string"!=typeof t||u(t)||e.push(t),e}),[]),l=o[a-1];a>0?c.facetFilters=h.concat(l.attribute+":"+l.value):h.length>0?c.facetFilters=h:delete c.facetFilters,r.push({indexName:e,params:c})}))}})),r},_getCompositionQueries:function(e){return[{compositionID:e.index,requestBody:{params:s._getCompositionHitsSearchParams(e)}}]},_getHitsSearchParams:function(e){var t=e.facets.concat(e.disjunctiveFacets).concat(s._getHitsHierarchicalFacetsAttributes(e)).sort(),r=s._getFacetFilters(e),a=s._getNumericFilters(e),c=s._getTagFilters(e),o={};return t.length>0&&(o.facets=t.indexOf("*")>-1?["*"]:t),c.length>0&&(o.tagFilters=c),r.length>0&&(o.facetFilters=r),a.length>0&&(o.numericFilters=a),n(i({},e.getQueryParams(),o))},_getCompositionHitsSearchParams:function(e){var t=e.facets.concat(e.disjunctiveFacets.map((function(t){return e.disjunctiveFacetsRefinements&&e.disjunctiveFacetsRefinements[t]&&e.disjunctiveFacetsRefinements[t].length>0?"disjunctive("+t+")":t}))).concat(s._getHitsHierarchicalFacetsAttributes(e)).sort(),r=s._getFacetFilters(e),a=s._getNumericFilters(e),c=s._getTagFilters(e),o={};t.length>0&&(o.facets=t.indexOf("*")>-1?["*"]:t),c.length>0&&(o.tagFilters=c),r.length>0&&(o.facetFilters=r),a.length>0&&(o.numericFilters=a);var u=e.getQueryParams();return delete u.highlightPreTag,delete u.highlightPostTag,delete u.index,n(i({},u,o))},_getDisjunctiveFacetSearchParams:function(e,t,r){var a=s._getFacetFilters(e,t,r),c=s._getNumericFilters(e,t),o=s._getTagFilters(e),u={hitsPerPage:0,page:0,analytics:!1,clickAnalytics:!1};o.length>0&&(u.tagFilters=o);var h=e.getHierarchicalFacetByName(t);return u.facets=h?s._getDisjunctiveHierarchicalFacetAttribute(e,h,r):t,c.length>0&&(u.numericFilters=c),a.length>0&&(u.facetFilters=a),n(i({},e.getQueryParams(),u))},_getNumericFilters:function(e,t){if(e.numericFilters)return e.numericFilters;var r=[];return Object.keys(e.numericRefinements).forEach((function(i){var n=e.numericRefinements[i]||{};Object.keys(n).forEach((function(e){var s=n[e]||[];t!==i&&s.forEach((function(t){if(Array.isArray(t)){var n=t.map((function(t){return i+e+t}));r.push(n)}else r.push(i+e+t)}))}))})),r},_getTagFilters:function(e){return e.tagFilters?e.tagFilters:e.tagRefinements.join(",")},_getFacetFilters:function(e,t,r){var i=[],n=e.facetsRefinements||{};Object.keys(n).sort().forEach((function(e){(n[e]||[]).slice().sort().forEach((function(t){i.push(e+":"+t)}))}));var s=e.facetsExcludes||{};Object.keys(s).sort().forEach((function(e){(s[e]||[]).sort().forEach((function(t){i.push(e+":-"+t)}))}));var a=e.disjunctiveFacetsRefinements||{};Object.keys(a).sort().forEach((function(e){var r=a[e]||[];if(e!==t&&r&&0!==r.length){var n=[];r.slice().sort().forEach((function(t){n.push(e+":"+t)})),i.push(n)}}));var c=e.hierarchicalFacetsRefinements||{};return Object.keys(c).sort().forEach((function(n){var s=(c[n]||[])[0];if(void 0!==s){var a,o,u=e.getHierarchicalFacetByName(n),h=e._getHierarchicalFacetSeparator(u),l=e._getHierarchicalRootPath(u);if(t===n){if(-1===s.indexOf(h)||!l&&!0===r||l&&l.split(h).length===s.split(h).length)return;l?(o=l.split(h).length-1,s=l):(o=s.split(h).length-2,s=s.slice(0,s.lastIndexOf(h))),a=u.attributes[o]}else o=s.split(h).length-1,a=u.attributes[o];a&&i.push([a+":"+s])}})),i},_getHitsHierarchicalFacetsAttributes:function(e){return e.hierarchicalFacets.reduce((function(t,r){var i=e.getHierarchicalRefinement(r.name)[0];if(!i)return t.push(r.attributes[0]),t;var n=e._getHierarchicalFacetSeparator(r),s=i.split(n).length,a=r.attributes.slice(0,s+1);return t.concat(a)}),[])},_getDisjunctiveHierarchicalFacetAttribute:function(e,t,r){var i=e._getHierarchicalFacetSeparator(t);if(!0===r){var n=e._getHierarchicalRootPath(t),s=0;return n&&(s=n.split(i).length),[t.attributes[s]]}var a=(e.getHierarchicalRefinement(t.name)[0]||"").split(i).length-1;return t.attributes.slice(0,a+1)},getSearchForFacetQuery:function(e,t,r,a){var c=a.isDisjunctiveFacet(e)?a.clearRefinements(e):a,o={facetQuery:t,facetName:e};return"number"==typeof r&&(o.maxFacetHits=r),n(i({},s._getHitsSearchParams(c),o))}};e.exports=s}}]);