"use strict";(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[2237],{2237:(e,t,n)=>{n.r(t),n.d(t,{default:()=>d});n(6540);var s=n(1312),i=n(5500),o=n(1330),a=n(3363),r=n(4848);function d(){const e=(0,s.T)({id:"theme.NotFound.title",message:"Page Not Found"});return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.be,{title:e}),(0,r.jsx)(o.A,{children:(0,r.jsx)(a.A,{})})]})}},3363:(e,t,n)=>{n.d(t,{A:()=>r});n(6540);var s=n(4164),i=n(1312),o=n(1107),a=n(4848);function r({className:e}){return(0,a.jsx)("main",{className:(0,s.A)("container margin-vert--xl",e),children:(0,a.jsx)("div",{className:"row",children:(0,a.jsxs)("div",{className:"col col--6 col--offset-3",children:[(0,a.jsx)(o.A,{as:"h1",className:"hero__title",children:(0,a.jsx)(i.A,{id:"theme.NotFound.title",description:"The title of the 404 page",children:"Page Not Found"})}),(0,a.jsx)("p",{children:(0,a.jsx)(i.A,{id:"theme.NotFound.p1",description:"The first paragraph of the 404 page",children:"We could not find what you were looking for."})}),(0,a.jsx)("p",{children:(0,a.jsx)(i.A,{id:"theme.NotFound.p2",description:"The 2nd paragraph of the 404 page",children:"Please contact the owner of the site that linked you to the original URL and let them know their link is broken."})})]})})})}}}]);