"use strict";(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[6560],{5791:(e,n,s)=>{s.r(n),s.d(n,{assets:()=>d,contentTitle:()=>o,default:()=>m,frontMatter:()=>a,metadata:()=>i,toc:()=>l});const i=JSON.parse('{"id":"ui-sdk/installation","title":"UI SDK Installation","description":"The AdMesh UI SDK is a production-ready React + TypeScript component library for displaying product recommendations with integrated tracking, theming, and conversational interface support.","source":"@site/docs/ui-sdk/installation.md","sourceDirName":"ui-sdk","slug":"/ui-sdk/installation","permalink":"/ui-sdk/installation","draft":false,"unlisted":false,"editUrl":"https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/ui-sdk/installation.md","tags":[],"version":"current","sidebarPosition":1,"frontMatter":{"sidebar_position":1},"sidebar":"tutorialSidebar","previous":{"title":"TypeScript SDK Installation","permalink":"/typescript-sdk/installation"},"next":{"title":"AI Agent Integration Overview","permalink":"/ai-integration/overview"}}');var t=s(4848),r=s(8453);const a={sidebar_position:1},o="UI SDK Installation",d={},l=[{value:"Requirements",id:"requirements",level:2},{value:"Installation",id:"installation",level:2},{value:"Using npm",id:"using-npm",level:3},{value:"Using yarn",id:"using-yarn",level:3},{value:"Using pnpm",id:"using-pnpm",level:3},{value:"Quick Setup",id:"quick-setup",level:2},{value:"Framework Compatibility",id:"framework-compatibility",level:2},{value:"React",id:"react",level:3},{value:"Next.js Setup",id:"nextjs-setup",level:3},{value:"Vite Setup",id:"vite-setup",level:3},{value:"TypeScript Support",id:"typescript-support",level:2},{value:"Styling and Theming",id:"styling-and-theming",level:2},{value:"Automatic Style Injection",id:"automatic-style-injection",level:3},{value:"Custom Theming",id:"custom-theming",level:3},{value:"CSS Custom Properties",id:"css-custom-properties",level:3},{value:"Bundle Size Optimization",id:"bundle-size-optimization",level:2},{value:"Tree Shaking",id:"tree-shaking",level:3},{value:"Dynamic Imports",id:"dynamic-imports",level:3},{value:"Development Setup",id:"development-setup",level:2},{value:"With Create React App",id:"with-create-react-app",level:3},{value:"With Next.js",id:"with-nextjs",level:3},{value:"With Vite",id:"with-vite",level:3},{value:"Integration with Backend SDKs",id:"integration-with-backend-sdks",level:2},{value:"With Python Backend",id:"with-python-backend",level:3},{value:"With TypeScript Backend",id:"with-typescript-backend",level:3},{value:"Troubleshooting",id:"troubleshooting",level:2},{value:"Common Issues",id:"common-issues",level:3},{value:"Styles Not Loading",id:"styles-not-loading",level:4},{value:"TypeScript Errors",id:"typescript-errors",level:4},{value:"Bundle Size Issues",id:"bundle-size-issues",level:4},{value:"React Version Conflicts",id:"react-version-conflicts",level:4},{value:"Performance Optimization",id:"performance-optimization",level:3},{value:"Lazy Loading",id:"lazy-loading",level:4},{value:"Memoization",id:"memoization",level:4},{value:"Next Steps",id:"next-steps",level:2},{value:"Support Resources",id:"support-resources",level:2}];function c(e){const n={a:"a",code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",ul:"ul",...(0,r.R)(),...e.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.header,{children:(0,t.jsx)(n.h1,{id:"ui-sdk-installation",children:"UI SDK Installation"})}),"\n",(0,t.jsx)(n.p,{children:"The AdMesh UI SDK is a production-ready React + TypeScript component library for displaying product recommendations with integrated tracking, theming, and conversational interface support."}),"\n",(0,t.jsx)(n.h2,{id:"requirements",children:"Requirements"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:(0,t.jsx)(n.strong,{children:"React 18.0 or higher"})}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"TypeScript 4.9 or higher"})," (optional but recommended)"]}),"\n",(0,t.jsx)(n.li,{children:(0,t.jsx)(n.strong,{children:"Node.js 18.0 or higher"})}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"installation",children:"Installation"}),"\n",(0,t.jsx)(n.h3,{id:"using-npm",children:"Using npm"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"npm install admesh-ui-sdk\n"})}),"\n",(0,t.jsx)(n.h3,{id:"using-yarn",children:"Using yarn"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"yarn add admesh-ui-sdk\n"})}),"\n",(0,t.jsx)(n.h3,{id:"using-pnpm",children:"Using pnpm"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"pnpm add admesh-ui-sdk\n"})}),"\n",(0,t.jsx)(n.h2,{id:"quick-setup",children:"Quick Setup"}),"\n",(0,t.jsx)(n.p,{children:"The UI SDK is designed to be completely self-contained with no additional setup required:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-tsx",children:'import React from \'react\';\nimport { AdMeshLayout } from \'admesh-ui-sdk\';\n// No CSS import needed! Styles are auto-injected \u2728\n\nfunction App() {\n  const recommendations = [\n    {\n      title: "HubSpot CRM",\n      reason: "Perfect for remote teams with excellent collaboration features",\n      intent_match_score: 0.92,\n      admesh_link: "https://useadmesh.com/track?ad_id=hubspot-123",\n      ad_id: "hubspot-123",\n      product_id: "hubspot-crm"\n    }\n  ];\n\n  return (\n    <AdMeshLayout\n      recommendations={recommendations}\n      autoLayout={true}\n      onProductClick={(adId, admeshLink) => {\n        window.open(admeshLink, \'_blank\');\n      }}\n    />\n  );\n}\n'})}),"\n",(0,t.jsx)(n.h2,{id:"framework-compatibility",children:"Framework Compatibility"}),"\n",(0,t.jsx)(n.h3,{id:"react",children:"React"}),"\n",(0,t.jsx)(n.p,{children:"The UI SDK is built for React and works with:"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Create React App"})," - Works out of the box"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Next.js"})," - Full SSR/SSG support"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Vite"})," - Fast development and building"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Remix"})," - Server-side rendering support"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Gatsby"})," - Static site generation"]}),"\n"]}),"\n",(0,t.jsx)(n.h3,{id:"nextjs-setup",children:"Next.js Setup"}),"\n",(0,t.jsx)(n.p,{children:"For Next.js applications, no special configuration is needed:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-tsx",children:"// pages/recommendations.tsx or app/recommendations/page.tsx\nimport { AdMeshLayout } from 'admesh-ui-sdk';\n\nexport default function RecommendationsPage() {\n  return (\n    <div>\n      <h1>Product Recommendations</h1>\n      <AdMeshLayout recommendations={recommendations} />\n    </div>\n  );\n}\n"})}),"\n",(0,t.jsx)(n.h3,{id:"vite-setup",children:"Vite Setup"}),"\n",(0,t.jsx)(n.p,{children:"For Vite applications:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-tsx",children:"// src/App.tsx\nimport { AdMeshLayout } from 'admesh-ui-sdk';\n\nfunction App() {\n  return (\n    <div className=\"App\">\n      <AdMeshLayout recommendations={recommendations} />\n    </div>\n  );\n}\n\nexport default App;\n"})}),"\n",(0,t.jsx)(n.h2,{id:"typescript-support",children:"TypeScript Support"}),"\n",(0,t.jsx)(n.p,{children:"The UI SDK includes full TypeScript definitions:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-tsx",children:"import React from 'react';\nimport { \n  AdMeshLayout, \n  AdMeshRecommendation, \n  AdMeshTheme \n} from 'admesh-ui-sdk';\n\ninterface AppProps {\n  recommendations: AdMeshRecommendation[];\n}\n\nconst theme: AdMeshTheme = {\n  mode: 'dark',\n  accentColor: '#8b5cf6'\n};\n\nfunction App({ recommendations }: AppProps) {\n  return (\n    <AdMeshLayout\n      recommendations={recommendations}\n      theme={theme}\n      autoLayout={true}\n    />\n  );\n}\n"})}),"\n",(0,t.jsx)(n.h2,{id:"styling-and-theming",children:"Styling and Theming"}),"\n",(0,t.jsx)(n.h3,{id:"automatic-style-injection",children:"Automatic Style Injection"}),"\n",(0,t.jsx)(n.p,{children:"The UI SDK automatically injects its styles - no manual CSS imports needed:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-tsx",children:"// \u2705 This is all you need\nimport { AdMeshLayout } from 'admesh-ui-sdk';\n\n// \u274c No need for this\n// import 'admesh-ui-sdk/styles.css';\n"})}),"\n",(0,t.jsx)(n.h3,{id:"custom-theming",children:"Custom Theming"}),"\n",(0,t.jsx)(n.p,{children:"Customize the appearance with theme props:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-tsx",children:"import { AdMeshLayout } from 'admesh-ui-sdk';\n\nconst customTheme = {\n  mode: 'dark', // 'light' | 'dark'\n  accentColor: '#3b82f6', // Custom accent color\n};\n\n<AdMeshLayout\n  recommendations={recommendations}\n  theme={customTheme}\n/>\n"})}),"\n",(0,t.jsx)(n.h3,{id:"css-custom-properties",children:"CSS Custom Properties"}),"\n",(0,t.jsx)(n.p,{children:"For advanced customization, override CSS custom properties:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-css",children:"/* Your global CSS */\n:root {\n  --admesh-primary-color: #8b5cf6;\n  --admesh-border-radius: 12px;\n  --admesh-font-family: 'Inter', sans-serif;\n}\n\n[data-theme='dark'] {\n  --admesh-primary-color: #a78bfa;\n  --admesh-background-color: #1f2937;\n  --admesh-text-color: #f9fafb;\n}\n"})}),"\n",(0,t.jsx)(n.h2,{id:"bundle-size-optimization",children:"Bundle Size Optimization"}),"\n",(0,t.jsx)(n.h3,{id:"tree-shaking",children:"Tree Shaking"}),"\n",(0,t.jsx)(n.p,{children:"Import only the components you need:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-tsx",children:"// \u2705 Tree-shakable imports\nimport { AdMeshLayout, AdMeshProductCard } from 'admesh-ui-sdk';\n\n// \u274c Imports entire library\nimport * as AdMesh from 'admesh-ui-sdk';\n"})}),"\n",(0,t.jsx)(n.h3,{id:"dynamic-imports",children:"Dynamic Imports"}),"\n",(0,t.jsx)(n.p,{children:"For code splitting, use dynamic imports:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-tsx",children:"import React, { lazy, Suspense } from 'react';\n\nconst AdMeshLayout = lazy(() => \n  import('admesh-ui-sdk').then(module => ({ \n    default: module.AdMeshLayout \n  }))\n);\n\nfunction App() {\n  return (\n    <Suspense fallback={<div>Loading recommendations...</div>}>\n      <AdMeshLayout recommendations={recommendations} />\n    </Suspense>\n  );\n}\n"})}),"\n",(0,t.jsx)(n.h2,{id:"development-setup",children:"Development Setup"}),"\n",(0,t.jsx)(n.h3,{id:"with-create-react-app",children:"With Create React App"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"npx create-react-app my-app --template typescript\ncd my-app\nnpm install admesh-ui-sdk\n"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-tsx",children:"// src/App.tsx\nimport React from 'react';\nimport { AdMeshLayout } from 'admesh-ui-sdk';\n\nfunction App() {\n  return (\n    <div className=\"App\">\n      <AdMeshLayout recommendations={[]} />\n    </div>\n  );\n}\n\nexport default App;\n"})}),"\n",(0,t.jsx)(n.h3,{id:"with-nextjs",children:"With Next.js"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"npx create-next-app@latest my-app --typescript\ncd my-app\nnpm install admesh-ui-sdk\n"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-tsx",children:"// pages/index.tsx\nimport { AdMeshLayout } from 'admesh-ui-sdk';\n\nexport default function Home() {\n  return (\n    <main>\n      <h1>Welcome to AdMesh</h1>\n      <AdMeshLayout recommendations={[]} />\n    </main>\n  );\n}\n"})}),"\n",(0,t.jsx)(n.h3,{id:"with-vite",children:"With Vite"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"npm create vite@latest my-app -- --template react-ts\ncd my-app\nnpm install admesh-ui-sdk\n"})}),"\n",(0,t.jsx)(n.h2,{id:"integration-with-backend-sdks",children:"Integration with Backend SDKs"}),"\n",(0,t.jsx)(n.p,{children:"Combine with AdMesh backend SDKs for full functionality:"}),"\n",(0,t.jsx)(n.h3,{id:"with-python-backend",children:"With Python Backend"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-python",children:"# backend/api.py\nfrom admesh import Admesh\nfrom flask import Flask, jsonify\n\napp = Flask(__name__)\nclient = Admesh()\n\<EMAIL>('/api/recommendations')\ndef get_recommendations():\n    response = client.recommend.get_recommendations(\n        query=request.args.get('query'),\n        format='auto'\n    )\n    return jsonify(response.response.recommendations)\n"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-tsx",children:"// frontend/src/App.tsx\nimport React, { useState, useEffect } from 'react';\nimport { AdMeshLayout } from 'admesh-ui-sdk';\n\nfunction App() {\n  const [recommendations, setRecommendations] = useState([]);\n\n  useEffect(() => {\n    fetch('/api/recommendations?query=CRM software')\n      .then(res => res.json())\n      .then(setRecommendations);\n  }, []);\n\n  return <AdMeshLayout recommendations={recommendations} />;\n}\n"})}),"\n",(0,t.jsx)(n.h3,{id:"with-typescript-backend",children:"With TypeScript Backend"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-typescript",children:"// backend/api.ts\nimport Admesh from 'admesh';\nimport express from 'express';\n\nconst app = express();\nconst client = new Admesh();\n\napp.get('/api/recommendations', async (req, res) => {\n  const response = await client.recommend.getRecommendations({\n    query: req.query.query as string,\n    format: 'auto'\n  });\n  \n  res.json(response.response?.recommendations || []);\n});\n"})}),"\n",(0,t.jsx)(n.h2,{id:"troubleshooting",children:"Troubleshooting"}),"\n",(0,t.jsx)(n.h3,{id:"common-issues",children:"Common Issues"}),"\n",(0,t.jsx)(n.h4,{id:"styles-not-loading",children:"Styles Not Loading"}),"\n",(0,t.jsx)(n.p,{children:"If styles aren't appearing:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-tsx",children:"// Make sure you're importing from the correct package\nimport { AdMeshLayout } from 'admesh-ui-sdk'; // \u2705 Correct\n\n// Not from a different package\nimport { AdMeshLayout } from 'admesh-ui-sdk'; // \u274c Wrong package\n"})}),"\n",(0,t.jsx)(n.h4,{id:"typescript-errors",children:"TypeScript Errors"}),"\n",(0,t.jsx)(n.p,{children:"If you get TypeScript errors:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"# Make sure you have the latest version\nnpm update admesh-ui-sdk\n\n# Check your TypeScript version\nnpx tsc --version\n# Should be 4.9 or higher\n"})}),"\n",(0,t.jsx)(n.h4,{id:"bundle-size-issues",children:"Bundle Size Issues"}),"\n",(0,t.jsx)(n.p,{children:"If your bundle is too large:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-tsx",children:"// Use specific imports instead of barrel imports\nimport { AdMeshLayout } from 'admesh-ui-sdk/components/AdMeshLayout';\nimport { AdMeshProductCard } from 'admesh-ui-sdk/components/AdMeshProductCard';\n"})}),"\n",(0,t.jsx)(n.h4,{id:"react-version-conflicts",children:"React Version Conflicts"}),"\n",(0,t.jsx)(n.p,{children:"If you get React version warnings:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"# Check your React version\nnpm list react\n\n# Update React if needed\nnpm update react react-dom\n"})}),"\n",(0,t.jsx)(n.h3,{id:"performance-optimization",children:"Performance Optimization"}),"\n",(0,t.jsx)(n.h4,{id:"lazy-loading",children:"Lazy Loading"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-tsx",children:"import React, { lazy, Suspense } from 'react';\n\nconst AdMeshSidebar = lazy(() => import('admesh-ui-sdk/components/AdMeshSidebar'));\n\nfunction App() {\n  return (\n    <Suspense fallback={<div>Loading...</div>}>\n      <AdMeshSidebar recommendations={recommendations} />\n    </Suspense>\n  );\n}\n"})}),"\n",(0,t.jsx)(n.h4,{id:"memoization",children:"Memoization"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-tsx",children:"import React, { memo, useMemo } from 'react';\nimport { AdMeshLayout } from 'admesh-ui-sdk';\n\nconst MemoizedRecommendations = memo(function Recommendations({ recommendations }) {\n  const memoizedRecommendations = useMemo(() => \n    recommendations.filter(rec => rec.intent_match_score > 0.8),\n    [recommendations]\n  );\n\n  return <AdMeshLayout recommendations={memoizedRecommendations} />;\n});\n"})}),"\n",(0,t.jsx)(n.h2,{id:"next-steps",children:"Next Steps"}),"\n",(0,t.jsx)(n.p,{children:"After completing UI SDK installation:"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:(0,t.jsx)(n.a,{href:"/getting-started/overview",children:"Getting Started"})})," - Core concepts and setup"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:(0,t.jsx)(n.a,{href:"/python-sdk/installation",children:"Python SDK"})})," - Backend SDK integration"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:(0,t.jsx)(n.a,{href:"/typescript-sdk/installation",children:"TypeScript SDK"})})," - Node.js SDK integration"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:(0,t.jsx)(n.a,{href:"/examples/ai-assistant",children:"Examples"})})," - Implementation examples"]}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"support-resources",children:"Support Resources"}),"\n",(0,t.jsx)(n.p,{children:"For installation assistance:"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"GitHub Issues"}),": ",(0,t.jsx)(n.a,{href:"https://github.com/GouniManikumar12/admesh-ui-sdk/issues",children:"Report issues"})]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Support"}),": ",(0,t.jsx)(n.a,{href:"mailto:<EMAIL>",children:"<EMAIL>"})]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Documentation"}),": Complete technical documentation"]}),"\n"]})]})}function m(e={}){const{wrapper:n}={...(0,r.R)(),...e.components};return n?(0,t.jsx)(n,{...e,children:(0,t.jsx)(c,{...e})}):c(e)}}}]);