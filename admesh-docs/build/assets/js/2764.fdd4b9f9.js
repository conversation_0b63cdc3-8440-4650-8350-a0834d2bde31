(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[2764],{2764:(t,e,i)=>{"use strict";i.d(e,{diagram:()=>X});var n=i(4431),r=i(5395),o=i(6627),s=i(2107),a=i(165),h=i(3457),l=i(7),c=i(8457),g=i(8041),u=i(5263),d=function(){var t=(0,s.K2)((function(t,e,i,n){for(i=i||{},n=t.length;n--;i[t[n]]=e);return i}),"o"),e=[1,4],i=[1,13],n=[1,12],r=[1,15],o=[1,16],a=[1,20],h=[1,19],l=[6,7,8],c=[1,26],g=[1,24],u=[1,25],d=[6,7,11],p=[1,6,13,15,16,19,22],f=[1,33],y=[1,34],E=[1,6,7,11,13,15,16,19,22],_={trace:(0,s.K2)((function(){}),"trace"),yy:{},symbols_:{error:2,start:3,mindMap:4,spaceLines:5,SPACELINE:6,NL:7,MINDMAP:8,document:9,stop:10,EOF:11,statement:12,SPACELIST:13,node:14,ICON:15,CLASS:16,nodeWithId:17,nodeWithoutId:18,NODE_DSTART:19,NODE_DESCR:20,NODE_DEND:21,NODE_ID:22,$accept:0,$end:1},terminals_:{2:"error",6:"SPACELINE",7:"NL",8:"MINDMAP",11:"EOF",13:"SPACELIST",15:"ICON",16:"CLASS",19:"NODE_DSTART",20:"NODE_DESCR",21:"NODE_DEND",22:"NODE_ID"},productions_:[0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,2],[12,2],[12,2],[12,1],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[18,3],[17,1],[17,4]],performAction:(0,s.K2)((function(t,e,i,n,r,o,s){var a=o.length-1;switch(r){case 6:case 7:return n;case 8:n.getLogger().trace("Stop NL ");break;case 9:n.getLogger().trace("Stop EOF ");break;case 11:n.getLogger().trace("Stop NL2 ");break;case 12:n.getLogger().trace("Stop EOF2 ");break;case 15:n.getLogger().info("Node: ",o[a].id),n.addNode(o[a-1].length,o[a].id,o[a].descr,o[a].type);break;case 16:n.getLogger().trace("Icon: ",o[a]),n.decorateNode({icon:o[a]});break;case 17:case 21:n.decorateNode({class:o[a]});break;case 18:n.getLogger().trace("SPACELIST");break;case 19:n.getLogger().trace("Node: ",o[a].id),n.addNode(0,o[a].id,o[a].descr,o[a].type);break;case 20:n.decorateNode({icon:o[a]});break;case 25:n.getLogger().trace("node found ..",o[a-2]),this.$={id:o[a-1],descr:o[a-1],type:n.getType(o[a-2],o[a])};break;case 26:this.$={id:o[a],descr:o[a],type:n.nodeType.DEFAULT};break;case 27:n.getLogger().trace("node found ..",o[a-3]),this.$={id:o[a-3],descr:o[a-1],type:n.getType(o[a-2],o[a])}}}),"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],8:e},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:e},{6:i,7:[1,10],9:9,12:11,13:n,14:14,15:r,16:o,17:17,18:18,19:a,22:h},t(l,[2,3]),{1:[2,2]},t(l,[2,4]),t(l,[2,5]),{1:[2,6],6:i,12:21,13:n,14:14,15:r,16:o,17:17,18:18,19:a,22:h},{6:i,9:22,12:11,13:n,14:14,15:r,16:o,17:17,18:18,19:a,22:h},{6:c,7:g,10:23,11:u},t(d,[2,22],{17:17,18:18,14:27,15:[1,28],16:[1,29],19:a,22:h}),t(d,[2,18]),t(d,[2,19]),t(d,[2,20]),t(d,[2,21]),t(d,[2,23]),t(d,[2,24]),t(d,[2,26],{19:[1,30]}),{20:[1,31]},{6:c,7:g,10:32,11:u},{1:[2,7],6:i,12:21,13:n,14:14,15:r,16:o,17:17,18:18,19:a,22:h},t(p,[2,14],{7:f,11:y}),t(E,[2,8]),t(E,[2,9]),t(E,[2,10]),t(d,[2,15]),t(d,[2,16]),t(d,[2,17]),{20:[1,35]},{21:[1,36]},t(p,[2,13],{7:f,11:y}),t(E,[2,11]),t(E,[2,12]),{21:[1,37]},t(d,[2,25]),t(d,[2,27])],defaultActions:{2:[2,1],6:[2,2]},parseError:(0,s.K2)((function(t,e){if(!e.recoverable){var i=new Error(t);throw i.hash=e,i}this.trace(t)}),"parseError"),parse:(0,s.K2)((function(t){var e=this,i=[0],n=[],r=[null],o=[],a=this.table,h="",l=0,c=0,g=0,u=o.slice.call(arguments,1),d=Object.create(this.lexer),p={yy:{}};for(var f in this.yy)Object.prototype.hasOwnProperty.call(this.yy,f)&&(p.yy[f]=this.yy[f]);d.setInput(t,p.yy),p.yy.lexer=d,p.yy.parser=this,void 0===d.yylloc&&(d.yylloc={});var y=d.yylloc;o.push(y);var E=d.options&&d.options.ranges;function _(){var t;return"number"!=typeof(t=n.pop()||d.lex()||1)&&(t instanceof Array&&(t=(n=t).pop()),t=e.symbols_[t]||t),t}"function"==typeof p.yy.parseError?this.parseError=p.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,(0,s.K2)((function(t){i.length=i.length-2*t,r.length=r.length-t,o.length=o.length-t}),"popStack"),(0,s.K2)(_,"lex");for(var m,v,N,A,L,T,O,D,I,w={};;){if(N=i[i.length-1],this.defaultActions[N]?A=this.defaultActions[N]:(null==m&&(m=_()),A=a[N]&&a[N][m]),void 0===A||!A.length||!A[0]){var R="";for(T in I=[],a[N])this.terminals_[T]&&T>2&&I.push("'"+this.terminals_[T]+"'");R=d.showPosition?"Parse error on line "+(l+1)+":\n"+d.showPosition()+"\nExpecting "+I.join(", ")+", got '"+(this.terminals_[m]||m)+"'":"Parse error on line "+(l+1)+": Unexpected "+(1==m?"end of input":"'"+(this.terminals_[m]||m)+"'"),this.parseError(R,{text:d.match,token:this.terminals_[m]||m,line:d.yylineno,loc:y,expected:I})}if(A[0]instanceof Array&&A.length>1)throw new Error("Parse Error: multiple actions possible at state: "+N+", token: "+m);switch(A[0]){case 1:i.push(m),r.push(d.yytext),o.push(d.yylloc),i.push(A[1]),m=null,v?(m=v,v=null):(c=d.yyleng,h=d.yytext,l=d.yylineno,y=d.yylloc,g>0&&g--);break;case 2:if(O=this.productions_[A[1]][1],w.$=r[r.length-O],w._$={first_line:o[o.length-(O||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-(O||1)].first_column,last_column:o[o.length-1].last_column},E&&(w._$.range=[o[o.length-(O||1)].range[0],o[o.length-1].range[1]]),void 0!==(L=this.performAction.apply(w,[h,c,l,p.yy,A[1],r,o].concat(u))))return L;O&&(i=i.slice(0,-1*O*2),r=r.slice(0,-1*O),o=o.slice(0,-1*O)),i.push(this.productions_[A[1]][0]),r.push(w.$),o.push(w._$),D=a[i[i.length-2]][i[i.length-1]],i.push(D);break;case 3:return!0}}return!0}),"parse")},m=function(){return{EOF:1,parseError:(0,s.K2)((function(t,e){if(!this.yy.parser)throw new Error(t);this.yy.parser.parseError(t,e)}),"parseError"),setInput:(0,s.K2)((function(t,e){return this.yy=e||this.yy||{},this._input=t,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this}),"setInput"),input:(0,s.K2)((function(){var t=this._input[0];return this.yytext+=t,this.yyleng++,this.offset++,this.match+=t,this.matched+=t,t.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),t}),"input"),unput:(0,s.K2)((function(t){var e=t.length,i=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-e),this.offset-=e;var n=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),i.length-1&&(this.yylineno-=i.length-1);var r=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:i?(i.length===n.length?this.yylloc.first_column:0)+n[n.length-i.length].length-i[0].length:this.yylloc.first_column-e},this.options.ranges&&(this.yylloc.range=[r[0],r[0]+this.yyleng-e]),this.yyleng=this.yytext.length,this}),"unput"),more:(0,s.K2)((function(){return this._more=!0,this}),"more"),reject:(0,s.K2)((function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}),"reject"),less:(0,s.K2)((function(t){this.unput(this.match.slice(t))}),"less"),pastInput:(0,s.K2)((function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,s.K2)((function(){var t=this.match;return t.length<20&&(t+=this._input.substr(0,20-t.length)),(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,s.K2)((function(){var t=this.pastInput(),e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"}),"showPosition"),test_match:(0,s.K2)((function(t,e){var i,n,r;if(this.options.backtrack_lexer&&(r={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(r.yylloc.range=this.yylloc.range.slice(0))),(n=t[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=n.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:n?n[n.length-1].length-n[n.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length},this.yytext+=t[0],this.match+=t[0],this.matches=t,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(t[0].length),this.matched+=t[0],i=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),i)return i;if(this._backtrack){for(var o in r)this[o]=r[o];return!1}return!1}),"test_match"),next:(0,s.K2)((function(){if(this.done)return this.EOF;var t,e,i,n;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var r=this._currentRules(),o=0;o<r.length;o++)if((i=this._input.match(this.rules[r[o]]))&&(!e||i[0].length>e[0].length)){if(e=i,n=o,this.options.backtrack_lexer){if(!1!==(t=this.test_match(i,r[o])))return t;if(this._backtrack){e=!1;continue}return!1}if(!this.options.flex)break}return e?!1!==(t=this.test_match(e,r[n]))&&t:""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}),"next"),lex:(0,s.K2)((function(){var t=this.next();return t||this.lex()}),"lex"),begin:(0,s.K2)((function(t){this.conditionStack.push(t)}),"begin"),popState:(0,s.K2)((function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]}),"popState"),_currentRules:(0,s.K2)((function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules}),"_currentRules"),topState:(0,s.K2)((function(t){return(t=this.conditionStack.length-1-Math.abs(t||0))>=0?this.conditionStack[t]:"INITIAL"}),"topState"),pushState:(0,s.K2)((function(t){this.begin(t)}),"pushState"),stateStackSize:(0,s.K2)((function(){return this.conditionStack.length}),"stateStackSize"),options:{"case-insensitive":!0},performAction:(0,s.K2)((function(t,e,i,n){switch(i){case 0:return t.getLogger().trace("Found comment",e.yytext),6;case 1:return 8;case 2:this.begin("CLASS");break;case 3:return this.popState(),16;case 4:case 23:case 26:this.popState();break;case 5:t.getLogger().trace("Begin icon"),this.begin("ICON");break;case 6:return t.getLogger().trace("SPACELINE"),6;case 7:return 7;case 8:return 15;case 9:t.getLogger().trace("end icon"),this.popState();break;case 10:return t.getLogger().trace("Exploding node"),this.begin("NODE"),19;case 11:return t.getLogger().trace("Cloud"),this.begin("NODE"),19;case 12:return t.getLogger().trace("Explosion Bang"),this.begin("NODE"),19;case 13:return t.getLogger().trace("Cloud Bang"),this.begin("NODE"),19;case 14:case 15:case 16:case 17:return this.begin("NODE"),19;case 18:return 13;case 19:return 22;case 20:return 11;case 21:this.begin("NSTR2");break;case 22:return"NODE_DESCR";case 24:t.getLogger().trace("Starting NSTR"),this.begin("NSTR");break;case 25:return t.getLogger().trace("description:",e.yytext),"NODE_DESCR";case 27:return this.popState(),t.getLogger().trace("node end ))"),"NODE_DEND";case 28:return this.popState(),t.getLogger().trace("node end )"),"NODE_DEND";case 29:return this.popState(),t.getLogger().trace("node end ...",e.yytext),"NODE_DEND";case 30:case 33:case 34:return this.popState(),t.getLogger().trace("node end (("),"NODE_DEND";case 31:case 32:return this.popState(),t.getLogger().trace("node end (-"),"NODE_DEND";case 35:case 36:return t.getLogger().trace("Long description:",e.yytext),20}}),"anonymous"),rules:[/^(?:\s*%%.*)/i,/^(?:mindmap\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\n)/i,/^(?:::icon\()/i,/^(?:[\s]+[\n])/i,/^(?:[\n]+)/i,/^(?:[^\)]+)/i,/^(?:\))/i,/^(?:-\))/i,/^(?:\(-)/i,/^(?:\)\))/i,/^(?:\))/i,/^(?:\(\()/i,/^(?:\{\{)/i,/^(?:\()/i,/^(?:\[)/i,/^(?:[\s]+)/i,/^(?:[^\(\[\n\)\{\}]+)/i,/^(?:$)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:[^"]+)/i,/^(?:["])/i,/^(?:[\)]\))/i,/^(?:[\)])/i,/^(?:[\]])/i,/^(?:\}\})/i,/^(?:\(-)/i,/^(?:-\))/i,/^(?:\(\()/i,/^(?:\()/i,/^(?:[^\)\]\(\}]+)/i,/^(?:.+(?!\(\())/i],conditions:{CLASS:{rules:[3,4],inclusive:!1},ICON:{rules:[8,9],inclusive:!1},NSTR2:{rules:[22,23],inclusive:!1},NSTR:{rules:[25,26],inclusive:!1},NODE:{rules:[21,24,27,28,29,30,31,32,33,34,35,36],inclusive:!1},INITIAL:{rules:[0,1,2,5,6,7,10,11,12,13,14,15,16,17,18,19,20],inclusive:!0}}}}();function v(){this.yy={}}return _.lexer=m,(0,s.K2)(v,"Parser"),v.prototype=_,_.Parser=v,new v}();d.parser=d;var p=d,f=[],y=0,E={},_=(0,s.K2)((()=>{f=[],y=0,E={}}),"clear"),m=(0,s.K2)((function(t){for(let e=f.length-1;e>=0;e--)if(f[e].level<t)return f[e];return null}),"getParent"),v=(0,s.K2)((()=>f.length>0?f[0]:null),"getMindmap"),N=(0,s.K2)(((t,e,i,n)=>{s.Rm.info("addNode",t,e,i,n);const r=(0,s.D7)();let o=r.mindmap?.padding??s.UI.mindmap.padding;switch(n){case A.ROUNDED_RECT:case A.RECT:case A.HEXAGON:o*=2}const a={id:y++,nodeId:(0,s.jZ)(e,r),level:t,descr:(0,s.jZ)(i,r),type:n,children:[],width:r.mindmap?.maxNodeWidth??s.UI.mindmap.maxNodeWidth,padding:o},h=m(t);if(h)h.children.push(a),f.push(a);else{if(0!==f.length)throw new Error('There can be only one root. No parent could be found for ("'+a.descr+'")');f.push(a)}}),"addNode"),A={DEFAULT:0,NO_BORDER:0,ROUNDED_RECT:1,RECT:2,CIRCLE:3,CLOUD:4,BANG:5,HEXAGON:6},L={clear:_,addNode:N,getMindmap:v,nodeType:A,getType:(0,s.K2)(((t,e)=>{switch(s.Rm.debug("In get type",t,e),t){case"[":return A.RECT;case"(":return")"===e?A.ROUNDED_RECT:A.CLOUD;case"((":return A.CIRCLE;case")":return A.CLOUD;case"))":return A.BANG;case"{{":return A.HEXAGON;default:return A.DEFAULT}}),"getType"),setElementForId:(0,s.K2)(((t,e)=>{E[t]=e}),"setElementForId"),decorateNode:(0,s.K2)((t=>{if(!t)return;const e=(0,s.D7)(),i=f[f.length-1];t.icon&&(i.icon=(0,s.jZ)(t.icon,e)),t.class&&(i.class=(0,s.jZ)(t.class,e))}),"decorateNode"),type2Str:(0,s.K2)((t=>{switch(t){case A.DEFAULT:return"no-border";case A.RECT:return"rect";case A.ROUNDED_RECT:return"rounded-rect";case A.CIRCLE:return"circle";case A.CLOUD:return"cloud";case A.BANG:return"bang";case A.HEXAGON:return"hexgon";default:return"no-border"}}),"type2Str"),getLogger:(0,s.K2)((()=>s.Rm),"getLogger"),getElementById:(0,s.K2)((t=>E[t]),"getElementById")},T=(0,s.K2)((function(t,e,i,n){e.append("path").attr("id","node-"+i.id).attr("class","node-bkg node-"+t.type2Str(i.type)).attr("d",`M0 ${i.height-5} v${10-i.height} q0,-5 5,-5 h${i.width-10} q5,0 5,5 v${i.height-5} H0 Z`),e.append("line").attr("class","node-line-"+n).attr("x1",0).attr("y1",i.height).attr("x2",i.width).attr("y2",i.height)}),"defaultBkg"),O=(0,s.K2)((function(t,e,i){e.append("rect").attr("id","node-"+i.id).attr("class","node-bkg node-"+t.type2Str(i.type)).attr("height",i.height).attr("width",i.width)}),"rectBkg"),D=(0,s.K2)((function(t,e,i){const n=i.width,r=i.height,o=.15*n,s=.25*n,a=.35*n,h=.2*n;e.append("path").attr("id","node-"+i.id).attr("class","node-bkg node-"+t.type2Str(i.type)).attr("d",`M0 0 a${o},${o} 0 0,1 ${.25*n},${-1*n*.1}\n      a${a},${a} 1 0,1 ${.4*n},${-1*n*.1}\n      a${s},${s} 1 0,1 ${.35*n},${1*n*.2}\n\n      a${o},${o} 1 0,1 ${.15*n},${1*r*.35}\n      a${h},${h} 1 0,1 ${-1*n*.15},${1*r*.65}\n\n      a${s},${o} 1 0,1 ${-1*n*.25},${.15*n}\n      a${a},${a} 1 0,1 ${-1*n*.5},0\n      a${o},${o} 1 0,1 ${-1*n*.25},${-1*n*.15}\n\n      a${o},${o} 1 0,1 ${-1*n*.1},${-1*r*.35}\n      a${h},${h} 1 0,1 ${.1*n},${-1*r*.65}\n\n    H0 V0 Z`)}),"cloudBkg"),I=(0,s.K2)((function(t,e,i){const n=i.width,r=i.height,o=.15*n;e.append("path").attr("id","node-"+i.id).attr("class","node-bkg node-"+t.type2Str(i.type)).attr("d",`M0 0 a${o},${o} 1 0,0 ${.25*n},${-1*r*.1}\n      a${o},${o} 1 0,0 ${.25*n},0\n      a${o},${o} 1 0,0 ${.25*n},0\n      a${o},${o} 1 0,0 ${.25*n},${1*r*.1}\n\n      a${o},${o} 1 0,0 ${.15*n},${1*r*.33}\n      a${.8*o},${.8*o} 1 0,0 0,${1*r*.34}\n      a${o},${o} 1 0,0 ${-1*n*.15},${1*r*.33}\n\n      a${o},${o} 1 0,0 ${-1*n*.25},${.15*r}\n      a${o},${o} 1 0,0 ${-1*n*.25},0\n      a${o},${o} 1 0,0 ${-1*n*.25},0\n      a${o},${o} 1 0,0 ${-1*n*.25},${-1*r*.15}\n\n      a${o},${o} 1 0,0 ${-1*n*.1},${-1*r*.33}\n      a${.8*o},${.8*o} 1 0,0 0,${-1*r*.34}\n      a${o},${o} 1 0,0 ${.1*n},${-1*r*.33}\n\n    H0 V0 Z`)}),"bangBkg"),w=(0,s.K2)((function(t,e,i){e.append("circle").attr("id","node-"+i.id).attr("class","node-bkg node-"+t.type2Str(i.type)).attr("r",i.width/2)}),"circleBkg");function R(t,e,i,n,r){return t.insert("polygon",":first-child").attr("points",n.map((function(t){return t.x+","+t.y})).join(" ")).attr("transform","translate("+(r.width-e)/2+", "+i+")")}(0,s.K2)(R,"insertPolygonShape");var C=(0,s.K2)((function(t,e,i){const n=i.height,r=n/4,o=i.width-i.padding+2*r;R(e,o,n,[{x:r,y:0},{x:o-r,y:0},{x:o,y:-n/2},{x:o-r,y:-n},{x:r,y:-n},{x:0,y:-n/2}],i)}),"hexagonBkg"),M=(0,s.K2)((function(t,e,i){e.append("rect").attr("id","node-"+i.id).attr("class","node-bkg node-"+t.type2Str(i.type)).attr("height",i.height).attr("rx",i.padding).attr("ry",i.padding).attr("width",i.width)}),"roundedRectBkg"),x=(0,s.K2)((async function(t,e,i,o,s){const a=s.htmlLabels,h=o%11,l=e.append("g");i.section=h;let c="section-"+h;h<0&&(c+=" section-root"),l.attr("class",(i.class?i.class+" ":"")+"mindmap-node "+c);const g=l.append("g"),u=l.append("g"),d=i.descr.replace(/(<br\/*>)/g,"\n");await(0,n.GZ)(u,d,{useHtmlLabels:a,width:i.width,classes:"mindmap-node-label"},s),a||u.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle");const p=u.node().getBBox(),[f]=(0,r.I5)(s.fontSize);if(i.height=p.height+1.1*f*.5+i.padding,i.width=p.width+2*i.padding,i.icon)if(i.type===t.nodeType.CIRCLE){i.height+=50,i.width+=50;l.append("foreignObject").attr("height","50px").attr("width",i.width).attr("style","text-align: center;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+h+" "+i.icon),u.attr("transform","translate("+i.width/2+", "+(i.height/2-1.5*i.padding)+")")}else{i.width+=50;const t=i.height;i.height=Math.max(t,60);const e=Math.abs(i.height-t);l.append("foreignObject").attr("width","60px").attr("height",i.height).attr("style","text-align: center;margin-top:"+e/2+"px;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+h+" "+i.icon),u.attr("transform","translate("+(25+i.width/2)+", "+(e/2+i.padding/2)+")")}else if(a){const t=(i.width-p.width)/2,e=(i.height-p.height)/2;u.attr("transform","translate("+t+", "+e+")")}else{const t=i.width/2,e=i.padding/2;u.attr("transform","translate("+t+", "+e+")")}switch(i.type){case t.nodeType.DEFAULT:T(t,g,i,h);break;case t.nodeType.ROUNDED_RECT:M(t,g,i,h);break;case t.nodeType.RECT:O(t,g,i,h);break;case t.nodeType.CIRCLE:g.attr("transform","translate("+i.width/2+", "+ +i.height/2+")"),w(t,g,i,h);break;case t.nodeType.CLOUD:D(t,g,i,h);break;case t.nodeType.BANG:I(t,g,i,h);break;case t.nodeType.HEXAGON:C(t,g,i,h)}return t.setElementForId(i.id,l),i.height}),"drawNode"),G=(0,s.K2)((function(t,e){const i=t.getElementById(e.id),n=e.x||0,r=e.y||0;i.attr("transform","translate("+n+","+r+")")}),"positionNode");async function S(t,e,i,n,r){await x(t,e,i,n,r),i.children&&await Promise.all(i.children.map(((i,o)=>S(t,e,i,n<0?o:n,r))))}function b(t,e){e.edges().map(((e,i)=>{const n=e.data();if(e[0]._private.bodyBounds){const r=e[0]._private.rscratch;s.Rm.trace("Edge: ",i,n),t.insert("path").attr("d",`M ${r.startX},${r.startY} L ${r.midX},${r.midY} L${r.endX},${r.endY} `).attr("class","edge section-edge-"+n.section+" edge-depth-"+n.depth)}}))}function F(t,e,i,n){e.add({group:"nodes",data:{id:t.id.toString(),labelText:t.descr,height:t.height,width:t.width,level:n,nodeId:t.id,padding:t.padding,type:t.type},position:{x:t.x,y:t.y}}),t.children&&t.children.forEach((r=>{F(r,e,i,n+1),e.add({group:"edges",data:{id:`${t.id}_${r.id}`,source:t.id,target:r.id,depth:n,section:r.section}})}))}function P(t,e){return new Promise((i=>{const n=(0,l.Ltv)("body").append("div").attr("id","cy").attr("style","display:none"),r=(0,a.A)({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"bezier"}}]});n.remove(),F(t,r,e,0),r.nodes().forEach((function(t){t.layoutDimensions=()=>{const e=t.data();return{w:e.width,h:e.height}}})),r.layout({name:"cose-bilkent",quality:"proof",styleEnabled:!1,animate:!1}).run(),r.ready((t=>{s.Rm.info("Ready",t),i(r)}))}))}function U(t,e){e.nodes().map(((e,i)=>{const n=e.data();n.x=e.position().x,n.y=e.position().y,G(t,n);const r=t.getElementById(n.nodeId);s.Rm.info("Id:",i,"Position: (",e.position().x,", ",e.position().y,")",n),r.attr("transform",`translate(${e.position().x-n.width/2}, ${e.position().y-n.height/2})`),r.attr("attr",`apa-${i})`)}))}a.A.use(h),(0,s.K2)(S,"drawNodes"),(0,s.K2)(b,"drawEdges"),(0,s.K2)(F,"addNodes"),(0,s.K2)(P,"layoutMindmap"),(0,s.K2)(U,"positionNodes");var Y={draw:(0,s.K2)((async(t,e,i,n)=>{s.Rm.debug("Rendering mindmap diagram\n"+t);const r=n.db,a=r.getMindmap();if(!a)return;const h=(0,s.D7)();h.htmlLabels=!1;const l=(0,o.D)(e),c=l.append("g");c.attr("class","mindmap-edges");const g=l.append("g");g.attr("class","mindmap-nodes"),await S(r,g,a,-1,h);const u=await P(a,h);b(c,u),U(r,u),(0,s.ot)(void 0,l,h.mindmap?.padding??s.UI.mindmap.padding,h.mindmap?.useMaxWidth??s.UI.mindmap.useMaxWidth)}),"draw")},k=(0,s.K2)((t=>{let e="";for(let i=0;i<t.THEME_COLOR_LIMIT;i++)t["lineColor"+i]=t["lineColor"+i]||t["cScaleInv"+i],(0,c.A)(t["lineColor"+i])?t["lineColor"+i]=(0,g.A)(t["lineColor"+i],20):t["lineColor"+i]=(0,u.A)(t["lineColor"+i],20);for(let i=0;i<t.THEME_COLOR_LIMIT;i++){const n=""+(17-3*i);e+=`\n    .section-${i-1} rect, .section-${i-1} path, .section-${i-1} circle, .section-${i-1} polygon, .section-${i-1} path  {\n      fill: ${t["cScale"+i]};\n    }\n    .section-${i-1} text {\n     fill: ${t["cScaleLabel"+i]};\n    }\n    .node-icon-${i-1} {\n      font-size: 40px;\n      color: ${t["cScaleLabel"+i]};\n    }\n    .section-edge-${i-1}{\n      stroke: ${t["cScale"+i]};\n    }\n    .edge-depth-${i-1}{\n      stroke-width: ${n};\n    }\n    .section-${i-1} line {\n      stroke: ${t["cScaleInv"+i]} ;\n      stroke-width: 3;\n    }\n\n    .disabled, .disabled circle, .disabled text {\n      fill: lightgray;\n    }\n    .disabled text {\n      fill: #efefef;\n    }\n    `}return e}),"genSections"),X={db:L,renderer:Y,parser:p,styles:(0,s.K2)((t=>`\n  .edge {\n    stroke-width: 3;\n  }\n  ${k(t)}\n  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {\n    fill: ${t.git0};\n  }\n  .section-root text {\n    fill: ${t.gitBranchLabel0};\n  }\n  .icon-container {\n    height:100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .edge {\n    fill: none;\n  }\n  .mindmap-node-label {\n    dy: 1em;\n    alignment-baseline: middle;\n    text-anchor: middle;\n    dominant-baseline: middle;\n    text-align: center;\n  }\n`),"getStyles")}},3143:function(t){var e;e=function(){return function(t){var e={};function i(n){if(e[n])return e[n].exports;var r=e[n]={i:n,l:!1,exports:{}};return t[n].call(r.exports,r,r.exports,i),r.l=!0,r.exports}return i.m=t,i.c=e,i.i=function(t){return t},i.d=function(t,e,n){i.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:n})},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=26)}([function(t,e,i){"use strict";function n(){}n.QUALITY=1,n.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,n.DEFAULT_INCREMENTAL=!1,n.DEFAULT_ANIMATION_ON_LAYOUT=!0,n.DEFAULT_ANIMATION_DURING_LAYOUT=!1,n.DEFAULT_ANIMATION_PERIOD=50,n.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,n.DEFAULT_GRAPH_MARGIN=15,n.NODE_DIMENSIONS_INCLUDE_LABELS=!1,n.SIMPLE_NODE_SIZE=40,n.SIMPLE_NODE_HALF_SIZE=n.SIMPLE_NODE_SIZE/2,n.EMPTY_COMPOUND_NODE_SIZE=40,n.MIN_EDGE_LENGTH=1,n.WORLD_BOUNDARY=1e6,n.INITIAL_WORLD_BOUNDARY=n.WORLD_BOUNDARY/1e3,n.WORLD_CENTER_X=1200,n.WORLD_CENTER_Y=900,t.exports=n},function(t,e,i){"use strict";var n=i(2),r=i(8),o=i(9);function s(t,e,i){n.call(this,i),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=i,this.bendpoints=[],this.source=t,this.target=e}for(var a in s.prototype=Object.create(n.prototype),n)s[a]=n[a];s.prototype.getSource=function(){return this.source},s.prototype.getTarget=function(){return this.target},s.prototype.isInterGraph=function(){return this.isInterGraph},s.prototype.getLength=function(){return this.length},s.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},s.prototype.getBendpoints=function(){return this.bendpoints},s.prototype.getLca=function(){return this.lca},s.prototype.getSourceInLca=function(){return this.sourceInLca},s.prototype.getTargetInLca=function(){return this.targetInLca},s.prototype.getOtherEnd=function(t){if(this.source===t)return this.target;if(this.target===t)return this.source;throw"Node is not incident with this edge"},s.prototype.getOtherEndInGraph=function(t,e){for(var i=this.getOtherEnd(t),n=e.getGraphManager().getRoot();;){if(i.getOwner()==e)return i;if(i.getOwner()==n)break;i=i.getOwner().getParent()}return null},s.prototype.updateLength=function(){var t=new Array(4);this.isOverlapingSourceAndTarget=r.getIntersection(this.target.getRect(),this.source.getRect(),t),this.isOverlapingSourceAndTarget||(this.lengthX=t[0]-t[2],this.lengthY=t[1]-t[3],Math.abs(this.lengthX)<1&&(this.lengthX=o.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=o.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},s.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=o.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=o.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},t.exports=s},function(t,e,i){"use strict";t.exports=function(t){this.vGraphObject=t}},function(t,e,i){"use strict";var n=i(2),r=i(10),o=i(13),s=i(0),a=i(16),h=i(4);function l(t,e,i,s){null==i&&null==s&&(s=e),n.call(this,s),null!=t.graphManager&&(t=t.graphManager),this.estimatedSize=r.MIN_VALUE,this.inclusionTreeDepth=r.MAX_VALUE,this.vGraphObject=s,this.edges=[],this.graphManager=t,this.rect=null!=i&&null!=e?new o(e.x,e.y,i.width,i.height):new o}for(var c in l.prototype=Object.create(n.prototype),n)l[c]=n[c];l.prototype.getEdges=function(){return this.edges},l.prototype.getChild=function(){return this.child},l.prototype.getOwner=function(){return this.owner},l.prototype.getWidth=function(){return this.rect.width},l.prototype.setWidth=function(t){this.rect.width=t},l.prototype.getHeight=function(){return this.rect.height},l.prototype.setHeight=function(t){this.rect.height=t},l.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},l.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},l.prototype.getCenter=function(){return new h(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},l.prototype.getLocation=function(){return new h(this.rect.x,this.rect.y)},l.prototype.getRect=function(){return this.rect},l.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},l.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},l.prototype.setRect=function(t,e){this.rect.x=t.x,this.rect.y=t.y,this.rect.width=e.width,this.rect.height=e.height},l.prototype.setCenter=function(t,e){this.rect.x=t-this.rect.width/2,this.rect.y=e-this.rect.height/2},l.prototype.setLocation=function(t,e){this.rect.x=t,this.rect.y=e},l.prototype.moveBy=function(t,e){this.rect.x+=t,this.rect.y+=e},l.prototype.getEdgeListToNode=function(t){var e=[],i=this;return i.edges.forEach((function(n){if(n.target==t){if(n.source!=i)throw"Incorrect edge source!";e.push(n)}})),e},l.prototype.getEdgesBetween=function(t){var e=[],i=this;return i.edges.forEach((function(n){if(n.source!=i&&n.target!=i)throw"Incorrect edge source and/or target";n.target!=t&&n.source!=t||e.push(n)})),e},l.prototype.getNeighborsList=function(){var t=new Set,e=this;return e.edges.forEach((function(i){if(i.source==e)t.add(i.target);else{if(i.target!=e)throw"Incorrect incidency!";t.add(i.source)}})),t},l.prototype.withChildren=function(){var t=new Set;if(t.add(this),null!=this.child)for(var e=this.child.getNodes(),i=0;i<e.length;i++)e[i].withChildren().forEach((function(e){t.add(e)}));return t},l.prototype.getNoOfChildren=function(){var t=0;if(null==this.child)t=1;else for(var e=this.child.getNodes(),i=0;i<e.length;i++)t+=e[i].getNoOfChildren();return 0==t&&(t=1),t},l.prototype.getEstimatedSize=function(){if(this.estimatedSize==r.MIN_VALUE)throw"assert failed";return this.estimatedSize},l.prototype.calcEstimatedSize=function(){return null==this.child?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},l.prototype.scatter=function(){var t,e,i=-s.INITIAL_WORLD_BOUNDARY,n=s.INITIAL_WORLD_BOUNDARY;t=s.WORLD_CENTER_X+a.nextDouble()*(n-i)+i;var r=-s.INITIAL_WORLD_BOUNDARY,o=s.INITIAL_WORLD_BOUNDARY;e=s.WORLD_CENTER_Y+a.nextDouble()*(o-r)+r,this.rect.x=t,this.rect.y=e},l.prototype.updateBounds=function(){if(null==this.getChild())throw"assert failed";if(0!=this.getChild().getNodes().length){var t=this.getChild();if(t.updateBounds(!0),this.rect.x=t.getLeft(),this.rect.y=t.getTop(),this.setWidth(t.getRight()-t.getLeft()),this.setHeight(t.getBottom()-t.getTop()),s.NODE_DIMENSIONS_INCLUDE_LABELS){var e=t.getRight()-t.getLeft(),i=t.getBottom()-t.getTop();this.labelWidth>e&&(this.rect.x-=(this.labelWidth-e)/2,this.setWidth(this.labelWidth)),this.labelHeight>i&&("center"==this.labelPos?this.rect.y-=(this.labelHeight-i)/2:"top"==this.labelPos&&(this.rect.y-=this.labelHeight-i),this.setHeight(this.labelHeight))}}},l.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==r.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},l.prototype.transform=function(t){var e=this.rect.x;e>s.WORLD_BOUNDARY?e=s.WORLD_BOUNDARY:e<-s.WORLD_BOUNDARY&&(e=-s.WORLD_BOUNDARY);var i=this.rect.y;i>s.WORLD_BOUNDARY?i=s.WORLD_BOUNDARY:i<-s.WORLD_BOUNDARY&&(i=-s.WORLD_BOUNDARY);var n=new h(e,i),r=t.inverseTransformPoint(n);this.setLocation(r.x,r.y)},l.prototype.getLeft=function(){return this.rect.x},l.prototype.getRight=function(){return this.rect.x+this.rect.width},l.prototype.getTop=function(){return this.rect.y},l.prototype.getBottom=function(){return this.rect.y+this.rect.height},l.prototype.getParent=function(){return null==this.owner?null:this.owner.getParent()},t.exports=l},function(t,e,i){"use strict";function n(t,e){null==t&&null==e?(this.x=0,this.y=0):(this.x=t,this.y=e)}n.prototype.getX=function(){return this.x},n.prototype.getY=function(){return this.y},n.prototype.setX=function(t){this.x=t},n.prototype.setY=function(t){this.y=t},n.prototype.getDifference=function(t){return new DimensionD(this.x-t.x,this.y-t.y)},n.prototype.getCopy=function(){return new n(this.x,this.y)},n.prototype.translate=function(t){return this.x+=t.width,this.y+=t.height,this},t.exports=n},function(t,e,i){"use strict";var n=i(2),r=i(10),o=i(0),s=i(6),a=i(3),h=i(1),l=i(13),c=i(12),g=i(11);function u(t,e,i){n.call(this,i),this.estimatedSize=r.MIN_VALUE,this.margin=o.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=t,null!=e&&e instanceof s?this.graphManager=e:null!=e&&e instanceof Layout&&(this.graphManager=e.graphManager)}for(var d in u.prototype=Object.create(n.prototype),n)u[d]=n[d];u.prototype.getNodes=function(){return this.nodes},u.prototype.getEdges=function(){return this.edges},u.prototype.getGraphManager=function(){return this.graphManager},u.prototype.getParent=function(){return this.parent},u.prototype.getLeft=function(){return this.left},u.prototype.getRight=function(){return this.right},u.prototype.getTop=function(){return this.top},u.prototype.getBottom=function(){return this.bottom},u.prototype.isConnected=function(){return this.isConnected},u.prototype.add=function(t,e,i){if(null==e&&null==i){var n=t;if(null==this.graphManager)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(n)>-1)throw"Node already in graph!";return n.owner=this,this.getNodes().push(n),n}var r=t;if(!(this.getNodes().indexOf(e)>-1&&this.getNodes().indexOf(i)>-1))throw"Source or target not in graph!";if(e.owner!=i.owner||e.owner!=this)throw"Both owners must be this graph!";return e.owner!=i.owner?null:(r.source=e,r.target=i,r.isInterGraph=!1,this.getEdges().push(r),e.edges.push(r),i!=e&&i.edges.push(r),r)},u.prototype.remove=function(t){var e=t;if(t instanceof a){if(null==e)throw"Node is null!";if(null==e.owner||e.owner!=this)throw"Owner graph is invalid!";if(null==this.graphManager)throw"Owner graph manager is invalid!";for(var i=e.edges.slice(),n=i.length,r=0;r<n;r++)(o=i[r]).isInterGraph?this.graphManager.remove(o):o.source.owner.remove(o);if(-1==(s=this.nodes.indexOf(e)))throw"Node not in owner node list!";this.nodes.splice(s,1)}else if(t instanceof h){var o;if(null==(o=t))throw"Edge is null!";if(null==o.source||null==o.target)throw"Source and/or target is null!";if(null==o.source.owner||null==o.target.owner||o.source.owner!=this||o.target.owner!=this)throw"Source and/or target owner is invalid!";var s,l=o.source.edges.indexOf(o),c=o.target.edges.indexOf(o);if(!(l>-1&&c>-1))throw"Source and/or target doesn't know this edge!";if(o.source.edges.splice(l,1),o.target!=o.source&&o.target.edges.splice(c,1),-1==(s=o.source.owner.getEdges().indexOf(o)))throw"Not in owner's edge list!";o.source.owner.getEdges().splice(s,1)}},u.prototype.updateLeftTop=function(){for(var t,e,i,n=r.MAX_VALUE,o=r.MAX_VALUE,s=this.getNodes(),a=s.length,h=0;h<a;h++){var l=s[h];n>(t=l.getTop())&&(n=t),o>(e=l.getLeft())&&(o=e)}return n==r.MAX_VALUE?null:(i=null!=s[0].getParent().paddingLeft?s[0].getParent().paddingLeft:this.margin,this.left=o-i,this.top=n-i,new c(this.left,this.top))},u.prototype.updateBounds=function(t){for(var e,i,n,o,s,a=r.MAX_VALUE,h=-r.MAX_VALUE,c=r.MAX_VALUE,g=-r.MAX_VALUE,u=this.nodes,d=u.length,p=0;p<d;p++){var f=u[p];t&&null!=f.child&&f.updateBounds(),a>(e=f.getLeft())&&(a=e),h<(i=f.getRight())&&(h=i),c>(n=f.getTop())&&(c=n),g<(o=f.getBottom())&&(g=o)}var y=new l(a,c,h-a,g-c);a==r.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),s=null!=u[0].getParent().paddingLeft?u[0].getParent().paddingLeft:this.margin,this.left=y.x-s,this.right=y.x+y.width+s,this.top=y.y-s,this.bottom=y.y+y.height+s},u.calculateBounds=function(t){for(var e,i,n,o,s=r.MAX_VALUE,a=-r.MAX_VALUE,h=r.MAX_VALUE,c=-r.MAX_VALUE,g=t.length,u=0;u<g;u++){var d=t[u];s>(e=d.getLeft())&&(s=e),a<(i=d.getRight())&&(a=i),h>(n=d.getTop())&&(h=n),c<(o=d.getBottom())&&(c=o)}return new l(s,h,a-s,c-h)},u.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},u.prototype.getEstimatedSize=function(){if(this.estimatedSize==r.MIN_VALUE)throw"assert failed";return this.estimatedSize},u.prototype.calcEstimatedSize=function(){for(var t=0,e=this.nodes,i=e.length,n=0;n<i;n++)t+=e[n].calcEstimatedSize();return this.estimatedSize=0==t?o.EMPTY_COMPOUND_NODE_SIZE:t/Math.sqrt(this.nodes.length),this.estimatedSize},u.prototype.updateConnected=function(){var t=this;if(0!=this.nodes.length){var e,i,n=new g,r=new Set,o=this.nodes[0];for(o.withChildren().forEach((function(t){n.push(t),r.add(t)}));0!==n.length;)for(var s=(e=(o=n.shift()).getEdges()).length,a=0;a<s;a++)null==(i=e[a].getOtherEndInGraph(o,this))||r.has(i)||i.withChildren().forEach((function(t){n.push(t),r.add(t)}));if(this.isConnected=!1,r.size>=this.nodes.length){var h=0;r.forEach((function(e){e.owner==t&&h++})),h==this.nodes.length&&(this.isConnected=!0)}}else this.isConnected=!0},t.exports=u},function(t,e,i){"use strict";var n,r=i(1);function o(t){n=i(5),this.layout=t,this.graphs=[],this.edges=[]}o.prototype.addRoot=function(){var t=this.layout.newGraph(),e=this.layout.newNode(null),i=this.add(t,e);return this.setRootGraph(i),this.rootGraph},o.prototype.add=function(t,e,i,n,r){if(null==i&&null==n&&null==r){if(null==t)throw"Graph is null!";if(null==e)throw"Parent node is null!";if(this.graphs.indexOf(t)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(t),null!=t.parent)throw"Already has a parent!";if(null!=e.child)throw"Already has a child!";return t.parent=e,e.child=t,t}r=i,i=t;var o=(n=e).getOwner(),s=r.getOwner();if(null==o||o.getGraphManager()!=this)throw"Source not in this graph mgr!";if(null==s||s.getGraphManager()!=this)throw"Target not in this graph mgr!";if(o==s)return i.isInterGraph=!1,o.add(i,n,r);if(i.isInterGraph=!0,i.source=n,i.target=r,this.edges.indexOf(i)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(i),null==i.source||null==i.target)throw"Edge source and/or target is null!";if(-1!=i.source.edges.indexOf(i)||-1!=i.target.edges.indexOf(i))throw"Edge already in source and/or target incidency list!";return i.source.edges.push(i),i.target.edges.push(i),i},o.prototype.remove=function(t){if(t instanceof n){var e=t;if(e.getGraphManager()!=this)throw"Graph not in this graph mgr";if(e!=this.rootGraph&&(null==e.parent||e.parent.graphManager!=this))throw"Invalid parent node!";for(var i,o=[],s=(o=o.concat(e.getEdges())).length,a=0;a<s;a++)i=o[a],e.remove(i);var h,l=[];for(s=(l=l.concat(e.getNodes())).length,a=0;a<s;a++)h=l[a],e.remove(h);e==this.rootGraph&&this.setRootGraph(null);var c=this.graphs.indexOf(e);this.graphs.splice(c,1),e.parent=null}else if(t instanceof r){if(null==(i=t))throw"Edge is null!";if(!i.isInterGraph)throw"Not an inter-graph edge!";if(null==i.source||null==i.target)throw"Source and/or target is null!";if(-1==i.source.edges.indexOf(i)||-1==i.target.edges.indexOf(i))throw"Source and/or target doesn't know this edge!";if(c=i.source.edges.indexOf(i),i.source.edges.splice(c,1),c=i.target.edges.indexOf(i),i.target.edges.splice(c,1),null==i.source.owner||null==i.source.owner.getGraphManager())throw"Edge owner graph or owner graph manager is null!";if(-1==i.source.owner.getGraphManager().edges.indexOf(i))throw"Not in owner graph manager's edge list!";c=i.source.owner.getGraphManager().edges.indexOf(i),i.source.owner.getGraphManager().edges.splice(c,1)}},o.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},o.prototype.getGraphs=function(){return this.graphs},o.prototype.getAllNodes=function(){if(null==this.allNodes){for(var t=[],e=this.getGraphs(),i=e.length,n=0;n<i;n++)t=t.concat(e[n].getNodes());this.allNodes=t}return this.allNodes},o.prototype.resetAllNodes=function(){this.allNodes=null},o.prototype.resetAllEdges=function(){this.allEdges=null},o.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},o.prototype.getAllEdges=function(){if(null==this.allEdges){for(var t=[],e=this.getGraphs(),i=(e.length,0);i<e.length;i++)t=t.concat(e[i].getEdges());t=t.concat(this.edges),this.allEdges=t}return this.allEdges},o.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},o.prototype.setAllNodesToApplyGravitation=function(t){if(null!=this.allNodesToApplyGravitation)throw"assert failed";this.allNodesToApplyGravitation=t},o.prototype.getRoot=function(){return this.rootGraph},o.prototype.setRootGraph=function(t){if(t.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=t,null==t.parent&&(t.parent=this.layout.newNode("Root node"))},o.prototype.getLayout=function(){return this.layout},o.prototype.isOneAncestorOfOther=function(t,e){if(null==t||null==e)throw"assert failed";if(t==e)return!0;for(var i,n=t.getOwner();null!=(i=n.getParent());){if(i==e)return!0;if(null==(n=i.getOwner()))break}for(n=e.getOwner();null!=(i=n.getParent());){if(i==t)return!0;if(null==(n=i.getOwner()))break}return!1},o.prototype.calcLowestCommonAncestors=function(){for(var t,e,i,n,r,o=this.getAllEdges(),s=o.length,a=0;a<s;a++)if(e=(t=o[a]).source,i=t.target,t.lca=null,t.sourceInLca=e,t.targetInLca=i,e!=i){for(n=e.getOwner();null==t.lca;){for(t.targetInLca=i,r=i.getOwner();null==t.lca;){if(r==n){t.lca=r;break}if(r==this.rootGraph)break;if(null!=t.lca)throw"assert failed";t.targetInLca=r.getParent(),r=t.targetInLca.getOwner()}if(n==this.rootGraph)break;null==t.lca&&(t.sourceInLca=n.getParent(),n=t.sourceInLca.getOwner())}if(null==t.lca)throw"assert failed"}else t.lca=e.getOwner()},o.prototype.calcLowestCommonAncestor=function(t,e){if(t==e)return t.getOwner();for(var i=t.getOwner();null!=i;){for(var n=e.getOwner();null!=n;){if(n==i)return n;n=n.getParent().getOwner()}i=i.getParent().getOwner()}return i},o.prototype.calcInclusionTreeDepths=function(t,e){var i;null==t&&null==e&&(t=this.rootGraph,e=1);for(var n=t.getNodes(),r=n.length,o=0;o<r;o++)(i=n[o]).inclusionTreeDepth=e,null!=i.child&&this.calcInclusionTreeDepths(i.child,e+1)},o.prototype.includesInvalidEdge=function(){for(var t,e=this.edges.length,i=0;i<e;i++)if(t=this.edges[i],this.isOneAncestorOfOther(t.source,t.target))return!0;return!1},t.exports=o},function(t,e,i){"use strict";var n=i(0);function r(){}for(var o in n)r[o]=n[o];r.MAX_ITERATIONS=2500,r.DEFAULT_EDGE_LENGTH=50,r.DEFAULT_SPRING_STRENGTH=.45,r.DEFAULT_REPULSION_STRENGTH=4500,r.DEFAULT_GRAVITY_STRENGTH=.4,r.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,r.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,r.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,r.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,r.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,r.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,r.COOLING_ADAPTATION_FACTOR=.33,r.ADAPTATION_LOWER_NODE_LIMIT=1e3,r.ADAPTATION_UPPER_NODE_LIMIT=5e3,r.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,r.MAX_NODE_DISPLACEMENT=3*r.MAX_NODE_DISPLACEMENT_INCREMENTAL,r.MIN_REPULSION_DIST=r.DEFAULT_EDGE_LENGTH/10,r.CONVERGENCE_CHECK_PERIOD=100,r.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,r.MIN_EDGE_LENGTH=1,r.GRID_CALCULATION_CHECK_PERIOD=10,t.exports=r},function(t,e,i){"use strict";var n=i(12);function r(){}r.calcSeparationAmount=function(t,e,i,n){if(!t.intersects(e))throw"assert failed";var r=new Array(2);this.decideDirectionsForOverlappingNodes(t,e,r),i[0]=Math.min(t.getRight(),e.getRight())-Math.max(t.x,e.x),i[1]=Math.min(t.getBottom(),e.getBottom())-Math.max(t.y,e.y),t.getX()<=e.getX()&&t.getRight()>=e.getRight()?i[0]+=Math.min(e.getX()-t.getX(),t.getRight()-e.getRight()):e.getX()<=t.getX()&&e.getRight()>=t.getRight()&&(i[0]+=Math.min(t.getX()-e.getX(),e.getRight()-t.getRight())),t.getY()<=e.getY()&&t.getBottom()>=e.getBottom()?i[1]+=Math.min(e.getY()-t.getY(),t.getBottom()-e.getBottom()):e.getY()<=t.getY()&&e.getBottom()>=t.getBottom()&&(i[1]+=Math.min(t.getY()-e.getY(),e.getBottom()-t.getBottom()));var o=Math.abs((e.getCenterY()-t.getCenterY())/(e.getCenterX()-t.getCenterX()));e.getCenterY()===t.getCenterY()&&e.getCenterX()===t.getCenterX()&&(o=1);var s=o*i[0],a=i[1]/o;i[0]<a?a=i[0]:s=i[1],i[0]=-1*r[0]*(a/2+n),i[1]=-1*r[1]*(s/2+n)},r.decideDirectionsForOverlappingNodes=function(t,e,i){t.getCenterX()<e.getCenterX()?i[0]=-1:i[0]=1,t.getCenterY()<e.getCenterY()?i[1]=-1:i[1]=1},r.getIntersection2=function(t,e,i){var n=t.getCenterX(),r=t.getCenterY(),o=e.getCenterX(),s=e.getCenterY();if(t.intersects(e))return i[0]=n,i[1]=r,i[2]=o,i[3]=s,!0;var a=t.getX(),h=t.getY(),l=t.getRight(),c=t.getX(),g=t.getBottom(),u=t.getRight(),d=t.getWidthHalf(),p=t.getHeightHalf(),f=e.getX(),y=e.getY(),E=e.getRight(),_=e.getX(),m=e.getBottom(),v=e.getRight(),N=e.getWidthHalf(),A=e.getHeightHalf(),L=!1,T=!1;if(n===o){if(r>s)return i[0]=n,i[1]=h,i[2]=o,i[3]=m,!1;if(r<s)return i[0]=n,i[1]=g,i[2]=o,i[3]=y,!1}else if(r===s){if(n>o)return i[0]=a,i[1]=r,i[2]=E,i[3]=s,!1;if(n<o)return i[0]=l,i[1]=r,i[2]=f,i[3]=s,!1}else{var O=t.height/t.width,D=e.height/e.width,I=(s-r)/(o-n),w=void 0,R=void 0,C=void 0,M=void 0,x=void 0,G=void 0;if(-O===I?n>o?(i[0]=c,i[1]=g,L=!0):(i[0]=l,i[1]=h,L=!0):O===I&&(n>o?(i[0]=a,i[1]=h,L=!0):(i[0]=u,i[1]=g,L=!0)),-D===I?o>n?(i[2]=_,i[3]=m,T=!0):(i[2]=E,i[3]=y,T=!0):D===I&&(o>n?(i[2]=f,i[3]=y,T=!0):(i[2]=v,i[3]=m,T=!0)),L&&T)return!1;if(n>o?r>s?(w=this.getCardinalDirection(O,I,4),R=this.getCardinalDirection(D,I,2)):(w=this.getCardinalDirection(-O,I,3),R=this.getCardinalDirection(-D,I,1)):r>s?(w=this.getCardinalDirection(-O,I,1),R=this.getCardinalDirection(-D,I,3)):(w=this.getCardinalDirection(O,I,2),R=this.getCardinalDirection(D,I,4)),!L)switch(w){case 1:M=h,C=n+-p/I,i[0]=C,i[1]=M;break;case 2:C=u,M=r+d*I,i[0]=C,i[1]=M;break;case 3:M=g,C=n+p/I,i[0]=C,i[1]=M;break;case 4:C=c,M=r+-d*I,i[0]=C,i[1]=M}if(!T)switch(R){case 1:G=y,x=o+-A/I,i[2]=x,i[3]=G;break;case 2:x=v,G=s+N*I,i[2]=x,i[3]=G;break;case 3:G=m,x=o+A/I,i[2]=x,i[3]=G;break;case 4:x=_,G=s+-N*I,i[2]=x,i[3]=G}}return!1},r.getCardinalDirection=function(t,e,i){return t>e?i:1+i%4},r.getIntersection=function(t,e,i,r){if(null==r)return this.getIntersection2(t,e,i);var o,s,a,h,l,c,g,u=t.x,d=t.y,p=e.x,f=e.y,y=i.x,E=i.y,_=r.x,m=r.y;return 0===(g=(o=f-d)*(h=y-_)-(s=m-E)*(a=u-p))?null:new n((a*(c=_*E-y*m)-h*(l=p*d-u*f))/g,(s*l-o*c)/g)},r.angleOfVector=function(t,e,i,n){var r=void 0;return t!==i?(r=Math.atan((n-e)/(i-t)),i<t?r+=Math.PI:n<e&&(r+=this.TWO_PI)):r=n<e?this.ONE_AND_HALF_PI:this.HALF_PI,r},r.doIntersect=function(t,e,i,n){var r=t.x,o=t.y,s=e.x,a=e.y,h=i.x,l=i.y,c=n.x,g=n.y,u=(s-r)*(g-l)-(c-h)*(a-o);if(0===u)return!1;var d=((g-l)*(c-r)+(h-c)*(g-o))/u,p=((o-a)*(c-r)+(s-r)*(g-o))/u;return 0<d&&d<1&&0<p&&p<1},r.HALF_PI=.5*Math.PI,r.ONE_AND_HALF_PI=1.5*Math.PI,r.TWO_PI=2*Math.PI,r.THREE_PI=3*Math.PI,t.exports=r},function(t,e,i){"use strict";function n(){}n.sign=function(t){return t>0?1:t<0?-1:0},n.floor=function(t){return t<0?Math.ceil(t):Math.floor(t)},n.ceil=function(t){return t<0?Math.floor(t):Math.ceil(t)},t.exports=n},function(t,e,i){"use strict";function n(){}n.MAX_VALUE=2147483647,n.MIN_VALUE=-2147483648,t.exports=n},function(t,e,i){"use strict";var n=function(){function t(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,i,n){return i&&t(e.prototype,i),n&&t(e,n),e}}(),r=function(t){return{value:t,next:null,prev:null}},o=function(t,e,i,n){return null!==t?t.next=e:n.head=e,null!==i?i.prev=e:n.tail=e,e.prev=t,e.next=i,n.length++,e},s=function(t,e){var i=t.prev,n=t.next;return null!==i?i.next=n:e.head=n,null!==n?n.prev=i:e.tail=i,t.prev=t.next=null,e.length--,t},a=function(){function t(e){var i=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.length=0,this.head=null,this.tail=null,null!=e&&e.forEach((function(t){return i.push(t)}))}return n(t,[{key:"size",value:function(){return this.length}},{key:"insertBefore",value:function(t,e){return o(e.prev,r(t),e,this)}},{key:"insertAfter",value:function(t,e){return o(e,r(t),e.next,this)}},{key:"insertNodeBefore",value:function(t,e){return o(e.prev,t,e,this)}},{key:"insertNodeAfter",value:function(t,e){return o(e,t,e.next,this)}},{key:"push",value:function(t){return o(this.tail,r(t),null,this)}},{key:"unshift",value:function(t){return o(null,r(t),this.head,this)}},{key:"remove",value:function(t){return s(t,this)}},{key:"pop",value:function(){return s(this.tail,this).value}},{key:"popNode",value:function(){return s(this.tail,this)}},{key:"shift",value:function(){return s(this.head,this).value}},{key:"shiftNode",value:function(){return s(this.head,this)}},{key:"get_object_at",value:function(t){if(t<=this.length()){for(var e=1,i=this.head;e<t;)i=i.next,e++;return i.value}}},{key:"set_object_at",value:function(t,e){if(t<=this.length()){for(var i=1,n=this.head;i<t;)n=n.next,i++;n.value=e}}}]),t}();t.exports=a},function(t,e,i){"use strict";function n(t,e,i){this.x=null,this.y=null,null==t&&null==e&&null==i?(this.x=0,this.y=0):"number"==typeof t&&"number"==typeof e&&null==i?(this.x=t,this.y=e):"Point"==t.constructor.name&&null==e&&null==i&&(i=t,this.x=i.x,this.y=i.y)}n.prototype.getX=function(){return this.x},n.prototype.getY=function(){return this.y},n.prototype.getLocation=function(){return new n(this.x,this.y)},n.prototype.setLocation=function(t,e,i){"Point"==t.constructor.name&&null==e&&null==i?(i=t,this.setLocation(i.x,i.y)):"number"==typeof t&&"number"==typeof e&&null==i&&(parseInt(t)==t&&parseInt(e)==e?this.move(t,e):(this.x=Math.floor(t+.5),this.y=Math.floor(e+.5)))},n.prototype.move=function(t,e){this.x=t,this.y=e},n.prototype.translate=function(t,e){this.x+=t,this.y+=e},n.prototype.equals=function(t){if("Point"==t.constructor.name){var e=t;return this.x==e.x&&this.y==e.y}return this==t},n.prototype.toString=function(){return(new n).constructor.name+"[x="+this.x+",y="+this.y+"]"},t.exports=n},function(t,e,i){"use strict";function n(t,e,i,n){this.x=0,this.y=0,this.width=0,this.height=0,null!=t&&null!=e&&null!=i&&null!=n&&(this.x=t,this.y=e,this.width=i,this.height=n)}n.prototype.getX=function(){return this.x},n.prototype.setX=function(t){this.x=t},n.prototype.getY=function(){return this.y},n.prototype.setY=function(t){this.y=t},n.prototype.getWidth=function(){return this.width},n.prototype.setWidth=function(t){this.width=t},n.prototype.getHeight=function(){return this.height},n.prototype.setHeight=function(t){this.height=t},n.prototype.getRight=function(){return this.x+this.width},n.prototype.getBottom=function(){return this.y+this.height},n.prototype.intersects=function(t){return!(this.getRight()<t.x||this.getBottom()<t.y||t.getRight()<this.x||t.getBottom()<this.y)},n.prototype.getCenterX=function(){return this.x+this.width/2},n.prototype.getMinX=function(){return this.getX()},n.prototype.getMaxX=function(){return this.getX()+this.width},n.prototype.getCenterY=function(){return this.y+this.height/2},n.prototype.getMinY=function(){return this.getY()},n.prototype.getMaxY=function(){return this.getY()+this.height},n.prototype.getWidthHalf=function(){return this.width/2},n.prototype.getHeightHalf=function(){return this.height/2},t.exports=n},function(t,e,i){"use strict";var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};function r(){}r.lastID=0,r.createID=function(t){return r.isPrimitive(t)?t:(null!=t.uniqueID||(t.uniqueID=r.getString(),r.lastID++),t.uniqueID)},r.getString=function(t){return null==t&&(t=r.lastID),"Object#"+t},r.isPrimitive=function(t){var e=void 0===t?"undefined":n(t);return null==t||"object"!=e&&"function"!=e},t.exports=r},function(t,e,i){"use strict";function n(t){if(Array.isArray(t)){for(var e=0,i=Array(t.length);e<t.length;e++)i[e]=t[e];return i}return Array.from(t)}var r=i(0),o=i(6),s=i(3),a=i(1),h=i(5),l=i(4),c=i(17),g=i(27);function u(t){g.call(this),this.layoutQuality=r.QUALITY,this.createBendsAsNeeded=r.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=r.DEFAULT_INCREMENTAL,this.animationOnLayout=r.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=r.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=r.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=r.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new o(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,null!=t&&(this.isRemoteUse=t)}u.RANDOM_SEED=1,u.prototype=Object.create(g.prototype),u.prototype.getGraphManager=function(){return this.graphManager},u.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},u.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},u.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},u.prototype.newGraphManager=function(){var t=new o(this);return this.graphManager=t,t},u.prototype.newGraph=function(t){return new h(null,this.graphManager,t)},u.prototype.newNode=function(t){return new s(this.graphManager,t)},u.prototype.newEdge=function(t){return new a(null,null,t)},u.prototype.checkLayoutSuccess=function(){return null==this.graphManager.getRoot()||0==this.graphManager.getRoot().getNodes().length||this.graphManager.includesInvalidEdge()},u.prototype.runLayout=function(){var t;return this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters(),t=!this.checkLayoutSuccess()&&this.layout(),"during"!==r.ANIMATE&&(t&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,t)},u.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},u.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var t=this.graphManager.getAllEdges(),e=0;e<t.length;e++)t[e];var i=this.graphManager.getRoot().getNodes();for(e=0;e<i.length;e++)i[e];this.update(this.graphManager.getRoot())}},u.prototype.update=function(t){if(null==t)this.update2();else if(t instanceof s){var e=t;if(null!=e.getChild())for(var i=e.getChild().getNodes(),n=0;n<i.length;n++)update(i[n]);null!=e.vGraphObject&&e.vGraphObject.update(e)}else if(t instanceof a){var r=t;null!=r.vGraphObject&&r.vGraphObject.update(r)}else if(t instanceof h){var o=t;null!=o.vGraphObject&&o.vGraphObject.update(o)}},u.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=r.QUALITY,this.animationDuringLayout=r.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=r.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=r.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=r.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=r.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=r.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},u.prototype.transform=function(t){if(null==t)this.transform(new l(0,0));else{var e=new c,i=this.graphManager.getRoot().updateLeftTop();if(null!=i){e.setWorldOrgX(t.x),e.setWorldOrgY(t.y),e.setDeviceOrgX(i.x),e.setDeviceOrgY(i.y);for(var n=this.getAllNodes(),r=0;r<n.length;r++)n[r].transform(e)}}},u.prototype.positionNodesRandomly=function(t){if(null==t)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var e,i,n=t.getNodes(),r=0;r<n.length;r++)null==(i=(e=n[r]).getChild())||0==i.getNodes().length?e.scatter():(this.positionNodesRandomly(i),e.updateBounds())},u.prototype.getFlatForest=function(){for(var t=[],e=!0,i=this.graphManager.getRoot().getNodes(),r=!0,o=0;o<i.length;o++)null!=i[o].getChild()&&(r=!1);if(!r)return t;var s=new Set,a=[],h=new Map,l=[];for(l=l.concat(i);l.length>0&&e;){for(a.push(l[0]);a.length>0&&e;){var c=a[0];a.splice(0,1),s.add(c);var g=c.getEdges();for(o=0;o<g.length;o++){var u=g[o].getOtherEnd(c);if(h.get(c)!=u){if(s.has(u)){e=!1;break}a.push(u),h.set(u,c)}}}if(e){var d=[].concat(n(s));for(t.push(d),o=0;o<d.length;o++){var p=d[o],f=l.indexOf(p);f>-1&&l.splice(f,1)}s=new Set,h=new Map}else t=[]}return t},u.prototype.createDummyNodesForBendpoints=function(t){for(var e=[],i=t.source,n=this.graphManager.calcLowestCommonAncestor(t.source,t.target),r=0;r<t.bendpoints.length;r++){var o=this.newNode(null);o.setRect(new Point(0,0),new Dimension(1,1)),n.add(o);var s=this.newEdge(null);this.graphManager.add(s,i,o),e.add(o),i=o}return s=this.newEdge(null),this.graphManager.add(s,i,t.target),this.edgeToDummyNodes.set(t,e),t.isInterGraph()?this.graphManager.remove(t):n.remove(t),e},u.prototype.createBendpointsFromDummyNodes=function(){var t=[];t=t.concat(this.graphManager.getAllEdges()),t=[].concat(n(this.edgeToDummyNodes.keys())).concat(t);for(var e=0;e<t.length;e++){var i=t[e];if(i.bendpoints.length>0){for(var r=this.edgeToDummyNodes.get(i),o=0;o<r.length;o++){var s=r[o],a=new l(s.getCenterX(),s.getCenterY()),h=i.bendpoints.get(o);h.x=a.x,h.y=a.y,s.getOwner().remove(s)}this.graphManager.add(i,i.source,i.target)}}},u.transform=function(t,e,i,n){if(null!=i&&null!=n){var r=e;return t<=50?r-=(e-e/i)/50*(50-t):r+=(e*n-e)/50*(t-50),r}var o,s;return t<=50?(o=9*e/500,s=e/10):(o=9*e/50,s=-8*e),o*t+s},u.findCenterOfTree=function(t){var e=[];e=e.concat(t);var i=[],n=new Map,r=!1,o=null;1!=e.length&&2!=e.length||(r=!0,o=e[0]);for(var s=0;s<e.length;s++){var a=(c=e[s]).getNeighborsList().size;n.set(c,c.getNeighborsList().size),1==a&&i.push(c)}var h=[];for(h=h.concat(i);!r;){var l=[];for(l=l.concat(h),h=[],s=0;s<e.length;s++){var c=e[s],g=e.indexOf(c);g>=0&&e.splice(g,1),c.getNeighborsList().forEach((function(t){if(i.indexOf(t)<0){var e=n.get(t)-1;1==e&&h.push(t),n.set(t,e)}}))}i=i.concat(h),1!=e.length&&2!=e.length||(r=!0,o=e[0])}return o},u.prototype.setGraphManager=function(t){this.graphManager=t},t.exports=u},function(t,e,i){"use strict";function n(){}n.seed=1,n.x=0,n.nextDouble=function(){return n.x=1e4*Math.sin(n.seed++),n.x-Math.floor(n.x)},t.exports=n},function(t,e,i){"use strict";var n=i(4);function r(t,e){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}r.prototype.getWorldOrgX=function(){return this.lworldOrgX},r.prototype.setWorldOrgX=function(t){this.lworldOrgX=t},r.prototype.getWorldOrgY=function(){return this.lworldOrgY},r.prototype.setWorldOrgY=function(t){this.lworldOrgY=t},r.prototype.getWorldExtX=function(){return this.lworldExtX},r.prototype.setWorldExtX=function(t){this.lworldExtX=t},r.prototype.getWorldExtY=function(){return this.lworldExtY},r.prototype.setWorldExtY=function(t){this.lworldExtY=t},r.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},r.prototype.setDeviceOrgX=function(t){this.ldeviceOrgX=t},r.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},r.prototype.setDeviceOrgY=function(t){this.ldeviceOrgY=t},r.prototype.getDeviceExtX=function(){return this.ldeviceExtX},r.prototype.setDeviceExtX=function(t){this.ldeviceExtX=t},r.prototype.getDeviceExtY=function(){return this.ldeviceExtY},r.prototype.setDeviceExtY=function(t){this.ldeviceExtY=t},r.prototype.transformX=function(t){var e=0,i=this.lworldExtX;return 0!=i&&(e=this.ldeviceOrgX+(t-this.lworldOrgX)*this.ldeviceExtX/i),e},r.prototype.transformY=function(t){var e=0,i=this.lworldExtY;return 0!=i&&(e=this.ldeviceOrgY+(t-this.lworldOrgY)*this.ldeviceExtY/i),e},r.prototype.inverseTransformX=function(t){var e=0,i=this.ldeviceExtX;return 0!=i&&(e=this.lworldOrgX+(t-this.ldeviceOrgX)*this.lworldExtX/i),e},r.prototype.inverseTransformY=function(t){var e=0,i=this.ldeviceExtY;return 0!=i&&(e=this.lworldOrgY+(t-this.ldeviceOrgY)*this.lworldExtY/i),e},r.prototype.inverseTransformPoint=function(t){return new n(this.inverseTransformX(t.x),this.inverseTransformY(t.y))},t.exports=r},function(t,e,i){"use strict";var n=i(15),r=i(7),o=i(0),s=i(8),a=i(9);function h(){n.call(this),this.useSmartIdealEdgeLengthCalculation=r.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.idealEdgeLength=r.DEFAULT_EDGE_LENGTH,this.springConstant=r.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=r.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=r.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=r.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=r.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=r.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*r.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=r.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=r.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=r.MAX_ITERATIONS}for(var l in h.prototype=Object.create(n.prototype),n)h[l]=n[l];h.prototype.initParameters=function(){n.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=r.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},h.prototype.calcIdealEdgeLengths=function(){for(var t,e,i,n,s,a,h=this.getGraphManager().getAllEdges(),l=0;l<h.length;l++)(t=h[l]).idealLength=this.idealEdgeLength,t.isInterGraph&&(i=t.getSource(),n=t.getTarget(),s=t.getSourceInLca().getEstimatedSize(),a=t.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(t.idealLength+=s+a-2*o.SIMPLE_NODE_SIZE),e=t.getLca().getInclusionTreeDepth(),t.idealLength+=r.DEFAULT_EDGE_LENGTH*r.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(i.getInclusionTreeDepth()+n.getInclusionTreeDepth()-2*e))},h.prototype.initSpringEmbedder=function(){var t=this.getAllNodes().length;this.incremental?(t>r.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*r.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(t-r.ADAPTATION_LOWER_NODE_LIMIT)/(r.ADAPTATION_UPPER_NODE_LIMIT-r.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-r.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=r.MAX_NODE_DISPLACEMENT_INCREMENTAL):(t>r.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(r.COOLING_ADAPTATION_FACTOR,1-(t-r.ADAPTATION_LOWER_NODE_LIMIT)/(r.ADAPTATION_UPPER_NODE_LIMIT-r.ADAPTATION_LOWER_NODE_LIMIT)*(1-r.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=r.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(5*this.getAllNodes().length,this.maxIterations),this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},h.prototype.calcSpringForces=function(){for(var t,e=this.getAllEdges(),i=0;i<e.length;i++)t=e[i],this.calcSpringForce(t,t.idealLength)},h.prototype.calcRepulsionForces=function(){var t,e,i,n,o,s=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],a=arguments.length>1&&void 0!==arguments[1]&&arguments[1],h=this.getAllNodes();if(this.useFRGridVariant)for(this.totalIterations%r.GRID_CALCULATION_CHECK_PERIOD==1&&s&&this.updateGrid(),o=new Set,t=0;t<h.length;t++)i=h[t],this.calculateRepulsionForceOfANode(i,o,s,a),o.add(i);else for(t=0;t<h.length;t++)for(i=h[t],e=t+1;e<h.length;e++)n=h[e],i.getOwner()==n.getOwner()&&this.calcRepulsionForce(i,n)},h.prototype.calcGravitationalForces=function(){for(var t,e=this.getAllNodesToApplyGravitation(),i=0;i<e.length;i++)t=e[i],this.calcGravitationalForce(t)},h.prototype.moveNodes=function(){for(var t=this.getAllNodes(),e=0;e<t.length;e++)t[e].move()},h.prototype.calcSpringForce=function(t,e){var i,n,r,o,s=t.getSource(),a=t.getTarget();if(this.uniformLeafNodeSizes&&null==s.getChild()&&null==a.getChild())t.updateLengthSimple();else if(t.updateLength(),t.isOverlapingSourceAndTarget)return;0!=(i=t.getLength())&&(r=(n=this.springConstant*(i-e))*(t.lengthX/i),o=n*(t.lengthY/i),s.springForceX+=r,s.springForceY+=o,a.springForceX-=r,a.springForceY-=o)},h.prototype.calcRepulsionForce=function(t,e){var i,n,o,h,l,c,g,u=t.getRect(),d=e.getRect(),p=new Array(2),f=new Array(4);if(u.intersects(d)){s.calcSeparationAmount(u,d,p,r.DEFAULT_EDGE_LENGTH/2),c=2*p[0],g=2*p[1];var y=t.noOfChildren*e.noOfChildren/(t.noOfChildren+e.noOfChildren);t.repulsionForceX-=y*c,t.repulsionForceY-=y*g,e.repulsionForceX+=y*c,e.repulsionForceY+=y*g}else this.uniformLeafNodeSizes&&null==t.getChild()&&null==e.getChild()?(i=d.getCenterX()-u.getCenterX(),n=d.getCenterY()-u.getCenterY()):(s.getIntersection(u,d,f),i=f[2]-f[0],n=f[3]-f[1]),Math.abs(i)<r.MIN_REPULSION_DIST&&(i=a.sign(i)*r.MIN_REPULSION_DIST),Math.abs(n)<r.MIN_REPULSION_DIST&&(n=a.sign(n)*r.MIN_REPULSION_DIST),o=i*i+n*n,h=Math.sqrt(o),c=(l=this.repulsionConstant*t.noOfChildren*e.noOfChildren/o)*i/h,g=l*n/h,t.repulsionForceX-=c,t.repulsionForceY-=g,e.repulsionForceX+=c,e.repulsionForceY+=g},h.prototype.calcGravitationalForce=function(t){var e,i,n,r,o,s,a,h;i=((e=t.getOwner()).getRight()+e.getLeft())/2,n=(e.getTop()+e.getBottom())/2,r=t.getCenterX()-i,o=t.getCenterY()-n,s=Math.abs(r)+t.getWidth()/2,a=Math.abs(o)+t.getHeight()/2,t.getOwner()==this.graphManager.getRoot()?(s>(h=e.getEstimatedSize()*this.gravityRangeFactor)||a>h)&&(t.gravitationForceX=-this.gravityConstant*r,t.gravitationForceY=-this.gravityConstant*o):(s>(h=e.getEstimatedSize()*this.compoundGravityRangeFactor)||a>h)&&(t.gravitationForceX=-this.gravityConstant*r*this.compoundGravityConstant,t.gravitationForceY=-this.gravityConstant*o*this.compoundGravityConstant)},h.prototype.isConverged=function(){var t,e=!1;return this.totalIterations>this.maxIterations/3&&(e=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),t=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,t||e},h.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},h.prototype.calcNoOfChildrenForAllNodes=function(){for(var t,e=this.graphManager.getAllNodes(),i=0;i<e.length;i++)(t=e[i]).noOfChildren=t.getNoOfChildren()},h.prototype.calcGrid=function(t){var e,i;e=parseInt(Math.ceil((t.getRight()-t.getLeft())/this.repulsionRange)),i=parseInt(Math.ceil((t.getBottom()-t.getTop())/this.repulsionRange));for(var n=new Array(e),r=0;r<e;r++)n[r]=new Array(i);for(r=0;r<e;r++)for(var o=0;o<i;o++)n[r][o]=new Array;return n},h.prototype.addNodeToGrid=function(t,e,i){var n,r,o,s;n=parseInt(Math.floor((t.getRect().x-e)/this.repulsionRange)),r=parseInt(Math.floor((t.getRect().width+t.getRect().x-e)/this.repulsionRange)),o=parseInt(Math.floor((t.getRect().y-i)/this.repulsionRange)),s=parseInt(Math.floor((t.getRect().height+t.getRect().y-i)/this.repulsionRange));for(var a=n;a<=r;a++)for(var h=o;h<=s;h++)this.grid[a][h].push(t),t.setGridCoordinates(n,r,o,s)},h.prototype.updateGrid=function(){var t,e,i=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),t=0;t<i.length;t++)e=i[t],this.addNodeToGrid(e,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},h.prototype.calculateRepulsionForceOfANode=function(t,e,i,n){if(this.totalIterations%r.GRID_CALCULATION_CHECK_PERIOD==1&&i||n){var o,s=new Set;t.surrounding=new Array;for(var a=this.grid,h=t.startX-1;h<t.finishX+2;h++)for(var l=t.startY-1;l<t.finishY+2;l++)if(!(h<0||l<0||h>=a.length||l>=a[0].length))for(var c=0;c<a[h][l].length;c++)if(o=a[h][l][c],t.getOwner()==o.getOwner()&&t!=o&&!e.has(o)&&!s.has(o)){var g=Math.abs(t.getCenterX()-o.getCenterX())-(t.getWidth()/2+o.getWidth()/2),u=Math.abs(t.getCenterY()-o.getCenterY())-(t.getHeight()/2+o.getHeight()/2);g<=this.repulsionRange&&u<=this.repulsionRange&&s.add(o)}t.surrounding=[].concat(function(t){if(Array.isArray(t)){for(var e=0,i=Array(t.length);e<t.length;e++)i[e]=t[e];return i}return Array.from(t)}(s))}for(h=0;h<t.surrounding.length;h++)this.calcRepulsionForce(t,t.surrounding[h])},h.prototype.calcRepulsionRange=function(){return 0},t.exports=h},function(t,e,i){"use strict";var n=i(1),r=i(7);function o(t,e,i){n.call(this,t,e,i),this.idealLength=r.DEFAULT_EDGE_LENGTH}for(var s in o.prototype=Object.create(n.prototype),n)o[s]=n[s];t.exports=o},function(t,e,i){"use strict";var n=i(3);function r(t,e,i,r){n.call(this,t,e,i,r),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}for(var o in r.prototype=Object.create(n.prototype),n)r[o]=n[o];r.prototype.setGridCoordinates=function(t,e,i,n){this.startX=t,this.finishX=e,this.startY=i,this.finishY=n},t.exports=r},function(t,e,i){"use strict";function n(t,e){this.width=0,this.height=0,null!==t&&null!==e&&(this.height=e,this.width=t)}n.prototype.getWidth=function(){return this.width},n.prototype.setWidth=function(t){this.width=t},n.prototype.getHeight=function(){return this.height},n.prototype.setHeight=function(t){this.height=t},t.exports=n},function(t,e,i){"use strict";var n=i(14);function r(){this.map={},this.keys=[]}r.prototype.put=function(t,e){var i=n.createID(t);this.contains(i)||(this.map[i]=e,this.keys.push(t))},r.prototype.contains=function(t){return n.createID(t),null!=this.map[t]},r.prototype.get=function(t){var e=n.createID(t);return this.map[e]},r.prototype.keySet=function(){return this.keys},t.exports=r},function(t,e,i){"use strict";var n=i(14);function r(){this.set={}}r.prototype.add=function(t){var e=n.createID(t);this.contains(e)||(this.set[e]=t)},r.prototype.remove=function(t){delete this.set[n.createID(t)]},r.prototype.clear=function(){this.set={}},r.prototype.contains=function(t){return this.set[n.createID(t)]==t},r.prototype.isEmpty=function(){return 0===this.size()},r.prototype.size=function(){return Object.keys(this.set).length},r.prototype.addAllTo=function(t){for(var e=Object.keys(this.set),i=e.length,n=0;n<i;n++)t.push(this.set[e[n]])},r.prototype.size=function(){return Object.keys(this.set).length},r.prototype.addAll=function(t){for(var e=t.length,i=0;i<e;i++){var n=t[i];this.add(n)}},t.exports=r},function(t,e,i){"use strict";var n=function(){function t(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,i,n){return i&&t(e.prototype,i),n&&t(e,n),e}}(),r=i(11),o=function(){function t(e,i){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),null===i&&void 0===i||(this.compareFunction=this._defaultCompareFunction);var n=void 0;n=e instanceof r?e.size():e.length,this._quicksort(e,0,n-1)}return n(t,[{key:"_quicksort",value:function(t,e,i){if(e<i){var n=this._partition(t,e,i);this._quicksort(t,e,n),this._quicksort(t,n+1,i)}}},{key:"_partition",value:function(t,e,i){for(var n=this._get(t,e),r=e,o=i;;){for(;this.compareFunction(n,this._get(t,o));)o--;for(;this.compareFunction(this._get(t,r),n);)r++;if(!(r<o))return o;this._swap(t,r,o),r++,o--}}},{key:"_get",value:function(t,e){return t instanceof r?t.get_object_at(e):t[e]}},{key:"_set",value:function(t,e,i){t instanceof r?t.set_object_at(e,i):t[e]=i}},{key:"_swap",value:function(t,e,i){var n=this._get(t,e);this._set(t,e,this._get(t,i)),this._set(t,i,n)}},{key:"_defaultCompareFunction",value:function(t,e){return e>t}}]),t}();t.exports=o},function(t,e,i){"use strict";var n=function(){function t(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,i,n){return i&&t(e.prototype,i),n&&t(e,n),e}}(),r=function(){function t(e,i){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:-1,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:-1;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.sequence1=e,this.sequence2=i,this.match_score=n,this.mismatch_penalty=r,this.gap_penalty=o,this.iMax=e.length+1,this.jMax=i.length+1,this.grid=new Array(this.iMax);for(var s=0;s<this.iMax;s++){this.grid[s]=new Array(this.jMax);for(var a=0;a<this.jMax;a++)this.grid[s][a]=0}this.tracebackGrid=new Array(this.iMax);for(var h=0;h<this.iMax;h++){this.tracebackGrid[h]=new Array(this.jMax);for(var l=0;l<this.jMax;l++)this.tracebackGrid[h][l]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return n(t,[{key:"getScore",value:function(){return this.score}},{key:"getAlignments",value:function(){return this.alignments}},{key:"computeGrids",value:function(){for(var t=1;t<this.jMax;t++)this.grid[0][t]=this.grid[0][t-1]+this.gap_penalty,this.tracebackGrid[0][t]=[!1,!1,!0];for(var e=1;e<this.iMax;e++)this.grid[e][0]=this.grid[e-1][0]+this.gap_penalty,this.tracebackGrid[e][0]=[!1,!0,!1];for(var i=1;i<this.iMax;i++)for(var n=1;n<this.jMax;n++){var r=[this.sequence1[i-1]===this.sequence2[n-1]?this.grid[i-1][n-1]+this.match_score:this.grid[i-1][n-1]+this.mismatch_penalty,this.grid[i-1][n]+this.gap_penalty,this.grid[i][n-1]+this.gap_penalty],o=this.arrayAllMaxIndexes(r);this.grid[i][n]=r[o[0]],this.tracebackGrid[i][n]=[o.includes(0),o.includes(1),o.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function(){var t=[];for(t.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});t[0];){var e=t[0],i=this.tracebackGrid[e.pos[0]][e.pos[1]];i[0]&&t.push({pos:[e.pos[0]-1,e.pos[1]-1],seq1:this.sequence1[e.pos[0]-1]+e.seq1,seq2:this.sequence2[e.pos[1]-1]+e.seq2}),i[1]&&t.push({pos:[e.pos[0]-1,e.pos[1]],seq1:this.sequence1[e.pos[0]-1]+e.seq1,seq2:"-"+e.seq2}),i[2]&&t.push({pos:[e.pos[0],e.pos[1]-1],seq1:"-"+e.seq1,seq2:this.sequence2[e.pos[1]-1]+e.seq2}),0===e.pos[0]&&0===e.pos[1]&&this.alignments.push({sequence1:e.seq1,sequence2:e.seq2}),t.shift()}return this.alignments}},{key:"getAllIndexes",value:function(t,e){for(var i=[],n=-1;-1!==(n=t.indexOf(e,n+1));)i.push(n);return i}},{key:"arrayAllMaxIndexes",value:function(t){return this.getAllIndexes(t,Math.max.apply(null,t))}}]),t}();t.exports=r},function(t,e,i){"use strict";var n=function(){};n.FDLayout=i(18),n.FDLayoutConstants=i(7),n.FDLayoutEdge=i(19),n.FDLayoutNode=i(20),n.DimensionD=i(21),n.HashMap=i(22),n.HashSet=i(23),n.IGeometry=i(8),n.IMath=i(9),n.Integer=i(10),n.Point=i(12),n.PointD=i(4),n.RandomSeed=i(16),n.RectangleD=i(13),n.Transform=i(17),n.UniqueIDGeneretor=i(14),n.Quicksort=i(24),n.LinkedList=i(11),n.LGraphObject=i(2),n.LGraph=i(5),n.LEdge=i(1),n.LGraphManager=i(6),n.LNode=i(3),n.Layout=i(15),n.LayoutConstants=i(0),n.NeedlemanWunsch=i(25),t.exports=n},function(t,e,i){"use strict";function n(){this.listeners=[]}var r=n.prototype;r.addListener=function(t,e){this.listeners.push({event:t,callback:e})},r.removeListener=function(t,e){for(var i=this.listeners.length;i>=0;i--){var n=this.listeners[i];n.event===t&&n.callback===e&&this.listeners.splice(i,1)}},r.emit=function(t,e){for(var i=0;i<this.listeners.length;i++){var n=this.listeners[i];t===n.event&&n.callback(e)}},t.exports=n}])},t.exports=e()},3457:function(t,e,i){var n;n=function(t){return function(t){var e={};function i(n){if(e[n])return e[n].exports;var r=e[n]={i:n,l:!1,exports:{}};return t[n].call(r.exports,r,r.exports,i),r.l=!0,r.exports}return i.m=t,i.c=e,i.i=function(t){return t},i.d=function(t,e,n){i.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:n})},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=1)}([function(e,i){e.exports=t},function(t,e,i){"use strict";var n=i(0).layoutBase.LayoutConstants,r=i(0).layoutBase.FDLayoutConstants,o=i(0).CoSEConstants,s=i(0).CoSELayout,a=i(0).CoSENode,h=i(0).layoutBase.PointD,l=i(0).layoutBase.DimensionD,c={ready:function(){},stop:function(){},quality:"default",nodeDimensionsIncludeLabels:!1,refresh:30,fit:!0,padding:10,randomize:!0,nodeRepulsion:4500,idealEdgeLength:50,edgeElasticity:.45,nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,animate:"end",animationDuration:500,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.5};function g(t){this.options=function(t,e){var i={};for(var n in t)i[n]=t[n];for(var n in e)i[n]=e[n];return i}(c,t),u(this.options)}var u=function(t){null!=t.nodeRepulsion&&(o.DEFAULT_REPULSION_STRENGTH=r.DEFAULT_REPULSION_STRENGTH=t.nodeRepulsion),null!=t.idealEdgeLength&&(o.DEFAULT_EDGE_LENGTH=r.DEFAULT_EDGE_LENGTH=t.idealEdgeLength),null!=t.edgeElasticity&&(o.DEFAULT_SPRING_STRENGTH=r.DEFAULT_SPRING_STRENGTH=t.edgeElasticity),null!=t.nestingFactor&&(o.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=r.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=t.nestingFactor),null!=t.gravity&&(o.DEFAULT_GRAVITY_STRENGTH=r.DEFAULT_GRAVITY_STRENGTH=t.gravity),null!=t.numIter&&(o.MAX_ITERATIONS=r.MAX_ITERATIONS=t.numIter),null!=t.gravityRange&&(o.DEFAULT_GRAVITY_RANGE_FACTOR=r.DEFAULT_GRAVITY_RANGE_FACTOR=t.gravityRange),null!=t.gravityCompound&&(o.DEFAULT_COMPOUND_GRAVITY_STRENGTH=r.DEFAULT_COMPOUND_GRAVITY_STRENGTH=t.gravityCompound),null!=t.gravityRangeCompound&&(o.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=r.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=t.gravityRangeCompound),null!=t.initialEnergyOnIncremental&&(o.DEFAULT_COOLING_FACTOR_INCREMENTAL=r.DEFAULT_COOLING_FACTOR_INCREMENTAL=t.initialEnergyOnIncremental),"draft"==t.quality?n.QUALITY=0:"proof"==t.quality?n.QUALITY=2:n.QUALITY=1,o.NODE_DIMENSIONS_INCLUDE_LABELS=r.NODE_DIMENSIONS_INCLUDE_LABELS=n.NODE_DIMENSIONS_INCLUDE_LABELS=t.nodeDimensionsIncludeLabels,o.DEFAULT_INCREMENTAL=r.DEFAULT_INCREMENTAL=n.DEFAULT_INCREMENTAL=!t.randomize,o.ANIMATE=r.ANIMATE=n.ANIMATE=t.animate,o.TILE=t.tile,o.TILING_PADDING_VERTICAL="function"==typeof t.tilingPaddingVertical?t.tilingPaddingVertical.call():t.tilingPaddingVertical,o.TILING_PADDING_HORIZONTAL="function"==typeof t.tilingPaddingHorizontal?t.tilingPaddingHorizontal.call():t.tilingPaddingHorizontal};g.prototype.run=function(){var t,e,i=this.options,n=(this.idToLNode={},this.layout=new s),r=this;r.stopped=!1,this.cy=this.options.cy,this.cy.trigger({type:"layoutstart",layout:this});var o=n.newGraphManager();this.gm=o;var a=this.options.eles.nodes(),h=this.options.eles.edges();this.root=o.addRoot(),this.processChildrenList(this.root,this.getTopMostNodes(a),n);for(var l=0;l<h.length;l++){var c=h[l],g=this.idToLNode[c.data("source")],u=this.idToLNode[c.data("target")];g!==u&&0==g.getEdgesBetween(u).length&&(o.add(n.newEdge(),g,u).id=c.id())}var d=function(t,e){"number"==typeof t&&(t=e);var i=t.data("id"),n=r.idToLNode[i];return{x:n.getRect().getCenterX(),y:n.getRect().getCenterY()}},p=function o(){for(var s,a=function(){i.fit&&i.cy.fit(i.eles,i.padding),t||(t=!0,r.cy.one("layoutready",i.ready),r.cy.trigger({type:"layoutready",layout:r}))},h=r.options.refresh,l=0;l<h&&!s;l++)s=r.stopped||r.layout.tick();if(s)return n.checkLayoutSuccess()&&!n.isSubLayout&&n.doPostLayout(),n.tilingPostLayout&&n.tilingPostLayout(),n.isLayoutFinished=!0,r.options.eles.nodes().positions(d),a(),r.cy.one("layoutstop",r.options.stop),r.cy.trigger({type:"layoutstop",layout:r}),e&&cancelAnimationFrame(e),void(t=!1);var c=r.layout.getPositionsData();i.eles.nodes().positions((function(t,e){if("number"==typeof t&&(t=e),!t.isParent()){for(var i=t.id(),n=c[i],r=t;null==n&&(n=c[r.data("parent")]||c["DummyCompound_"+r.data("parent")],c[i]=n,null!=(r=r.parent()[0])););return null!=n?{x:n.x,y:n.y}:{x:t.position("x"),y:t.position("y")}}})),a(),e=requestAnimationFrame(o)};return n.addListener("layoutstarted",(function(){"during"===r.options.animate&&(e=requestAnimationFrame(p))})),n.runLayout(),"during"!==this.options.animate&&(r.options.eles.nodes().not(":parent").layoutPositions(r,r.options,d),t=!1),this},g.prototype.getTopMostNodes=function(t){for(var e={},i=0;i<t.length;i++)e[t[i].id()]=!0;var n=t.filter((function(t,i){"number"==typeof t&&(t=i);for(var n=t.parent()[0];null!=n;){if(e[n.id()])return!1;n=n.parent()[0]}return!0}));return n},g.prototype.processChildrenList=function(t,e,i){for(var n=e.length,r=0;r<n;r++){var o,s,c=e[r],g=c.children(),u=c.layoutDimensions({nodeDimensionsIncludeLabels:this.options.nodeDimensionsIncludeLabels});if((o=null!=c.outerWidth()&&null!=c.outerHeight()?t.add(new a(i.graphManager,new h(c.position("x")-u.w/2,c.position("y")-u.h/2),new l(parseFloat(u.w),parseFloat(u.h)))):t.add(new a(this.graphManager))).id=c.data("id"),o.paddingLeft=parseInt(c.css("padding")),o.paddingTop=parseInt(c.css("padding")),o.paddingRight=parseInt(c.css("padding")),o.paddingBottom=parseInt(c.css("padding")),this.options.nodeDimensionsIncludeLabels&&c.isParent()){var d=c.boundingBox({includeLabels:!0,includeNodes:!1}).w,p=c.boundingBox({includeLabels:!0,includeNodes:!1}).h,f=c.css("text-halign");o.labelWidth=d,o.labelHeight=p,o.labelPos=f}this.idToLNode[c.data("id")]=o,isNaN(o.rect.x)&&(o.rect.x=0),isNaN(o.rect.y)&&(o.rect.y=0),null!=g&&g.length>0&&(s=i.getGraphManager().add(i.newGraph(),o),this.processChildrenList(s,g,i))}},g.prototype.stop=function(){return this.stopped=!0,this};var d=function(t){t("layout","cose-bilkent",g)};"undefined"!=typeof cytoscape&&d(cytoscape),t.exports=d}])},t.exports=n(i(7799))},7799:function(t,e,i){var n;n=function(t){return function(t){var e={};function i(n){if(e[n])return e[n].exports;var r=e[n]={i:n,l:!1,exports:{}};return t[n].call(r.exports,r,r.exports,i),r.l=!0,r.exports}return i.m=t,i.c=e,i.i=function(t){return t},i.d=function(t,e,n){i.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:n})},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=7)}([function(e,i){e.exports=t},function(t,e,i){"use strict";var n=i(0).FDLayoutConstants;function r(){}for(var o in n)r[o]=n[o];r.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,r.DEFAULT_RADIAL_SEPARATION=n.DEFAULT_EDGE_LENGTH,r.DEFAULT_COMPONENT_SEPERATION=60,r.TILE=!0,r.TILING_PADDING_VERTICAL=10,r.TILING_PADDING_HORIZONTAL=10,r.TREE_REDUCTION_ON_INCREMENTAL=!1,t.exports=r},function(t,e,i){"use strict";var n=i(0).FDLayoutEdge;function r(t,e,i){n.call(this,t,e,i)}for(var o in r.prototype=Object.create(n.prototype),n)r[o]=n[o];t.exports=r},function(t,e,i){"use strict";var n=i(0).LGraph;function r(t,e,i){n.call(this,t,e,i)}for(var o in r.prototype=Object.create(n.prototype),n)r[o]=n[o];t.exports=r},function(t,e,i){"use strict";var n=i(0).LGraphManager;function r(t){n.call(this,t)}for(var o in r.prototype=Object.create(n.prototype),n)r[o]=n[o];t.exports=r},function(t,e,i){"use strict";var n=i(0).FDLayoutNode,r=i(0).IMath;function o(t,e,i,r){n.call(this,t,e,i,r)}for(var s in o.prototype=Object.create(n.prototype),n)o[s]=n[s];o.prototype.move=function(){var t=this.graphManager.getLayout();this.displacementX=t.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY=t.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren,Math.abs(this.displacementX)>t.coolingFactor*t.maxNodeDisplacement&&(this.displacementX=t.coolingFactor*t.maxNodeDisplacement*r.sign(this.displacementX)),Math.abs(this.displacementY)>t.coolingFactor*t.maxNodeDisplacement&&(this.displacementY=t.coolingFactor*t.maxNodeDisplacement*r.sign(this.displacementY)),null==this.child||0==this.child.getNodes().length?this.moveBy(this.displacementX,this.displacementY):this.propogateDisplacementToChildren(this.displacementX,this.displacementY),t.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},o.prototype.propogateDisplacementToChildren=function(t,e){for(var i,n=this.getChild().getNodes(),r=0;r<n.length;r++)null==(i=n[r]).getChild()?(i.moveBy(t,e),i.displacementX+=t,i.displacementY+=e):i.propogateDisplacementToChildren(t,e)},o.prototype.setPred1=function(t){this.pred1=t},o.prototype.getPred1=function(){return pred1},o.prototype.getPred2=function(){return pred2},o.prototype.setNext=function(t){this.next=t},o.prototype.getNext=function(){return next},o.prototype.setProcessed=function(t){this.processed=t},o.prototype.isProcessed=function(){return processed},t.exports=o},function(t,e,i){"use strict";var n=i(0).FDLayout,r=i(4),o=i(3),s=i(5),a=i(2),h=i(1),l=i(0).FDLayoutConstants,c=i(0).LayoutConstants,g=i(0).Point,u=i(0).PointD,d=i(0).Layout,p=i(0).Integer,f=i(0).IGeometry,y=i(0).LGraph,E=i(0).Transform;function _(){n.call(this),this.toBeTiled={}}for(var m in _.prototype=Object.create(n.prototype),n)_[m]=n[m];_.prototype.newGraphManager=function(){var t=new r(this);return this.graphManager=t,t},_.prototype.newGraph=function(t){return new o(null,this.graphManager,t)},_.prototype.newNode=function(t){return new s(this.graphManager,t)},_.prototype.newEdge=function(t){return new a(null,null,t)},_.prototype.initParameters=function(){n.prototype.initParameters.call(this,arguments),this.isSubLayout||(h.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=h.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=h.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.springConstant=l.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=l.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=l.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=l.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=l.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=l.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1,this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/l.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=l.CONVERGENCE_CHECK_PERIOD/this.maxIterations,this.coolingAdjuster=1)},_.prototype.layout=function(){return c.DEFAULT_CREATE_BENDS_AS_NEEDED&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},_.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental)h.TREE_REDUCTION_ON_INCREMENTAL&&(this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation(),e=new Set(this.getAllNodes()),i=this.nodesWithGravity.filter((function(t){return e.has(t)})),this.graphManager.setAllNodesToApplyGravitation(i));else{var t=this.getFlatForest();if(t.length>0)this.positionNodesRadially(t);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var e=new Set(this.getAllNodes()),i=this.nodesWithGravity.filter((function(t){return e.has(t)}));this.graphManager.setAllNodesToApplyGravitation(i),this.positionNodesRandomly()}}return this.initSpringEmbedder(),this.runSpringEmbedder(),!0},_.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished){if(!(this.prunedNodesAll.length>0))return!0;this.isTreeGrowing=!0}if(this.totalIterations%l.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged()){if(!(this.prunedNodesAll.length>0))return!0;this.isTreeGrowing=!0}this.coolingCycle++,0==this.layoutQuality?this.coolingAdjuster=this.coolingCycle:1==this.layoutQuality&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var t=new Set(this.getAllNodes()),e=this.nodesWithGravity.filter((function(e){return t.has(e)}));this.graphManager.setAllNodesToApplyGravitation(e),this.graphManager.updateBounds(),this.updateGrid(),this.coolingFactor=l.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),this.coolingFactor=l.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var i=!this.isTreeGrowing&&!this.isGrowthFinished,n=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(i,n),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},_.prototype.getPositionsData=function(){for(var t=this.graphManager.getAllNodes(),e={},i=0;i<t.length;i++){var n=t[i].rect,r=t[i].id;e[r]={id:r,x:n.getCenterX(),y:n.getCenterY(),w:n.width,h:n.height}}return e},_.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var t=!1;if("during"===l.ANIMATE)this.emit("layoutstarted");else{for(;!t;)t=this.tick();this.graphManager.updateBounds()}},_.prototype.calculateNodesToApplyGravitationTo=function(){var t,e,i=[],n=this.graphManager.getGraphs(),r=n.length;for(e=0;e<r;e++)(t=n[e]).updateConnected(),t.isConnected||(i=i.concat(t.getNodes()));return i},_.prototype.createBendpoints=function(){var t=[];t=t.concat(this.graphManager.getAllEdges());var e,i=new Set;for(e=0;e<t.length;e++){var n=t[e];if(!i.has(n)){var r=n.getSource(),o=n.getTarget();if(r==o)n.getBendpoints().push(new u),n.getBendpoints().push(new u),this.createDummyNodesForBendpoints(n),i.add(n);else{var s=[];if(s=(s=s.concat(r.getEdgeListToNode(o))).concat(o.getEdgeListToNode(r)),!i.has(s[0])){var a;if(s.length>1)for(a=0;a<s.length;a++){var h=s[a];h.getBendpoints().push(new u),this.createDummyNodesForBendpoints(h)}s.forEach((function(t){i.add(t)}))}}}if(i.size==t.length)break}},_.prototype.positionNodesRadially=function(t){for(var e=new g(0,0),i=Math.ceil(Math.sqrt(t.length)),n=0,r=0,o=0,s=new u(0,0),a=0;a<t.length;a++){a%i==0&&(o=0,r=n,0!=a&&(r+=h.DEFAULT_COMPONENT_SEPERATION),n=0);var l=t[a],p=d.findCenterOfTree(l);e.x=o,e.y=r,(s=_.radialLayout(l,p,e)).y>n&&(n=Math.floor(s.y)),o=Math.floor(s.x+h.DEFAULT_COMPONENT_SEPERATION)}this.transform(new u(c.WORLD_CENTER_X-s.x/2,c.WORLD_CENTER_Y-s.y/2))},_.radialLayout=function(t,e,i){var n=Math.max(this.maxDiagonalInTree(t),h.DEFAULT_RADIAL_SEPARATION);_.branchRadialLayout(e,null,0,359,0,n);var r=y.calculateBounds(t),o=new E;o.setDeviceOrgX(r.getMinX()),o.setDeviceOrgY(r.getMinY()),o.setWorldOrgX(i.x),o.setWorldOrgY(i.y);for(var s=0;s<t.length;s++)t[s].transform(o);var a=new u(r.getMaxX(),r.getMaxY());return o.inverseTransformPoint(a)},_.branchRadialLayout=function(t,e,i,n,r,o){var s=(n-i+1)/2;s<0&&(s+=180);var a=(s+i)%360*f.TWO_PI/360,h=(Math.cos(a),r*Math.cos(a)),l=r*Math.sin(a);t.setCenter(h,l);var c=[],g=(c=c.concat(t.getEdges())).length;null!=e&&g--;for(var u,d=0,p=c.length,y=t.getEdgesBetween(e);y.length>1;){var E=y[0];y.splice(0,1);var m=c.indexOf(E);m>=0&&c.splice(m,1),p--,g--}u=null!=e?(c.indexOf(y[0])+1)%p:0;for(var v=Math.abs(n-i)/g,N=u;d!=g;N=++N%p){var A=c[N].getOtherEnd(t);if(A!=e){var L=(i+d*v)%360,T=(L+v)%360;_.branchRadialLayout(A,t,L,T,r+o,o),d++}}},_.maxDiagonalInTree=function(t){for(var e=p.MIN_VALUE,i=0;i<t.length;i++){var n=t[i].getDiagonal();n>e&&(e=n)}return e},_.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},_.prototype.groupZeroDegreeMembers=function(){var t=this,e={};this.memberGroups={},this.idToDummyNode={};for(var i=[],n=this.graphManager.getAllNodes(),r=0;r<n.length;r++){var o=(a=n[r]).getParent();0!==this.getNodeDegreeWithChildren(a)||null!=o.id&&this.getToBeTiled(o)||i.push(a)}for(r=0;r<i.length;r++){var a,h=(a=i[r]).getParent().id;void 0===e[h]&&(e[h]=[]),e[h]=e[h].concat(a)}Object.keys(e).forEach((function(i){if(e[i].length>1){var n="DummyCompound_"+i;t.memberGroups[n]=e[i];var r=e[i][0].getParent(),o=new s(t.graphManager);o.id=n,o.paddingLeft=r.paddingLeft||0,o.paddingRight=r.paddingRight||0,o.paddingBottom=r.paddingBottom||0,o.paddingTop=r.paddingTop||0,t.idToDummyNode[n]=o;var a=t.getGraphManager().add(t.newGraph(),o),h=r.getChild();h.add(o);for(var l=0;l<e[i].length;l++){var c=e[i][l];h.remove(c),a.add(c)}}}))},_.prototype.clearCompounds=function(){var t={},e={};this.performDFSOnCompounds();for(var i=0;i<this.compoundOrder.length;i++)e[this.compoundOrder[i].id]=this.compoundOrder[i],t[this.compoundOrder[i].id]=[].concat(this.compoundOrder[i].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[i].getChild()),this.compoundOrder[i].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(t,e)},_.prototype.clearZeroDegreeMembers=function(){var t=this,e=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach((function(i){var n=t.idToDummyNode[i];e[i]=t.tileNodes(t.memberGroups[i],n.paddingLeft+n.paddingRight),n.rect.width=e[i].width,n.rect.height=e[i].height}))},_.prototype.repopulateCompounds=function(){for(var t=this.compoundOrder.length-1;t>=0;t--){var e=this.compoundOrder[t],i=e.id,n=e.paddingLeft,r=e.paddingTop;this.adjustLocations(this.tiledMemberPack[i],e.rect.x,e.rect.y,n,r)}},_.prototype.repopulateZeroDegreeMembers=function(){var t=this,e=this.tiledZeroDegreePack;Object.keys(e).forEach((function(i){var n=t.idToDummyNode[i],r=n.paddingLeft,o=n.paddingTop;t.adjustLocations(e[i],n.rect.x,n.rect.y,r,o)}))},_.prototype.getToBeTiled=function(t){var e=t.id;if(null!=this.toBeTiled[e])return this.toBeTiled[e];var i=t.getChild();if(null==i)return this.toBeTiled[e]=!1,!1;for(var n=i.getNodes(),r=0;r<n.length;r++){var o=n[r];if(this.getNodeDegree(o)>0)return this.toBeTiled[e]=!1,!1;if(null!=o.getChild()){if(!this.getToBeTiled(o))return this.toBeTiled[e]=!1,!1}else this.toBeTiled[o.id]=!1}return this.toBeTiled[e]=!0,!0},_.prototype.getNodeDegree=function(t){t.id;for(var e=t.getEdges(),i=0,n=0;n<e.length;n++){var r=e[n];r.getSource().id!==r.getTarget().id&&(i+=1)}return i},_.prototype.getNodeDegreeWithChildren=function(t){var e=this.getNodeDegree(t);if(null==t.getChild())return e;for(var i=t.getChild().getNodes(),n=0;n<i.length;n++){var r=i[n];e+=this.getNodeDegreeWithChildren(r)}return e},_.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},_.prototype.fillCompexOrderByDFS=function(t){for(var e=0;e<t.length;e++){var i=t[e];null!=i.getChild()&&this.fillCompexOrderByDFS(i.getChild().getNodes()),this.getToBeTiled(i)&&this.compoundOrder.push(i)}},_.prototype.adjustLocations=function(t,e,i,n,r){i+=r;for(var o=e+=n,s=0;s<t.rows.length;s++){var a=t.rows[s];e=o;for(var h=0,l=0;l<a.length;l++){var c=a[l];c.rect.x=e,c.rect.y=i,e+=c.rect.width+t.horizontalPadding,c.rect.height>h&&(h=c.rect.height)}i+=h+t.verticalPadding}},_.prototype.tileCompoundMembers=function(t,e){var i=this;this.tiledMemberPack=[],Object.keys(t).forEach((function(n){var r=e[n];i.tiledMemberPack[n]=i.tileNodes(t[n],r.paddingLeft+r.paddingRight),r.rect.width=i.tiledMemberPack[n].width,r.rect.height=i.tiledMemberPack[n].height}))},_.prototype.tileNodes=function(t,e){var i={rows:[],rowWidth:[],rowHeight:[],width:0,height:e,verticalPadding:h.TILING_PADDING_VERTICAL,horizontalPadding:h.TILING_PADDING_HORIZONTAL};t.sort((function(t,e){return t.rect.width*t.rect.height>e.rect.width*e.rect.height?-1:t.rect.width*t.rect.height<e.rect.width*e.rect.height?1:0}));for(var n=0;n<t.length;n++){var r=t[n];0==i.rows.length?this.insertNodeToRow(i,r,0,e):this.canAddHorizontal(i,r.rect.width,r.rect.height)?this.insertNodeToRow(i,r,this.getShortestRowIndex(i),e):this.insertNodeToRow(i,r,i.rows.length,e),this.shiftToLastRow(i)}return i},_.prototype.insertNodeToRow=function(t,e,i,n){var r=n;i==t.rows.length&&(t.rows.push([]),t.rowWidth.push(r),t.rowHeight.push(0));var o=t.rowWidth[i]+e.rect.width;t.rows[i].length>0&&(o+=t.horizontalPadding),t.rowWidth[i]=o,t.width<o&&(t.width=o);var s=e.rect.height;i>0&&(s+=t.verticalPadding);var a=0;s>t.rowHeight[i]&&(a=t.rowHeight[i],t.rowHeight[i]=s,a=t.rowHeight[i]-a),t.height+=a,t.rows[i].push(e)},_.prototype.getShortestRowIndex=function(t){for(var e=-1,i=Number.MAX_VALUE,n=0;n<t.rows.length;n++)t.rowWidth[n]<i&&(e=n,i=t.rowWidth[n]);return e},_.prototype.getLongestRowIndex=function(t){for(var e=-1,i=Number.MIN_VALUE,n=0;n<t.rows.length;n++)t.rowWidth[n]>i&&(e=n,i=t.rowWidth[n]);return e},_.prototype.canAddHorizontal=function(t,e,i){var n=this.getShortestRowIndex(t);if(n<0)return!0;var r=t.rowWidth[n];if(r+t.horizontalPadding+e<=t.width)return!0;var o,s,a=0;return t.rowHeight[n]<i&&n>0&&(a=i+t.verticalPadding-t.rowHeight[n]),o=t.width-r>=e+t.horizontalPadding?(t.height+a)/(r+e+t.horizontalPadding):(t.height+a)/t.width,a=i+t.verticalPadding,(s=t.width<e?(t.height+a)/e:(t.height+a)/t.width)<1&&(s=1/s),o<1&&(o=1/o),o<s},_.prototype.shiftToLastRow=function(t){var e=this.getLongestRowIndex(t),i=t.rowWidth.length-1,n=t.rows[e],r=n[n.length-1],o=r.width+t.horizontalPadding;if(t.width-t.rowWidth[i]>o&&e!=i){n.splice(-1,1),t.rows[i].push(r),t.rowWidth[e]=t.rowWidth[e]-o,t.rowWidth[i]=t.rowWidth[i]+o,t.width=t.rowWidth[instance.getLongestRowIndex(t)];for(var s=Number.MIN_VALUE,a=0;a<n.length;a++)n[a].height>s&&(s=n[a].height);e>0&&(s+=t.verticalPadding);var h=t.rowHeight[e]+t.rowHeight[i];t.rowHeight[e]=s,t.rowHeight[i]<r.height+t.verticalPadding&&(t.rowHeight[i]=r.height+t.verticalPadding);var l=t.rowHeight[e]+t.rowHeight[i];t.height+=l-h,this.shiftToLastRow(t)}},_.prototype.tilingPreLayout=function(){h.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},_.prototype.tilingPostLayout=function(){h.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},_.prototype.reduceTrees=function(){for(var t,e=[],i=!0;i;){var n=this.graphManager.getAllNodes(),r=[];i=!1;for(var o=0;o<n.length;o++)1!=(t=n[o]).getEdges().length||t.getEdges()[0].isInterGraph||null!=t.getChild()||(r.push([t,t.getEdges()[0],t.getOwner()]),i=!0);if(1==i){for(var s=[],a=0;a<r.length;a++)1==r[a][0].getEdges().length&&(s.push(r[a]),r[a][0].getOwner().remove(r[a][0]));e.push(s),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=e},_.prototype.growTree=function(t){for(var e,i=t[t.length-1],n=0;n<i.length;n++)e=i[n],this.findPlaceforPrunedNode(e),e[2].add(e[0]),e[2].add(e[1],e[1].source,e[1].target);t.splice(t.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},_.prototype.findPlaceforPrunedNode=function(t){var e,i,n=t[0],r=(i=n==t[1].source?t[1].target:t[1].source).startX,o=i.finishX,s=i.startY,a=i.finishY,h=[0,0,0,0];if(s>0)for(var c=r;c<=o;c++)h[0]+=this.grid[c][s-1].length+this.grid[c][s].length-1;if(o<this.grid.length-1)for(c=s;c<=a;c++)h[1]+=this.grid[o+1][c].length+this.grid[o][c].length-1;if(a<this.grid[0].length-1)for(c=r;c<=o;c++)h[2]+=this.grid[c][a+1].length+this.grid[c][a].length-1;if(r>0)for(c=s;c<=a;c++)h[3]+=this.grid[r-1][c].length+this.grid[r][c].length-1;for(var g,u,d=p.MAX_VALUE,f=0;f<h.length;f++)h[f]<d?(d=h[f],g=1,u=f):h[f]==d&&g++;if(3==g&&0==d)0==h[0]&&0==h[1]&&0==h[2]?e=1:0==h[0]&&0==h[1]&&0==h[3]?e=0:0==h[0]&&0==h[2]&&0==h[3]?e=3:0==h[1]&&0==h[2]&&0==h[3]&&(e=2);else if(2==g&&0==d){var y=Math.floor(2*Math.random());e=0==h[0]&&0==h[1]?0==y?0:1:0==h[0]&&0==h[2]?0==y?0:2:0==h[0]&&0==h[3]?0==y?0:3:0==h[1]&&0==h[2]?0==y?1:2:0==h[1]&&0==h[3]?0==y?1:3:0==y?2:3}else e=4==g&&0==d?y=Math.floor(4*Math.random()):u;0==e?n.setCenter(i.getCenterX(),i.getCenterY()-i.getHeight()/2-l.DEFAULT_EDGE_LENGTH-n.getHeight()/2):1==e?n.setCenter(i.getCenterX()+i.getWidth()/2+l.DEFAULT_EDGE_LENGTH+n.getWidth()/2,i.getCenterY()):2==e?n.setCenter(i.getCenterX(),i.getCenterY()+i.getHeight()/2+l.DEFAULT_EDGE_LENGTH+n.getHeight()/2):n.setCenter(i.getCenterX()-i.getWidth()/2-l.DEFAULT_EDGE_LENGTH-n.getWidth()/2,i.getCenterY())},t.exports=_},function(t,e,i){"use strict";var n={};n.layoutBase=i(0),n.CoSEConstants=i(1),n.CoSEEdge=i(2),n.CoSEGraph=i(3),n.CoSEGraphManager=i(4),n.CoSELayout=i(6),n.CoSENode=i(5),t.exports=n}])},t.exports=n(i(3143))}}]);