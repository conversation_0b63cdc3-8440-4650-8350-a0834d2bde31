"use strict";(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[4324],{9254:(e,n,r)=>{r.r(n),r.d(n,{assets:()=>E,contentTitle:()=>w,default:()=>N,frontMatter:()=>R,metadata:()=>t,toc:()=>q});const t=JSON.parse('{"id":"getting-started/quick-start","title":"Quick Start Guide","description":"Get up and running with AdMesh in under 5 minutes! This guide will walk you through making your first recommendation request using any of our SDKs.","source":"@site/docs/getting-started/quick-start.md","sourceDirName":"getting-started","slug":"/getting-started/quick-start","permalink":"/getting-started/quick-start","draft":false,"unlisted":false,"editUrl":"https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/getting-started/quick-start.md","tags":[],"version":"current","sidebarPosition":3,"frontMatter":{"sidebar_position":3},"sidebar":"tutorialSidebar","previous":{"title":"Getting Your API Key","permalink":"/getting-started/api-keys"},"next":{"title":"AdMesh Ad Formats","permalink":"/getting-started/ad-formats"}}');var s=r(4848),o=r(8453),a=r(6540),i=r(4164),l=r(3104),c=r(6347),d=r(205),u=r(7485),h=r(1682),m=r(679);function p(e){return a.Children.toArray(e).filter((e=>"\n"!==e)).map((e=>{if(!e||(0,a.isValidElement)(e)&&function(e){const{props:n}=e;return!!n&&"object"==typeof n&&"value"in n}(e))return e;throw new Error(`Docusaurus error: Bad <Tabs> child <${"string"==typeof e.type?e.type:e.type.name}>: all children of the <Tabs> component should be <TabItem>, and every <TabItem> should have a unique "value" prop.`)}))?.filter(Boolean)??[]}function x(e){const{values:n,children:r}=e;return(0,a.useMemo)((()=>{const e=n??function(e){return p(e).map((({props:{value:e,label:n,attributes:r,default:t}})=>({value:e,label:n,attributes:r,default:t})))}(r);return function(e){const n=(0,h.XI)(e,((e,n)=>e.value===n.value));if(n.length>0)throw new Error(`Docusaurus error: Duplicate values "${n.map((e=>e.value)).join(", ")}" found in <Tabs>. Every value needs to be unique.`)}(e),e}),[n,r])}function f({value:e,tabValues:n}){return n.some((n=>n.value===e))}function g({queryString:e=!1,groupId:n}){const r=(0,c.W6)(),t=function({queryString:e=!1,groupId:n}){if("string"==typeof e)return e;if(!1===e)return null;if(!0===e&&!n)throw new Error('Docusaurus error: The <Tabs> component groupId prop is required if queryString=true, because this value is used as the search param name. You can also provide an explicit value such as queryString="my-search-param".');return n??null}({queryString:e,groupId:n});return[(0,u.aZ)(t),(0,a.useCallback)((e=>{if(!t)return;const n=new URLSearchParams(r.location.search);n.set(t,e),r.replace({...r.location,search:n.toString()})}),[t,r])]}function j(e){const{defaultValue:n,queryString:r=!1,groupId:t}=e,s=x(e),[o,i]=(0,a.useState)((()=>function({defaultValue:e,tabValues:n}){if(0===n.length)throw new Error("Docusaurus error: the <Tabs> component requires at least one <TabItem> children component");if(e){if(!f({value:e,tabValues:n}))throw new Error(`Docusaurus error: The <Tabs> has a defaultValue "${e}" but none of its children has the corresponding value. Available values are: ${n.map((e=>e.value)).join(", ")}. If you intend to show no default tab, use defaultValue={null} instead.`);return e}const r=n.find((e=>e.default))??n[0];if(!r)throw new Error("Unexpected error: 0 tabValues");return r.value}({defaultValue:n,tabValues:s}))),[l,c]=g({queryString:r,groupId:t}),[u,h]=function({groupId:e}){const n=function(e){return e?`docusaurus.tab.${e}`:null}(e),[r,t]=(0,m.Dv)(n);return[r,(0,a.useCallback)((e=>{n&&t.set(e)}),[n,t])]}({groupId:t}),p=(()=>{const e=l??u;return f({value:e,tabValues:s})?e:null})();(0,d.A)((()=>{p&&i(p)}),[p]);return{selectedValue:o,selectValue:(0,a.useCallback)((e=>{if(!f({value:e,tabValues:s}))throw new Error(`Can't select invalid tab value=${e}`);i(e),c(e),h(e)}),[c,h,s]),tabValues:s}}var y=r(2303);const v={tabList:"tabList__CuJ",tabItem:"tabItem_LNqP"};function b({className:e,block:n,selectedValue:r,selectValue:t,tabValues:o}){const a=[],{blockElementScrollPositionUntilNextRender:c}=(0,l.a_)(),d=e=>{const n=e.currentTarget,s=a.indexOf(n),i=o[s].value;i!==r&&(c(n),t(i))},u=e=>{let n=null;switch(e.key){case"Enter":d(e);break;case"ArrowRight":{const r=a.indexOf(e.currentTarget)+1;n=a[r]??a[0];break}case"ArrowLeft":{const r=a.indexOf(e.currentTarget)-1;n=a[r]??a[a.length-1];break}}n?.focus()};return(0,s.jsx)("ul",{role:"tablist","aria-orientation":"horizontal",className:(0,i.A)("tabs",{"tabs--block":n},e),children:o.map((({value:e,label:n,attributes:t})=>(0,s.jsx)("li",{role:"tab",tabIndex:r===e?0:-1,"aria-selected":r===e,ref:e=>{a.push(e)},onKeyDown:u,onClick:d,...t,className:(0,i.A)("tabs__item",v.tabItem,t?.className,{"tabs__item--active":r===e}),children:n??e},e)))})}function k({lazy:e,children:n,selectedValue:r}){const t=(Array.isArray(n)?n:[n]).filter(Boolean);if(e){const e=t.find((e=>e.props.value===r));return e?(0,a.cloneElement)(e,{className:(0,i.A)("margin-top--md",e.props.className)}):null}return(0,s.jsx)("div",{className:"margin-top--md",children:t.map(((e,n)=>(0,a.cloneElement)(e,{key:n,hidden:e.props.value!==r})))})}function _(e){const n=j(e);return(0,s.jsxs)("div",{className:(0,i.A)("tabs-container",v.tabList),children:[(0,s.jsx)(b,{...n,...e}),(0,s.jsx)(k,{...n,...e})]})}function I(e){const n=(0,y.A)();return(0,s.jsx)(_,{...e,children:p(e.children)},String(n))}const S={tabItem:"tabItem_Ymn6"};function A({children:e,hidden:n,className:r}){return(0,s.jsx)("div",{role:"tabpanel",className:(0,i.A)(S.tabItem,r),hidden:n,children:e})}const R={sidebar_position:3},w="Quick Start Guide",E={},q=[{value:"Prerequisites",id:"prerequisites",level:2},{value:"Choose Your Adventure",id:"choose-your-adventure",level:2},{value:"1. Install the Python SDK",id:"1-install-the-python-sdk",level:3},{value:"2. Set up your environment",id:"2-set-up-your-environment",level:3},{value:"3. Make your first request",id:"3-make-your-first-request",level:3},{value:"4. Run your code",id:"4-run-your-code",level:3},{value:"1. Install the TypeScript SDK",id:"1-install-the-typescript-sdk",level:3},{value:"2. Set up your environment",id:"2-set-up-your-environment-1",level:3},{value:"3. Make your first request",id:"3-make-your-first-request-1",level:3},{value:"4. Run your code",id:"4-run-your-code-1",level:3},{value:"1. Install the UI SDK",id:"1-install-the-ui-sdk",level:3},{value:"2. Create a simple React component",id:"2-create-a-simple-react-component",level:3},{value:"3. Run your React app",id:"3-run-your-react-app",level:3},{value:"Expected Output",id:"expected-output",level:2},{value:"Understanding the Response",id:"understanding-the-response",level:2},{value:"Next Steps",id:"next-steps",level:2},{value:"\ud83d\udd27 Customize Your Integration",id:"-customize-your-integration",level:3},{value:"\ud83d\udcda Explore More Features",id:"-explore-more-features",level:3},{value:"\ud83d\udee0 Advanced Configuration",id:"-advanced-configuration",level:3},{value:"Error Handling",id:"error-handling",level:4},{value:"Custom Parameters",id:"custom-parameters",level:4},{value:"Troubleshooting",id:"troubleshooting",level:2},{value:"Common Issues",id:"common-issues",level:3},{value:"Authentication Error",id:"authentication-error",level:4},{value:"No Recommendations",id:"no-recommendations",level:4},{value:"Rate Limit",id:"rate-limit",level:4},{value:"Getting Help",id:"getting-help",level:3}];function M(e){const n={a:"a",code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",hr:"hr",li:"li",p:"p",pre:"pre",strong:"strong",ul:"ul",...(0,o.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.header,{children:(0,s.jsx)(n.h1,{id:"quick-start-guide",children:"Quick Start Guide"})}),"\n",(0,s.jsx)(n.p,{children:"Get up and running with AdMesh in under 5 minutes! This guide will walk you through making your first recommendation request using any of our SDKs."}),"\n",(0,s.jsx)(n.h2,{id:"prerequisites",children:"Prerequisites"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:["\u2705 AdMesh account (",(0,s.jsx)(n.a,{href:"https://useadmesh.com/agent",children:"sign up here"}),")"]}),"\n",(0,s.jsxs)(n.li,{children:["\u2705 API key (",(0,s.jsx)(n.a,{href:"/getting-started/api-keys",children:"get yours here"}),")"]}),"\n",(0,s.jsx)(n.li,{children:"\u2705 Development environment (Python 3.8+, Node.js 18+, or React app)"}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"choose-your-adventure",children:"Choose Your Adventure"}),"\n",(0,s.jsx)(n.p,{children:"Select the SDK that matches your technology stack:"}),"\n","\n",(0,s.jsxs)(I,{children:[(0,s.jsxs)(A,{value:"python",label:"\ud83d\udc0d Python",default:!0,children:[(0,s.jsx)(n.h3,{id:"1-install-the-python-sdk",children:"1. Install the Python SDK"}),(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-bash",children:"pip install admesh-python\n"})}),(0,s.jsx)(n.h3,{id:"2-set-up-your-environment",children:"2. Set up your environment"}),(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-bash",children:'# Create a .env file\necho "ADMESH_API_KEY=your_api_key_here" > .env\n'})}),(0,s.jsx)(n.h3,{id:"3-make-your-first-request",children:"3. Make your first request"}),(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-python",children:'import os\nfrom admesh import Admesh\n\n# Initialize the client\nclient = Admesh(api_key=os.environ.get("ADMESH_API_KEY"))\n\n# Get recommendations\nresponse = client.recommend.get_recommendations(\n    query="Best CRM for remote teams",\n    format="auto"\n)\n\n# Print results\nprint(f"Recommendation ID: {response.recommendation_id}")\nprint(f"Found {len(response.response.recommendations)} recommendations:")\n\nfor rec in response.response.recommendations:\n    print(f"\\n\ud83c\udfaf {rec.title}")\n    print(f"   Reason: {rec.reason}")\n    print(f"   Match Score: {rec.intent_match_score:.2f}")\n    print(f"   Link: {rec.admesh_link}")\n'})}),(0,s.jsx)(n.h3,{id:"4-run-your-code",children:"4. Run your code"}),(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-bash",children:"python your_script.py\n"})})]}),(0,s.jsxs)(A,{value:"typescript",label:"\ud83d\udfe6 TypeScript",children:[(0,s.jsx)(n.h3,{id:"1-install-the-typescript-sdk",children:"1. Install the TypeScript SDK"}),(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-bash",children:"npm install admesh\n# or\nyarn add admesh\n"})}),(0,s.jsx)(n.h3,{id:"2-set-up-your-environment-1",children:"2. Set up your environment"}),(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-bash",children:'# Create a .env file\necho "ADMESH_API_KEY=your_api_key_here" > .env\n'})}),(0,s.jsx)(n.h3,{id:"3-make-your-first-request-1",children:"3. Make your first request"}),(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-typescript",children:"import Admesh from 'admesh';\n\n// Initialize the client\nconst client = new Admesh({\n  apiKey: process.env.ADMESH_API_KEY\n});\n\nasync function getRecommendations() {\n  try {\n    // Get recommendations\n    const response = await client.recommend.getRecommendations({\n      query: 'Best CRM for remote teams',\n      format: 'auto'\n    });\n\n    // Print results\n    console.log(`Recommendation ID: ${response.recommendation_id}`);\n    console.log(`Found ${response.response?.recommendations?.length} recommendations:`);\n\n    response.response?.recommendations?.forEach(rec => {\n      console.log(`\\n\ud83c\udfaf ${rec.title}`);\n      console.log(`   Reason: ${rec.reason}`);\n      console.log(`   Match Score: ${rec.intent_match_score?.toFixed(2)}`);\n      console.log(`   Link: ${rec.admesh_link}`);\n    });\n  } catch (error) {\n    console.error('Error getting recommendations:', error);\n  }\n}\n\ngetRecommendations();\n"})}),(0,s.jsx)(n.h3,{id:"4-run-your-code-1",children:"4. Run your code"}),(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-bash",children:"# For Node.js with dotenv\nnpm install dotenv\nnode -r dotenv/config your_script.js\n\n# For TypeScript\nnpx ts-node your_script.ts\n"})})]}),(0,s.jsxs)(A,{value:"react",label:"\u269b\ufe0f React",children:[(0,s.jsx)(n.h3,{id:"1-install-the-ui-sdk",children:"1. Install the UI SDK"}),(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-bash",children:"npm install admesh-ui-sdk\n# or\nyarn add admesh-ui-sdk\n"})}),(0,s.jsx)(n.h3,{id:"2-create-a-simple-react-component",children:"2. Create a simple React component"}),(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-tsx",children:'import React, { useState, useEffect } from \'react\';\nimport { AdMeshLayout } from \'admesh-ui-sdk\';\n\n// Mock recommendations for this example\nconst mockRecommendations = [\n  {\n    title: "HubSpot CRM",\n    reason: "Perfect for remote teams with excellent collaboration features",\n    intent_match_score: 0.92,\n    admesh_link: "https://useadmesh.com/track?ad_id=hubspot-123",\n    ad_id: "hubspot-123",\n    product_id: "hubspot-crm",\n    has_free_tier: true,\n    trial_days: 14,\n    keywords: ["CRM", "Sales", "Marketing"]\n  },\n  {\n    title: "Salesforce",\n    reason: "Enterprise-grade CRM with powerful automation",\n    intent_match_score: 0.88,\n    admesh_link: "https://useadmesh.com/track?ad_id=salesforce-456",\n    ad_id: "salesforce-456", \n    product_id: "salesforce-crm",\n    has_free_tier: false,\n    trial_days: 30,\n    keywords: ["CRM", "Enterprise", "Automation"]\n  }\n];\n\nfunction App() {\n  const [recommendations, setRecommendations] = useState([]);\n\n  useEffect(() => {\n    // In a real app, you\'d fetch from your backend\n    // const response = await fetch(\'/api/recommendations\');\n    // const data = await response.json();\n    // setRecommendations(data.recommendations);\n    \n    setRecommendations(mockRecommendations);\n  }, []);\n\n  return (\n    <div className="App">\n      <h1>AdMesh Quick Start</h1>\n      <p>Here are some CRM recommendations for remote teams:</p>\n      \n      <AdMeshLayout\n        recommendations={recommendations}\n        autoLayout={true}\n        showMatchScores={true}\n        onProductClick={(adId, admeshLink) => {\n          console.log(\'Product clicked:\', { adId, admeshLink });\n          window.open(admeshLink, \'_blank\');\n        }}\n      />\n    </div>\n  );\n}\n\nexport default App;\n'})}),(0,s.jsx)(n.h3,{id:"3-run-your-react-app",children:"3. Run your React app"}),(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-bash",children:"npm start\n# or\nyarn start\n"})})]})]}),"\n",(0,s.jsx)(n.h2,{id:"expected-output",children:"Expected Output"}),"\n",(0,s.jsx)(n.p,{children:"When you run any of the examples above, you should see output similar to:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"Recommendation ID: rec_abc123xyz\nFound 2 recommendations:\n\n\ud83c\udfaf HubSpot CRM\n   Reason: Perfect for remote teams with excellent collaboration features\n   Match Score: 0.92\n   Link: https://useadmesh.com/track?ad_id=hubspot-123\n\n\ud83c\udfaf Salesforce\n   Reason: Enterprise-grade CRM with powerful automation\n   Match Score: 0.88\n   Link: https://useadmesh.com/track?ad_id=salesforce-456\n"})}),"\n",(0,s.jsx)(n.h2,{id:"understanding-the-response",children:"Understanding the Response"}),"\n",(0,s.jsx)(n.p,{children:"Each recommendation includes:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"title"})," - Product name"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"reason"})," - AI-generated explanation for why it's recommended"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"intent_match_score"})," - How well it matches the query (0-1)"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"admesh_link"})," - Tracked link for analytics and monetization"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"ad_id"})," - Unique identifier for the advertisement"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"product_id"})," - Unique identifier for the product"]}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"next-steps",children:"Next Steps"}),"\n",(0,s.jsx)(n.h3,{id:"-customize-your-integration",children:"\ud83d\udd27 Customize Your Integration"}),"\n",(0,s.jsx)("div",{className:"feature-box feature-box--tip",children:(0,s.jsxs)(n.p,{children:[(0,s.jsx)(n.strong,{children:"For AI Applications"}),": Check out our ",(0,s.jsx)(n.a,{href:"/ai-integration/overview",children:"AI Integration Guide"})," for chatbots and AI assistants."]})}),"\n",(0,s.jsx)("div",{className:"feature-box feature-box--tip",children:(0,s.jsxs)(n.p,{children:[(0,s.jsx)(n.strong,{children:"For Web Applications"}),": Explore our ",(0,s.jsx)(n.a,{href:"/ui-sdk/installation",children:"UI SDK Installation"})," for rich recommendation displays."]})}),"\n",(0,s.jsx)("div",{className:"feature-box feature-box--tip",children:(0,s.jsxs)(n.p,{children:[(0,s.jsx)(n.strong,{children:"For Backend Services"}),": Learn about ",(0,s.jsx)(n.a,{href:"/python-sdk/installation",children:"Python SDK"})," and ",(0,s.jsx)(n.a,{href:"/typescript-sdk/installation",children:"TypeScript SDK"}),"."]})}),"\n",(0,s.jsx)(n.h3,{id:"-explore-more-features",children:"\ud83d\udcda Explore More Features"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:(0,s.jsx)(n.a,{href:"/ai-integration/overview",children:"AI Integration"})})," - Citation-based recommendations for AI assistants"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:(0,s.jsx)(n.a,{href:"/api/authentication",children:"API Reference"})})," - Complete API documentation"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:(0,s.jsx)(n.a,{href:"/examples/ai-assistant",children:"Examples"})})," - Implementation examples"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:(0,s.jsx)(n.a,{href:"/ui-sdk/installation",children:"UI SDK"})})," - Frontend component integration"]}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"-advanced-configuration",children:"\ud83d\udee0 Advanced Configuration"}),"\n",(0,s.jsx)(n.h4,{id:"error-handling",children:"Error Handling"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-python",children:'import admesh\n\ntry:\n    response = client.recommend.get_recommendations(\n        query="Best CRM for remote teams",\n        format="auto"\n    )\nexcept admesh.NoRecommendationsError:\n    print("No recommendations found for this query")\nexcept admesh.RateLimitError:\n    print("Rate limit exceeded, please try again later")\nexcept admesh.APIError as e:\n    print(f"API error: {e}")\n'})}),"\n",(0,s.jsx)(n.h4,{id:"custom-parameters",children:"Custom Parameters"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-python",children:'response = client.recommend.get_recommendations(\n    query="Best CRM for remote teams",\n    format="auto",\n    max_recommendations=5,\n    include_free_tier=True,\n    min_trust_score=0.8\n)\n'})}),"\n",(0,s.jsx)(n.h2,{id:"troubleshooting",children:"Troubleshooting"}),"\n",(0,s.jsx)(n.h3,{id:"common-issues",children:"Common Issues"}),"\n",(0,s.jsx)(n.h4,{id:"authentication-error",children:"Authentication Error"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"Error: Invalid API key\n"})}),"\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)(n.strong,{children:"Solution"}),": Double-check your API key and ensure it's set correctly in your environment variables."]}),"\n",(0,s.jsx)(n.h4,{id:"no-recommendations",children:"No Recommendations"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"NoRecommendationsError: No recommendations available\n"})}),"\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)(n.strong,{children:"Solution"}),": Try a different query or set ",(0,s.jsx)(n.code,{children:"raise_on_empty_recommendations=False"})," to handle empty results gracefully."]}),"\n",(0,s.jsx)(n.h4,{id:"rate-limit",children:"Rate Limit"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"RateLimitError: Rate limit exceeded\n"})}),"\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)(n.strong,{children:"Solution"}),": Implement exponential backoff or upgrade your plan for higher limits."]}),"\n",(0,s.jsx)(n.h3,{id:"getting-help",children:"Getting Help"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:["\ud83d\udcd6 ",(0,s.jsx)(n.strong,{children:"Documentation"}),": Browse our comprehensive guides"]}),"\n",(0,s.jsxs)(n.li,{children:["\ud83d\udc1b ",(0,s.jsx)(n.strong,{children:"Issues"}),": ",(0,s.jsx)(n.a,{href:"https://github.com/GouniManikumar12/admesh-python/issues",children:"Report bugs on GitHub"})]}),"\n",(0,s.jsxs)(n.li,{children:["\ud83d\udcac ",(0,s.jsx)(n.strong,{children:"Support"}),": ",(0,s.jsx)(n.a,{href:"mailto:<EMAIL>",children:"<EMAIL>"})]}),"\n"]}),"\n",(0,s.jsx)(n.hr,{}),"\n",(0,s.jsxs)(n.p,{children:["\ud83c\udf89 ",(0,s.jsx)(n.strong,{children:"Congratulations!"})," You've successfully made your first AdMesh API call. Ready to build something amazing? Explore our detailed SDK guides and examples to learn more!"]})]})}function N(e={}){const{wrapper:n}={...(0,o.R)(),...e.components};return n?(0,s.jsx)(n,{...e,children:(0,s.jsx)(M,{...e})}):M(e)}}}]);