"use strict";(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[3271],{2776:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>c,contentTitle:()=>o,default:()=>h,frontMatter:()=>a,metadata:()=>t,toc:()=>l});const t=JSON.parse('{"id":"getting-started/overview","title":"Overview","description":"This guide provides technical overview and core concepts for integrating AdMesh AI-powered product recommendation capabilities into enterprise applications.","source":"@site/docs/getting-started/overview.md","sourceDirName":"getting-started","slug":"/getting-started/overview","permalink":"/getting-started/overview","draft":false,"unlisted":false,"editUrl":"https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/getting-started/overview.md","tags":[],"version":"current","sidebarPosition":1,"frontMatter":{"sidebar_position":1},"sidebar":"tutorialSidebar","previous":{"title":"Introduction","permalink":"/"},"next":{"title":"Getting Your API Key","permalink":"/getting-started/api-keys"}}');var s=i(4848),r=i(8453);const a={sidebar_position:1},o="Overview",c={},l=[{value:"What is AdMesh?",id:"what-is-admesh",level:2},{value:"Core Concepts",id:"core-concepts",level:2},{value:"Intent Detection",id:"intent-detection",level:3},{value:"Semantic Matching",id:"semantic-matching",level:3},{value:"Recommendation Scoring",id:"recommendation-scoring",level:3},{value:"Architecture Overview",id:"architecture-overview",level:2},{value:"SDK Ecosystem",id:"sdk-ecosystem",level:2},{value:"Backend SDKs",id:"backend-sdks",level:3},{value:"Frontend SDK",id:"frontend-sdk",level:3},{value:"Integration Patterns",id:"integration-patterns",level:2},{value:"AI Assistant Integration",id:"ai-assistant-integration",level:3},{value:"E-commerce Integration",id:"e-commerce-integration",level:3},{value:"Content-Based Integration",id:"content-based-integration",level:3},{value:"Key Features",id:"key-features",level:2},{value:"AI-First Architecture",id:"ai-first-architecture",level:3},{value:"UI Component Library",id:"ui-component-library",level:3},{value:"Analytics and Tracking",id:"analytics-and-tracking",level:3},{value:"Customization Options",id:"customization-options",level:3},{value:"Implementation Checklist",id:"implementation-checklist",level:2},{value:"Next Steps",id:"next-steps",level:2},{value:"Use Cases",id:"use-cases",level:2},{value:"AI Conversational Interfaces",id:"ai-conversational-interfaces",level:3},{value:"E-commerce Platforms",id:"e-commerce-platforms",level:3},{value:"Content Platforms",id:"content-platforms",level:3},{value:"SaaS Applications",id:"saas-applications",level:3}];function d(e){const n={a:"a",code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",hr:"hr",input:"input",li:"li",mermaid:"mermaid",ol:"ol",p:"p",pre:"pre",strong:"strong",ul:"ul",...(0,r.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.header,{children:(0,s.jsx)(n.h1,{id:"overview",children:"Overview"})}),"\n",(0,s.jsx)(n.p,{children:"This guide provides technical overview and core concepts for integrating AdMesh AI-powered product recommendation capabilities into enterprise applications."}),"\n",(0,s.jsx)(n.h2,{id:"what-is-admesh",children:"What is AdMesh?"}),"\n",(0,s.jsx)(n.p,{children:"AdMesh is an enterprise recommendation engine designed for AI applications, conversational interfaces, and modern web platforms. The system uses machine learning algorithms to analyze user intent and deliver contextually relevant product recommendations."}),"\n",(0,s.jsx)(n.h2,{id:"core-concepts",children:"Core Concepts"}),"\n",(0,s.jsx)(n.h3,{id:"intent-detection",children:"Intent Detection"}),"\n",(0,s.jsx)(n.p,{children:"AdMesh analyzes user queries to categorize intent types:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"compare_products"})," - Comparative analysis requests"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"best_for_use_case"})," - Scenario-specific recommendations"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"trial_demo"})," - Product evaluation inquiries"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"budget_conscious"})," - Cost-optimized suggestions"]}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"semantic-matching",children:"Semantic Matching"}),"\n",(0,s.jsx)(n.p,{children:"The recommendation engine implements:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Text embeddings"})," using OpenAI's text-embedding-3-small model"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Cosine similarity"})," algorithms for semantic matching"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Trust scores"})," for quality assurance"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Keyword matching"})," for precision targeting"]}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"recommendation-scoring",children:"Recommendation Scoring"}),"\n",(0,s.jsx)(n.p,{children:"Each recommendation provides:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Intent match score"})," (0-1) - Query relevance measurement"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Trust score"})," - Quality and reliability metrics"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Reason"})," - AI-generated recommendation rationale"]}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"architecture-overview",children:"Architecture Overview"}),"\n",(0,s.jsx)(n.mermaid,{value:"graph TD\n    A[User Query] --\x3e B[Intent Detection]\n    B --\x3e C[Semantic Matching]\n    C --\x3e D[Trust Score Filtering]\n    D --\x3e E[Recommendation Generation]\n    E --\x3e F[Response with AdMesh Links]\n    F --\x3e G[Tracking & Analytics]"}),"\n",(0,s.jsx)(n.h2,{id:"sdk-ecosystem",children:"SDK Ecosystem"}),"\n",(0,s.jsx)(n.h3,{id:"backend-sdks",children:"Backend SDKs"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Python SDK"})," - For AI applications, data processing, and backend services"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"TypeScript SDK"})," - For Node.js applications and serverless functions"]}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"frontend-sdk",children:"Frontend SDK"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"UI SDK"})," - React components for displaying recommendations with built-in tracking"]}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"integration-patterns",children:"Integration Patterns"}),"\n",(0,s.jsx)(n.h3,{id:"ai-assistant-integration",children:"AI Assistant Integration"}),"\n",(0,s.jsx)(n.p,{children:"Implementation for conversational interfaces and AI assistants:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-python",children:'# Intent detection and recommendation retrieval\nresponse = client.recommend.get_recommendations(\n    query="Enterprise CRM solution requirements",\n    format="auto"\n)\n\n# Process recommendations for chat interface\nfor rec in response.response.recommendations:\n    print(f"Recommendation: {rec.title} - {rec.reason}")\n'})}),"\n",(0,s.jsx)(n.h3,{id:"e-commerce-integration",children:"E-commerce Integration"}),"\n",(0,s.jsx)(n.p,{children:"Product discovery enhancement for e-commerce platforms:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-typescript",children:"// User behavior-based recommendations\nconst recommendations = await client.recommend.getRecommendations({\n  query: userQuery,\n  format: 'auto'\n});\n\n// UI component integration\n<AdMeshLayout recommendations={recommendations} />\n"})}),"\n",(0,s.jsx)(n.h3,{id:"content-based-integration",children:"Content-Based Integration"}),"\n",(0,s.jsx)(n.p,{children:"Contextual product recommendations for content platforms:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-tsx",children:'// Citation-based recommendation display\n<AdMeshCitationUnit\n  recommendations={recommendations}\n  conversationText="For project management solutions..."\n  citationStyle="numbered"\n/>\n'})}),"\n",(0,s.jsx)(n.h2,{id:"key-features",children:"Key Features"}),"\n",(0,s.jsx)(n.h3,{id:"ai-first-architecture",children:"AI-First Architecture"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Purpose-built for AI applications"}),"\n",(0,s.jsx)(n.li,{children:"Advanced intent detection algorithms"}),"\n",(0,s.jsx)(n.li,{children:"Contextual analysis capabilities"}),"\n",(0,s.jsx)(n.li,{children:"Natural language processing integration"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"ui-component-library",children:"UI Component Library"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Production-ready React components"}),"\n",(0,s.jsx)(n.li,{children:"Citation-based conversational interfaces"}),"\n",(0,s.jsx)(n.li,{children:"Floating chat widget implementations"}),"\n",(0,s.jsx)(n.li,{children:"Sidebar component options"}),"\n",(0,s.jsx)(n.li,{children:"Automated recommendation widgets"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"analytics-and-tracking",children:"Analytics and Tracking"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Automated view tracking"}),"\n",(0,s.jsx)(n.li,{children:"Click-through rate monitoring"}),"\n",(0,s.jsx)(n.li,{children:"Conversion attribution"}),"\n",(0,s.jsx)(n.li,{children:"Revenue analytics"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"customization-options",children:"Customization Options"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Light and dark theme support"}),"\n",(0,s.jsx)(n.li,{children:"Custom accent color configuration"}),"\n",(0,s.jsx)(n.li,{children:"Responsive design implementation"}),"\n",(0,s.jsx)(n.li,{children:"Accessibility compliance"}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"implementation-checklist",children:"Implementation Checklist"}),"\n",(0,s.jsxs)(n.ul,{className:"contains-task-list",children:["\n",(0,s.jsxs)(n.li,{className:"task-list-item",children:[(0,s.jsx)(n.input,{type:"checkbox",disabled:!0})," ","Register account at ",(0,s.jsx)(n.a,{href:"https://useadmesh.com/agent",children:"useadmesh.com/agent"})]}),"\n",(0,s.jsxs)(n.li,{className:"task-list-item",children:[(0,s.jsx)(n.input,{type:"checkbox",disabled:!0})," ","Obtain API credentials from dashboard"]}),"\n",(0,s.jsxs)(n.li,{className:"task-list-item",children:[(0,s.jsx)(n.input,{type:"checkbox",disabled:!0})," ","Select appropriate SDK (Python or UI)"]}),"\n",(0,s.jsxs)(n.li,{className:"task-list-item",children:[(0,s.jsx)(n.input,{type:"checkbox",disabled:!0})," ","Install SDK in development environment"]}),"\n",(0,s.jsxs)(n.li,{className:"task-list-item",children:[(0,s.jsx)(n.input,{type:"checkbox",disabled:!0})," ","Execute initial API integration"]}),"\n",(0,s.jsxs)(n.li,{className:"task-list-item",children:[(0,s.jsx)(n.input,{type:"checkbox",disabled:!0})," ","Implement recommendation display"]}),"\n",(0,s.jsxs)(n.li,{className:"task-list-item",children:[(0,s.jsx)(n.input,{type:"checkbox",disabled:!0})," ","Configure tracking and analytics"]}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"next-steps",children:"Next Steps"}),"\n",(0,s.jsxs)(n.ol,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:(0,s.jsx)(n.a,{href:"/getting-started/api-keys",children:"Configure API Authentication"})})," - Set up credentials"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:(0,s.jsx)(n.a,{href:"/getting-started/quick-start",children:"Quick Start Implementation"})})," - Execute first API call"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"SDK Selection"}),":","\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.a,{href:"/python-sdk/installation",children:"Python SDK"})," for backend applications"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.a,{href:"/ui-sdk/installation",children:"UI SDK"})," for React frontend components"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"use-cases",children:"Use Cases"}),"\n",(0,s.jsx)(n.h3,{id:"ai-conversational-interfaces",children:"AI Conversational Interfaces"}),"\n",(0,s.jsx)(n.p,{children:"Product recommendation integration for conversational systems:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Customer support automation"}),"\n",(0,s.jsx)(n.li,{children:"Shopping assistance platforms"}),"\n",(0,s.jsx)(n.li,{children:"Business advisory systems"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"e-commerce-platforms",children:"E-commerce Platforms"}),"\n",(0,s.jsx)(n.p,{children:"Product discovery and conversion optimization:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Recommendation engine implementation"}),"\n",(0,s.jsx)(n.li,{children:"Search result enhancement"}),"\n",(0,s.jsx)(n.li,{children:"Personalized user experiences"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"content-platforms",children:"Content Platforms"}),"\n",(0,s.jsx)(n.p,{children:"Contextual product suggestion integration:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Editorial content recommendations"}),"\n",(0,s.jsx)(n.li,{children:"Tutorial tool suggestions"}),"\n",(0,s.jsx)(n.li,{children:"Review platform integrations"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"saas-applications",children:"SaaS Applications"}),"\n",(0,s.jsx)(n.p,{children:"Tool discovery and optimization:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Workflow optimization recommendations"}),"\n",(0,s.jsx)(n.li,{children:"Integration suggestions"}),"\n",(0,s.jsx)(n.li,{children:"Feature discovery systems"}),"\n"]}),"\n",(0,s.jsx)(n.hr,{}),"\n",(0,s.jsxs)(n.p,{children:["Begin implementation by ",(0,s.jsx)(n.a,{href:"/getting-started/api-keys",children:"configuring API authentication"})," and executing your first recommendation request."]})]})}function h(e={}){const{wrapper:n}={...(0,r.R)(),...e.components};return n?(0,s.jsx)(n,{...e,children:(0,s.jsx)(d,{...e})}):d(e)}}}]);