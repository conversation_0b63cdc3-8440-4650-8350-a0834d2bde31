"use strict";(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[8731],{418:(e,t,n)=>{n.d(t,{Bd:()=>f,P3:()=>x,PV:()=>k,Rp:()=>T,S:()=>I,SS:()=>g,U5:()=>A,Uz:()=>$,Xq:()=>R,YV:()=>l,eb:()=>h,g4:()=>d,qO:()=>p});var r=n(1564),i=n(2151),s=n(2479),a=n(9683),o=n(6373),c=n(2806);function l(e,t){const n=new Set,r=function(e){return e.rules.find((e=>i.s7(e)&&e.entry))}(e);if(!r)return new Set(e.rules);const s=[r].concat(function(e){return e.rules.filter((e=>i.rE(e)&&e.hidden))}(e));for(const i of s)u(i,n,t);const a=new Set;for(const o of e.rules)(n.has(o.name)||i.rE(o)&&o.hidden)&&a.add(o);return a}function u(e,t,n){t.add(e.name),(0,a.Uo)(e).forEach((e=>{if(i.$g(e)||n&&i.lF(e)){const r=e.rule.ref;r&&!t.has(r.name)&&u(r,t,n)}}))}function d(e){if(e.terminal)return e.terminal;if(e.type.ref){const t=A(e.type.ref);return null==t?void 0:t.terminal}}function h(e){return e.hidden&&!(0,c.Yv)(I(e))}function f(e,t){return e&&t?m(e,t,e.astNode,!0):[]}function p(e,t,n){if(!e||!t)return;const r=m(e,t,e.astNode,!0);return 0!==r.length?r[n=void 0!==n?Math.max(0,Math.min(n,r.length-1)):0]:void 0}function m(e,t,n,r){if(!r){const n=(0,a.XG)(e.grammarSource,i.wh);if(n&&n.feature===t)return[e]}return(0,s.mD)(e)&&e.astNode===n?e.content.flatMap((e=>m(e,t,n,!1))):[]}function g(e,t,n){if(!e)return;const r=y(e,t,null==e?void 0:e.astNode);return 0!==r.length?r[n=void 0!==n?Math.max(0,Math.min(n,r.length-1)):0]:void 0}function y(e,t,n){if(e.astNode!==n)return[];if(i.wb(e.grammarSource)&&e.grammarSource.value===t)return[e];const r=(0,o.NS)(e).iterator();let s;const a=[];do{if(s=r.next(),!s.done){const e=s.value;e.astNode===n?i.wb(e.grammarSource)&&e.grammarSource.value===t&&a.push(e):r.prune()}}while(!s.done);return a}function T(e){var t;const n=e.astNode;for(;n===(null===(t=e.container)||void 0===t?void 0:t.astNode);){const t=(0,a.XG)(e.grammarSource,i.wh);if(t)return t;e=e.container}}function A(e){let t=e;return i.SP(t)&&(i.ve(t.$container)?t=t.$container.$container:i.s7(t.$container)?t=t.$container:(0,r.d)(t.$container)),v(e,t,new Map)}function v(e,t,n){var r;function s(t,r){let s;return(0,a.XG)(t,i.wh)||(s=v(r,r,n)),n.set(e,s),s}if(n.has(e))return n.get(e);n.set(e,void 0);for(const o of(0,a.Uo)(t)){if(i.wh(o)&&"name"===o.feature.toLowerCase())return n.set(e,o),o;if(i.$g(o)&&i.s7(o.rule.ref))return s(o,o.rule.ref);if(i.D8(o)&&(null===(r=o.typeRef)||void 0===r?void 0:r.ref))return s(o,o.typeRef.ref)}}function R(e){return E(e,new Set)}function E(e,t){if(t.has(e))return!0;t.add(e);for(const n of(0,a.Uo)(e))if(i.$g(n)){if(!n.rule.ref)return!1;if(i.s7(n.rule.ref)&&!E(n.rule.ref,t))return!1}else{if(i.wh(n))return!1;if(i.ve(n))return!1}return Boolean(e.definition)}function k(e){if(e.inferredType)return e.inferredType.name;if(e.dataType)return e.dataType;if(e.returnType){const t=e.returnType.ref;if(t){if(i.s7(t))return t.name;if(i.S2(t)||i.Xj(t))return t.name}}}function $(e){var t;if(i.s7(e))return R(e)?e.name:null!==(t=k(e))&&void 0!==t?t:e.name;if(i.S2(e)||i.Xj(e)||i.fG(e))return e.name;if(i.ve(e)){const t=function(e){var t;if(e.inferredType)return e.inferredType.name;if(null===(t=e.type)||void 0===t?void 0:t.ref)return $(e.type.ref);return}(e);if(t)return t}else if(i.SP(e))return e.name;throw new Error("Cannot get name of Unknown Type")}function x(e){var t,n,r;return i.rE(e)?null!==(n=null===(t=e.type)||void 0===t?void 0:t.name)&&void 0!==n?n:"string":null!==(r=k(e))&&void 0!==r?r:e.name}function I(e){const t={s:!1,i:!1,u:!1},n=N(e.definition,t),r=Object.entries(t).filter((([,e])=>e)).map((([e])=>e)).join("");return new RegExp(n,r)}const S=/[\s\S]/.source;function N(e,t){if(i.Fy(e))return w((a=e).elements.map((e=>N(e))).join("|"),{cardinality:a.cardinality,lookahead:a.lookahead});if(i.O4(e))return w((s=e).elements.map((e=>N(e))).join(""),{cardinality:s.cardinality,lookahead:s.lookahead});if(i.Bg(e))return function(e){if(e.right)return w(`[${C(e.left)}-${C(e.right)}]`,{cardinality:e.cardinality,lookahead:e.lookahead,wrap:!1});return w(C(e.left),{cardinality:e.cardinality,lookahead:e.lookahead,wrap:!1})}(e);if(i.lF(e)){const t=e.rule.ref;if(!t)throw new Error("Missing rule reference.");return w(N(t.definition),{cardinality:e.cardinality,lookahead:e.lookahead})}if(i.GL(e))return w(`(?!${N((r=e).terminal)})${S}*?`,{cardinality:r.cardinality,lookahead:r.lookahead});if(i.Mz(e))return w(`${S}*?${N((n=e).terminal)}`,{cardinality:n.cardinality,lookahead:n.lookahead});if(i.vd(e)){const n=e.regex.lastIndexOf("/"),r=e.regex.substring(1,n),i=e.regex.substring(n+1);return t&&(t.i=i.includes("i"),t.s=i.includes("s"),t.u=i.includes("u")),w(r,{cardinality:e.cardinality,lookahead:e.lookahead,wrap:!1})}if(i.z2(e))return w(S,{cardinality:e.cardinality,lookahead:e.lookahead});throw new Error(`Invalid terminal element: ${null==e?void 0:e.$type}`);var n,r,s,a}function C(e){return(0,c.Nt)(e.value)}function w(e,t){var n;return(!1!==t.wrap||t.lookahead)&&(e=`(${null!==(n=t.lookahead)&&void 0!==n?n:""}${e})`),t.cardinality?`${e}${t.cardinality}`:e}},655:(e,t,n)=>{n.d(t,{f:()=>l});var r=n(3579),i=n(4298),s=n(9364),a=n(1294),o=class extends r.mR{static{(0,r.K2)(this,"RadarTokenBuilder")}constructor(){super(["radar-beta"])}},c={parser:{TokenBuilder:(0,r.K2)((()=>new o),"TokenBuilder"),ValueConverter:(0,r.K2)((()=>new r.Tm),"ValueConverter")}};function l(e=i.D){const t=(0,s.WQ)((0,a.u)(e),r.sr),n=(0,s.WQ)((0,a.t)({shared:t}),r.YP,c);return t.ServiceRegistry.register(n),{shared:t,Radar:n}}(0,r.K2)(l,"createRadarServices")},966:(e,t)=>{function n(e){return"string"==typeof e||e instanceof String}function r(e){return Array.isArray(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.stringArray=t.array=t.func=t.error=t.number=t.string=t.boolean=void 0,t.boolean=function(e){return!0===e||!1===e},t.string=n,t.number=function(e){return"number"==typeof e||e instanceof Number},t.error=function(e){return e instanceof Error},t.func=function(e){return"function"==typeof e},t.array=r,t.stringArray=function(e){return r(e)&&e.every((e=>n(e)))}},1294:(e,t,n)=>{n.d(t,{t:()=>Ur,u:()=>Fr});var r=n(6373),i=n(418),s=n(2806),a=n(2151);var o=n(9637),c=n(4722),l=n(4092);function u(e,t,n){return`${e.name}_${t}_${n}`}class d{constructor(e){this.target=e}isEpsilon(){return!1}}class h extends d{constructor(e,t){super(e),this.tokenType=t}}class f extends d{constructor(e){super(e)}isEpsilon(){return!0}}class p extends d{constructor(e,t,n){super(e),this.rule=t,this.followState=n}isEpsilon(){return!0}}function m(e){const t={decisionMap:{},decisionStates:[],ruleToStartState:new Map,ruleToStopState:new Map,states:[]};!function(e,t){const n=t.length;for(let r=0;r<n;r++){const n=t[r],i=x(e,n,void 0,{type:2}),s=x(e,n,void 0,{type:7});i.stop=s,e.ruleToStartState.set(n,i),e.ruleToStopState.set(n,s)}}(t,e);const n=e.length;for(let r=0;r<n;r++){const n=e[r],i=y(t,n,n);void 0!==i&&k(t,n,i)}return t}function g(e,t,n){return n instanceof o.BK?E(e,t,n.terminalType,n):n instanceof o.wL?function(e,t,n){const r=n.referencedRule,i=e.ruleToStartState.get(r),s=x(e,t,n,{type:1}),a=x(e,t,n,{type:1}),o=new p(i,r,a);return I(s,o),{left:s,right:a}}(e,t,n):n instanceof o.ak?function(e,t,n){const r=x(e,t,n,{type:1});v(e,r);const i=(0,c.A)(n.definition,(n=>g(e,t,n))),s=R(e,t,r,n,...i);return s}(e,t,n):n instanceof o.c$?function(e,t,n){const r=x(e,t,n,{type:1});v(e,r);const i=R(e,t,r,n,y(e,t,n));return function(e,t,n,r){const i=r.left,s=r.right;return $(i,s),e.decisionMap[u(t,"Option",n.idx)]=i,r}(e,t,n,i)}(e,t,n):n instanceof o.Y2?function(e,t,n){const r=x(e,t,n,{type:5});v(e,r);const i=R(e,t,r,n,y(e,t,n));return A(e,t,n,i)}(e,t,n):n instanceof o.Pp?function(e,t,n){const r=x(e,t,n,{type:5});v(e,r);const i=R(e,t,r,n,y(e,t,n)),s=E(e,t,n.separator,n);return A(e,t,n,i,s)}(e,t,n):n instanceof o.$P?function(e,t,n){const r=x(e,t,n,{type:4});v(e,r);const i=R(e,t,r,n,y(e,t,n));return T(e,t,n,i)}(e,t,n):n instanceof o.Cy?function(e,t,n){const r=x(e,t,n,{type:4});v(e,r);const i=R(e,t,r,n,y(e,t,n)),s=E(e,t,n.separator,n);return T(e,t,n,i,s)}(e,t,n):y(e,t,n)}function y(e,t,n){const r=(0,l.A)((0,c.A)(n.definition,(n=>g(e,t,n))),(e=>void 0!==e));return 1===r.length?r[0]:0===r.length?void 0:function(e,t){const n=t.length;for(let s=0;s<n-1;s++){const n=t[s];let r;1===n.left.transitions.length&&(r=n.left.transitions[0]);const i=r instanceof p,a=r,o=t[s+1].left;1===n.left.type&&1===n.right.type&&void 0!==r&&(i&&a.followState===n.right||r.target===n.right)?(i?a.followState=o:r.target=o,S(e,n.right)):$(n.right,o)}const r=t[0],i=t[n-1];return{left:r.left,right:i.right}}(e,r)}function T(e,t,n,r,i){const s=r.left,a=r.right,o=x(e,t,n,{type:11});v(e,o);const c=x(e,t,n,{type:12});return s.loopback=o,c.loopback=o,e.decisionMap[u(t,i?"RepetitionMandatoryWithSeparator":"RepetitionMandatory",n.idx)]=o,$(a,o),void 0===i?($(o,s),$(o,c)):($(o,c),$(o,i.left),$(i.right,s)),{left:s,right:c}}function A(e,t,n,r,i){const s=r.left,a=r.right,o=x(e,t,n,{type:10});v(e,o);const c=x(e,t,n,{type:12}),l=x(e,t,n,{type:9});return o.loopback=l,c.loopback=l,$(o,s),$(o,c),$(a,l),void 0!==i?($(l,c),$(l,i.left),$(i.right,s)):$(l,o),e.decisionMap[u(t,i?"RepetitionWithSeparator":"Repetition",n.idx)]=o,{left:o,right:c}}function v(e,t){return e.decisionStates.push(t),t.decision=e.decisionStates.length-1,t.decision}function R(e,t,n,r,...i){const s=x(e,t,r,{type:8,start:n});n.end=s;for(const o of i)void 0!==o?($(n,o.left),$(o.right,s)):$(n,s);const a={left:n,right:s};return e.decisionMap[u(t,function(e){if(e instanceof o.ak)return"Alternation";if(e instanceof o.c$)return"Option";if(e instanceof o.Y2)return"Repetition";if(e instanceof o.Pp)return"RepetitionWithSeparator";if(e instanceof o.$P)return"RepetitionMandatory";if(e instanceof o.Cy)return"RepetitionMandatoryWithSeparator";throw new Error("Invalid production type encountered")}(r),r.idx)]=n,a}function E(e,t,n,r){const i=x(e,t,r,{type:1}),s=x(e,t,r,{type:1});return I(i,new h(s,n)),{left:i,right:s}}function k(e,t,n){const r=e.ruleToStartState.get(t);$(r,n.left);const i=e.ruleToStopState.get(t);$(n.right,i);return{left:r,right:i}}function $(e,t){I(e,new f(t))}function x(e,t,n,r){const i=Object.assign({atn:e,production:n,epsilonOnlyTransitions:!1,rule:t,transitions:[],nextTokenWithinRule:[],stateNumber:e.states.length},r);return e.states.push(i),i}function I(e,t){0===e.transitions.length&&(e.epsilonOnlyTransitions=t.isEpsilon()),e.transitions.push(t)}function S(e,t){e.states.splice(e.states.indexOf(t),1)}const N={};class C{constructor(){this.map={},this.configs=[]}get size(){return this.configs.length}finalize(){this.map={}}add(e){const t=w(e);t in this.map||(this.map[t]=this.configs.length,this.configs.push(e))}get elements(){return this.configs}get alts(){return(0,c.A)(this.configs,(e=>e.alt))}get key(){let e="";for(const t in this.map)e+=t+":";return e}}function w(e,t=!0){return`${t?`a${e.alt}`:""}s${e.state.stateNumber}:${e.stack.map((e=>e.stateNumber.toString())).join("_")}`}var L=n(6452),b=n(8139),O=n(3958),_=n(9902);const P=function(e,t){return e&&e.length?(0,_.A)(e,(0,O.A)(t,2)):[]};var M=n(4098),D=n(8058),U=n(6401),F=n(9463);function G(e,t){const n={};return r=>{const i=r.toString();let s=n[i];return void 0!==s||(s={atnStartState:e,decision:t,states:{}},n[i]=s),s}}class K{constructor(){this.predicates=[]}is(e){return e>=this.predicates.length||this.predicates[e]}set(e,t){this.predicates[e]=t}toString(){let e="";const t=this.predicates.length;for(let n=0;n<t;n++)e+=!0===this.predicates[n]?"1":"0";return e}}const B=new K;class j extends o.T6{constructor(e){var t;super(),this.logging=null!==(t=null==e?void 0:e.logging)&&void 0!==t?t:e=>console.log(e)}initialize(e){this.atn=m(e.rules),this.dfas=function(e){const t=e.decisionStates.length,n=Array(t);for(let r=0;r<t;r++)n[r]=G(e.decisionStates[r],r);return n}(this.atn)}validateAmbiguousAlternationAlternatives(){return[]}validateEmptyOrAlternatives(){return[]}buildLookaheadForAlternation(e){const{prodOccurrence:t,rule:n,hasPredicates:r,dynamicTokensEnabled:i}=e,s=this.dfas,a=this.logging,l=u(n,"Alternation",t),d=this.atn.decisionMap[l].decision,h=(0,c.A)((0,o.jk)({maxLookahead:1,occurrence:t,prodType:"Alternation",rule:n}),(e=>(0,c.A)(e,(e=>e[0]))));if(V(h,!1)&&!i){const e=(0,F.A)(h,((e,t,n)=>((0,D.A)(t,(t=>{t&&(e[t.tokenTypeIdx]=n,(0,D.A)(t.categoryMatches,(t=>{e[t]=n})))})),e)),{});return r?function(t){var n;const r=this.LA(1),i=e[r.tokenTypeIdx];if(void 0!==t&&void 0!==i){const e=null===(n=t[i])||void 0===n?void 0:n.GATE;if(void 0!==e&&!1===e.call(this))return}return i}:function(){const t=this.LA(1);return e[t.tokenTypeIdx]}}return r?function(e){const t=new K,n=void 0===e?0:e.length;for(let i=0;i<n;i++){const n=null==e?void 0:e[i].GATE;t.set(i,void 0===n||n.call(this))}const r=H.call(this,s,d,t,a);return"number"==typeof r?r:void 0}:function(){const e=H.call(this,s,d,B,a);return"number"==typeof e?e:void 0}}buildLookaheadForOptional(e){const{prodOccurrence:t,rule:n,prodType:r,dynamicTokensEnabled:i}=e,s=this.dfas,a=this.logging,l=u(n,r,t),d=this.atn.decisionMap[l].decision,h=(0,c.A)((0,o.jk)({maxLookahead:1,occurrence:t,prodType:r,rule:n}),(e=>(0,c.A)(e,(e=>e[0]))));if(V(h)&&h[0][0]&&!i){const e=h[0],t=(0,M.A)(e);if(1===t.length&&(0,U.A)(t[0].categoryMatches)){const e=t[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===e}}{const e=(0,F.A)(t,((e,t)=>(void 0!==t&&(e[t.tokenTypeIdx]=!0,(0,D.A)(t.categoryMatches,(t=>{e[t]=!0}))),e)),{});return function(){const t=this.LA(1);return!0===e[t.tokenTypeIdx]}}}return function(){const e=H.call(this,s,d,B,a);return"object"!=typeof e&&0===e}}}function V(e,t=!0){const n=new Set;for(const r of e){const e=new Set;for(const i of r){if(void 0===i){if(t)break;return!1}const r=[i.tokenTypeIdx].concat(i.categoryMatches);for(const t of r)if(n.has(t)){if(!e.has(t))return!1}else n.add(t),e.add(t)}}return!0}function H(e,t,n,r){const i=e[t](n);let s=i.start;if(void 0===s){s=J(i,Q(ee(i.atnStartState))),i.start=s}return W.apply(this,[i,s,n,r])}function W(e,t,n,r){let i=t,s=1;const a=[];let o=this.LA(s++);for(;;){let t=(c=o,i.edges[c.tokenTypeIdx]);if(void 0===t&&(t=z.apply(this,[e,i,o,s,n,r])),t===N)return X(a,i,o);if(!0===t.isAcceptState)return t.prediction;i=t,a.push(o),o=this.LA(s++)}var c}function z(e,t,n,r,i,s){const a=function(e,t,n){const r=new C,i=[];for(const a of e.elements){if(!1===n.is(a.alt))continue;if(7===a.state.type){i.push(a);continue}const e=a.state.transitions.length;for(let n=0;n<e;n++){const e=q(a.state.transitions[n],t);void 0!==e&&r.add({state:e,alt:a.alt,stack:a.stack})}}let s;0===i.length&&1===r.size&&(s=r);if(void 0===s){s=new C;for(const e of r.elements)te(e,s)}if(i.length>0&&!function(e){for(const t of e.elements)if(7===t.state.type)return!0;return!1}(s))for(const a of i)s.add(a);return s}(t.configs,n,i);if(0===a.size)return Z(e,t,n,N),N;let o=Q(a);const c=function(e,t){let n;for(const r of e.elements)if(!0===t.is(r.alt))if(void 0===n)n=r.alt;else if(n!==r.alt)return;return n}(a,i);if(void 0!==c)o.isAcceptState=!0,o.prediction=c,o.configs.uniqueAlt=c;else if(function(e){if(function(e){for(const t of e.elements)if(7!==t.state.type)return!1;return!0}(e))return!0;const t=function(e){const t=new Map;for(const n of e){const e=w(n,!1);let r=t.get(e);void 0===r&&(r={},t.set(e,r)),r[n.alt]=!0}return t}(e.elements);return function(e){for(const t of Array.from(e.values()))if(Object.keys(t).length>1)return!0;return!1}(t)&&!function(e){for(const t of Array.from(e.values()))if(1===Object.keys(t).length)return!0;return!1}(t)}(a)){const t=(0,L.A)(a.alts);o.isAcceptState=!0,o.prediction=t,o.configs.uniqueAlt=t,Y.apply(this,[e,r,a.alts,s])}return o=Z(e,t,n,o),o}function Y(e,t,n,r){const i=[];for(let a=1;a<=t;a++)i.push(this.LA(a).tokenType);const s=e.atnStartState;r(function(e){const t=(0,c.A)(e.prefixPath,(e=>(0,o.Sk)(e))).join(", "),n=0===e.production.idx?"":e.production.idx;let r=`Ambiguous Alternatives Detected: <${e.ambiguityIndices.join(", ")}> in <${function(e){if(e instanceof o.wL)return"SUBRULE";if(e instanceof o.c$)return"OPTION";if(e instanceof o.ak)return"OR";if(e instanceof o.$P)return"AT_LEAST_ONE";if(e instanceof o.Cy)return"AT_LEAST_ONE_SEP";if(e instanceof o.Pp)return"MANY_SEP";if(e instanceof o.Y2)return"MANY";if(e instanceof o.BK)return"CONSUME";throw Error("non exhaustive match")}(e.production)}${n}> inside <${e.topLevelRule.name}> Rule,\n<${t}> may appears as a prefix path in all these alternatives.\n`;return r+="See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES\nFor Further details.",r}({topLevelRule:s.rule,ambiguityIndices:n,production:s.production,prefixPath:i}))}function X(e,t,n){const r=(0,b.A)(t.configs.elements,(e=>e.state.transitions));return{actualToken:n,possibleTokenTypes:P(r.filter((e=>e instanceof h)).map((e=>e.tokenType)),(e=>e.tokenTypeIdx)),tokenPath:e}}function q(e,t){if(e instanceof h&&(0,o.G)(t,e.tokenType))return e.target}function Q(e){return{configs:e,edges:{},isAcceptState:!1,prediction:-1}}function Z(e,t,n,r){return r=J(e,r),t.edges[n.tokenTypeIdx]=r,r}function J(e,t){if(t===N)return t;const n=t.configs.key,r=e.states[n];return void 0!==r?r:(t.configs.finalize(),e.states[n]=t,t)}function ee(e){const t=new C,n=e.transitions.length;for(let r=0;r<n;r++){te({state:e.transitions[r].target,alt:r,stack:[]},t)}return t}function te(e,t){const n=e.state;if(7===n.type){if(e.stack.length>0){const n=[...e.stack];te({state:n.pop(),alt:e.alt,stack:n},t)}else t.add(e);return}n.epsilonOnlyTransitions||t.add(e);const r=n.transitions.length;for(let i=0;i<r;i++){const r=ne(e,n.transitions[i]);void 0!==r&&te(r,t)}}function ne(e,t){if(t instanceof f)return{state:t.target,alt:e.alt,stack:e.stack};if(t instanceof p){const n=[...e.stack,t.followState];return{state:t.target,alt:e.alt,stack:n}}}var re,ie,se,ae,oe,ce,le,ue,de,he,fe,pe,me,ge,ye,Te,Ae,ve,Re,Ee,ke,$e,xe,Ie,Se,Ne,Ce,we,Le,be,Oe,_e,Pe,Me,De,Ue,Fe,Ge,Ke,Be,je,Ve,He,We,ze,Ye,Xe,qe,Qe,Ze,Je,et,tt,nt,rt,it,st,at,ot,ct,lt,ut,dt,ht,ft,pt,mt,gt,yt,Tt,At,vt,Rt,Et,kt,$t,xt,It,St=n(9683);!function(e){e.is=function(e){return"string"==typeof e}}(re||(re={})),function(e){e.is=function(e){return"string"==typeof e}}(ie||(ie={})),function(e){e.MIN_VALUE=-2147483648,e.MAX_VALUE=2147483647,e.is=function(t){return"number"==typeof t&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}}(se||(se={})),function(e){e.MIN_VALUE=0,e.MAX_VALUE=2147483647,e.is=function(t){return"number"==typeof t&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}}(ae||(ae={})),function(e){e.create=function(e,t){return e===Number.MAX_VALUE&&(e=ae.MAX_VALUE),t===Number.MAX_VALUE&&(t=ae.MAX_VALUE),{line:e,character:t}},e.is=function(e){let t=e;return Ct.objectLiteral(t)&&Ct.uinteger(t.line)&&Ct.uinteger(t.character)}}(oe||(oe={})),function(e){e.create=function(e,t,n,r){if(Ct.uinteger(e)&&Ct.uinteger(t)&&Ct.uinteger(n)&&Ct.uinteger(r))return{start:oe.create(e,t),end:oe.create(n,r)};if(oe.is(e)&&oe.is(t))return{start:e,end:t};throw new Error(`Range#create called with invalid arguments[${e}, ${t}, ${n}, ${r}]`)},e.is=function(e){let t=e;return Ct.objectLiteral(t)&&oe.is(t.start)&&oe.is(t.end)}}(ce||(ce={})),function(e){e.create=function(e,t){return{uri:e,range:t}},e.is=function(e){let t=e;return Ct.objectLiteral(t)&&ce.is(t.range)&&(Ct.string(t.uri)||Ct.undefined(t.uri))}}(le||(le={})),function(e){e.create=function(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}},e.is=function(e){let t=e;return Ct.objectLiteral(t)&&ce.is(t.targetRange)&&Ct.string(t.targetUri)&&ce.is(t.targetSelectionRange)&&(ce.is(t.originSelectionRange)||Ct.undefined(t.originSelectionRange))}}(ue||(ue={})),function(e){e.create=function(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}},e.is=function(e){const t=e;return Ct.objectLiteral(t)&&Ct.numberRange(t.red,0,1)&&Ct.numberRange(t.green,0,1)&&Ct.numberRange(t.blue,0,1)&&Ct.numberRange(t.alpha,0,1)}}(de||(de={})),function(e){e.create=function(e,t){return{range:e,color:t}},e.is=function(e){const t=e;return Ct.objectLiteral(t)&&ce.is(t.range)&&de.is(t.color)}}(he||(he={})),function(e){e.create=function(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}},e.is=function(e){const t=e;return Ct.objectLiteral(t)&&Ct.string(t.label)&&(Ct.undefined(t.textEdit)||Ee.is(t))&&(Ct.undefined(t.additionalTextEdits)||Ct.typedArray(t.additionalTextEdits,Ee.is))}}(fe||(fe={})),function(e){e.Comment="comment",e.Imports="imports",e.Region="region"}(pe||(pe={})),function(e){e.create=function(e,t,n,r,i,s){const a={startLine:e,endLine:t};return Ct.defined(n)&&(a.startCharacter=n),Ct.defined(r)&&(a.endCharacter=r),Ct.defined(i)&&(a.kind=i),Ct.defined(s)&&(a.collapsedText=s),a},e.is=function(e){const t=e;return Ct.objectLiteral(t)&&Ct.uinteger(t.startLine)&&Ct.uinteger(t.startLine)&&(Ct.undefined(t.startCharacter)||Ct.uinteger(t.startCharacter))&&(Ct.undefined(t.endCharacter)||Ct.uinteger(t.endCharacter))&&(Ct.undefined(t.kind)||Ct.string(t.kind))}}(me||(me={})),function(e){e.create=function(e,t){return{location:e,message:t}},e.is=function(e){let t=e;return Ct.defined(t)&&le.is(t.location)&&Ct.string(t.message)}}(ge||(ge={})),function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4}(ye||(ye={})),function(e){e.Unnecessary=1,e.Deprecated=2}(Te||(Te={})),function(e){e.is=function(e){const t=e;return Ct.objectLiteral(t)&&Ct.string(t.href)}}(Ae||(Ae={})),function(e){e.create=function(e,t,n,r,i,s){let a={range:e,message:t};return Ct.defined(n)&&(a.severity=n),Ct.defined(r)&&(a.code=r),Ct.defined(i)&&(a.source=i),Ct.defined(s)&&(a.relatedInformation=s),a},e.is=function(e){var t;let n=e;return Ct.defined(n)&&ce.is(n.range)&&Ct.string(n.message)&&(Ct.number(n.severity)||Ct.undefined(n.severity))&&(Ct.integer(n.code)||Ct.string(n.code)||Ct.undefined(n.code))&&(Ct.undefined(n.codeDescription)||Ct.string(null===(t=n.codeDescription)||void 0===t?void 0:t.href))&&(Ct.string(n.source)||Ct.undefined(n.source))&&(Ct.undefined(n.relatedInformation)||Ct.typedArray(n.relatedInformation,ge.is))}}(ve||(ve={})),function(e){e.create=function(e,t,...n){let r={title:e,command:t};return Ct.defined(n)&&n.length>0&&(r.arguments=n),r},e.is=function(e){let t=e;return Ct.defined(t)&&Ct.string(t.title)&&Ct.string(t.command)}}(Re||(Re={})),function(e){e.replace=function(e,t){return{range:e,newText:t}},e.insert=function(e,t){return{range:{start:e,end:e},newText:t}},e.del=function(e){return{range:e,newText:""}},e.is=function(e){const t=e;return Ct.objectLiteral(t)&&Ct.string(t.newText)&&ce.is(t.range)}}(Ee||(Ee={})),function(e){e.create=function(e,t,n){const r={label:e};return void 0!==t&&(r.needsConfirmation=t),void 0!==n&&(r.description=n),r},e.is=function(e){const t=e;return Ct.objectLiteral(t)&&Ct.string(t.label)&&(Ct.boolean(t.needsConfirmation)||void 0===t.needsConfirmation)&&(Ct.string(t.description)||void 0===t.description)}}(ke||(ke={})),function(e){e.is=function(e){const t=e;return Ct.string(t)}}($e||($e={})),function(e){e.replace=function(e,t,n){return{range:e,newText:t,annotationId:n}},e.insert=function(e,t,n){return{range:{start:e,end:e},newText:t,annotationId:n}},e.del=function(e,t){return{range:e,newText:"",annotationId:t}},e.is=function(e){const t=e;return Ee.is(t)&&(ke.is(t.annotationId)||$e.is(t.annotationId))}}(xe||(xe={})),function(e){e.create=function(e,t){return{textDocument:e,edits:t}},e.is=function(e){let t=e;return Ct.defined(t)&&Oe.is(t.textDocument)&&Array.isArray(t.edits)}}(Ie||(Ie={})),function(e){e.create=function(e,t,n){let r={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},e.is=function(e){let t=e;return t&&"create"===t.kind&&Ct.string(t.uri)&&(void 0===t.options||(void 0===t.options.overwrite||Ct.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||Ct.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||$e.is(t.annotationId))}}(Se||(Se={})),function(e){e.create=function(e,t,n,r){let i={kind:"rename",oldUri:e,newUri:t};return void 0===n||void 0===n.overwrite&&void 0===n.ignoreIfExists||(i.options=n),void 0!==r&&(i.annotationId=r),i},e.is=function(e){let t=e;return t&&"rename"===t.kind&&Ct.string(t.oldUri)&&Ct.string(t.newUri)&&(void 0===t.options||(void 0===t.options.overwrite||Ct.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||Ct.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||$e.is(t.annotationId))}}(Ne||(Ne={})),function(e){e.create=function(e,t,n){let r={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},e.is=function(e){let t=e;return t&&"delete"===t.kind&&Ct.string(t.uri)&&(void 0===t.options||(void 0===t.options.recursive||Ct.boolean(t.options.recursive))&&(void 0===t.options.ignoreIfNotExists||Ct.boolean(t.options.ignoreIfNotExists)))&&(void 0===t.annotationId||$e.is(t.annotationId))}}(Ce||(Ce={})),function(e){e.is=function(e){let t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every((e=>Ct.string(e.kind)?Se.is(e)||Ne.is(e)||Ce.is(e):Ie.is(e))))}}(we||(we={}));!function(e){e.create=function(e){return{uri:e}},e.is=function(e){let t=e;return Ct.defined(t)&&Ct.string(t.uri)}}(Le||(Le={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){let t=e;return Ct.defined(t)&&Ct.string(t.uri)&&Ct.integer(t.version)}}(be||(be={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){let t=e;return Ct.defined(t)&&Ct.string(t.uri)&&(null===t.version||Ct.integer(t.version))}}(Oe||(Oe={})),function(e){e.create=function(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}},e.is=function(e){let t=e;return Ct.defined(t)&&Ct.string(t.uri)&&Ct.string(t.languageId)&&Ct.integer(t.version)&&Ct.string(t.text)}}(_e||(_e={})),function(e){e.PlainText="plaintext",e.Markdown="markdown",e.is=function(t){const n=t;return n===e.PlainText||n===e.Markdown}}(Pe||(Pe={})),function(e){e.is=function(e){const t=e;return Ct.objectLiteral(e)&&Pe.is(t.kind)&&Ct.string(t.value)}}(Me||(Me={})),function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25}(De||(De={})),function(e){e.PlainText=1,e.Snippet=2}(Ue||(Ue={})),function(e){e.Deprecated=1}(Fe||(Fe={})),function(e){e.create=function(e,t,n){return{newText:e,insert:t,replace:n}},e.is=function(e){const t=e;return t&&Ct.string(t.newText)&&ce.is(t.insert)&&ce.is(t.replace)}}(Ge||(Ge={})),function(e){e.asIs=1,e.adjustIndentation=2}(Ke||(Ke={})),function(e){e.is=function(e){const t=e;return t&&(Ct.string(t.detail)||void 0===t.detail)&&(Ct.string(t.description)||void 0===t.description)}}(Be||(Be={})),function(e){e.create=function(e){return{label:e}}}(je||(je={})),function(e){e.create=function(e,t){return{items:e||[],isIncomplete:!!t}}}(Ve||(Ve={})),function(e){e.fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},e.is=function(e){const t=e;return Ct.string(t)||Ct.objectLiteral(t)&&Ct.string(t.language)&&Ct.string(t.value)}}(He||(He={})),function(e){e.is=function(e){let t=e;return!!t&&Ct.objectLiteral(t)&&(Me.is(t.contents)||He.is(t.contents)||Ct.typedArray(t.contents,He.is))&&(void 0===e.range||ce.is(e.range))}}(We||(We={})),function(e){e.create=function(e,t){return t?{label:e,documentation:t}:{label:e}}}(ze||(ze={})),function(e){e.create=function(e,t,...n){let r={label:e};return Ct.defined(t)&&(r.documentation=t),Ct.defined(n)?r.parameters=n:r.parameters=[],r}}(Ye||(Ye={})),function(e){e.Text=1,e.Read=2,e.Write=3}(Xe||(Xe={})),function(e){e.create=function(e,t){let n={range:e};return Ct.number(t)&&(n.kind=t),n}}(qe||(qe={})),function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26}(Qe||(Qe={})),function(e){e.Deprecated=1}(Ze||(Ze={})),function(e){e.create=function(e,t,n,r,i){let s={name:e,kind:t,location:{uri:r,range:n}};return i&&(s.containerName=i),s}}(Je||(Je={})),function(e){e.create=function(e,t,n,r){return void 0!==r?{name:e,kind:t,location:{uri:n,range:r}}:{name:e,kind:t,location:{uri:n}}}}(et||(et={})),function(e){e.create=function(e,t,n,r,i,s){let a={name:e,detail:t,kind:n,range:r,selectionRange:i};return void 0!==s&&(a.children=s),a},e.is=function(e){let t=e;return t&&Ct.string(t.name)&&Ct.number(t.kind)&&ce.is(t.range)&&ce.is(t.selectionRange)&&(void 0===t.detail||Ct.string(t.detail))&&(void 0===t.deprecated||Ct.boolean(t.deprecated))&&(void 0===t.children||Array.isArray(t.children))&&(void 0===t.tags||Array.isArray(t.tags))}}(tt||(tt={})),function(e){e.Empty="",e.QuickFix="quickfix",e.Refactor="refactor",e.RefactorExtract="refactor.extract",e.RefactorInline="refactor.inline",e.RefactorRewrite="refactor.rewrite",e.Source="source",e.SourceOrganizeImports="source.organizeImports",e.SourceFixAll="source.fixAll"}(nt||(nt={})),function(e){e.Invoked=1,e.Automatic=2}(rt||(rt={})),function(e){e.create=function(e,t,n){let r={diagnostics:e};return null!=t&&(r.only=t),null!=n&&(r.triggerKind=n),r},e.is=function(e){let t=e;return Ct.defined(t)&&Ct.typedArray(t.diagnostics,ve.is)&&(void 0===t.only||Ct.typedArray(t.only,Ct.string))&&(void 0===t.triggerKind||t.triggerKind===rt.Invoked||t.triggerKind===rt.Automatic)}}(it||(it={})),function(e){e.create=function(e,t,n){let r={title:e},i=!0;return"string"==typeof t?(i=!1,r.kind=t):Re.is(t)?r.command=t:r.edit=t,i&&void 0!==n&&(r.kind=n),r},e.is=function(e){let t=e;return t&&Ct.string(t.title)&&(void 0===t.diagnostics||Ct.typedArray(t.diagnostics,ve.is))&&(void 0===t.kind||Ct.string(t.kind))&&(void 0!==t.edit||void 0!==t.command)&&(void 0===t.command||Re.is(t.command))&&(void 0===t.isPreferred||Ct.boolean(t.isPreferred))&&(void 0===t.edit||we.is(t.edit))}}(st||(st={})),function(e){e.create=function(e,t){let n={range:e};return Ct.defined(t)&&(n.data=t),n},e.is=function(e){let t=e;return Ct.defined(t)&&ce.is(t.range)&&(Ct.undefined(t.command)||Re.is(t.command))}}(at||(at={})),function(e){e.create=function(e,t){return{tabSize:e,insertSpaces:t}},e.is=function(e){let t=e;return Ct.defined(t)&&Ct.uinteger(t.tabSize)&&Ct.boolean(t.insertSpaces)}}(ot||(ot={})),function(e){e.create=function(e,t,n){return{range:e,target:t,data:n}},e.is=function(e){let t=e;return Ct.defined(t)&&ce.is(t.range)&&(Ct.undefined(t.target)||Ct.string(t.target))}}(ct||(ct={})),function(e){e.create=function(e,t){return{range:e,parent:t}},e.is=function(t){let n=t;return Ct.objectLiteral(n)&&ce.is(n.range)&&(void 0===n.parent||e.is(n.parent))}}(lt||(lt={})),function(e){e.namespace="namespace",e.type="type",e.class="class",e.enum="enum",e.interface="interface",e.struct="struct",e.typeParameter="typeParameter",e.parameter="parameter",e.variable="variable",e.property="property",e.enumMember="enumMember",e.event="event",e.function="function",e.method="method",e.macro="macro",e.keyword="keyword",e.modifier="modifier",e.comment="comment",e.string="string",e.number="number",e.regexp="regexp",e.operator="operator",e.decorator="decorator"}(ut||(ut={})),function(e){e.declaration="declaration",e.definition="definition",e.readonly="readonly",e.static="static",e.deprecated="deprecated",e.abstract="abstract",e.async="async",e.modification="modification",e.documentation="documentation",e.defaultLibrary="defaultLibrary"}(dt||(dt={})),function(e){e.is=function(e){const t=e;return Ct.objectLiteral(t)&&(void 0===t.resultId||"string"==typeof t.resultId)&&Array.isArray(t.data)&&(0===t.data.length||"number"==typeof t.data[0])}}(ht||(ht={})),function(e){e.create=function(e,t){return{range:e,text:t}},e.is=function(e){const t=e;return null!=t&&ce.is(t.range)&&Ct.string(t.text)}}(ft||(ft={})),function(e){e.create=function(e,t,n){return{range:e,variableName:t,caseSensitiveLookup:n}},e.is=function(e){const t=e;return null!=t&&ce.is(t.range)&&Ct.boolean(t.caseSensitiveLookup)&&(Ct.string(t.variableName)||void 0===t.variableName)}}(pt||(pt={})),function(e){e.create=function(e,t){return{range:e,expression:t}},e.is=function(e){const t=e;return null!=t&&ce.is(t.range)&&(Ct.string(t.expression)||void 0===t.expression)}}(mt||(mt={})),function(e){e.create=function(e,t){return{frameId:e,stoppedLocation:t}},e.is=function(e){const t=e;return Ct.defined(t)&&ce.is(e.stoppedLocation)}}(gt||(gt={})),function(e){e.Type=1,e.Parameter=2,e.is=function(e){return 1===e||2===e}}(yt||(yt={})),function(e){e.create=function(e){return{value:e}},e.is=function(e){const t=e;return Ct.objectLiteral(t)&&(void 0===t.tooltip||Ct.string(t.tooltip)||Me.is(t.tooltip))&&(void 0===t.location||le.is(t.location))&&(void 0===t.command||Re.is(t.command))}}(Tt||(Tt={})),function(e){e.create=function(e,t,n){const r={position:e,label:t};return void 0!==n&&(r.kind=n),r},e.is=function(e){const t=e;return Ct.objectLiteral(t)&&oe.is(t.position)&&(Ct.string(t.label)||Ct.typedArray(t.label,Tt.is))&&(void 0===t.kind||yt.is(t.kind))&&void 0===t.textEdits||Ct.typedArray(t.textEdits,Ee.is)&&(void 0===t.tooltip||Ct.string(t.tooltip)||Me.is(t.tooltip))&&(void 0===t.paddingLeft||Ct.boolean(t.paddingLeft))&&(void 0===t.paddingRight||Ct.boolean(t.paddingRight))}}(At||(At={})),function(e){e.createSnippet=function(e){return{kind:"snippet",value:e}}}(vt||(vt={})),function(e){e.create=function(e,t,n,r){return{insertText:e,filterText:t,range:n,command:r}}}(Rt||(Rt={})),function(e){e.create=function(e){return{items:e}}}(Et||(Et={})),function(e){e.Invoked=0,e.Automatic=1}(kt||(kt={})),function(e){e.create=function(e,t){return{range:e,text:t}}}($t||($t={})),function(e){e.create=function(e,t){return{triggerKind:e,selectedCompletionInfo:t}}}(xt||(xt={})),function(e){e.is=function(e){const t=e;return Ct.objectLiteral(t)&&ie.is(t.uri)&&Ct.string(t.name)}}(It||(It={}));var Nt,Ct;!function(e){function t(e,n){if(e.length<=1)return e;const r=e.length/2|0,i=e.slice(0,r),s=e.slice(r);t(i,n),t(s,n);let a=0,o=0,c=0;for(;a<i.length&&o<s.length;){let t=n(i[a],s[o]);e[c++]=t<=0?i[a++]:s[o++]}for(;a<i.length;)e[c++]=i[a++];for(;o<s.length;)e[c++]=s[o++];return e}e.create=function(e,t,n,r){return new wt(e,t,n,r)},e.is=function(e){let t=e;return!!(Ct.defined(t)&&Ct.string(t.uri)&&(Ct.undefined(t.languageId)||Ct.string(t.languageId))&&Ct.uinteger(t.lineCount)&&Ct.func(t.getText)&&Ct.func(t.positionAt)&&Ct.func(t.offsetAt))},e.applyEdits=function(e,n){let r=e.getText(),i=t(n,((e,t)=>{let n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),s=r.length;for(let t=i.length-1;t>=0;t--){let n=i[t],a=e.offsetAt(n.range.start),o=e.offsetAt(n.range.end);if(!(o<=s))throw new Error("Overlapping edit");r=r.substring(0,a)+n.newText+r.substring(o,r.length),s=a}return r}}(Nt||(Nt={}));class wt{constructor(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(void 0===this._lineOffsets){let e=[],t=this._content,n=!0;for(let r=0;r<t.length;r++){n&&(e.push(r),n=!1);let i=t.charAt(r);n="\r"===i||"\n"===i,"\r"===i&&r+1<t.length&&"\n"===t.charAt(r+1)&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),n=0,r=t.length;if(0===r)return oe.create(0,e);for(;n<r;){let i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}let i=n-1;return oe.create(i,e-t[i])}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)}get lineCount(){return this.getLineOffsets().length}}!function(e){const t=Object.prototype.toString;e.defined=function(e){return void 0!==e},e.undefined=function(e){return void 0===e},e.boolean=function(e){return!0===e||!1===e},e.string=function(e){return"[object String]"===t.call(e)},e.number=function(e){return"[object Number]"===t.call(e)},e.numberRange=function(e,n,r){return"[object Number]"===t.call(e)&&n<=e&&e<=r},e.integer=function(e){return"[object Number]"===t.call(e)&&-2147483648<=e&&e<=2147483647},e.uinteger=function(e){return"[object Number]"===t.call(e)&&0<=e&&e<=2147483647},e.func=function(e){return"[object Function]"===t.call(e)},e.objectLiteral=function(e){return null!==e&&"object"==typeof e},e.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)}}(Ct||(Ct={}));class Lt{constructor(){this.nodeStack=[]}get current(){var e;return null!==(e=this.nodeStack[this.nodeStack.length-1])&&void 0!==e?e:this.rootNode}buildRootNode(e){return this.rootNode=new Mt(e),this.rootNode.root=this.rootNode,this.nodeStack=[this.rootNode],this.rootNode}buildCompositeNode(e){const t=new _t;return t.grammarSource=e,t.root=this.rootNode,this.current.content.push(t),this.nodeStack.push(t),t}buildLeafNode(e,t){const n=new Ot(e.startOffset,e.image.length,(0,r.wf)(e),e.tokenType,!t);return n.grammarSource=t,n.root=this.rootNode,this.current.content.push(n),n}removeNode(e){const t=e.container;if(t){const n=t.content.indexOf(e);n>=0&&t.content.splice(n,1)}}addHiddenNodes(e){const t=[];for(const s of e){const e=new Ot(s.startOffset,s.image.length,(0,r.wf)(s),s.tokenType,!0);e.root=this.rootNode,t.push(e)}let n=this.current,i=!1;if(n.content.length>0)n.content.push(...t);else{for(;n.container;){const e=n.container.content.indexOf(n);if(e>0){n.container.content.splice(e,0,...t),i=!0;break}n=n.container}i||this.rootNode.content.unshift(...t)}}construct(e){const t=this.current;"string"==typeof e.$type&&(this.current.astNode=e),e.$cstNode=t;const n=this.nodeStack.pop();0===(null==n?void 0:n.content.length)&&this.removeNode(n)}}class bt{get parent(){return this.container}get feature(){return this.grammarSource}get hidden(){return!1}get astNode(){var e,t;const n="string"==typeof(null===(e=this._astNode)||void 0===e?void 0:e.$type)?this._astNode:null===(t=this.container)||void 0===t?void 0:t.astNode;if(!n)throw new Error("This node has no associated AST element");return n}set astNode(e){this._astNode=e}get element(){return this.astNode}get text(){return this.root.fullText.substring(this.offset,this.end)}}class Ot extends bt{get offset(){return this._offset}get length(){return this._length}get end(){return this._offset+this._length}get hidden(){return this._hidden}get tokenType(){return this._tokenType}get range(){return this._range}constructor(e,t,n,r,i=!1){super(),this._hidden=i,this._offset=e,this._tokenType=r,this._length=t,this._range=n}}class _t extends bt{constructor(){super(...arguments),this.content=new Pt(this)}get children(){return this.content}get offset(){var e,t;return null!==(t=null===(e=this.firstNonHiddenNode)||void 0===e?void 0:e.offset)&&void 0!==t?t:0}get length(){return this.end-this.offset}get end(){var e,t;return null!==(t=null===(e=this.lastNonHiddenNode)||void 0===e?void 0:e.end)&&void 0!==t?t:0}get range(){const e=this.firstNonHiddenNode,t=this.lastNonHiddenNode;if(e&&t){if(void 0===this._rangeCache){const{range:n}=e,{range:r}=t;this._rangeCache={start:n.start,end:r.end.line<n.start.line?n.start:r.end}}return this._rangeCache}return{start:oe.create(0,0),end:oe.create(0,0)}}get firstNonHiddenNode(){for(const e of this.content)if(!e.hidden)return e;return this.content[0]}get lastNonHiddenNode(){for(let e=this.content.length-1;e>=0;e--){const t=this.content[e];if(!t.hidden)return t}return this.content[this.content.length-1]}}class Pt extends Array{constructor(e){super(),this.parent=e,Object.setPrototypeOf(this,Pt.prototype)}push(...e){return this.addParents(e),super.push(...e)}unshift(...e){return this.addParents(e),super.unshift(...e)}splice(e,t,...n){return this.addParents(n),super.splice(e,t,...n)}addParents(e){for(const t of e)t.container=this.parent}}class Mt extends _t{get text(){return this._text.substring(this.offset,this.end)}get fullText(){return this._text}constructor(e){super(),this._text="",this._text=null!=e?e:""}}const Dt=Symbol("Datatype");function Ut(e){return e.$type===Dt}const Ft=e=>e.endsWith("\u200b")?e:e+"\u200b";class Gt{constructor(e){this._unorderedGroups=new Map,this.allRules=new Map,this.lexer=e.parser.Lexer;const t=this.lexer.definition,n="production"===e.LanguageMetaData.mode;this.wrapper=new Wt(t,Object.assign(Object.assign({},e.parser.ParserConfig),{skipValidations:n,errorMessageProvider:e.parser.ParserErrorMessageProvider}))}alternatives(e,t){this.wrapper.wrapOr(e,t)}optional(e,t){this.wrapper.wrapOption(e,t)}many(e,t){this.wrapper.wrapMany(e,t)}atLeastOne(e,t){this.wrapper.wrapAtLeastOne(e,t)}getRule(e){return this.allRules.get(e)}isRecording(){return this.wrapper.IS_RECORDING}get unorderedGroups(){return this._unorderedGroups}getRuleStack(){return this.wrapper.RULE_STACK}finalize(){this.wrapper.wrapSelfAnalysis()}}class Kt extends Gt{get current(){return this.stack[this.stack.length-1]}constructor(e){super(e),this.nodeBuilder=new Lt,this.stack=[],this.assignmentMap=new Map,this.linker=e.references.Linker,this.converter=e.parser.ValueConverter,this.astReflection=e.shared.AstReflection}rule(e,t){const n=this.computeRuleType(e),r=this.wrapper.DEFINE_RULE(Ft(e.name),this.startImplementation(n,t).bind(this));return this.allRules.set(e.name,r),e.entry&&(this.mainRule=r),r}computeRuleType(e){if(!e.fragment){if((0,i.Xq)(e))return Dt;{const t=(0,i.PV)(e);return null!=t?t:e.name}}}parse(e,t={}){this.nodeBuilder.buildRootNode(e);const n=this.lexerResult=this.lexer.tokenize(e);this.wrapper.input=n.tokens;const r=t.rule?this.allRules.get(t.rule):this.mainRule;if(!r)throw new Error(t.rule?`No rule found with name '${t.rule}'`:"No main rule available.");const i=r.call(this.wrapper,{});return this.nodeBuilder.addHiddenNodes(n.hidden),this.unorderedGroups.clear(),this.lexerResult=void 0,{value:i,lexerErrors:n.errors,lexerReport:n.report,parserErrors:this.wrapper.errors}}startImplementation(e,t){return n=>{const r=!this.isRecording()&&void 0!==e;if(r){const t={$type:e};this.stack.push(t),e===Dt&&(t.value="")}let i;try{i=t(n)}catch(s){i=void 0}return void 0===i&&r&&(i=this.construct()),i}}extractHiddenTokens(e){const t=this.lexerResult.hidden;if(!t.length)return[];const n=e.startOffset;for(let r=0;r<t.length;r++){if(t[r].startOffset>n)return t.splice(0,r)}return t.splice(0,t.length)}consume(e,t,n){const r=this.wrapper.wrapConsume(e,t);if(!this.isRecording()&&this.isValidToken(r)){const e=this.extractHiddenTokens(r);this.nodeBuilder.addHiddenNodes(e);const t=this.nodeBuilder.buildLeafNode(r,n),{assignment:i,isCrossRef:s}=this.getAssignment(n),o=this.current;if(i){const e=(0,a.wb)(n)?r.image:this.converter.convert(r.image,t);this.assign(i.operator,i.feature,e,t,s)}else if(Ut(o)){let e=r.image;(0,a.wb)(n)||(e=this.converter.convert(e,t).toString()),o.value+=e}}}isValidToken(e){return!e.isInsertedInRecovery&&!isNaN(e.startOffset)&&"number"==typeof e.endOffset&&!isNaN(e.endOffset)}subrule(e,t,n,r,i){let s;this.isRecording()||n||(s=this.nodeBuilder.buildCompositeNode(r));const a=this.wrapper.wrapSubrule(e,t,i);!this.isRecording()&&s&&s.length>0&&this.performSubruleAssignment(a,r,s)}performSubruleAssignment(e,t,n){const{assignment:r,isCrossRef:i}=this.getAssignment(t);if(r)this.assign(r.operator,r.feature,e,n,i);else if(!r){const t=this.current;if(Ut(t))t.value+=e.toString();else if("object"==typeof e&&e){const n=this.assignWithoutOverride(e,t);this.stack.pop(),this.stack.push(n)}}}action(e,t){if(!this.isRecording()){let n=this.current;if(t.feature&&t.operator){n=this.construct(),this.nodeBuilder.removeNode(n.$cstNode);this.nodeBuilder.buildCompositeNode(t).content.push(n.$cstNode);const r={$type:e};this.stack.push(r),this.assign(t.operator,t.feature,n,n.$cstNode,!1)}else n.$type=e}}construct(){if(this.isRecording())return;const e=this.current;return(0,St.SD)(e),this.nodeBuilder.construct(e),this.stack.pop(),Ut(e)?this.converter.convert(e.value,e.$cstNode):((0,St.OP)(this.astReflection,e),e)}getAssignment(e){if(!this.assignmentMap.has(e)){const t=(0,St.XG)(e,a.wh);this.assignmentMap.set(e,{assignment:t,isCrossRef:!!t&&(0,a._c)(t.terminal)})}return this.assignmentMap.get(e)}assign(e,t,n,r,i){const s=this.current;let a;switch(a=i&&"string"==typeof n?this.linker.buildReference(s,t,r,n):n,e){case"=":s[t]=a;break;case"?=":s[t]=!0;break;case"+=":Array.isArray(s[t])||(s[t]=[]),s[t].push(a)}}assignWithoutOverride(e,t){for(const[r,i]of Object.entries(t)){const t=e[r];void 0===t?e[r]=i:Array.isArray(t)&&Array.isArray(i)&&(i.push(...t),e[r]=i)}const n=e.$cstNode;return n&&(n.astNode=void 0,e.$cstNode=void 0),e}get definitionErrors(){return this.wrapper.definitionErrors}}class Bt{buildMismatchTokenMessage(e){return o.my.buildMismatchTokenMessage(e)}buildNotAllInputParsedMessage(e){return o.my.buildNotAllInputParsedMessage(e)}buildNoViableAltMessage(e){return o.my.buildNoViableAltMessage(e)}buildEarlyExitMessage(e){return o.my.buildEarlyExitMessage(e)}}class jt extends Bt{buildMismatchTokenMessage({expected:e,actual:t}){return`Expecting ${e.LABEL?"`"+e.LABEL+"`":e.name.endsWith(":KW")?`keyword '${e.name.substring(0,e.name.length-3)}'`:`token of type '${e.name}'`} but found \`${t.image}\`.`}buildNotAllInputParsedMessage({firstRedundant:e}){return`Expecting end of file but found \`${e.image}\`.`}}class Vt extends Gt{constructor(){super(...arguments),this.tokens=[],this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}action(){}construct(){}parse(e){this.resetState();const t=this.lexer.tokenize(e,{mode:"partial"});return this.tokens=t.tokens,this.wrapper.input=[...this.tokens],this.mainRule.call(this.wrapper,{}),this.unorderedGroups.clear(),{tokens:this.tokens,elementStack:[...this.lastElementStack],tokenIndex:this.nextTokenIndex}}rule(e,t){const n=this.wrapper.DEFINE_RULE(Ft(e.name),this.startImplementation(t).bind(this));return this.allRules.set(e.name,n),e.entry&&(this.mainRule=n),n}resetState(){this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}startImplementation(e){return t=>{const n=this.keepStackSize();try{e(t)}finally{this.resetStackSize(n)}}}removeUnexpectedElements(){this.elementStack.splice(this.stackSize)}keepStackSize(){const e=this.elementStack.length;return this.stackSize=e,e}resetStackSize(e){this.removeUnexpectedElements(),this.stackSize=e}consume(e,t,n){this.wrapper.wrapConsume(e,t),this.isRecording()||(this.lastElementStack=[...this.elementStack,n],this.nextTokenIndex=this.currIdx+1)}subrule(e,t,n,r,i){this.before(r),this.wrapper.wrapSubrule(e,t,i),this.after(r)}before(e){this.isRecording()||this.elementStack.push(e)}after(e){if(!this.isRecording()){const t=this.elementStack.lastIndexOf(e);t>=0&&this.elementStack.splice(t)}}get currIdx(){return this.wrapper.currIdx}}const Ht={recoveryEnabled:!0,nodeLocationTracking:"full",skipValidations:!0,errorMessageProvider:new jt};class Wt extends o.jr{constructor(e,t){const n=t&&"maxLookahead"in t;super(e,Object.assign(Object.assign(Object.assign({},Ht),{lookaheadStrategy:n?new o.T6({maxLookahead:t.maxLookahead}):new j({logging:t.skipValidations?()=>{}:void 0})}),t))}get IS_RECORDING(){return this.RECORDING_PHASE}DEFINE_RULE(e,t){return this.RULE(e,t)}wrapSelfAnalysis(){this.performSelfAnalysis()}wrapConsume(e,t){return this.consume(e,t)}wrapSubrule(e,t,n){return this.subrule(e,t,{ARGS:[n]})}wrapOr(e,t){this.or(e,t)}wrapOption(e,t){this.option(e,t)}wrapMany(e,t){this.many(e,t)}wrapAtLeastOne(e,t){this.atLeastOne(e,t)}}var zt=n(1564),Yt=n(1719);function Xt(e,t,n){return function(e,t){const n=(0,i.YV)(t,!1),r=(0,Yt.Td)(t.rules).filter(a.s7).filter((e=>n.has(e)));for(const i of r){const t=Object.assign(Object.assign({},e),{consume:1,optional:1,subrule:1,many:1,or:1});e.parser.rule(i,qt(t,i.definition))}}({parser:t,tokens:n,ruleNames:new Map},e),t}function qt(e,t,n=!1){let r;if((0,a.wb)(t))r=function(e,t){const n=e.consume++,r=e.tokens[t.value];if(!r)throw new Error("Could not find token for keyword: "+t.value);return()=>e.parser.consume(n,r,t)}(e,t);else if((0,a.ve)(t))r=function(e,t){const n=(0,i.Uz)(t);return()=>e.parser.action(n,t)}(e,t);else if((0,a.wh)(t))r=qt(e,t.terminal);else if((0,a._c)(t))r=Jt(e,t);else if((0,a.$g)(t))r=function(e,t){const n=t.rule.ref;if((0,a.s7)(n)){const r=e.subrule++,i=n.fragment,s=t.arguments.length>0?function(e,t){const n=t.map((e=>Qt(e.value)));return t=>{const r={};for(let i=0;i<n.length;i++){const s=e.parameters[i],a=n[i];r[s.name]=a(t)}return r}}(n,t.arguments):()=>({});return a=>e.parser.subrule(r,tn(e,n),i,t,s(a))}if((0,a.rE)(n)){const r=e.consume++,i=nn(e,n.name);return()=>e.parser.consume(r,i,t)}if(!n)throw new zt.W(t.$cstNode,`Undefined rule: ${t.rule.$refText}`);(0,zt.d)(n)}(e,t);else if((0,a.jp)(t))r=function(e,t){if(1===t.elements.length)return qt(e,t.elements[0]);{const n=[];for(const i of t.elements){const t={ALT:qt(e,i,!0)},r=Zt(i);r&&(t.GATE=Qt(r)),n.push(t)}const r=e.or++;return t=>e.parser.alternatives(r,n.map((e=>{const n={ALT:()=>e.ALT(t)},r=e.GATE;return r&&(n.GATE=()=>r(t)),n})))}}(e,t);else if((0,a.cY)(t))r=function(e,t){if(1===t.elements.length)return qt(e,t.elements[0]);const n=[];for(const o of t.elements){const t={ALT:qt(e,o,!0)},r=Zt(o);r&&(t.GATE=Qt(r)),n.push(t)}const r=e.or++,i=(e,t)=>`uGroup_${e}_${t.getRuleStack().join("-")}`,s=t=>e.parser.alternatives(r,n.map(((n,s)=>{const a={ALT:()=>!0},o=e.parser;a.ALT=()=>{if(n.ALT(t),!o.isRecording()){const e=i(r,o);o.unorderedGroups.get(e)||o.unorderedGroups.set(e,[]);const t=o.unorderedGroups.get(e);void 0===(null==t?void 0:t[s])&&(t[s]=!0)}};const c=n.GATE;return a.GATE=c?()=>c(t):()=>{const e=o.unorderedGroups.get(i(r,o));return!(null==e?void 0:e[s])},a}))),a=en(e,Zt(t),s,"*");return t=>{a(t),e.parser.isRecording()||e.parser.unorderedGroups.delete(i(r,e.parser))}}(e,t);else if((0,a.IZ)(t))r=function(e,t){const n=t.elements.map((t=>qt(e,t)));return e=>n.forEach((t=>t(e)))}(e,t);else{if(!(0,a.FO)(t))throw new zt.W(t.$cstNode,`Unexpected element type: ${t.$type}`);{const n=e.consume++;r=()=>e.parser.consume(n,o.LT,t)}}return en(e,n?void 0:Zt(t),r,t.cardinality)}function Qt(e){if((0,a.RP)(e)){const t=Qt(e.left),n=Qt(e.right);return e=>t(e)||n(e)}if((0,a.Tu)(e)){const t=Qt(e.left),n=Qt(e.right);return e=>t(e)&&n(e)}if((0,a.Ct)(e)){const t=Qt(e.value);return e=>!t(e)}if((0,a.TF)(e)){const t=e.parameter.ref.name;return e=>void 0!==e&&!0===e[t]}if((0,a.Cz)(e)){const t=Boolean(e.true);return()=>t}(0,zt.d)(e)}function Zt(e){if((0,a.IZ)(e))return e.guardCondition}function Jt(e,t,n=t.terminal){if(n){if((0,a.$g)(n)&&(0,a.s7)(n.rule.ref)){const r=n.rule.ref,i=e.subrule++;return n=>e.parser.subrule(i,tn(e,r),!1,t,n)}if((0,a.$g)(n)&&(0,a.rE)(n.rule.ref)){const r=e.consume++,i=nn(e,n.rule.ref.name);return()=>e.parser.consume(r,i,t)}if((0,a.wb)(n)){const r=e.consume++,i=nn(e,n.value);return()=>e.parser.consume(r,i,t)}throw new Error("Could not build cross reference parser")}{if(!t.type.ref)throw new Error("Could not resolve reference to type: "+t.type.$refText);const n=(0,i.U5)(t.type.ref),r=null==n?void 0:n.terminal;if(!r)throw new Error("Could not find name assignment for type: "+(0,i.Uz)(t.type.ref));return Jt(e,t,r)}}function en(e,t,n,r){const i=t&&Qt(t);if(!r){if(i){const t=e.or++;return r=>e.parser.alternatives(t,[{ALT:()=>n(r),GATE:()=>i(r)},{ALT:(0,o.mT)(),GATE:()=>!i(r)}])}return n}if("*"===r){const t=e.many++;return r=>e.parser.many(t,{DEF:()=>n(r),GATE:i?()=>i(r):void 0})}if("+"===r){const t=e.many++;if(i){const r=e.or++;return s=>e.parser.alternatives(r,[{ALT:()=>e.parser.atLeastOne(t,{DEF:()=>n(s)}),GATE:()=>i(s)},{ALT:(0,o.mT)(),GATE:()=>!i(s)}])}return r=>e.parser.atLeastOne(t,{DEF:()=>n(r)})}if("?"===r){const t=e.optional++;return r=>e.parser.optional(t,{DEF:()=>n(r),GATE:i?()=>i(r):void 0})}(0,zt.d)(r)}function tn(e,t){const n=function(e,t){if((0,a.s7)(t))return t.name;if(e.ruleNames.has(t))return e.ruleNames.get(t);{let n=t,r=n.$container,i=t.$type;for(;!(0,a.s7)(r);){if((0,a.IZ)(r)||(0,a.jp)(r)||(0,a.cY)(r)){i=r.elements.indexOf(n).toString()+":"+i}n=r,r=r.$container}return i=r.name+":"+i,e.ruleNames.set(t,i),i}}(e,t),r=e.parser.getRule(n);if(!r)throw new Error(`Rule "${n}" not found."`);return r}function nn(e,t){const n=e.tokens[t];if(!n)throw new Error(`Token "${t}" not found."`);return n}function rn(e){const t=function(e){const t=e.Grammar,n=e.parser.Lexer,r=new Kt(e);return Xt(t,r,n.definition)}(e);return t.finalize(),t}var sn=n(1945),an=n(5033),on=n(9850),cn=n(2479);let ln=0,un=10;const dn=Symbol("OperationCancelled");function hn(e){return e===dn}async function fn(e){if(e===on.XO.None)return;const t=performance.now();if(t-ln>=un&&(ln=t,await new Promise((e=>{"undefined"==typeof setImmediate?setTimeout(e,0):setImmediate(e)})),ln=performance.now()),e.isCancellationRequested)throw dn}class pn{constructor(){this.promise=new Promise(((e,t)=>{this.resolve=t=>(e(t),this),this.reject=e=>(t(e),this)}))}}class mn{constructor(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){const t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){for(const n of e)if(mn.isIncremental(n)){const e=vn(n.range),t=this.offsetAt(e.start),r=this.offsetAt(e.end);this._content=this._content.substring(0,t)+n.text+this._content.substring(r,this._content.length);const i=Math.max(e.start.line,0),s=Math.max(e.end.line,0);let a=this._lineOffsets;const o=Tn(n.text,!1,t);if(s-i===o.length)for(let n=0,l=o.length;n<l;n++)a[n+i+1]=o[n];else o.length<1e4?a.splice(i+1,s-i,...o):this._lineOffsets=a=a.slice(0,i+1).concat(o,a.slice(s+1));const c=n.text.length-(r-t);if(0!==c)for(let n=i+1+o.length,l=a.length;n<l;n++)a[n]=a[n]+c}else{if(!mn.isFull(n))throw new Error("Unknown change event received");this._content=n.text,this._lineOffsets=void 0}this._version=t}getLineOffsets(){return void 0===this._lineOffsets&&(this._lineOffsets=Tn(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);const t=this.getLineOffsets();let n=0,r=t.length;if(0===r)return{line:0,character:e};for(;n<r;){const i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}const i=n-1;return{line:i,character:(e=this.ensureBeforeEOL(e,t[i]))-t[i]}}offsetAt(e){const t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;const n=t[e.line];if(e.character<=0)return n;const r=e.line+1<t.length?t[e.line+1]:this._content.length,i=Math.min(n+e.character,r);return this.ensureBeforeEOL(i,n)}ensureBeforeEOL(e,t){for(;e>t&&An(this._content.charCodeAt(e-1));)e--;return e}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){const t=e;return null!=t&&"string"==typeof t.text&&void 0!==t.range&&(void 0===t.rangeLength||"number"==typeof t.rangeLength)}static isFull(e){const t=e;return null!=t&&"string"==typeof t.text&&void 0===t.range&&void 0===t.rangeLength}}var gn;function yn(e,t){if(e.length<=1)return e;const n=e.length/2|0,r=e.slice(0,n),i=e.slice(n);yn(r,t),yn(i,t);let s=0,a=0,o=0;for(;s<r.length&&a<i.length;){const n=t(r[s],i[a]);e[o++]=n<=0?r[s++]:i[a++]}for(;s<r.length;)e[o++]=r[s++];for(;a<i.length;)e[o++]=i[a++];return e}function Tn(e,t,n=0){const r=t?[n]:[];for(let i=0;i<e.length;i++){const t=e.charCodeAt(i);An(t)&&(13===t&&i+1<e.length&&10===e.charCodeAt(i+1)&&i++,r.push(n+i+1))}return r}function An(e){return 13===e||10===e}function vn(e){const t=e.start,n=e.end;return t.line>n.line||t.line===n.line&&t.character>n.character?{start:n,end:t}:e}function Rn(e){const t=vn(e.range);return t!==e.range?{newText:e.newText,range:t}:e}!function(e){e.create=function(e,t,n,r){return new mn(e,t,n,r)},e.update=function(e,t,n){if(e instanceof mn)return e.update(t,n),e;throw new Error("TextDocument.update: document must be created by TextDocument.create")},e.applyEdits=function(e,t){const n=e.getText(),r=yn(t.map(Rn),((e,t)=>{const n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n}));let i=0;const s=[];for(const a of r){const t=e.offsetAt(a.range.start);if(t<i)throw new Error("Overlapping edit");t>i&&s.push(n.substring(i,t)),a.newText.length&&s.push(a.newText),i=e.offsetAt(a.range.end)}return s.push(n.substr(i)),s.join("")}}(gn||(gn={}));var En,kn=n(7608);!function(e){e[e.Changed=0]="Changed",e[e.Parsed=1]="Parsed",e[e.IndexedContent=2]="IndexedContent",e[e.ComputedScopes=3]="ComputedScopes",e[e.Linked=4]="Linked",e[e.IndexedReferences=5]="IndexedReferences",e[e.Validated=6]="Validated"}(En||(En={}));class $n{constructor(e){this.serviceRegistry=e.ServiceRegistry,this.textDocuments=e.workspace.TextDocuments,this.fileSystemProvider=e.workspace.FileSystemProvider}async fromUri(e,t=on.XO.None){const n=await this.fileSystemProvider.readFile(e);return this.createAsync(e,n,t)}fromTextDocument(e,t,n){return t=null!=t?t:kn.r.parse(e.uri),on.XO.is(n)?this.createAsync(t,e,n):this.create(t,e,n)}fromString(e,t,n){return on.XO.is(n)?this.createAsync(t,e,n):this.create(t,e,n)}fromModel(e,t){return this.create(t,{$model:e})}create(e,t,n){if("string"==typeof t){const r=this.parse(e,t,n);return this.createLangiumDocument(r,e,void 0,t)}if("$model"in t){const n={value:t.$model,parserErrors:[],lexerErrors:[]};return this.createLangiumDocument(n,e)}{const r=this.parse(e,t.getText(),n);return this.createLangiumDocument(r,e,t)}}async createAsync(e,t,n){if("string"==typeof t){const r=await this.parseAsync(e,t,n);return this.createLangiumDocument(r,e,void 0,t)}{const r=await this.parseAsync(e,t.getText(),n);return this.createLangiumDocument(r,e,t)}}createLangiumDocument(e,t,n,r){let i;if(n)i={parseResult:e,uri:t,state:En.Parsed,references:[],textDocument:n};else{const n=this.createTextDocumentGetter(t,r);i={parseResult:e,uri:t,state:En.Parsed,references:[],get textDocument(){return n()}}}return e.value.$document=i,i}async update(e,t){var n,r;const i=null===(n=e.parseResult.value.$cstNode)||void 0===n?void 0:n.root.fullText,s=null===(r=this.textDocuments)||void 0===r?void 0:r.get(e.uri.toString()),a=s?s.getText():await this.fileSystemProvider.readFile(e.uri);if(s)Object.defineProperty(e,"textDocument",{value:s});else{const t=this.createTextDocumentGetter(e.uri,a);Object.defineProperty(e,"textDocument",{get:t})}return i!==a&&(e.parseResult=await this.parseAsync(e.uri,a,t),e.parseResult.value.$document=e),e.state=En.Parsed,e}parse(e,t,n){return this.serviceRegistry.getServices(e).parser.LangiumParser.parse(t,n)}parseAsync(e,t,n){return this.serviceRegistry.getServices(e).parser.AsyncParser.parse(t,n)}createTextDocumentGetter(e,t){const n=this.serviceRegistry;let r;return()=>null!=r?r:r=gn.create(e.toString(),n.getServices(e).LanguageMetaData.languageId,0,null!=t?t:"")}}class xn{constructor(e){this.documentMap=new Map,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.serviceRegistry=e.ServiceRegistry}get all(){return(0,Yt.Td)(this.documentMap.values())}addDocument(e){const t=e.uri.toString();if(this.documentMap.has(t))throw new Error(`A document with the URI '${t}' is already present.`);this.documentMap.set(t,e)}getDocument(e){const t=e.toString();return this.documentMap.get(t)}async getOrCreateDocument(e,t){let n=this.getDocument(e);return n||(n=await this.langiumDocumentFactory.fromUri(e,t),this.addDocument(n),n)}createDocument(e,t,n){if(n)return this.langiumDocumentFactory.fromString(t,e,n).then((e=>(this.addDocument(e),e)));{const n=this.langiumDocumentFactory.fromString(t,e);return this.addDocument(n),n}}hasDocument(e){return this.documentMap.has(e.toString())}invalidateDocument(e){const t=e.toString(),n=this.documentMap.get(t);if(n){this.serviceRegistry.getServices(e).references.Linker.unlink(n),n.state=En.Changed,n.precomputedScopes=void 0,n.diagnostics=void 0}return n}deleteDocument(e){const t=e.toString(),n=this.documentMap.get(t);return n&&(n.state=En.Changed,this.documentMap.delete(t)),n}}const In=Symbol("ref_resolving");class Sn{constructor(e){this.reflection=e.shared.AstReflection,this.langiumDocuments=()=>e.shared.workspace.LangiumDocuments,this.scopeProvider=e.references.ScopeProvider,this.astNodeLocator=e.workspace.AstNodeLocator}async link(e,t=on.XO.None){for(const n of(0,St.jm)(e.parseResult.value))await fn(t),(0,St.DM)(n).forEach((t=>this.doLink(t,e)))}doLink(e,t){var n;const r=e.reference;if(void 0===r._ref){r._ref=In;try{const t=this.getCandidate(e);if((0,cn.Zl)(t))r._ref=t;else if(r._nodeDescription=t,this.langiumDocuments().hasDocument(t.documentUri)){const n=this.loadAstNode(t);r._ref=null!=n?n:this.createLinkingError(e,t)}else r._ref=void 0}catch(i){console.error(`An error occurred while resolving reference to '${r.$refText}':`,i);const t=null!==(n=i.message)&&void 0!==n?n:String(i);r._ref=Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${r.$refText}': ${t}`})}t.references.push(r)}}unlink(e){for(const t of e.references)delete t._ref,delete t._nodeDescription;e.references=[]}getCandidate(e){const t=this.scopeProvider.getScope(e).getElement(e.reference.$refText);return null!=t?t:this.createLinkingError(e)}buildReference(e,t,n,r){const i=this,s={$refNode:n,$refText:r,get ref(){var n;if((0,cn.ng)(this._ref))return this._ref;if((0,cn.Nr)(this._nodeDescription)){const n=i.loadAstNode(this._nodeDescription);this._ref=null!=n?n:i.createLinkingError({reference:s,container:e,property:t},this._nodeDescription)}else if(void 0===this._ref){this._ref=In;const r=(0,St.cQ)(e).$document,a=i.getLinkedNode({reference:s,container:e,property:t});if(a.error&&r&&r.state<En.ComputedScopes)return this._ref=void 0;this._ref=null!==(n=a.node)&&void 0!==n?n:a.error,this._nodeDescription=a.descr,null==r||r.references.push(this)}else if(this._ref===In)throw new Error(`Cyclic reference resolution detected: ${i.astNodeLocator.getAstNodePath(e)}/${t} (symbol '${r}')`);return(0,cn.ng)(this._ref)?this._ref:void 0},get $nodeDescription(){return this._nodeDescription},get error(){return(0,cn.Zl)(this._ref)?this._ref:void 0}};return s}getLinkedNode(e){var t;try{const t=this.getCandidate(e);if((0,cn.Zl)(t))return{error:t};const n=this.loadAstNode(t);return n?{node:n,descr:t}:{descr:t,error:this.createLinkingError(e,t)}}catch(n){console.error(`An error occurred while resolving reference to '${e.reference.$refText}':`,n);const r=null!==(t=n.message)&&void 0!==t?t:String(n);return{error:Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${e.reference.$refText}': ${r}`})}}}loadAstNode(e){if(e.node)return e.node;const t=this.langiumDocuments().getDocument(e.documentUri);return t?this.astNodeLocator.getAstNode(t.parseResult.value,e.path):void 0}createLinkingError(e,t){const n=(0,St.cQ)(e.container).$document;n&&n.state<En.ComputedScopes&&console.warn(`Attempted reference resolution before document reached ComputedScopes state (${n.uri}).`);const r=this.reflection.getReferenceType(e);return Object.assign(Object.assign({},e),{message:`Could not resolve reference to ${r} named '${e.reference.$refText}'.`,targetDescription:t})}}class Nn{getName(e){if(function(e){return"string"==typeof e.name}(e))return e.name}getNameNode(e){return(0,i.qO)(e.$cstNode,"name")}}var Cn;!function(e){e.basename=kn.A.basename,e.dirname=kn.A.dirname,e.extname=kn.A.extname,e.joinPath=kn.A.joinPath,e.resolvePath=kn.A.resolvePath,e.equals=function(e,t){return(null==e?void 0:e.toString())===(null==t?void 0:t.toString())},e.relative=function(e,t){const n="string"==typeof e?e:e.path,r="string"==typeof t?t:t.path,i=n.split("/").filter((e=>e.length>0)),s=r.split("/").filter((e=>e.length>0));let a=0;for(;a<i.length&&i[a]===s[a];a++);return"../".repeat(i.length-a)+s.slice(a).join("/")},e.normalize=function(e){return kn.r.parse(e.toString()).toString()}}(Cn||(Cn={}));class wn{constructor(e){this.nameProvider=e.references.NameProvider,this.index=e.shared.workspace.IndexManager,this.nodeLocator=e.workspace.AstNodeLocator}findDeclaration(e){if(e){const t=(0,i.Rp)(e),n=e.astNode;if(t&&n){const r=n[t.feature];if((0,cn.A_)(r))return r.ref;if(Array.isArray(r))for(const t of r)if((0,cn.A_)(t)&&t.$refNode&&t.$refNode.offset<=e.offset&&t.$refNode.end>=e.end)return t.ref}if(n){const t=this.nameProvider.getNameNode(n);if(t&&(t===e||(0,r.pO)(e,t)))return n}}}findDeclarationNode(e){const t=this.findDeclaration(e);if(null==t?void 0:t.$cstNode){const e=this.nameProvider.getNameNode(t);return null!=e?e:t.$cstNode}}findReferences(e,t){const n=[];if(t.includeDeclaration){const t=this.getReferenceToSelf(e);t&&n.push(t)}let r=this.index.findAllReferences(e,this.nodeLocator.getAstNodePath(e));return t.documentUri&&(r=r.filter((e=>Cn.equals(e.sourceUri,t.documentUri)))),n.push(...r),(0,Yt.Td)(n)}getReferenceToSelf(e){const t=this.nameProvider.getNameNode(e);if(t){const n=(0,St.YE)(e),i=this.nodeLocator.getAstNodePath(e);return{sourceUri:n.uri,sourcePath:i,targetUri:n.uri,targetPath:i,segment:(0,r.SX)(t),local:!0}}}}class Ln{constructor(e){if(this.map=new Map,e)for(const[t,n]of e)this.add(t,n)}get size(){return Yt.iD.sum((0,Yt.Td)(this.map.values()).map((e=>e.length)))}clear(){this.map.clear()}delete(e,t){if(void 0===t)return this.map.delete(e);{const n=this.map.get(e);if(n){const r=n.indexOf(t);if(r>=0)return 1===n.length?this.map.delete(e):n.splice(r,1),!0}return!1}}get(e){var t;return null!==(t=this.map.get(e))&&void 0!==t?t:[]}has(e,t){if(void 0===t)return this.map.has(e);{const n=this.map.get(e);return!!n&&n.indexOf(t)>=0}}add(e,t){return this.map.has(e)?this.map.get(e).push(t):this.map.set(e,[t]),this}addAll(e,t){return this.map.has(e)?this.map.get(e).push(...t):this.map.set(e,Array.from(t)),this}forEach(e){this.map.forEach(((t,n)=>t.forEach((t=>e(t,n,this)))))}[Symbol.iterator](){return this.entries().iterator()}entries(){return(0,Yt.Td)(this.map.entries()).flatMap((([e,t])=>t.map((t=>[e,t]))))}keys(){return(0,Yt.Td)(this.map.keys())}values(){return(0,Yt.Td)(this.map.values()).flat()}entriesGroupedByKey(){return(0,Yt.Td)(this.map.entries())}}class bn{get size(){return this.map.size}constructor(e){if(this.map=new Map,this.inverse=new Map,e)for(const[t,n]of e)this.set(t,n)}clear(){this.map.clear(),this.inverse.clear()}set(e,t){return this.map.set(e,t),this.inverse.set(t,e),this}get(e){return this.map.get(e)}getKey(e){return this.inverse.get(e)}delete(e){const t=this.map.get(e);return void 0!==t&&(this.map.delete(e),this.inverse.delete(t),!0)}}class On{constructor(e){this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider}async computeExports(e,t=on.XO.None){return this.computeExportsForNode(e.parseResult.value,e,void 0,t)}async computeExportsForNode(e,t,n=St.VN,r=on.XO.None){const i=[];this.exportNode(e,i,t);for(const s of n(e))await fn(r),this.exportNode(s,i,t);return i}exportNode(e,t,n){const r=this.nameProvider.getName(e);r&&t.push(this.descriptions.createDescription(e,r,n))}async computeLocalScopes(e,t=on.XO.None){const n=e.parseResult.value,r=new Ln;for(const i of(0,St.Uo)(n))await fn(t),this.processNode(i,e,r);return r}processNode(e,t,n){const r=e.$container;if(r){const i=this.nameProvider.getName(e);i&&n.add(r,this.descriptions.createDescription(e,i,t))}}}class _n{constructor(e,t,n){var r;this.elements=e,this.outerScope=t,this.caseInsensitive=null!==(r=null==n?void 0:n.caseInsensitive)&&void 0!==r&&r}getAllElements(){return this.outerScope?this.elements.concat(this.outerScope.getAllElements()):this.elements}getElement(e){const t=this.caseInsensitive?this.elements.find((t=>t.name.toLowerCase()===e.toLowerCase())):this.elements.find((t=>t.name===e));return t||(this.outerScope?this.outerScope.getElement(e):void 0)}}class Pn{constructor(e,t,n){var r;this.elements=new Map,this.caseInsensitive=null!==(r=null==n?void 0:n.caseInsensitive)&&void 0!==r&&r;for(const i of e){const e=this.caseInsensitive?i.name.toLowerCase():i.name;this.elements.set(e,i)}this.outerScope=t}getElement(e){const t=this.caseInsensitive?e.toLowerCase():e,n=this.elements.get(t);return n||(this.outerScope?this.outerScope.getElement(e):void 0)}getAllElements(){let e=(0,Yt.Td)(this.elements.values());return this.outerScope&&(e=e.concat(this.outerScope.getAllElements())),e}}class Mn{constructor(){this.toDispose=[],this.isDisposed=!1}onDispose(e){this.toDispose.push(e)}dispose(){this.throwIfDisposed(),this.clear(),this.isDisposed=!0,this.toDispose.forEach((e=>e.dispose()))}throwIfDisposed(){if(this.isDisposed)throw new Error("This cache has already been disposed")}}class Dn extends Mn{constructor(){super(...arguments),this.cache=new Map}has(e){return this.throwIfDisposed(),this.cache.has(e)}set(e,t){this.throwIfDisposed(),this.cache.set(e,t)}get(e,t){if(this.throwIfDisposed(),this.cache.has(e))return this.cache.get(e);if(t){const n=t();return this.cache.set(e,n),n}}delete(e){return this.throwIfDisposed(),this.cache.delete(e)}clear(){this.throwIfDisposed(),this.cache.clear()}}class Un extends Mn{constructor(e){super(),this.cache=new Map,this.converter=null!=e?e:e=>e}has(e,t){return this.throwIfDisposed(),this.cacheForContext(e).has(t)}set(e,t,n){this.throwIfDisposed(),this.cacheForContext(e).set(t,n)}get(e,t,n){this.throwIfDisposed();const r=this.cacheForContext(e);if(r.has(t))return r.get(t);if(n){const e=n();return r.set(t,e),e}}delete(e,t){return this.throwIfDisposed(),this.cacheForContext(e).delete(t)}clear(e){if(this.throwIfDisposed(),e){const t=this.converter(e);this.cache.delete(t)}else this.cache.clear()}cacheForContext(e){const t=this.converter(e);let n=this.cache.get(t);return n||(n=new Map,this.cache.set(t,n)),n}}class Fn extends Dn{constructor(e,t){super(),t?(this.toDispose.push(e.workspace.DocumentBuilder.onBuildPhase(t,(()=>{this.clear()}))),this.toDispose.push(e.workspace.DocumentBuilder.onUpdate(((e,t)=>{t.length>0&&this.clear()})))):this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((()=>{this.clear()})))}}class Gn{constructor(e){this.reflection=e.shared.AstReflection,this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider,this.indexManager=e.shared.workspace.IndexManager,this.globalScopeCache=new Fn(e.shared)}getScope(e){const t=[],n=this.reflection.getReferenceType(e),r=(0,St.YE)(e.container).precomputedScopes;if(r){let i=e.container;do{const e=r.get(i);e.length>0&&t.push((0,Yt.Td)(e).filter((e=>this.reflection.isSubtype(e.type,n)))),i=i.$container}while(i)}let i=this.getGlobalScope(n,e);for(let s=t.length-1;s>=0;s--)i=this.createScope(t[s],i);return i}createScope(e,t,n){return new _n((0,Yt.Td)(e),t,n)}createScopeForNodes(e,t,n){const r=(0,Yt.Td)(e).map((e=>{const t=this.nameProvider.getName(e);if(t)return this.descriptions.createDescription(e,t)})).nonNullable();return new _n(r,t,n)}getGlobalScope(e,t){return this.globalScopeCache.get(e,(()=>new Pn(this.indexManager.allElements(e))))}}function Kn(e){return"object"==typeof e&&!!e&&("$ref"in e||"$error"in e)}class Bn{constructor(e){this.ignoreProperties=new Set(["$container","$containerProperty","$containerIndex","$document","$cstNode"]),this.langiumDocuments=e.shared.workspace.LangiumDocuments,this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider,this.commentProvider=e.documentation.CommentProvider}serialize(e,t){const n=null!=t?t:{},r=null==t?void 0:t.replacer,i=(e,t)=>this.replacer(e,t,n),s=r?(e,t)=>r(e,t,i):i;try{return this.currentDocument=(0,St.YE)(e),JSON.stringify(e,s,null==t?void 0:t.space)}finally{this.currentDocument=void 0}}deserialize(e,t){const n=null!=t?t:{},r=JSON.parse(e);return this.linkNode(r,r,n),r}replacer(e,t,{refText:n,sourceText:r,textRegions:i,comments:s,uriConverter:a}){var o,c,l,u;if(!this.ignoreProperties.has(e)){if((0,cn.A_)(t)){const e=t.ref,r=n?t.$refText:void 0;if(e){const n=(0,St.YE)(e);let i="";this.currentDocument&&this.currentDocument!==n&&(i=a?a(n.uri,t):n.uri.toString());return{$ref:`${i}#${this.astNodeLocator.getAstNodePath(e)}`,$refText:r}}return{$error:null!==(c=null===(o=t.error)||void 0===o?void 0:o.message)&&void 0!==c?c:"Could not resolve reference",$refText:r}}if((0,cn.ng)(t)){let n;if(i&&(n=this.addAstNodeRegionWithAssignmentsTo(Object.assign({},t)),e&&!t.$document||!(null==n?void 0:n.$textRegion)||(n.$textRegion.documentURI=null===(l=this.currentDocument)||void 0===l?void 0:l.uri.toString())),r&&!e&&(null!=n||(n=Object.assign({},t)),n.$sourceText=null===(u=t.$cstNode)||void 0===u?void 0:u.text),s){null!=n||(n=Object.assign({},t));const e=this.commentProvider.getComment(t);e&&(n.$comment=e.replace(/\r/g,""))}return null!=n?n:t}return t}}addAstNodeRegionWithAssignmentsTo(e){const t=e=>({offset:e.offset,end:e.end,length:e.length,range:e.range});if(e.$cstNode){const n=(e.$textRegion=t(e.$cstNode)).assignments={};return Object.keys(e).filter((e=>!e.startsWith("$"))).forEach((r=>{const s=(0,i.Bd)(e.$cstNode,r).map(t);0!==s.length&&(n[r]=s)})),e}}linkNode(e,t,n,r,i,s){for(const[o,c]of Object.entries(e))if(Array.isArray(c))for(let r=0;r<c.length;r++){const i=c[r];Kn(i)?c[r]=this.reviveReference(e,o,t,i,n):(0,cn.ng)(i)&&this.linkNode(i,t,n,e,o,r)}else Kn(c)?e[o]=this.reviveReference(e,o,t,c,n):(0,cn.ng)(c)&&this.linkNode(c,t,n,e,o);const a=e;a.$container=r,a.$containerProperty=i,a.$containerIndex=s}reviveReference(e,t,n,r,i){let s=r.$refText,a=r.$error;if(r.$ref){const e=this.getRefNode(n,r.$ref,i.uriConverter);if((0,cn.ng)(e))return s||(s=this.nameProvider.getName(e)),{$refText:null!=s?s:"",ref:e};a=e}if(a){const n={$refText:null!=s?s:""};return n.error={container:e,property:t,message:a,reference:n},n}}getRefNode(e,t,n){try{const r=t.indexOf("#");if(0===r){const n=this.astNodeLocator.getAstNode(e,t.substring(1));return n||"Could not resolve path: "+t}if(r<0){const e=n?n(t):kn.r.parse(t),r=this.langiumDocuments.getDocument(e);return r?r.parseResult.value:"Could not find document for URI: "+t}const i=n?n(t.substring(0,r)):kn.r.parse(t.substring(0,r)),s=this.langiumDocuments.getDocument(i);if(!s)return"Could not find document for URI: "+t;if(r===t.length-1)return s.parseResult.value;const a=this.astNodeLocator.getAstNode(s.parseResult.value,t.substring(r+1));return a||"Could not resolve URI: "+t}catch(r){return String(r)}}}class jn{get map(){return this.fileExtensionMap}constructor(e){this.languageIdMap=new Map,this.fileExtensionMap=new Map,this.textDocuments=null==e?void 0:e.workspace.TextDocuments}register(e){const t=e.LanguageMetaData;for(const n of t.fileExtensions)this.fileExtensionMap.has(n)&&console.warn(`The file extension ${n} is used by multiple languages. It is now assigned to '${t.languageId}'.`),this.fileExtensionMap.set(n,e);this.languageIdMap.set(t.languageId,e),1===this.languageIdMap.size?this.singleton=e:this.singleton=void 0}getServices(e){var t,n;if(void 0!==this.singleton)return this.singleton;if(0===this.languageIdMap.size)throw new Error("The service registry is empty. Use `register` to register the services of a language.");const r=null===(n=null===(t=this.textDocuments)||void 0===t?void 0:t.get(e))||void 0===n?void 0:n.languageId;if(void 0!==r){const e=this.languageIdMap.get(r);if(e)return e}const i=Cn.extname(e),s=this.fileExtensionMap.get(i);if(!s)throw r?new Error(`The service registry contains no services for the extension '${i}' for language '${r}'.`):new Error(`The service registry contains no services for the extension '${i}'.`);return s}hasServices(e){try{return this.getServices(e),!0}catch(t){return!1}}get all(){return Array.from(this.languageIdMap.values())}}function Vn(e){return{code:e}}var Hn,Wn;!function(e){e.all=["fast","slow","built-in"]}(Hn||(Hn={}));class zn{constructor(e){this.entries=new Ln,this.entriesBefore=[],this.entriesAfter=[],this.reflection=e.shared.AstReflection}register(e,t=this,n="fast"){if("built-in"===n)throw new Error("The 'built-in' category is reserved for lexer, parser, and linker errors.");for(const[r,i]of Object.entries(e)){const e=i;if(Array.isArray(e))for(const i of e){const e={check:this.wrapValidationException(i,t),category:n};this.addEntry(r,e)}else if("function"==typeof e){const i={check:this.wrapValidationException(e,t),category:n};this.addEntry(r,i)}else(0,zt.d)(e)}}wrapValidationException(e,t){return async(n,r,i)=>{await this.handleException((()=>e.call(t,n,r,i)),"An error occurred during validation",r,n)}}async handleException(e,t,n,r){try{await e()}catch(i){if(hn(i))throw i;console.error(`${t}:`,i),i instanceof Error&&i.stack&&console.error(i.stack);n("error",`${t}: ${i instanceof Error?i.message:String(i)}`,{node:r})}}addEntry(e,t){if("AstNode"!==e)for(const n of this.reflection.getAllSubTypes(e))this.entries.add(n,t);else this.entries.add("AstNode",t)}getChecks(e,t){let n=(0,Yt.Td)(this.entries.get(e)).concat(this.entries.get("AstNode"));return t&&(n=n.filter((e=>t.includes(e.category)))),n.map((e=>e.check))}registerBeforeDocument(e,t=this){this.entriesBefore.push(this.wrapPreparationException(e,"An error occurred during set-up of the validation",t))}registerAfterDocument(e,t=this){this.entriesAfter.push(this.wrapPreparationException(e,"An error occurred during tear-down of the validation",t))}wrapPreparationException(e,t,n){return async(r,i,s,a)=>{await this.handleException((()=>e.call(n,r,i,s,a)),t,i,r)}}get checksBefore(){return this.entriesBefore}get checksAfter(){return this.entriesAfter}}class Yn{constructor(e){this.validationRegistry=e.validation.ValidationRegistry,this.metadata=e.LanguageMetaData}async validateDocument(e,t={},n=on.XO.None){const r=e.parseResult,i=[];if(await fn(n),!t.categories||t.categories.includes("built-in")){if(this.processLexingErrors(r,i,t),t.stopAfterLexingErrors&&i.some((e=>{var t;return(null===(t=e.data)||void 0===t?void 0:t.code)===Wn.LexingError})))return i;if(this.processParsingErrors(r,i,t),t.stopAfterParsingErrors&&i.some((e=>{var t;return(null===(t=e.data)||void 0===t?void 0:t.code)===Wn.ParsingError})))return i;if(this.processLinkingErrors(e,i,t),t.stopAfterLinkingErrors&&i.some((e=>{var t;return(null===(t=e.data)||void 0===t?void 0:t.code)===Wn.LinkingError})))return i}try{i.push(...await this.validateAst(r.value,t,n))}catch(s){if(hn(s))throw s;console.error("An error occurred during validation:",s)}return await fn(n),i}processLexingErrors(e,t,n){var r,i,s;const a=[...e.lexerErrors,...null!==(i=null===(r=e.lexerReport)||void 0===r?void 0:r.diagnostics)&&void 0!==i?i:[]];for(const o of a){const e=null!==(s=o.severity)&&void 0!==s?s:"error",n={severity:qn(e),range:{start:{line:o.line-1,character:o.column-1},end:{line:o.line-1,character:o.column+o.length-1}},message:o.message,data:Qn(e),source:this.getSource()};t.push(n)}}processParsingErrors(e,t,n){for(const i of e.parserErrors){let e;if(isNaN(i.token.startOffset)){if("previousToken"in i){const t=i.previousToken;if(isNaN(t.startOffset)){const t={line:0,character:0};e={start:t,end:t}}else{const n={line:t.endLine-1,character:t.endColumn};e={start:n,end:n}}}}else e=(0,r.wf)(i.token);if(e){const n={severity:qn("error"),range:e,message:i.message,data:Vn(Wn.ParsingError),source:this.getSource()};t.push(n)}}}processLinkingErrors(e,t,n){for(const r of e.references){const e=r.error;if(e){const n={node:e.container,property:e.property,index:e.index,data:{code:Wn.LinkingError,containerType:e.container.$type,property:e.property,refText:e.reference.$refText}};t.push(this.toDiagnostic("error",e.message,n))}}}async validateAst(e,t,n=on.XO.None){const r=[],i=(e,t,n)=>{r.push(this.toDiagnostic(e,t,n))};return await this.validateAstBefore(e,t,i,n),await this.validateAstNodes(e,t,i,n),await this.validateAstAfter(e,t,i,n),r}async validateAstBefore(e,t,n,r=on.XO.None){var i;const s=this.validationRegistry.checksBefore;for(const a of s)await fn(r),await a(e,n,null!==(i=t.categories)&&void 0!==i?i:[],r)}async validateAstNodes(e,t,n,r=on.XO.None){await Promise.all((0,St.jm)(e).map((async e=>{await fn(r);const i=this.validationRegistry.getChecks(e.$type,t.categories);for(const t of i)await t(e,n,r)})))}async validateAstAfter(e,t,n,r=on.XO.None){var i;const s=this.validationRegistry.checksAfter;for(const a of s)await fn(r),await a(e,n,null!==(i=t.categories)&&void 0!==i?i:[],r)}toDiagnostic(e,t,n){return{message:t,range:Xn(n),severity:qn(e),code:n.code,codeDescription:n.codeDescription,tags:n.tags,relatedInformation:n.relatedInformation,data:n.data,source:this.getSource()}}getSource(){return this.metadata.languageId}}function Xn(e){if(e.range)return e.range;let t;return"string"==typeof e.property?t=(0,i.qO)(e.node.$cstNode,e.property,e.index):"string"==typeof e.keyword&&(t=(0,i.SS)(e.node.$cstNode,e.keyword,e.index)),null!=t||(t=e.node.$cstNode),t?t.range:{start:{line:0,character:0},end:{line:0,character:0}}}function qn(e){switch(e){case"error":return 1;case"warning":return 2;case"info":return 3;case"hint":return 4;default:throw new Error("Invalid diagnostic severity: "+e)}}function Qn(e){switch(e){case"error":return Vn(Wn.LexingError);case"warning":return Vn(Wn.LexingWarning);case"info":return Vn(Wn.LexingInfo);case"hint":return Vn(Wn.LexingHint);default:throw new Error("Invalid diagnostic severity: "+e)}}!function(e){e.LexingError="lexing-error",e.LexingWarning="lexing-warning",e.LexingInfo="lexing-info",e.LexingHint="lexing-hint",e.ParsingError="parsing-error",e.LinkingError="linking-error"}(Wn||(Wn={}));class Zn{constructor(e){this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider}createDescription(e,t,n){const i=null!=n?n:(0,St.YE)(e);null!=t||(t=this.nameProvider.getName(e));const s=this.astNodeLocator.getAstNodePath(e);if(!t)throw new Error(`Node at path ${s} has no name.`);let a;const o=()=>{var t;return null!=a?a:a=(0,r.SX)(null!==(t=this.nameProvider.getNameNode(e))&&void 0!==t?t:e.$cstNode)};return{node:e,name:t,get nameSegment(){return o()},selectionSegment:(0,r.SX)(e.$cstNode),type:e.$type,documentUri:i.uri,path:s}}}class Jn{constructor(e){this.nodeLocator=e.workspace.AstNodeLocator}async createDescriptions(e,t=on.XO.None){const n=[],r=e.parseResult.value;for(const i of(0,St.jm)(r))await fn(t),(0,St.DM)(i).filter((e=>!(0,cn.Zl)(e))).forEach((e=>{const t=this.createDescription(e);t&&n.push(t)}));return n}createDescription(e){const t=e.reference.$nodeDescription,n=e.reference.$refNode;if(!t||!n)return;const i=(0,St.YE)(e.container).uri;return{sourceUri:i,sourcePath:this.nodeLocator.getAstNodePath(e.container),targetUri:t.documentUri,targetPath:t.path,segment:(0,r.SX)(n),local:Cn.equals(t.documentUri,i)}}}class er{constructor(){this.segmentSeparator="/",this.indexSeparator="@"}getAstNodePath(e){if(e.$container){const t=this.getAstNodePath(e.$container),n=this.getPathSegment(e);return t+this.segmentSeparator+n}return""}getPathSegment({$containerProperty:e,$containerIndex:t}){if(!e)throw new Error("Missing '$containerProperty' in AST node.");return void 0!==t?e+this.indexSeparator+t:e}getAstNode(e,t){return t.split(this.segmentSeparator).reduce(((e,t)=>{if(!e||0===t.length)return e;const n=t.indexOf(this.indexSeparator);if(n>0){const r=t.substring(0,n),i=parseInt(t.substring(n+1)),s=e[r];return null==s?void 0:s[i]}return e[t]}),e)}}var tr,nr=n(2676);class rr{constructor(e){this._ready=new pn,this.settings={},this.workspaceConfig=!1,this.onConfigurationSectionUpdateEmitter=new nr.Emitter,this.serviceRegistry=e.ServiceRegistry}get ready(){return this._ready.promise}initialize(e){var t,n;this.workspaceConfig=null!==(n=null===(t=e.capabilities.workspace)||void 0===t?void 0:t.configuration)&&void 0!==n&&n}async initialized(e){if(this.workspaceConfig){if(e.register){const t=this.serviceRegistry.all;e.register({section:t.map((e=>this.toSectionName(e.LanguageMetaData.languageId)))})}if(e.fetchConfiguration){const t=this.serviceRegistry.all.map((e=>({section:this.toSectionName(e.LanguageMetaData.languageId)}))),n=await e.fetchConfiguration(t);t.forEach(((e,t)=>{this.updateSectionConfiguration(e.section,n[t])}))}}this._ready.resolve()}updateConfiguration(e){e.settings&&Object.keys(e.settings).forEach((t=>{const n=e.settings[t];this.updateSectionConfiguration(t,n),this.onConfigurationSectionUpdateEmitter.fire({section:t,configuration:n})}))}updateSectionConfiguration(e,t){this.settings[e]=t}async getConfiguration(e,t){await this.ready;const n=this.toSectionName(e);if(this.settings[n])return this.settings[n][t]}toSectionName(e){return`${e}`}get onConfigurationSectionUpdate(){return this.onConfigurationSectionUpdateEmitter.event}}!function(e){e.create=function(e){return{dispose:async()=>await e()}}}(tr||(tr={}));class ir{constructor(e){this.updateBuildOptions={validation:{categories:["built-in","fast"]}},this.updateListeners=[],this.buildPhaseListeners=new Ln,this.documentPhaseListeners=new Ln,this.buildState=new Map,this.documentBuildWaiters=new Map,this.currentState=En.Changed,this.langiumDocuments=e.workspace.LangiumDocuments,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.textDocuments=e.workspace.TextDocuments,this.indexManager=e.workspace.IndexManager,this.serviceRegistry=e.ServiceRegistry}async build(e,t={},n=on.XO.None){var r,i;for(const s of e){const e=s.uri.toString();if(s.state===En.Validated){if("boolean"==typeof t.validation&&t.validation)s.state=En.IndexedReferences,s.diagnostics=void 0,this.buildState.delete(e);else if("object"==typeof t.validation){const n=this.buildState.get(e),a=null===(r=null==n?void 0:n.result)||void 0===r?void 0:r.validationChecks;if(a){const r=(null!==(i=t.validation.categories)&&void 0!==i?i:Hn.all).filter((e=>!a.includes(e)));r.length>0&&(this.buildState.set(e,{completed:!1,options:{validation:Object.assign(Object.assign({},t.validation),{categories:r})},result:n.result}),s.state=En.IndexedReferences)}}}else this.buildState.delete(e)}this.currentState=En.Changed,await this.emitUpdate(e.map((e=>e.uri)),[]),await this.buildDocuments(e,t,n)}async update(e,t,n=on.XO.None){this.currentState=En.Changed;for(const s of t)this.langiumDocuments.deleteDocument(s),this.buildState.delete(s.toString()),this.indexManager.remove(s);for(const s of e){if(!this.langiumDocuments.invalidateDocument(s)){const e=this.langiumDocumentFactory.fromModel({$type:"INVALID"},s);e.state=En.Changed,this.langiumDocuments.addDocument(e)}this.buildState.delete(s.toString())}const r=(0,Yt.Td)(e).concat(t).map((e=>e.toString())).toSet();this.langiumDocuments.all.filter((e=>!r.has(e.uri.toString())&&this.shouldRelink(e,r))).forEach((e=>{this.serviceRegistry.getServices(e.uri).references.Linker.unlink(e),e.state=Math.min(e.state,En.ComputedScopes),e.diagnostics=void 0})),await this.emitUpdate(e,t),await fn(n);const i=this.sortDocuments(this.langiumDocuments.all.filter((e=>{var t;return e.state<En.Linked||!(null===(t=this.buildState.get(e.uri.toString()))||void 0===t?void 0:t.completed)})).toArray());await this.buildDocuments(i,this.updateBuildOptions,n)}async emitUpdate(e,t){await Promise.all(this.updateListeners.map((n=>n(e,t))))}sortDocuments(e){let t=0,n=e.length-1;for(;t<n;){for(;t<e.length&&this.hasTextDocument(e[t]);)t++;for(;n>=0&&!this.hasTextDocument(e[n]);)n--;t<n&&([e[t],e[n]]=[e[n],e[t]])}return e}hasTextDocument(e){var t;return Boolean(null===(t=this.textDocuments)||void 0===t?void 0:t.get(e.uri))}shouldRelink(e,t){return!!e.references.some((e=>void 0!==e.error))||this.indexManager.isAffected(e,t)}onUpdate(e){return this.updateListeners.push(e),tr.create((()=>{const t=this.updateListeners.indexOf(e);t>=0&&this.updateListeners.splice(t,1)}))}async buildDocuments(e,t,n){this.prepareBuild(e,t),await this.runCancelable(e,En.Parsed,n,(e=>this.langiumDocumentFactory.update(e,n))),await this.runCancelable(e,En.IndexedContent,n,(e=>this.indexManager.updateContent(e,n))),await this.runCancelable(e,En.ComputedScopes,n,(async e=>{const t=this.serviceRegistry.getServices(e.uri).references.ScopeComputation;e.precomputedScopes=await t.computeLocalScopes(e,n)})),await this.runCancelable(e,En.Linked,n,(e=>this.serviceRegistry.getServices(e.uri).references.Linker.link(e,n))),await this.runCancelable(e,En.IndexedReferences,n,(e=>this.indexManager.updateReferences(e,n)));const r=e.filter((e=>this.shouldValidate(e)));await this.runCancelable(r,En.Validated,n,(e=>this.validate(e,n)));for(const i of e){const e=this.buildState.get(i.uri.toString());e&&(e.completed=!0)}}prepareBuild(e,t){for(const n of e){const e=n.uri.toString(),r=this.buildState.get(e);r&&!r.completed||this.buildState.set(e,{completed:!1,options:t,result:null==r?void 0:r.result})}}async runCancelable(e,t,n,r){const i=e.filter((e=>e.state<t));for(const a of i)await fn(n),await r(a),a.state=t,await this.notifyDocumentPhase(a,t,n);const s=e.filter((e=>e.state===t));await this.notifyBuildPhase(s,t,n),this.currentState=t}onBuildPhase(e,t){return this.buildPhaseListeners.add(e,t),tr.create((()=>{this.buildPhaseListeners.delete(e,t)}))}onDocumentPhase(e,t){return this.documentPhaseListeners.add(e,t),tr.create((()=>{this.documentPhaseListeners.delete(e,t)}))}waitUntil(e,t,n){let r;if(t&&"path"in t?r=t:n=t,null!=n||(n=on.XO.None),r){const t=this.langiumDocuments.getDocument(r);if(t&&t.state>e)return Promise.resolve(r)}return this.currentState>=e?Promise.resolve(void 0):n.isCancellationRequested?Promise.reject(dn):new Promise(((t,i)=>{const s=this.onBuildPhase(e,(()=>{if(s.dispose(),a.dispose(),r){const e=this.langiumDocuments.getDocument(r);t(null==e?void 0:e.uri)}else t(void 0)})),a=n.onCancellationRequested((()=>{s.dispose(),a.dispose(),i(dn)}))}))}async notifyDocumentPhase(e,t,n){const r=this.documentPhaseListeners.get(t).slice();for(const s of r)try{await s(e,n)}catch(i){if(!hn(i))throw i}}async notifyBuildPhase(e,t,n){if(0===e.length)return;const r=this.buildPhaseListeners.get(t).slice();for(const i of r)await fn(n),await i(e,n)}shouldValidate(e){return Boolean(this.getBuildOptions(e).validation)}async validate(e,t){var n,r;const i=this.serviceRegistry.getServices(e.uri).validation.DocumentValidator,s=this.getBuildOptions(e).validation,a="object"==typeof s?s:void 0,o=await i.validateDocument(e,a,t);e.diagnostics?e.diagnostics.push(...o):e.diagnostics=o;const c=this.buildState.get(e.uri.toString());if(c){null!==(n=c.result)&&void 0!==n||(c.result={});const e=null!==(r=null==a?void 0:a.categories)&&void 0!==r?r:Hn.all;c.result.validationChecks?c.result.validationChecks.push(...e):c.result.validationChecks=[...e]}}getBuildOptions(e){var t,n;return null!==(n=null===(t=this.buildState.get(e.uri.toString()))||void 0===t?void 0:t.options)&&void 0!==n?n:{}}}class sr{constructor(e){this.symbolIndex=new Map,this.symbolByTypeIndex=new Un,this.referenceIndex=new Map,this.documents=e.workspace.LangiumDocuments,this.serviceRegistry=e.ServiceRegistry,this.astReflection=e.AstReflection}findAllReferences(e,t){const n=(0,St.YE)(e).uri,r=[];return this.referenceIndex.forEach((e=>{e.forEach((e=>{Cn.equals(e.targetUri,n)&&e.targetPath===t&&r.push(e)}))})),(0,Yt.Td)(r)}allElements(e,t){let n=(0,Yt.Td)(this.symbolIndex.keys());return t&&(n=n.filter((e=>!t||t.has(e)))),n.map((t=>this.getFileDescriptions(t,e))).flat()}getFileDescriptions(e,t){var n;if(!t)return null!==(n=this.symbolIndex.get(e))&&void 0!==n?n:[];const r=this.symbolByTypeIndex.get(e,t,(()=>{var n;return(null!==(n=this.symbolIndex.get(e))&&void 0!==n?n:[]).filter((e=>this.astReflection.isSubtype(e.type,t)))}));return r}remove(e){const t=e.toString();this.symbolIndex.delete(t),this.symbolByTypeIndex.clear(t),this.referenceIndex.delete(t)}async updateContent(e,t=on.XO.None){const n=this.serviceRegistry.getServices(e.uri),r=await n.references.ScopeComputation.computeExports(e,t),i=e.uri.toString();this.symbolIndex.set(i,r),this.symbolByTypeIndex.clear(i)}async updateReferences(e,t=on.XO.None){const n=this.serviceRegistry.getServices(e.uri),r=await n.workspace.ReferenceDescriptionProvider.createDescriptions(e,t);this.referenceIndex.set(e.uri.toString(),r)}isAffected(e,t){const n=this.referenceIndex.get(e.uri.toString());return!!n&&n.some((e=>!e.local&&t.has(e.targetUri.toString())))}}class ar{constructor(e){this.initialBuildOptions={},this._ready=new pn,this.serviceRegistry=e.ServiceRegistry,this.langiumDocuments=e.workspace.LangiumDocuments,this.documentBuilder=e.workspace.DocumentBuilder,this.fileSystemProvider=e.workspace.FileSystemProvider,this.mutex=e.workspace.WorkspaceLock}get ready(){return this._ready.promise}get workspaceFolders(){return this.folders}initialize(e){var t;this.folders=null!==(t=e.workspaceFolders)&&void 0!==t?t:void 0}initialized(e){return this.mutex.write((e=>{var t;return this.initializeWorkspace(null!==(t=this.folders)&&void 0!==t?t:[],e)}))}async initializeWorkspace(e,t=on.XO.None){const n=await this.performStartup(e);await fn(t),await this.documentBuilder.build(n,this.initialBuildOptions,t)}async performStartup(e){const t=this.serviceRegistry.all.flatMap((e=>e.LanguageMetaData.fileExtensions)),n=[],r=e=>{n.push(e),this.langiumDocuments.hasDocument(e.uri)||this.langiumDocuments.addDocument(e)};return await this.loadAdditionalDocuments(e,r),await Promise.all(e.map((e=>[e,this.getRootFolder(e)])).map((async e=>this.traverseFolder(...e,t,r)))),this._ready.resolve(),n}loadAdditionalDocuments(e,t){return Promise.resolve()}getRootFolder(e){return kn.r.parse(e.uri)}async traverseFolder(e,t,n,r){const i=await this.fileSystemProvider.readDirectory(t);await Promise.all(i.map((async t=>{if(this.includeEntry(e,t,n))if(t.isDirectory)await this.traverseFolder(e,t.uri,n,r);else if(t.isFile){const e=await this.langiumDocuments.getOrCreateDocument(t.uri);r(e)}})))}includeEntry(e,t,n){const r=Cn.basename(t.uri);if(r.startsWith("."))return!1;if(t.isDirectory)return"node_modules"!==r&&"out"!==r;if(t.isFile){const e=Cn.extname(t.uri);return n.includes(e)}return!1}}class or{buildUnexpectedCharactersMessage(e,t,n,r,i){return o.PW.buildUnexpectedCharactersMessage(e,t,n,r,i)}buildUnableToPopLexerModeMessage(e){return o.PW.buildUnableToPopLexerModeMessage(e)}}const cr={mode:"full"};class lr{constructor(e){this.errorMessageProvider=e.parser.LexerErrorMessageProvider,this.tokenBuilder=e.parser.TokenBuilder;const t=this.tokenBuilder.buildTokens(e.Grammar,{caseInsensitive:e.LanguageMetaData.caseInsensitive});this.tokenTypes=this.toTokenTypeDictionary(t);const n=dr(t)?Object.values(t):t,r="production"===e.LanguageMetaData.mode;this.chevrotainLexer=new o.JG(n,{positionTracking:"full",skipValidations:r,errorMessageProvider:this.errorMessageProvider})}get definition(){return this.tokenTypes}tokenize(e,t=cr){var n,r,i;const s=this.chevrotainLexer.tokenize(e);return{tokens:s.tokens,errors:s.errors,hidden:null!==(n=s.groups.hidden)&&void 0!==n?n:[],report:null===(i=(r=this.tokenBuilder).flushLexingReport)||void 0===i?void 0:i.call(r,e)}}toTokenTypeDictionary(e){if(dr(e))return e;const t=ur(e)?Object.values(e.modes).flat():e,n={};return t.forEach((e=>n[e.name]=e)),n}}function ur(e){return e&&"modes"in e&&"defaultMode"in e}function dr(e){return!function(e){return Array.isArray(e)&&(0===e.length||"name"in e[0])}(e)&&!ur(e)}function hr(e,t,n){let r,i;"string"==typeof e?(i=t,r=n):(i=e.range.start,r=t),i||(i=oe.create(0,0));const s=function(e){var t,n,r;const i=[];let s=e.position.line,a=e.position.character;for(let o=0;o<e.lines.length;o++){const c=0===o,l=o===e.lines.length-1;let u=e.lines[o],d=0;if(c&&e.options.start){const n=null===(t=e.options.start)||void 0===t?void 0:t.exec(u);n&&(d=n.index+n[0].length)}else{const t=null===(n=e.options.line)||void 0===n?void 0:n.exec(u);t&&(d=t.index+t[0].length)}if(l){const t=null===(r=e.options.end)||void 0===r?void 0:r.exec(u);t&&(u=u.substring(0,t.index))}u=u.substring(0,vr(u));if(Ar(u,d)>=u.length){if(i.length>0){const e=oe.create(s,a);i.push({type:"break",content:"",range:ce.create(e,e)})}}else{pr.lastIndex=d;const e=pr.exec(u);if(e){const t=e[0],n=e[1],r=oe.create(s,a+d),o=oe.create(s,a+d+t.length);i.push({type:"tag",content:n,range:ce.create(r,o)}),d+=t.length,d=Ar(u,d)}if(d<u.length){const e=u.substring(d),t=Array.from(e.matchAll(mr));i.push(...gr(t,e,s,a+d))}}s++,a=0}if(i.length>0&&"break"===i[i.length-1].type)return i.slice(0,-1);return i}({lines:fr(e),position:i,options:Ir(r)});return function(e){var t,n,r,i;const s=oe.create(e.position.line,e.position.character);if(0===e.tokens.length)return new Nr([],ce.create(s,s));const a=[];for(;e.index<e.tokens.length;){const t=Rr(e,a[a.length-1]);t&&a.push(t)}const o=null!==(n=null===(t=a[0])||void 0===t?void 0:t.range.start)&&void 0!==n?n:s,c=null!==(i=null===(r=a[a.length-1])||void 0===r?void 0:r.range.end)&&void 0!==i?i:s;return new Nr(a,ce.create(o,c))}({index:0,tokens:s,position:i})}function fr(e){let t="";t="string"==typeof e?e:e.text;return t.split(s.TH)}const pr=/\s*(@([\p{L}][\p{L}\p{N}]*)?)/uy,mr=/\{(@[\p{L}][\p{L}\p{N}]*)(\s*)([^\r\n}]+)?\}/gu;function gr(e,t,n,r){const i=[];if(0===e.length){const e=oe.create(n,r),s=oe.create(n,r+t.length);i.push({type:"text",content:t,range:ce.create(e,s)})}else{let s=0;for(const o of e){const e=o.index,a=t.substring(s,e);a.length>0&&i.push({type:"text",content:t.substring(s,e),range:ce.create(oe.create(n,s+r),oe.create(n,e+r))});let c=a.length+1;const l=o[1];if(i.push({type:"inline-tag",content:l,range:ce.create(oe.create(n,s+c+r),oe.create(n,s+c+l.length+r))}),c+=l.length,4===o.length){c+=o[2].length;const e=o[3];i.push({type:"text",content:e,range:ce.create(oe.create(n,s+c+r),oe.create(n,s+c+e.length+r))})}else i.push({type:"text",content:"",range:ce.create(oe.create(n,s+c+r),oe.create(n,s+c+r))});s=e+o[0].length}const a=t.substring(s);a.length>0&&i.push({type:"text",content:a,range:ce.create(oe.create(n,s+r),oe.create(n,s+r+a.length))})}return i}const yr=/\S/,Tr=/\s*$/;function Ar(e,t){const n=e.substring(t).match(yr);return n?t+n.index:e.length}function vr(e){const t=e.match(Tr);if(t&&"number"==typeof t.index)return t.index}function Rr(e,t){const n=e.tokens[e.index];return"tag"===n.type?$r(e,!1):"text"===n.type||"inline-tag"===n.type?Er(e):(function(e,t){if(t){const n=new Lr("",e.range);"inlines"in t?t.inlines.push(n):t.content.inlines.push(n)}}(n,t),void e.index++)}function Er(e){let t=e.tokens[e.index];const n=t;let r=t;const i=[];for(;t&&"break"!==t.type&&"tag"!==t.type;)i.push(kr(e)),r=t,t=e.tokens[e.index];return new wr(i,ce.create(n.range.start,r.range.end))}function kr(e){return"inline-tag"===e.tokens[e.index].type?$r(e,!0):xr(e)}function $r(e,t){const n=e.tokens[e.index++],r=n.content.substring(1),i=e.tokens[e.index];if("text"===(null==i?void 0:i.type)){if(t){const i=xr(e);return new Cr(r,new wr([i],i.range),t,ce.create(n.range.start,i.range.end))}{const i=Er(e);return new Cr(r,i,t,ce.create(n.range.start,i.range.end))}}{const e=n.range;return new Cr(r,new wr([],e),t,e)}}function xr(e){const t=e.tokens[e.index++];return new Lr(t.content,t.range)}function Ir(e){if(!e)return Ir({start:"/**",end:"*/",line:"*"});const{start:t,end:n,line:r}=e;return{start:Sr(t,!0),end:Sr(n,!1),line:Sr(r,!0)}}function Sr(e,t){if("string"==typeof e||"object"==typeof e){const n="string"==typeof e?(0,s.Nt)(e):e.source;return t?new RegExp(`^\\s*${n}`):new RegExp(`\\s*${n}\\s*$`)}return e}class Nr{constructor(e,t){this.elements=e,this.range=t}getTag(e){return this.getAllTags().find((t=>t.name===e))}getTags(e){return this.getAllTags().filter((t=>t.name===e))}getAllTags(){return this.elements.filter((e=>"name"in e))}toString(){let e="";for(const t of this.elements)if(0===e.length)e=t.toString();else{const n=t.toString();e+=br(e)+n}return e.trim()}toMarkdown(e){let t="";for(const n of this.elements)if(0===t.length)t=n.toMarkdown(e);else{const r=n.toMarkdown(e);t+=br(t)+r}return t.trim()}}class Cr{constructor(e,t,n,r){this.name=e,this.content=t,this.inline=n,this.range=r}toString(){let e=`@${this.name}`;const t=this.content.toString();return 1===this.content.inlines.length?e=`${e} ${t}`:this.content.inlines.length>1&&(e=`${e}\n${t}`),this.inline?`{${e}}`:e}toMarkdown(e){var t,n;return null!==(n=null===(t=null==e?void 0:e.renderTag)||void 0===t?void 0:t.call(e,this))&&void 0!==n?n:this.toMarkdownDefault(e)}toMarkdownDefault(e){const t=this.content.toMarkdown(e);if(this.inline){const n=function(e,t,n){var r,i;if("linkplain"===e||"linkcode"===e||"link"===e){const s=t.indexOf(" ");let a=t;if(s>0){const e=Ar(t,s);a=t.substring(e),t=t.substring(0,s)}("linkcode"===e||"link"===e&&"code"===n.link)&&(a=`\`${a}\``);const o=null!==(i=null===(r=n.renderLink)||void 0===r?void 0:r.call(n,t,a))&&void 0!==i?i:function(e,t){try{return kn.r.parse(e,!0),`[${t}](${e})`}catch(r){return e}}(t,a);return o}return}(this.name,t,null!=e?e:{});if("string"==typeof n)return n}let n="";"italic"===(null==e?void 0:e.tag)||void 0===(null==e?void 0:e.tag)?n="*":"bold"===(null==e?void 0:e.tag)?n="**":"bold-italic"===(null==e?void 0:e.tag)&&(n="***");let r=`${n}@${this.name}${n}`;return 1===this.content.inlines.length?r=`${r} \u2014 ${t}`:this.content.inlines.length>1&&(r=`${r}\n${t}`),this.inline?`{${r}}`:r}}class wr{constructor(e,t){this.inlines=e,this.range=t}toString(){let e="";for(let t=0;t<this.inlines.length;t++){const n=this.inlines[t],r=this.inlines[t+1];e+=n.toString(),r&&r.range.start.line>n.range.start.line&&(e+="\n")}return e}toMarkdown(e){let t="";for(let n=0;n<this.inlines.length;n++){const r=this.inlines[n],i=this.inlines[n+1];t+=r.toMarkdown(e),i&&i.range.start.line>r.range.start.line&&(t+="\n")}return t}}class Lr{constructor(e,t){this.text=e,this.range=t}toString(){return this.text}toMarkdown(){return this.text}}function br(e){return e.endsWith("\n")?"\n":"\n\n"}class Or{constructor(e){this.indexManager=e.shared.workspace.IndexManager,this.commentProvider=e.documentation.CommentProvider}getDocumentation(e){const t=this.commentProvider.getComment(e);if(t&&function(e,t){const n=Ir(t),r=fr(e);if(0===r.length)return!1;const i=r[0],s=r[r.length-1],a=n.start,o=n.end;return Boolean(null==a?void 0:a.exec(i))&&Boolean(null==o?void 0:o.exec(s))}(t)){return hr(t).toMarkdown({renderLink:(t,n)=>this.documentationLinkRenderer(e,t,n),renderTag:t=>this.documentationTagRenderer(e,t)})}}documentationLinkRenderer(e,t,n){var r;const i=null!==(r=this.findNameInPrecomputedScopes(e,t))&&void 0!==r?r:this.findNameInGlobalScope(e,t);if(i&&i.nameSegment){const e=i.nameSegment.range.start.line+1,t=i.nameSegment.range.start.character+1;return`[${n}](${i.documentUri.with({fragment:`L${e},${t}`}).toString()})`}}documentationTagRenderer(e,t){}findNameInPrecomputedScopes(e,t){const n=(0,St.YE)(e).precomputedScopes;if(!n)return;let r=e;do{const e=n.get(r).find((e=>e.name===t));if(e)return e;r=r.$container}while(r)}findNameInGlobalScope(e,t){return this.indexManager.allElements().find((e=>e.name===t))}}class _r{constructor(e){this.grammarConfig=()=>e.parser.GrammarConfig}getComment(e){var t;return function(e){return"string"==typeof e.$comment}(e)?e.$comment:null===(t=(0,r.v)(e.$cstNode,this.grammarConfig().multilineCommentRules))||void 0===t?void 0:t.text}}class Pr{constructor(e){this.syncParser=e.parser.LangiumParser}parse(e,t){return Promise.resolve(this.syncParser.parse(e))}}class Mr{constructor(){this.previousTokenSource=new on.Qi,this.writeQueue=[],this.readQueue=[],this.done=!0}write(e){this.cancelWrite();const t=(ln=performance.now(),new on.Qi);return this.previousTokenSource=t,this.enqueue(this.writeQueue,e,t.token)}read(e){return this.enqueue(this.readQueue,e)}enqueue(e,t,n=on.XO.None){const r=new pn,i={action:t,deferred:r,cancellationToken:n};return e.push(i),this.performNextOperation(),r.promise}async performNextOperation(){if(!this.done)return;const e=[];if(this.writeQueue.length>0)e.push(this.writeQueue.shift());else{if(!(this.readQueue.length>0))return;e.push(...this.readQueue.splice(0,this.readQueue.length))}this.done=!1,await Promise.all(e.map((async({action:e,deferred:t,cancellationToken:n})=>{try{const r=await Promise.resolve().then((()=>e(n)));t.resolve(r)}catch(r){hn(r)?t.resolve(void 0):t.reject(r)}}))),this.done=!0,this.performNextOperation()}cancelWrite(){this.previousTokenSource.cancel()}}class Dr{constructor(e){this.grammarElementIdMap=new bn,this.tokenTypeIdMap=new bn,this.grammar=e.Grammar,this.lexer=e.parser.Lexer,this.linker=e.references.Linker}dehydrate(e){return{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport?this.dehydrateLexerReport(e.lexerReport):void 0,parserErrors:e.parserErrors.map((e=>Object.assign(Object.assign({},e),{message:e.message}))),value:this.dehydrateAstNode(e.value,this.createDehyrationContext(e.value))}}dehydrateLexerReport(e){return e}createDehyrationContext(e){const t=new Map,n=new Map;for(const r of(0,St.jm)(e))t.set(r,{});if(e.$cstNode)for(const i of(0,r.NS)(e.$cstNode))n.set(i,{});return{astNodes:t,cstNodes:n}}dehydrateAstNode(e,t){const n=t.astNodes.get(e);n.$type=e.$type,n.$containerIndex=e.$containerIndex,n.$containerProperty=e.$containerProperty,void 0!==e.$cstNode&&(n.$cstNode=this.dehydrateCstNode(e.$cstNode,t));for(const[r,i]of Object.entries(e))if(!r.startsWith("$"))if(Array.isArray(i)){const e=[];n[r]=e;for(const n of i)(0,cn.ng)(n)?e.push(this.dehydrateAstNode(n,t)):(0,cn.A_)(n)?e.push(this.dehydrateReference(n,t)):e.push(n)}else(0,cn.ng)(i)?n[r]=this.dehydrateAstNode(i,t):(0,cn.A_)(i)?n[r]=this.dehydrateReference(i,t):void 0!==i&&(n[r]=i);return n}dehydrateReference(e,t){const n={};return n.$refText=e.$refText,e.$refNode&&(n.$refNode=t.cstNodes.get(e.$refNode)),n}dehydrateCstNode(e,t){const n=t.cstNodes.get(e);return(0,cn.br)(e)?n.fullText=e.fullText:n.grammarSource=this.getGrammarElementId(e.grammarSource),n.hidden=e.hidden,n.astNode=t.astNodes.get(e.astNode),(0,cn.mD)(e)?n.content=e.content.map((e=>this.dehydrateCstNode(e,t))):(0,cn.FC)(e)&&(n.tokenType=e.tokenType.name,n.offset=e.offset,n.length=e.length,n.startLine=e.range.start.line,n.startColumn=e.range.start.character,n.endLine=e.range.end.line,n.endColumn=e.range.end.character),n}hydrate(e){const t=e.value,n=this.createHydrationContext(t);return"$cstNode"in t&&this.hydrateCstNode(t.$cstNode,n),{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport,parserErrors:e.parserErrors,value:this.hydrateAstNode(t,n)}}createHydrationContext(e){const t=new Map,n=new Map;for(const r of(0,St.jm)(e))t.set(r,{});let i;if(e.$cstNode)for(const s of(0,r.NS)(e.$cstNode)){let e;"fullText"in s?(e=new Mt(s.fullText),i=e):"content"in s?e=new _t:"tokenType"in s&&(e=this.hydrateCstLeafNode(s)),e&&(n.set(s,e),e.root=i)}return{astNodes:t,cstNodes:n}}hydrateAstNode(e,t){const n=t.astNodes.get(e);n.$type=e.$type,n.$containerIndex=e.$containerIndex,n.$containerProperty=e.$containerProperty,e.$cstNode&&(n.$cstNode=t.cstNodes.get(e.$cstNode));for(const[r,i]of Object.entries(e))if(!r.startsWith("$"))if(Array.isArray(i)){const e=[];n[r]=e;for(const s of i)(0,cn.ng)(s)?e.push(this.setParent(this.hydrateAstNode(s,t),n)):(0,cn.A_)(s)?e.push(this.hydrateReference(s,n,r,t)):e.push(s)}else(0,cn.ng)(i)?n[r]=this.setParent(this.hydrateAstNode(i,t),n):(0,cn.A_)(i)?n[r]=this.hydrateReference(i,n,r,t):void 0!==i&&(n[r]=i);return n}setParent(e,t){return e.$container=t,e}hydrateReference(e,t,n,r){return this.linker.buildReference(t,n,r.cstNodes.get(e.$refNode),e.$refText)}hydrateCstNode(e,t,n=0){const r=t.cstNodes.get(e);if("number"==typeof e.grammarSource&&(r.grammarSource=this.getGrammarElement(e.grammarSource)),r.astNode=t.astNodes.get(e.astNode),(0,cn.mD)(r))for(const i of e.content){const e=this.hydrateCstNode(i,t,n++);r.content.push(e)}return r}hydrateCstLeafNode(e){const t=this.getTokenType(e.tokenType),n=e.offset,r=e.length,i=e.startLine,s=e.startColumn,a=e.endLine,o=e.endColumn,c=e.hidden;return new Ot(n,r,{start:{line:i,character:s},end:{line:a,character:o}},t,c)}getTokenType(e){return this.lexer.definition[e]}getGrammarElementId(e){if(e)return 0===this.grammarElementIdMap.size&&this.createGrammarElementIdMap(),this.grammarElementIdMap.get(e)}getGrammarElement(e){0===this.grammarElementIdMap.size&&this.createGrammarElementIdMap();return this.grammarElementIdMap.getKey(e)}createGrammarElementIdMap(){let e=0;for(const t of(0,St.jm)(this.grammar))(0,a.r1)(t)&&this.grammarElementIdMap.set(t,e++)}}function Ur(e){return{documentation:{CommentProvider:e=>new _r(e),DocumentationProvider:e=>new Or(e)},parser:{AsyncParser:e=>new Pr(e),GrammarConfig:e=>function(e){const t=[],n=e.Grammar;for(const r of n.rules)(0,a.rE)(r)&&(0,i.eb)(r)&&(0,s.lU)((0,i.S)(r))&&t.push(r.name);return{multilineCommentRules:t,nameRegexp:r.El}}(e),LangiumParser:e=>rn(e),CompletionParser:e=>function(e){const t=e.Grammar,n=e.parser.Lexer,r=new Vt(e);return Xt(t,r,n.definition),r.finalize(),r}(e),ValueConverter:()=>new an.d,TokenBuilder:()=>new sn.Q,Lexer:e=>new lr(e),ParserErrorMessageProvider:()=>new jt,LexerErrorMessageProvider:()=>new or},workspace:{AstNodeLocator:()=>new er,AstNodeDescriptionProvider:e=>new Zn(e),ReferenceDescriptionProvider:e=>new Jn(e)},references:{Linker:e=>new Sn(e),NameProvider:()=>new Nn,ScopeProvider:e=>new Gn(e),ScopeComputation:e=>new On(e),References:e=>new wn(e)},serializer:{Hydrator:e=>new Dr(e),JsonSerializer:e=>new Bn(e)},validation:{DocumentValidator:e=>new Yn(e),ValidationRegistry:e=>new zn(e)},shared:()=>e.shared}}function Fr(e){return{ServiceRegistry:e=>new jn(e),workspace:{LangiumDocuments:e=>new xn(e),LangiumDocumentFactory:e=>new $n(e),DocumentBuilder:e=>new ir(e),IndexManager:e=>new sr(e),WorkspaceManager:e=>new ar(e),FileSystemProvider:t=>e.fileSystemProvider(t),WorkspaceLock:()=>new Mr,ConfigurationProvider:e=>new rr(e)}}}},1564:(e,t,n)=>{n.d(t,{W:()=>r,d:()=>i});class r extends Error{constructor(e,t){super(e?`${t} at ${e.range.start.line}:${e.range.start.character}`:t)}}function i(e){throw new Error("Error! The input value was not handled.")}},1719:(e,t,n)=>{n.d(t,{B5:()=>a,Rf:()=>o,Td:()=>c,Vj:()=>l,fq:()=>r,iD:()=>u});class r{constructor(e,t){this.startFn=e,this.nextFn=t}iterator(){const e={state:this.startFn(),next:()=>this.nextFn(e.state),[Symbol.iterator]:()=>e};return e}[Symbol.iterator](){return this.iterator()}isEmpty(){const e=this.iterator();return Boolean(e.next().done)}count(){const e=this.iterator();let t=0,n=e.next();for(;!n.done;)t++,n=e.next();return t}toArray(){const e=[],t=this.iterator();let n;do{n=t.next(),void 0!==n.value&&e.push(n.value)}while(!n.done);return e}toSet(){return new Set(this)}toMap(e,t){const n=this.map((n=>[e?e(n):n,t?t(n):n]));return new Map(n)}toString(){return this.join()}concat(e){return new r((()=>({first:this.startFn(),firstDone:!1,iterator:e[Symbol.iterator]()})),(e=>{let t;if(!e.firstDone){do{if(t=this.nextFn(e.first),!t.done)return t}while(!t.done);e.firstDone=!0}do{if(t=e.iterator.next(),!t.done)return t}while(!t.done);return o}))}join(e=","){const t=this.iterator();let n,r="",s=!1;do{n=t.next(),n.done||(s&&(r+=e),r+=i(n.value)),s=!0}while(!n.done);return r}indexOf(e,t=0){const n=this.iterator();let r=0,i=n.next();for(;!i.done;){if(r>=t&&i.value===e)return r;i=n.next(),r++}return-1}every(e){const t=this.iterator();let n=t.next();for(;!n.done;){if(!e(n.value))return!1;n=t.next()}return!0}some(e){const t=this.iterator();let n=t.next();for(;!n.done;){if(e(n.value))return!0;n=t.next()}return!1}forEach(e){const t=this.iterator();let n=0,r=t.next();for(;!r.done;)e(r.value,n),r=t.next(),n++}map(e){return new r(this.startFn,(t=>{const{done:n,value:r}=this.nextFn(t);return n?o:{done:!1,value:e(r)}}))}filter(e){return new r(this.startFn,(t=>{let n;do{if(n=this.nextFn(t),!n.done&&e(n.value))return n}while(!n.done);return o}))}nonNullable(){return this.filter((e=>null!=e))}reduce(e,t){const n=this.iterator();let r=t,i=n.next();for(;!i.done;)r=void 0===r?i.value:e(r,i.value),i=n.next();return r}reduceRight(e,t){return this.recursiveReduce(this.iterator(),e,t)}recursiveReduce(e,t,n){const r=e.next();if(r.done)return n;const i=this.recursiveReduce(e,t,n);return void 0===i?r.value:t(i,r.value)}find(e){const t=this.iterator();let n=t.next();for(;!n.done;){if(e(n.value))return n.value;n=t.next()}}findIndex(e){const t=this.iterator();let n=0,r=t.next();for(;!r.done;){if(e(r.value))return n;r=t.next(),n++}return-1}includes(e){const t=this.iterator();let n=t.next();for(;!n.done;){if(n.value===e)return!0;n=t.next()}return!1}flatMap(e){return new r((()=>({this:this.startFn()})),(t=>{do{if(t.iterator){const e=t.iterator.next();if(!e.done)return e;t.iterator=void 0}const{done:n,value:r}=this.nextFn(t.this);if(!n){const n=e(r);if(!s(n))return{done:!1,value:n};t.iterator=n[Symbol.iterator]()}}while(t.iterator);return o}))}flat(e){if(void 0===e&&(e=1),e<=0)return this;const t=e>1?this.flat(e-1):this;return new r((()=>({this:t.startFn()})),(e=>{do{if(e.iterator){const t=e.iterator.next();if(!t.done)return t;e.iterator=void 0}const{done:n,value:r}=t.nextFn(e.this);if(!n){if(!s(r))return{done:!1,value:r};e.iterator=r[Symbol.iterator]()}}while(e.iterator);return o}))}head(){const e=this.iterator().next();if(!e.done)return e.value}tail(e=1){return new r((()=>{const t=this.startFn();for(let n=0;n<e;n++){if(this.nextFn(t).done)return t}return t}),this.nextFn)}limit(e){return new r((()=>({size:0,state:this.startFn()})),(t=>(t.size++,t.size>e?o:this.nextFn(t.state))))}distinct(e){return new r((()=>({set:new Set,internalState:this.startFn()})),(t=>{let n;do{if(n=this.nextFn(t.internalState),!n.done){const r=e?e(n.value):n.value;if(!t.set.has(r))return t.set.add(r),n}}while(!n.done);return o}))}exclude(e,t){const n=new Set;for(const r of e){const e=t?t(r):r;n.add(e)}return this.filter((e=>{const r=t?t(e):e;return!n.has(r)}))}}function i(e){return"string"==typeof e?e:void 0===e?"undefined":"function"==typeof e.toString?e.toString():Object.prototype.toString.call(e)}function s(e){return!!e&&"function"==typeof e[Symbol.iterator]}const a=new r((()=>{}),(()=>o)),o=Object.freeze({done:!0,value:void 0});function c(...e){if(1===e.length){const t=e[0];if(t instanceof r)return t;if(s(t))return new r((()=>t[Symbol.iterator]()),(e=>e.next()));if("number"==typeof t.length)return new r((()=>({index:0})),(e=>e.index<t.length?{done:!1,value:t[e.index++]}:o))}return e.length>1?new r((()=>({collIndex:0,arrIndex:0})),(t=>{do{if(t.iterator){const e=t.iterator.next();if(!e.done)return e;t.iterator=void 0}if(t.array){if(t.arrIndex<t.array.length)return{done:!1,value:t.array[t.arrIndex++]};t.array=void 0,t.arrIndex=0}if(t.collIndex<e.length){const n=e[t.collIndex++];s(n)?t.iterator=n[Symbol.iterator]():n&&"number"==typeof n.length&&(t.array=n)}}while(t.iterator||t.array||t.collIndex<e.length);return o})):a}class l extends r{constructor(e,t,n){super((()=>({iterators:(null==n?void 0:n.includeRoot)?[[e][Symbol.iterator]()]:[t(e)[Symbol.iterator]()],pruned:!1})),(e=>{for(e.pruned&&(e.iterators.pop(),e.pruned=!1);e.iterators.length>0;){const n=e.iterators[e.iterators.length-1].next();if(!n.done)return e.iterators.push(t(n.value)[Symbol.iterator]()),n;e.iterators.pop()}return o}))}iterator(){const e={state:this.startFn(),next:()=>this.nextFn(e.state),prune:()=>{e.state.pruned=!0},[Symbol.iterator]:()=>e};return e}}var u;!function(e){e.sum=function(e){return e.reduce(((e,t)=>e+t),0)},e.product=function(e){return e.reduce(((e,t)=>e*t),0)},e.min=function(e){return e.reduce(((e,t)=>Math.min(e,t)))},e.max=function(e){return e.reduce(((e,t)=>Math.max(e,t)))}}(u||(u={}))},1945:(e,t,n)=>{n.d(t,{Q:()=>l});var r=n(9637),i=n(2151),s=n(9683),a=n(418),o=n(2806),c=n(1719);class l{constructor(){this.diagnostics=[]}buildTokens(e,t){const n=(0,c.Td)((0,a.YV)(e,!1)),r=this.buildTerminalTokens(n),i=this.buildKeywordTokens(n,r,t);return r.forEach((e=>{const t=e.PATTERN;"object"==typeof t&&t&&"test"in t&&(0,o.Yv)(t)?i.unshift(e):i.push(e)})),i}flushLexingReport(e){return{diagnostics:this.popDiagnostics()}}popDiagnostics(){const e=[...this.diagnostics];return this.diagnostics=[],e}buildTerminalTokens(e){return e.filter(i.rE).filter((e=>!e.fragment)).map((e=>this.buildTerminalToken(e))).toArray()}buildTerminalToken(e){const t=(0,a.S)(e),n=this.requiresCustomPattern(t)?this.regexPatternFunction(t):t,i={name:e.name,PATTERN:n};return"function"==typeof n&&(i.LINE_BREAKS=!0),e.hidden&&(i.GROUP=(0,o.Yv)(t)?r.JG.SKIPPED:"hidden"),i}requiresCustomPattern(e){return!(!e.flags.includes("u")&&!e.flags.includes("s"))||!(!e.source.includes("?<=")&&!e.source.includes("?<!"))}regexPatternFunction(e){const t=new RegExp(e,e.flags+"y");return(e,n)=>{t.lastIndex=n;return t.exec(e)}}buildKeywordTokens(e,t,n){return e.filter(i.s7).flatMap((e=>(0,s.Uo)(e).filter(i.wb))).distinct((e=>e.value)).toArray().sort(((e,t)=>t.value.length-e.value.length)).map((e=>this.buildKeywordToken(e,t,Boolean(null==n?void 0:n.caseInsensitive))))}buildKeywordToken(e,t,n){const r=this.buildKeywordPattern(e,n),i={name:e.value,PATTERN:r,LONGER_ALT:this.findLongerAlt(e,t)};return"function"==typeof r&&(i.LINE_BREAKS=!0),i}buildKeywordPattern(e,t){return t?new RegExp((0,o.Ao)(e.value)):e.value}findLongerAlt(e,t){return t.reduce(((t,n)=>{const r=null==n?void 0:n.PATTERN;return(null==r?void 0:r.source)&&(0,o.PC)("^"+r.source+"$",e.value)&&t.push(n),t}),[])}}},2151:(e,t,n)=>{n.d(t,{$g:()=>fe,Bg:()=>J,Ct:()=>S,Cz:()=>p,D8:()=>U,FO:()=>re,Fy:()=>me,GL:()=>le,IZ:()=>se,Mz:()=>ke,O4:()=>ye,QX:()=>Ie,RP:()=>T,S2:()=>$,SP:()=>E,TF:()=>L,Tu:()=>g,Xj:()=>j,_c:()=>te,cY:()=>Re,fG:()=>M,jp:()=>X,lF:()=>Ae,r1:()=>u,rE:()=>K,s7:()=>O,vd:()=>de,ve:()=>z,wb:()=>oe,wh:()=>Q,z2:()=>xe});var r=n(2479);const i="AbstractRule";const s="AbstractType";const a="Condition";const o="TypeDefinition";const c="ValueLiteral";const l="AbstractElement";function u(e){return Se.isInstance(e,l)}const d="ArrayLiteral";const h="ArrayType";const f="BooleanLiteral";function p(e){return Se.isInstance(e,f)}const m="Conjunction";function g(e){return Se.isInstance(e,m)}const y="Disjunction";function T(e){return Se.isInstance(e,y)}const A="Grammar";const v="GrammarImport";const R="InferredType";function E(e){return Se.isInstance(e,R)}const k="Interface";function $(e){return Se.isInstance(e,k)}const x="NamedArgument";const I="Negation";function S(e){return Se.isInstance(e,I)}const N="NumberLiteral";const C="Parameter";const w="ParameterReference";function L(e){return Se.isInstance(e,w)}const b="ParserRule";function O(e){return Se.isInstance(e,b)}const _="ReferenceType";const P="ReturnType";function M(e){return Se.isInstance(e,P)}const D="SimpleType";function U(e){return Se.isInstance(e,D)}const F="StringLiteral";const G="TerminalRule";function K(e){return Se.isInstance(e,G)}const B="Type";function j(e){return Se.isInstance(e,B)}const V="TypeAttribute";const H="UnionType";const W="Action";function z(e){return Se.isInstance(e,W)}const Y="Alternatives";function X(e){return Se.isInstance(e,Y)}const q="Assignment";function Q(e){return Se.isInstance(e,q)}const Z="CharacterRange";function J(e){return Se.isInstance(e,Z)}const ee="CrossReference";function te(e){return Se.isInstance(e,ee)}const ne="EndOfFile";function re(e){return Se.isInstance(e,ne)}const ie="Group";function se(e){return Se.isInstance(e,ie)}const ae="Keyword";function oe(e){return Se.isInstance(e,ae)}const ce="NegatedToken";function le(e){return Se.isInstance(e,ce)}const ue="RegexToken";function de(e){return Se.isInstance(e,ue)}const he="RuleCall";function fe(e){return Se.isInstance(e,he)}const pe="TerminalAlternatives";function me(e){return Se.isInstance(e,pe)}const ge="TerminalGroup";function ye(e){return Se.isInstance(e,ge)}const Te="TerminalRuleCall";function Ae(e){return Se.isInstance(e,Te)}const ve="UnorderedGroup";function Re(e){return Se.isInstance(e,ve)}const Ee="UntilToken";function ke(e){return Se.isInstance(e,Ee)}const $e="Wildcard";function xe(e){return Se.isInstance(e,$e)}class Ie extends r.kD{getAllTypes(){return[l,i,s,W,Y,d,h,q,f,Z,a,m,ee,y,ne,A,v,ie,R,k,ae,x,ce,I,N,C,w,b,_,ue,P,he,D,F,pe,ge,G,Te,B,V,o,H,ve,Ee,c,$e]}computeIsSubtype(e,t){switch(e){case W:case Y:case q:case Z:case ee:case ne:case ie:case ae:case ce:case ue:case he:case pe:case ge:case Te:case ve:case Ee:case $e:return this.isSubtype(l,t);case d:case N:case F:return this.isSubtype(c,t);case h:case _:case D:case H:return this.isSubtype(o,t);case f:return this.isSubtype(a,t)||this.isSubtype(c,t);case m:case y:case I:case w:return this.isSubtype(a,t);case R:case k:case B:return this.isSubtype(s,t);case b:return this.isSubtype(i,t)||this.isSubtype(s,t);case G:return this.isSubtype(i,t);default:return!1}}getReferenceType(e){const t=`${e.container.$type}:${e.property}`;switch(t){case"Action:type":case"CrossReference:type":case"Interface:superTypes":case"ParserRule:returnType":case"SimpleType:typeRef":return s;case"Grammar:hiddenTokens":case"ParserRule:hiddenTokens":case"RuleCall:rule":return i;case"Grammar:usedGrammars":return A;case"NamedArgument:parameter":case"ParameterReference:parameter":return C;case"TerminalRuleCall:rule":return G;default:throw new Error(`${t} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case l:return{name:l,properties:[{name:"cardinality"},{name:"lookahead"}]};case d:return{name:d,properties:[{name:"elements",defaultValue:[]}]};case h:return{name:h,properties:[{name:"elementType"}]};case f:return{name:f,properties:[{name:"true",defaultValue:!1}]};case m:return{name:m,properties:[{name:"left"},{name:"right"}]};case y:return{name:y,properties:[{name:"left"},{name:"right"}]};case A:return{name:A,properties:[{name:"definesHiddenTokens",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"imports",defaultValue:[]},{name:"interfaces",defaultValue:[]},{name:"isDeclared",defaultValue:!1},{name:"name"},{name:"rules",defaultValue:[]},{name:"types",defaultValue:[]},{name:"usedGrammars",defaultValue:[]}]};case v:return{name:v,properties:[{name:"path"}]};case R:return{name:R,properties:[{name:"name"}]};case k:return{name:k,properties:[{name:"attributes",defaultValue:[]},{name:"name"},{name:"superTypes",defaultValue:[]}]};case x:return{name:x,properties:[{name:"calledByName",defaultValue:!1},{name:"parameter"},{name:"value"}]};case I:return{name:I,properties:[{name:"value"}]};case N:return{name:N,properties:[{name:"value"}]};case C:return{name:C,properties:[{name:"name"}]};case w:return{name:w,properties:[{name:"parameter"}]};case b:return{name:b,properties:[{name:"dataType"},{name:"definesHiddenTokens",defaultValue:!1},{name:"definition"},{name:"entry",defaultValue:!1},{name:"fragment",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"inferredType"},{name:"name"},{name:"parameters",defaultValue:[]},{name:"returnType"},{name:"wildcard",defaultValue:!1}]};case _:return{name:_,properties:[{name:"referenceType"}]};case P:return{name:P,properties:[{name:"name"}]};case D:return{name:D,properties:[{name:"primitiveType"},{name:"stringType"},{name:"typeRef"}]};case F:return{name:F,properties:[{name:"value"}]};case G:return{name:G,properties:[{name:"definition"},{name:"fragment",defaultValue:!1},{name:"hidden",defaultValue:!1},{name:"name"},{name:"type"}]};case B:return{name:B,properties:[{name:"name"},{name:"type"}]};case V:return{name:V,properties:[{name:"defaultValue"},{name:"isOptional",defaultValue:!1},{name:"name"},{name:"type"}]};case H:return{name:H,properties:[{name:"types",defaultValue:[]}]};case W:return{name:W,properties:[{name:"cardinality"},{name:"feature"},{name:"inferredType"},{name:"lookahead"},{name:"operator"},{name:"type"}]};case Y:return{name:Y,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case q:return{name:q,properties:[{name:"cardinality"},{name:"feature"},{name:"lookahead"},{name:"operator"},{name:"terminal"}]};case Z:return{name:Z,properties:[{name:"cardinality"},{name:"left"},{name:"lookahead"},{name:"right"}]};case ee:return{name:ee,properties:[{name:"cardinality"},{name:"deprecatedSyntax",defaultValue:!1},{name:"lookahead"},{name:"terminal"},{name:"type"}]};case ne:return{name:ne,properties:[{name:"cardinality"},{name:"lookahead"}]};case ie:return{name:ie,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"guardCondition"},{name:"lookahead"}]};case ae:return{name:ae,properties:[{name:"cardinality"},{name:"lookahead"},{name:"value"}]};case ce:return{name:ce,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case ue:return{name:ue,properties:[{name:"cardinality"},{name:"lookahead"},{name:"regex"}]};case he:return{name:he,properties:[{name:"arguments",defaultValue:[]},{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case pe:return{name:pe,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case ge:return{name:ge,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Te:return{name:Te,properties:[{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case ve:return{name:ve,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Ee:return{name:Ee,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case $e:return{name:$e,properties:[{name:"cardinality"},{name:"lookahead"}]};default:return{name:e,properties:[]}}}}const Se=new Ie},2479:(e,t,n)=>{function r(e){return"object"==typeof e&&null!==e&&"string"==typeof e.$type}function i(e){return"object"==typeof e&&null!==e&&"string"==typeof e.$refText}function s(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name&&"string"==typeof e.type&&"string"==typeof e.path}function a(e){return"object"==typeof e&&null!==e&&r(e.container)&&i(e.reference)&&"string"==typeof e.message}n.d(t,{A_:()=>i,FC:()=>l,Nr:()=>s,Zl:()=>a,br:()=>u,kD:()=>o,mD:()=>c,ng:()=>r});class o{constructor(){this.subtypes={},this.allSubtypes={}}isInstance(e,t){return r(e)&&this.isSubtype(e.$type,t)}isSubtype(e,t){if(e===t)return!0;let n=this.subtypes[e];n||(n=this.subtypes[e]={});const r=n[t];if(void 0!==r)return r;{const r=this.computeIsSubtype(e,t);return n[t]=r,r}}getAllSubTypes(e){const t=this.allSubtypes[e];if(t)return t;{const t=this.getAllTypes(),n=[];for(const r of t)this.isSubtype(r,e)&&n.push(r);return this.allSubtypes[e]=n,n}}}function c(e){return"object"==typeof e&&null!==e&&Array.isArray(e.content)}function l(e){return"object"==typeof e&&null!==e&&"object"==typeof e.tokenType}function u(e){return c(e)&&"string"==typeof e.fullText}},2676:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Emitter=t.Event=void 0;const r=n(9590);var i;!function(e){const t={dispose(){}};e.None=function(){return t}}(i||(t.Event=i={}));class s{add(e,t=null,n){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(e),this._contexts.push(t),Array.isArray(n)&&n.push({dispose:()=>this.remove(e,t)})}remove(e,t=null){if(!this._callbacks)return;let n=!1;for(let r=0,i=this._callbacks.length;r<i;r++)if(this._callbacks[r]===e){if(this._contexts[r]===t)return this._callbacks.splice(r,1),void this._contexts.splice(r,1);n=!0}if(n)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...e){if(!this._callbacks)return[];const t=[],n=this._callbacks.slice(0),i=this._contexts.slice(0);for(let a=0,o=n.length;a<o;a++)try{t.push(n[a].apply(i[a],e))}catch(s){(0,r.default)().console.error(s)}return t}isEmpty(){return!this._callbacks||0===this._callbacks.length}dispose(){this._callbacks=void 0,this._contexts=void 0}}class a{constructor(e){this._options=e}get event(){return this._event||(this._event=(e,t,n)=>{this._callbacks||(this._callbacks=new s),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(e,t);const r={dispose:()=>{this._callbacks&&(this._callbacks.remove(e,t),r.dispose=a._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))}};return Array.isArray(n)&&n.push(r),r}),this._event}fire(e){this._callbacks&&this._callbacks.invoke.call(this._callbacks,e)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}}t.Emitter=a,a._noop=function(){}},2806:(e,t,n)=>{n.d(t,{Ao:()=>h,Nt:()=>d,PC:()=>f,TH:()=>i,Yv:()=>u,lU:()=>c});var r=n(5186);const i=/\r?\n/gm,s=new r.H;class a extends r.z{constructor(){super(...arguments),this.isStarting=!0,this.endRegexpStack=[],this.multiline=!1}get endRegex(){return this.endRegexpStack.join("")}reset(e){this.multiline=!1,this.regex=e,this.startRegexp="",this.isStarting=!0,this.endRegexpStack=[]}visitGroup(e){e.quantifier&&(this.isStarting=!1,this.endRegexpStack=[])}visitCharacter(e){const t=String.fromCharCode(e.value);if(this.multiline||"\n"!==t||(this.multiline=!0),e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{const e=d(t);this.endRegexpStack.push(e),this.isStarting&&(this.startRegexp+=e)}}visitSet(e){if(!this.multiline){const t=this.regex.substring(e.loc.begin,e.loc.end),n=new RegExp(t);this.multiline=Boolean("\n".match(n))}if(e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{const t=this.regex.substring(e.loc.begin,e.loc.end);this.endRegexpStack.push(t),this.isStarting&&(this.startRegexp+=t)}}visitChildren(e){if("Group"===e.type){if(e.quantifier)return}super.visitChildren(e)}}const o=new a;function c(e){try{return"string"==typeof e&&(e=new RegExp(e)),e=e.toString(),o.reset(e),o.visit(s.pattern(e)),o.multiline}catch(t){return!1}}const l="\f\n\r\t\v \xa0\u1680\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u2028\u2029\u202f\u205f\u3000\ufeff".split("");function u(e){const t="string"==typeof e?new RegExp(e):e;return l.some((e=>t.test(e)))}function d(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function h(e){return Array.prototype.map.call(e,(e=>/\w/.test(e)?`[${e.toLowerCase()}${e.toUpperCase()}]`:d(e))).join("")}function f(e,t){const n=function(e){"string"==typeof e&&(e=new RegExp(e));const t=e,n=e.source;let r=0;function i(){let e,s="";function a(e){s+=n.substr(r,e),r+=e}function o(e){s+="(?:"+n.substr(r,e)+"|$)",r+=e}for(;r<n.length;)switch(n[r]){case"\\":switch(n[r+1]){case"c":o(3);break;case"x":o(4);break;case"u":t.unicode?"{"===n[r+2]?o(n.indexOf("}",r)-r+1):o(6):o(2);break;case"p":case"P":t.unicode?o(n.indexOf("}",r)-r+1):o(2);break;case"k":o(n.indexOf(">",r)-r+1);break;default:o(2)}break;case"[":e=/\[(?:\\.|.)*?\]/g,e.lastIndex=r,e=e.exec(n)||[],o(e[0].length);break;case"|":case"^":case"$":case"*":case"+":case"?":a(1);break;case"{":e=/\{\d+,?\d*\}/g,e.lastIndex=r,e=e.exec(n),e?a(e[0].length):o(1);break;case"(":if("?"===n[r+1])switch(n[r+2]){case":":s+="(?:",r+=3,s+=i()+"|$)";break;case"=":s+="(?=",r+=3,s+=i()+")";break;case"!":e=r,r+=3,i(),s+=n.substr(e,r-e);break;case"<":switch(n[r+3]){case"=":case"!":e=r,r+=4,i(),s+=n.substr(e,r-e);break;default:a(n.indexOf(">",r)-r+1),s+=i()+"|$)"}}else a(1),s+=i()+"|$)";break;case")":return++r,s;default:o(1)}return s}return new RegExp(i(),e.flags)}(e),r=t.match(n);return!!r&&r[0].length>0}},3579:(e,t,n)=>{n.d(t,{mR:()=>ge,dg:()=>pe,jE:()=>ue,Tm:()=>me,eZ:()=>de,e5:()=>oe,sr:()=>ae,AM:()=>ce,KX:()=>le,YP:()=>he,K2:()=>m});var r=n(2479),i=n(1294),s=n(9364),a=n(2151),o=n(4298),c=n(7608);const l={Grammar:()=>{},LanguageMetaData:()=>({caseInsensitive:!1,fileExtensions:[".langium"],languageId:"langium"})},u={AstReflection:()=>new a.QX};function d(e){var t;const n=function(){const e=(0,s.WQ)((0,i.u)(o.D),u),t=(0,s.WQ)((0,i.t)({shared:e}),l);return e.ServiceRegistry.register(t),t}(),r=n.serializer.JsonSerializer.deserialize(e);return n.shared.workspace.LangiumDocumentFactory.fromModel(r,c.r.parse(`memory://${null!==(t=r.name)&&void 0!==t?t:"grammar"}.langium`)),r}var h=n(5033),f=n(1945),p=Object.defineProperty,m=(e,t)=>p(e,"name",{value:t,configurable:!0}),g="Statement",y="Architecture";m((function(e){return z.isInstance(e,y)}),"isArchitecture");var T="Axis",A="Branch";m((function(e){return z.isInstance(e,A)}),"isBranch");var v="Checkout",R="CherryPicking",E="Commit";m((function(e){return z.isInstance(e,E)}),"isCommit");var k="Common";m((function(e){return z.isInstance(e,k)}),"isCommon");var $="Curve",x="Edge",I="Entry",S="GitGraph";m((function(e){return z.isInstance(e,S)}),"isGitGraph");var N="Group",C="Info";m((function(e){return z.isInstance(e,C)}),"isInfo");var w="Junction",L="Merge";m((function(e){return z.isInstance(e,L)}),"isMerge");var b="Option",O="Packet";m((function(e){return z.isInstance(e,O)}),"isPacket");var _="PacketBlock";m((function(e){return z.isInstance(e,_)}),"isPacketBlock");var P="Pie";m((function(e){return z.isInstance(e,P)}),"isPie");var M="PieSection";m((function(e){return z.isInstance(e,M)}),"isPieSection");var D,U,F,G,K,B,j="Radar",V="Service",H="Direction",W=class extends r.kD{static{m(this,"MermaidAstReflection")}getAllTypes(){return[y,T,A,v,R,E,k,$,H,x,I,S,N,C,w,L,b,O,_,P,M,j,V,g]}computeIsSubtype(e,t){switch(e){case A:case v:case R:case E:case L:return this.isSubtype(g,t);case H:return this.isSubtype(S,t);default:return!1}}getReferenceType(e){const t=`${e.container.$type}:${e.property}`;if("Entry:axis"===t)return T;throw new Error(`${t} is not a valid reference id.`)}getTypeMetaData(e){switch(e){case y:return{name:y,properties:[{name:"accDescr"},{name:"accTitle"},{name:"edges",defaultValue:[]},{name:"groups",defaultValue:[]},{name:"junctions",defaultValue:[]},{name:"services",defaultValue:[]},{name:"title"}]};case T:return{name:T,properties:[{name:"label"},{name:"name"}]};case A:return{name:A,properties:[{name:"name"},{name:"order"}]};case v:return{name:v,properties:[{name:"branch"}]};case R:return{name:R,properties:[{name:"id"},{name:"parent"},{name:"tags",defaultValue:[]}]};case E:return{name:E,properties:[{name:"id"},{name:"message"},{name:"tags",defaultValue:[]},{name:"type"}]};case k:return{name:k,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case $:return{name:$,properties:[{name:"entries",defaultValue:[]},{name:"label"},{name:"name"}]};case x:return{name:x,properties:[{name:"lhsDir"},{name:"lhsGroup",defaultValue:!1},{name:"lhsId"},{name:"lhsInto",defaultValue:!1},{name:"rhsDir"},{name:"rhsGroup",defaultValue:!1},{name:"rhsId"},{name:"rhsInto",defaultValue:!1},{name:"title"}]};case I:return{name:I,properties:[{name:"axis"},{name:"value"}]};case S:return{name:S,properties:[{name:"accDescr"},{name:"accTitle"},{name:"statements",defaultValue:[]},{name:"title"}]};case N:return{name:N,properties:[{name:"icon"},{name:"id"},{name:"in"},{name:"title"}]};case C:return{name:C,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case w:return{name:w,properties:[{name:"id"},{name:"in"}]};case L:return{name:L,properties:[{name:"branch"},{name:"id"},{name:"tags",defaultValue:[]},{name:"type"}]};case b:return{name:b,properties:[{name:"name"},{name:"value",defaultValue:!1}]};case O:return{name:O,properties:[{name:"accDescr"},{name:"accTitle"},{name:"blocks",defaultValue:[]},{name:"title"}]};case _:return{name:_,properties:[{name:"end"},{name:"label"},{name:"start"}]};case P:return{name:P,properties:[{name:"accDescr"},{name:"accTitle"},{name:"sections",defaultValue:[]},{name:"showData",defaultValue:!1},{name:"title"}]};case M:return{name:M,properties:[{name:"label"},{name:"value"}]};case j:return{name:j,properties:[{name:"accDescr"},{name:"accTitle"},{name:"axes",defaultValue:[]},{name:"curves",defaultValue:[]},{name:"options",defaultValue:[]},{name:"title"}]};case V:return{name:V,properties:[{name:"icon"},{name:"iconText"},{name:"id"},{name:"in"},{name:"title"}]};case H:return{name:H,properties:[{name:"accDescr"},{name:"accTitle"},{name:"dir"},{name:"statements",defaultValue:[]},{name:"title"}]};default:return{name:e,properties:[]}}}},z=new W,Y=m((()=>D??(D=d('{"$type":"Grammar","isDeclared":true,"name":"Info","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Info","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"info"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"Keyword","value":"showInfo"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}'))),"InfoGrammar"),X=m((()=>U??(U=d('{"$type":"Grammar","isDeclared":true,"name":"Packet","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Packet","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"packet-beta"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"+"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PacketBlock","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"start","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"end","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}}],"cardinality":"?"},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|\'[^\']*\'/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}'))),"PacketGrammar"),q=m((()=>F??(F=d('{"$type":"Grammar","isDeclared":true,"name":"Pie","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Pie","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"pie"},{"$type":"Assignment","feature":"showData","operator":"?=","terminal":{"$type":"Keyword","value":"showData"},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"+"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PieSection","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"PIE_SECTION_LABEL","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]+\\"/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"PIE_SECTION_VALUE","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/(0|[1-9][0-9]*)(\\\\.[0-9]+)?/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}'))),"PieGrammar"),Q=m((()=>G??(G=d('{"$type":"Grammar","isDeclared":true,"name":"Architecture","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Architecture","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"architecture-beta"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"groups","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"services","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"junctions","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"edges","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"LeftPort","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"lhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"RightPort","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"rhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Keyword","value":":"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Arrow","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Assignment","feature":"lhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"--"},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Keyword","value":"-"}]}]},{"$type":"Assignment","feature":"rhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Group","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"group"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]},"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Service","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"service"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"iconText","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}}],"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Junction","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"junction"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Edge","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"lhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"lhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"rhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"rhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"ARROW_DIRECTION","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"L"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"R"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"T"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"B"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_ID","definition":{"$type":"RegexToken","regex":"/[\\\\w]+/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TEXT_ICON","definition":{"$type":"RegexToken","regex":"/\\\\(\\"[^\\"]+\\"\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_ICON","definition":{"$type":"RegexToken","regex":"/\\\\([\\\\w-:]+\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TITLE","definition":{"$type":"RegexToken","regex":"/\\\\[[\\\\w ]+\\\\]/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_GROUP","definition":{"$type":"RegexToken","regex":"/\\\\{group\\\\}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_INTO","definition":{"$type":"RegexToken","regex":"/<|>/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}'))),"ArchitectureGrammar"),Z=m((()=>K??(K=d('{"$type":"Grammar","isDeclared":true,"name":"GitGraph","interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"rules":[{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"ParserRule","entry":true,"name":"GitGraph","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Keyword","value":":"}]},{"$type":"Keyword","value":"gitGraph:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Assignment","feature":"statements","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Direction","definition":{"$type":"Assignment","feature":"dir","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"LR"},{"$type":"Keyword","value":"TB"},{"$type":"Keyword","value":"BT"}]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Commit","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"commit"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"msg:","cardinality":"?"},{"$type":"Assignment","feature":"message","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Branch","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"branch"},{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"order:"},{"$type":"Assignment","feature":"order","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Merge","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"merge"},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Checkout","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"checkout"},{"$type":"Keyword","value":"switch"}]},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"CherryPicking","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"cherry-pick"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"parent:"},{"$type":"Assignment","feature":"parent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+(?=\\\\s)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\\\w([-\\\\./\\\\w]*[-\\\\w])?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|\'[^\']*\'/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[]}'))),"GitGraphGrammar"),J=m((()=>B??(B=d('{"$type":"Grammar","isDeclared":true,"name":"Radar","interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]},{"$type":"Interface","name":"Entry","attributes":[{"$type":"TypeAttribute","name":"axis","isOptional":true,"type":{"$type":"ReferenceType","referenceType":{"$type":"SimpleType","typeRef":{"$ref":"#/rules@12"}}}},{"$type":"TypeAttribute","name":"value","type":{"$type":"SimpleType","primitiveType":"number"},"isOptional":false}],"superTypes":[]}],"rules":[{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"ParserRule","entry":true,"name":"Radar","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":"radar-beta:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Group","elements":[{"$type":"Keyword","value":"axis"},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"curve"},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Label","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"["},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Keyword","value":"]"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Axis","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Curve","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[],"cardinality":"?"},{"$type":"Keyword","value":"{"},{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},{"$type":"Keyword","value":"}"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Entries","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"DetailedEntry","returnType":{"$ref":"#/interfaces@1"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"axis","operator":"=","terminal":{"$type":"CrossReference","type":{"$ref":"#/rules@12"},"terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]},"deprecatedSyntax":false}},{"$type":"Keyword","value":":","cardinality":"?"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"NumberEntry","returnType":{"$ref":"#/interfaces@1"},"definition":{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Option","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"showLegend"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"ticks"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"max"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"min"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"graticule"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/(0|[1-9][0-9]*)(\\\\.[0-9]+)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"GRATICULE","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"circle"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"polygon"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[a-zA-Z_][a-zA-Z0-9\\\\-_]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|\'[^\']*\'/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[]}'))),"RadarGrammar"),ee={languageId:"info",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},te={languageId:"packet",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},ne={languageId:"pie",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},re={languageId:"architecture",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},ie={languageId:"gitGraph",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},se={languageId:"radar",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},ae={AstReflection:m((()=>new W),"AstReflection")},oe={Grammar:m((()=>Y()),"Grammar"),LanguageMetaData:m((()=>ee),"LanguageMetaData"),parser:{}},ce={Grammar:m((()=>X()),"Grammar"),LanguageMetaData:m((()=>te),"LanguageMetaData"),parser:{}},le={Grammar:m((()=>q()),"Grammar"),LanguageMetaData:m((()=>ne),"LanguageMetaData"),parser:{}},ue={Grammar:m((()=>Q()),"Grammar"),LanguageMetaData:m((()=>re),"LanguageMetaData"),parser:{}},de={Grammar:m((()=>Z()),"Grammar"),LanguageMetaData:m((()=>ie),"LanguageMetaData"),parser:{}},he={Grammar:m((()=>J()),"Grammar"),LanguageMetaData:m((()=>se),"LanguageMetaData"),parser:{}},fe={ACC_DESCR:/accDescr(?:[\t ]*:([^\n\r]*)|\s*{([^}]*)})/,ACC_TITLE:/accTitle[\t ]*:([^\n\r]*)/,TITLE:/title([\t ][^\n\r]*|)/},pe=class extends h.d{static{m(this,"AbstractMermaidValueConverter")}runConverter(e,t,n){let r=this.runCommonConverter(e,t,n);return void 0===r&&(r=this.runCustomConverter(e,t,n)),void 0===r?super.runConverter(e,t,n):r}runCommonConverter(e,t,n){const r=fe[e.name];if(void 0===r)return;const i=r.exec(t);return null!==i?void 0!==i[1]?i[1].trim().replace(/[\t ]{2,}/gm," "):void 0!==i[2]?i[2].replace(/^\s*/gm,"").replace(/\s+$/gm,"").replace(/[\t ]{2,}/gm," ").replace(/[\n\r]{2,}/gm,"\n"):void 0:void 0}},me=class extends pe{static{m(this,"CommonValueConverter")}runCustomConverter(e,t,n){}},ge=class extends f.Q{static{m(this,"AbstractMermaidTokenBuilder")}constructor(e){super(),this.keywords=new Set(e)}buildKeywordTokens(e,t,n){const r=super.buildKeywordTokens(e,t,n);return r.forEach((e=>{this.keywords.has(e.name)&&void 0!==e.PATTERN&&(e.PATTERN=new RegExp(e.PATTERN.toString()+"(?:(?=%%)|(?!\\S))"))})),r}};(class extends ge{static{m(this,"CommonTokenBuilder")}})},4298:(e,t,n)=>{n.d(t,{D:()=>i});class r{readFile(){throw new Error("No file system is available.")}async readDirectory(){return[]}}const i={fileSystemProvider:()=>new r}},5033:(e,t,n)=>{n.d(t,{d:()=>a});var r,i=n(2151),s=n(418);class a{convert(e,t){let n=t.grammarSource;if((0,i._c)(n)&&(n=(0,s.g4)(n)),(0,i.$g)(n)){const r=n.rule.ref;if(!r)throw new Error("This cst node was not parsed by a rule.");return this.runConverter(r,e,t)}return e}runConverter(e,t,n){var i;switch(e.name.toUpperCase()){case"INT":return r.convertInt(t);case"STRING":return r.convertString(t);case"ID":return r.convertID(t)}switch(null===(i=(0,s.P3)(e))||void 0===i?void 0:i.toLowerCase()){case"number":return r.convertNumber(t);case"boolean":return r.convertBoolean(t);case"bigint":return r.convertBigint(t);case"date":return r.convertDate(t);default:return t}}}!function(e){function t(e){switch(e){case"b":return"\b";case"f":return"\f";case"n":return"\n";case"r":return"\r";case"t":return"\t";case"v":return"\v";case"0":return"\0";default:return e}}e.convertString=function(e){let n="";for(let r=1;r<e.length-1;r++){const i=e.charAt(r);if("\\"===i){n+=t(e.charAt(++r))}else n+=i}return n},e.convertID=function(e){return"^"===e.charAt(0)?e.substring(1):e},e.convertInt=function(e){return parseInt(e)},e.convertBigint=function(e){return BigInt(e)},e.convertDate=function(e){return new Date(e)},e.convertNumber=function(e){return Number(e)},e.convertBoolean=function(e){return"true"===e.toLowerCase()}}(r||(r={}))},5186:(e,t,n)=>{function r(e){return e.charCodeAt(0)}function i(e,t){Array.isArray(e)?e.forEach((function(e){t.push(e)})):t.push(e)}function s(e,t){if(!0===e[t])throw"duplicate flag "+t;e[t];e[t]=!0}function a(e){if(void 0===e)throw Error("Internal Error - Should never get here!");return!0}function o(){throw Error("Internal Error - Should never get here!")}function c(e){return"Character"===e.type}n.d(t,{z:()=>g,H:()=>m});const l=[];for(let y=r("0");y<=r("9");y++)l.push(y);const u=[r("_")].concat(l);for(let y=r("a");y<=r("z");y++)u.push(y);for(let y=r("A");y<=r("Z");y++)u.push(y);const d=[r(" "),r("\f"),r("\n"),r("\r"),r("\t"),r("\v"),r("\t"),r("\xa0"),r("\u1680"),r("\u2000"),r("\u2001"),r("\u2002"),r("\u2003"),r("\u2004"),r("\u2005"),r("\u2006"),r("\u2007"),r("\u2008"),r("\u2009"),r("\u200a"),r("\u2028"),r("\u2029"),r("\u202f"),r("\u205f"),r("\u3000"),r("\ufeff")],h=/[0-9a-fA-F]/,f=/[0-9]/,p=/[1-9]/;class m{constructor(){this.idx=0,this.input="",this.groupIdx=0}saveState(){return{idx:this.idx,input:this.input,groupIdx:this.groupIdx}}restoreState(e){this.idx=e.idx,this.input=e.input,this.groupIdx=e.groupIdx}pattern(e){this.idx=0,this.input=e,this.groupIdx=0,this.consumeChar("/");const t=this.disjunction();this.consumeChar("/");const n={type:"Flags",loc:{begin:this.idx,end:e.length},global:!1,ignoreCase:!1,multiLine:!1,unicode:!1,sticky:!1};for(;this.isRegExpFlag();)switch(this.popChar()){case"g":s(n,"global");break;case"i":s(n,"ignoreCase");break;case"m":s(n,"multiLine");break;case"u":s(n,"unicode");break;case"y":s(n,"sticky")}if(this.idx!==this.input.length)throw Error("Redundant input: "+this.input.substring(this.idx));return{type:"Pattern",flags:n,value:t,loc:this.loc(0)}}disjunction(){const e=[],t=this.idx;for(e.push(this.alternative());"|"===this.peekChar();)this.consumeChar("|"),e.push(this.alternative());return{type:"Disjunction",value:e,loc:this.loc(t)}}alternative(){const e=[],t=this.idx;for(;this.isTerm();)e.push(this.term());return{type:"Alternative",value:e,loc:this.loc(t)}}term(){return this.isAssertion()?this.assertion():this.atom()}assertion(){const e=this.idx;switch(this.popChar()){case"^":return{type:"StartAnchor",loc:this.loc(e)};case"$":return{type:"EndAnchor",loc:this.loc(e)};case"\\":switch(this.popChar()){case"b":return{type:"WordBoundary",loc:this.loc(e)};case"B":return{type:"NonWordBoundary",loc:this.loc(e)}}throw Error("Invalid Assertion Escape");case"(":let t;switch(this.consumeChar("?"),this.popChar()){case"=":t="Lookahead";break;case"!":t="NegativeLookahead"}a(t);const n=this.disjunction();return this.consumeChar(")"),{type:t,value:n,loc:this.loc(e)}}return o()}quantifier(e=!1){let t;const n=this.idx;switch(this.popChar()){case"*":t={atLeast:0,atMost:1/0};break;case"+":t={atLeast:1,atMost:1/0};break;case"?":t={atLeast:0,atMost:1};break;case"{":const n=this.integerIncludingZero();switch(this.popChar()){case"}":t={atLeast:n,atMost:n};break;case",":let e;this.isDigit()?(e=this.integerIncludingZero(),t={atLeast:n,atMost:e}):t={atLeast:n,atMost:1/0},this.consumeChar("}")}if(!0===e&&void 0===t)return;a(t)}if(!0!==e||void 0!==t)return a(t)?("?"===this.peekChar(0)?(this.consumeChar("?"),t.greedy=!1):t.greedy=!0,t.type="Quantifier",t.loc=this.loc(n),t):void 0}atom(){let e;const t=this.idx;switch(this.peekChar()){case".":e=this.dotAll();break;case"\\":e=this.atomEscape();break;case"[":e=this.characterClass();break;case"(":e=this.group()}return void 0===e&&this.isPatternCharacter()&&(e=this.patternCharacter()),a(e)?(e.loc=this.loc(t),this.isQuantifier()&&(e.quantifier=this.quantifier()),e):o()}dotAll(){return this.consumeChar("."),{type:"Set",complement:!0,value:[r("\n"),r("\r"),r("\u2028"),r("\u2029")]}}atomEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return this.decimalEscapeAtom();case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}decimalEscapeAtom(){return{type:"GroupBackReference",value:this.positiveInteger()}}characterClassEscape(){let e,t=!1;switch(this.popChar()){case"d":e=l;break;case"D":e=l,t=!0;break;case"s":e=d;break;case"S":e=d,t=!0;break;case"w":e=u;break;case"W":e=u,t=!0}return a(e)?{type:"Set",value:e,complement:t}:o()}controlEscapeAtom(){let e;switch(this.popChar()){case"f":e=r("\f");break;case"n":e=r("\n");break;case"r":e=r("\r");break;case"t":e=r("\t");break;case"v":e=r("\v")}return a(e)?{type:"Character",value:e}:o()}controlLetterEscapeAtom(){this.consumeChar("c");const e=this.popChar();if(!1===/[a-zA-Z]/.test(e))throw Error("Invalid ");return{type:"Character",value:e.toUpperCase().charCodeAt(0)-64}}nulCharacterAtom(){return this.consumeChar("0"),{type:"Character",value:r("\0")}}hexEscapeSequenceAtom(){return this.consumeChar("x"),this.parseHexDigits(2)}regExpUnicodeEscapeSequenceAtom(){return this.consumeChar("u"),this.parseHexDigits(4)}identityEscapeAtom(){return{type:"Character",value:r(this.popChar())}}classPatternCharacterAtom(){switch(this.peekChar()){case"\n":case"\r":case"\u2028":case"\u2029":case"\\":case"]":throw Error("TBD");default:return{type:"Character",value:r(this.popChar())}}}characterClass(){const e=[];let t=!1;for(this.consumeChar("["),"^"===this.peekChar(0)&&(this.consumeChar("^"),t=!0);this.isClassAtom();){const t=this.classAtom();t.type;if(c(t)&&this.isRangeDash()){this.consumeChar("-");const n=this.classAtom();n.type;if(c(n)){if(n.value<t.value)throw Error("Range out of order in character class");e.push({from:t.value,to:n.value})}else i(t.value,e),e.push(r("-")),i(n.value,e)}else i(t.value,e)}return this.consumeChar("]"),{type:"Set",complement:t,value:e}}classAtom(){switch(this.peekChar()){case"]":case"\n":case"\r":case"\u2028":case"\u2029":throw Error("TBD");case"\\":return this.classEscape();default:return this.classPatternCharacterAtom()}}classEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"b":return this.consumeChar("b"),{type:"Character",value:r("\b")};case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}group(){let e=!0;if(this.consumeChar("("),"?"===this.peekChar(0))this.consumeChar("?"),this.consumeChar(":"),e=!1;else this.groupIdx++;const t=this.disjunction();this.consumeChar(")");const n={type:"Group",capturing:e,value:t};return e&&(n.idx=this.groupIdx),n}positiveInteger(){let e=this.popChar();if(!1===p.test(e))throw Error("Expecting a positive integer");for(;f.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}integerIncludingZero(){let e=this.popChar();if(!1===f.test(e))throw Error("Expecting an integer");for(;f.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}patternCharacter(){const e=this.popChar();switch(e){case"\n":case"\r":case"\u2028":case"\u2029":case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":throw Error("TBD");default:return{type:"Character",value:r(e)}}}isRegExpFlag(){switch(this.peekChar(0)){case"g":case"i":case"m":case"u":case"y":return!0;default:return!1}}isRangeDash(){return"-"===this.peekChar()&&this.isClassAtom(1)}isDigit(){return f.test(this.peekChar(0))}isClassAtom(e=0){switch(this.peekChar(e)){case"]":case"\n":case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}isTerm(){return this.isAtom()||this.isAssertion()}isAtom(){if(this.isPatternCharacter())return!0;switch(this.peekChar(0)){case".":case"\\":case"[":case"(":return!0;default:return!1}}isAssertion(){switch(this.peekChar(0)){case"^":case"$":return!0;case"\\":switch(this.peekChar(1)){case"b":case"B":return!0;default:return!1}case"(":return"?"===this.peekChar(1)&&("="===this.peekChar(2)||"!"===this.peekChar(2));default:return!1}}isQuantifier(){const e=this.saveState();try{return void 0!==this.quantifier(!0)}catch(t){return!1}finally{this.restoreState(e)}}isPatternCharacter(){switch(this.peekChar()){case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":case"/":case"\n":case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}parseHexDigits(e){let t="";for(let n=0;n<e;n++){const e=this.popChar();if(!1===h.test(e))throw Error("Expecting a HexDecimal digits");t+=e}return{type:"Character",value:parseInt(t,16)}}peekChar(e=0){return this.input[this.idx+e]}popChar(){const e=this.peekChar(0);return this.consumeChar(void 0),e}consumeChar(e){if(void 0!==e&&this.input[this.idx]!==e)throw Error("Expected: '"+e+"' but found: '"+this.input[this.idx]+"' at offset: "+this.idx);if(this.idx>=this.input.length)throw Error("Unexpected end of input");this.idx++}loc(e){return{begin:e,end:this.idx}}}class g{visitChildren(e){for(const t in e){const n=e[t];e.hasOwnProperty(t)&&(void 0!==n.type?this.visit(n):Array.isArray(n)&&n.forEach((e=>{this.visit(e)}),this))}}visit(e){switch(e.type){case"Pattern":this.visitPattern(e);break;case"Flags":this.visitFlags(e);break;case"Disjunction":this.visitDisjunction(e);break;case"Alternative":this.visitAlternative(e);break;case"StartAnchor":this.visitStartAnchor(e);break;case"EndAnchor":this.visitEndAnchor(e);break;case"WordBoundary":this.visitWordBoundary(e);break;case"NonWordBoundary":this.visitNonWordBoundary(e);break;case"Lookahead":this.visitLookahead(e);break;case"NegativeLookahead":this.visitNegativeLookahead(e);break;case"Character":this.visitCharacter(e);break;case"Set":this.visitSet(e);break;case"Group":this.visitGroup(e);break;case"GroupBackReference":this.visitGroupBackReference(e);break;case"Quantifier":this.visitQuantifier(e)}this.visitChildren(e)}visitPattern(e){}visitFlags(e){}visitDisjunction(e){}visitAlternative(e){}visitStartAnchor(e){}visitEndAnchor(e){}visitWordBoundary(e){}visitNonWordBoundary(e){}visitLookahead(e){}visitNegativeLookahead(e){}visitCharacter(e){}visitSet(e){}visitGroup(e){}visitGroupBackReference(e){}visitQuantifier(e){}}},6373:(e,t,n)=>{n.d(t,{El:()=>d,NS:()=>a,SX:()=>l,pO:()=>o,r4:()=>u,v:()=>h,wf:()=>c});var r,i=n(2479),s=n(1719);function a(e){return new s.Vj(e,(e=>(0,i.mD)(e)?e.content:[]),{includeRoot:!0})}function o(e,t){for(;e.container;)if((e=e.container)===t)return!0;return!1}function c(e){return{start:{character:e.startColumn-1,line:e.startLine-1},end:{character:e.endColumn,line:e.endLine-1}}}function l(e){if(!e)return;const{offset:t,end:n,range:r}=e;return{range:r,offset:t,end:n,length:n-t}}function u(e,t){const n=function(e,t){if(e.end.line<t.start.line||e.end.line===t.start.line&&e.end.character<=t.start.character)return r.Before;if(e.start.line>t.end.line||e.start.line===t.end.line&&e.start.character>=t.end.character)return r.After;const n=e.start.line>t.start.line||e.start.line===t.start.line&&e.start.character>=t.start.character,i=e.end.line<t.end.line||e.end.line===t.end.line&&e.end.character<=t.end.character;return n&&i?r.Inside:n?r.OverlapBack:i?r.OverlapFront:r.Outside}(e,t);return n>r.After}!function(e){e[e.Before=0]="Before",e[e.After=1]="After",e[e.OverlapFront=2]="OverlapFront",e[e.OverlapBack=3]="OverlapBack",e[e.Inside=4]="Inside",e[e.Outside=5]="Outside"}(r||(r={}));const d=/^[\w\p{L}]$/u;function h(e,t){if(e){const n=function(e,t=!0){for(;e.container;){const n=e.container;let r=n.content.indexOf(e);for(;r>0;){r--;const e=n.content[r];if(t||!e.hidden)return e}e=n}return}(e,!0);if(n&&f(n,t))return n;if((0,i.br)(e)){for(let n=e.content.findIndex((e=>!e.hidden))-1;n>=0;n--){const r=e.content[n];if(f(r,t))return r}}}}function f(e,t){return(0,i.FC)(e)&&t.includes(e.tokenType.name)}},7123:(e,t,n)=>{n.d(t,{b:()=>l});var r=n(3579),i=n(4298),s=n(9364),a=n(1294),o=class extends r.mR{static{(0,r.K2)(this,"GitGraphTokenBuilder")}constructor(){super(["gitGraph"])}},c={parser:{TokenBuilder:(0,r.K2)((()=>new o),"TokenBuilder"),ValueConverter:(0,r.K2)((()=>new r.Tm),"ValueConverter")}};function l(e=i.D){const t=(0,s.WQ)((0,a.u)(e),r.sr),n=(0,s.WQ)((0,a.t)({shared:t}),r.eZ,c);return t.ServiceRegistry.register(n),{shared:t,GitGraph:n}}(0,r.K2)(l,"createGitGraphServices")},7608:(e,t,n)=>{var r;n.d(t,{A:()=>s,r:()=>i}),(()=>{var e={470:e=>{function t(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function n(e,t){for(var n,r="",i=0,s=-1,a=0,o=0;o<=e.length;++o){if(o<e.length)n=e.charCodeAt(o);else{if(47===n)break;n=47}if(47===n){if(s===o-1||1===a);else if(s!==o-1&&2===a){if(r.length<2||2!==i||46!==r.charCodeAt(r.length-1)||46!==r.charCodeAt(r.length-2))if(r.length>2){var c=r.lastIndexOf("/");if(c!==r.length-1){-1===c?(r="",i=0):i=(r=r.slice(0,c)).length-1-r.lastIndexOf("/"),s=o,a=0;continue}}else if(2===r.length||1===r.length){r="",i=0,s=o,a=0;continue}t&&(r.length>0?r+="/..":r="..",i=2)}else r.length>0?r+="/"+e.slice(s+1,o):r=e.slice(s+1,o),i=o-s-1;s=o,a=0}else 46===n&&-1!==a?++a:a=-1}return r}var r={resolve:function(){for(var e,r="",i=!1,s=arguments.length-1;s>=-1&&!i;s--){var a;s>=0?a=arguments[s]:(void 0===e&&(e=process.cwd()),a=e),t(a),0!==a.length&&(r=a+"/"+r,i=47===a.charCodeAt(0))}return r=n(r,!i),i?r.length>0?"/"+r:"/":r.length>0?r:"."},normalize:function(e){if(t(e),0===e.length)return".";var r=47===e.charCodeAt(0),i=47===e.charCodeAt(e.length-1);return 0!==(e=n(e,!r)).length||r||(e="."),e.length>0&&i&&(e+="/"),r?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,n=0;n<arguments.length;++n){var i=arguments[n];t(i),i.length>0&&(void 0===e?e=i:e+="/"+i)}return void 0===e?".":r.normalize(e)},relative:function(e,n){if(t(e),t(n),e===n)return"";if((e=r.resolve(e))===(n=r.resolve(n)))return"";for(var i=1;i<e.length&&47===e.charCodeAt(i);++i);for(var s=e.length,a=s-i,o=1;o<n.length&&47===n.charCodeAt(o);++o);for(var c=n.length-o,l=a<c?a:c,u=-1,d=0;d<=l;++d){if(d===l){if(c>l){if(47===n.charCodeAt(o+d))return n.slice(o+d+1);if(0===d)return n.slice(o+d)}else a>l&&(47===e.charCodeAt(i+d)?u=d:0===d&&(u=0));break}var h=e.charCodeAt(i+d);if(h!==n.charCodeAt(o+d))break;47===h&&(u=d)}var f="";for(d=i+u+1;d<=s;++d)d!==s&&47!==e.charCodeAt(d)||(0===f.length?f+="..":f+="/..");return f.length>0?f+n.slice(o+u):(o+=u,47===n.charCodeAt(o)&&++o,n.slice(o))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var n=e.charCodeAt(0),r=47===n,i=-1,s=!0,a=e.length-1;a>=1;--a)if(47===(n=e.charCodeAt(a))){if(!s){i=a;break}}else s=!1;return-1===i?r?"/":".":r&&1===i?"//":e.slice(0,i)},basename:function(e,n){if(void 0!==n&&"string"!=typeof n)throw new TypeError('"ext" argument must be a string');t(e);var r,i=0,s=-1,a=!0;if(void 0!==n&&n.length>0&&n.length<=e.length){if(n.length===e.length&&n===e)return"";var o=n.length-1,c=-1;for(r=e.length-1;r>=0;--r){var l=e.charCodeAt(r);if(47===l){if(!a){i=r+1;break}}else-1===c&&(a=!1,c=r+1),o>=0&&(l===n.charCodeAt(o)?-1==--o&&(s=r):(o=-1,s=c))}return i===s?s=c:-1===s&&(s=e.length),e.slice(i,s)}for(r=e.length-1;r>=0;--r)if(47===e.charCodeAt(r)){if(!a){i=r+1;break}}else-1===s&&(a=!1,s=r+1);return-1===s?"":e.slice(i,s)},extname:function(e){t(e);for(var n=-1,r=0,i=-1,s=!0,a=0,o=e.length-1;o>=0;--o){var c=e.charCodeAt(o);if(47!==c)-1===i&&(s=!1,i=o+1),46===c?-1===n?n=o:1!==a&&(a=1):-1!==n&&(a=-1);else if(!s){r=o+1;break}}return-1===n||-1===i||0===a||1===a&&n===i-1&&n===r+1?"":e.slice(n,i)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var n=t.dir||t.root,r=t.base||(t.name||"")+(t.ext||"");return n?n===t.root?n+r:n+"/"+r:r}(0,e)},parse:function(e){t(e);var n={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return n;var r,i=e.charCodeAt(0),s=47===i;s?(n.root="/",r=1):r=0;for(var a=-1,o=0,c=-1,l=!0,u=e.length-1,d=0;u>=r;--u)if(47!==(i=e.charCodeAt(u)))-1===c&&(l=!1,c=u+1),46===i?-1===a?a=u:1!==d&&(d=1):-1!==a&&(d=-1);else if(!l){o=u+1;break}return-1===a||-1===c||0===d||1===d&&a===c-1&&a===o+1?-1!==c&&(n.base=n.name=0===o&&s?e.slice(1,c):e.slice(o,c)):(0===o&&s?(n.name=e.slice(1,a),n.base=e.slice(1,c)):(n.name=e.slice(o,a),n.base=e.slice(o,c)),n.ext=e.slice(a,c)),o>0?n.dir=e.slice(0,o-1):s&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};r.posix=r,e.exports=r}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var s=t[r]={exports:{}};return e[r](s,s.exports,n),s.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};(()=>{let e;if(n.r(i),n.d(i,{URI:()=>u,Utils:()=>$}),"object"==typeof process)e="win32"===process.platform;else if("object"==typeof navigator){let t=navigator.userAgent;e=t.indexOf("Windows")>=0}const t=/^\w[\w\d+.-]*$/,r=/^\//,s=/^\/\//;function a(e,n){if(!e.scheme&&n)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!t.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!r.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(s.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}const o="",c="/",l=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class u{static isUri(e){return e instanceof u||!!e&&"string"==typeof e.authority&&"string"==typeof e.fragment&&"string"==typeof e.path&&"string"==typeof e.query&&"string"==typeof e.scheme&&"string"==typeof e.fsPath&&"function"==typeof e.with&&"function"==typeof e.toString}scheme;authority;path;query;fragment;constructor(e,t,n,r,i,s=!1){"object"==typeof e?(this.scheme=e.scheme||o,this.authority=e.authority||o,this.path=e.path||o,this.query=e.query||o,this.fragment=e.fragment||o):(this.scheme=function(e,t){return e||t?e:"file"}(e,s),this.authority=t||o,this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==c&&(t=c+t):t=c}return t}(this.scheme,n||o),this.query=r||o,this.fragment=i||o,a(this,s))}get fsPath(){return g(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:n,path:r,query:i,fragment:s}=e;return void 0===t?t=this.scheme:null===t&&(t=o),void 0===n?n=this.authority:null===n&&(n=o),void 0===r?r=this.path:null===r&&(r=o),void 0===i?i=this.query:null===i&&(i=o),void 0===s?s=this.fragment:null===s&&(s=o),t===this.scheme&&n===this.authority&&r===this.path&&i===this.query&&s===this.fragment?this:new h(t,n,r,i,s)}static parse(e,t=!1){const n=l.exec(e);return n?new h(n[2]||o,v(n[4]||o),v(n[5]||o),v(n[7]||o),v(n[9]||o),t):new h(o,o,o,o,o)}static file(t){let n=o;if(e&&(t=t.replace(/\\/g,c)),t[0]===c&&t[1]===c){const e=t.indexOf(c,2);-1===e?(n=t.substring(2),t=c):(n=t.substring(2,e),t=t.substring(e)||c)}return new h("file",n,t,o,o)}static from(e){const t=new h(e.scheme,e.authority,e.path,e.query,e.fragment);return a(t,!0),t}toString(e=!1){return y(this,e)}toJSON(){return this}static revive(e){if(e){if(e instanceof u)return e;{const t=new h(e);return t._formatted=e.external,t._fsPath=e._sep===d?e.fsPath:null,t}}return e}}const d=e?1:void 0;class h extends u{_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=g(this,!1)),this._fsPath}toString(e=!1){return e?y(this,!0):(this._formatted||(this._formatted=y(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=d),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}const f={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function p(e,t,n){let r,i=-1;for(let s=0;s<e.length;s++){const a=e.charCodeAt(s);if(a>=97&&a<=122||a>=65&&a<=90||a>=48&&a<=57||45===a||46===a||95===a||126===a||t&&47===a||n&&91===a||n&&93===a||n&&58===a)-1!==i&&(r+=encodeURIComponent(e.substring(i,s)),i=-1),void 0!==r&&(r+=e.charAt(s));else{void 0===r&&(r=e.substr(0,s));const t=f[a];void 0!==t?(-1!==i&&(r+=encodeURIComponent(e.substring(i,s)),i=-1),r+=t):-1===i&&(i=s)}}return-1!==i&&(r+=encodeURIComponent(e.substring(i))),void 0!==r?r:e}function m(e){let t;for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);35===r||63===r?(void 0===t&&(t=e.substr(0,n)),t+=f[r]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function g(t,n){let r;return r=t.authority&&t.path.length>1&&"file"===t.scheme?`//${t.authority}${t.path}`:47===t.path.charCodeAt(0)&&(t.path.charCodeAt(1)>=65&&t.path.charCodeAt(1)<=90||t.path.charCodeAt(1)>=97&&t.path.charCodeAt(1)<=122)&&58===t.path.charCodeAt(2)?n?t.path.substr(1):t.path[1].toLowerCase()+t.path.substr(2):t.path,e&&(r=r.replace(/\//g,"\\")),r}function y(e,t){const n=t?m:p;let r="",{scheme:i,authority:s,path:a,query:o,fragment:l}=e;if(i&&(r+=i,r+=":"),(s||"file"===i)&&(r+=c,r+=c),s){let e=s.indexOf("@");if(-1!==e){const t=s.substr(0,e);s=s.substr(e+1),e=t.lastIndexOf(":"),-1===e?r+=n(t,!1,!1):(r+=n(t.substr(0,e),!1,!1),r+=":",r+=n(t.substr(e+1),!1,!0)),r+="@"}s=s.toLowerCase(),e=s.lastIndexOf(":"),-1===e?r+=n(s,!1,!0):(r+=n(s.substr(0,e),!1,!0),r+=s.substr(e))}if(a){if(a.length>=3&&47===a.charCodeAt(0)&&58===a.charCodeAt(2)){const e=a.charCodeAt(1);e>=65&&e<=90&&(a=`/${String.fromCharCode(e+32)}:${a.substr(3)}`)}else if(a.length>=2&&58===a.charCodeAt(1)){const e=a.charCodeAt(0);e>=65&&e<=90&&(a=`${String.fromCharCode(e+32)}:${a.substr(2)}`)}r+=n(a,!0,!1)}return o&&(r+="?",r+=n(o,!1,!1)),l&&(r+="#",r+=t?l:p(l,!1,!1)),r}function T(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+T(e.substr(3)):e}}const A=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function v(e){return e.match(A)?e.replace(A,(e=>T(e))):e}var R=n(470);const E=R.posix||R,k="/";var $;!function(e){e.joinPath=function(e,...t){return e.with({path:E.join(e.path,...t)})},e.resolvePath=function(e,...t){let n=e.path,r=!1;n[0]!==k&&(n=k+n,r=!0);let i=E.resolve(n,...t);return r&&i[0]===k&&!e.authority&&(i=i.substring(1)),e.with({path:i})},e.dirname=function(e){if(0===e.path.length||e.path===k)return e;let t=E.dirname(e.path);return 1===t.length&&46===t.charCodeAt(0)&&(t=""),e.with({path:t})},e.basename=function(e){return E.basename(e.path)},e.extname=function(e){return E.extname(e.path)}}($||($={}))})(),r=i})();const{URI:i,Utils:s}=r},8139:(e,t,n)=>{n.d(t,{A:()=>s});var r=n(3588),i=n(4722);const s=function(e,t){return(0,r.A)((0,i.A)(e,t),1)}},8525:(e,t,n)=>{n.d(t,{v:()=>l});var r=n(3579),i=n(4298),s=n(9364),a=n(1294),o=class extends r.mR{static{(0,r.K2)(this,"InfoTokenBuilder")}constructor(){super(["info","showInfo"])}},c={parser:{TokenBuilder:(0,r.K2)((()=>new o),"TokenBuilder"),ValueConverter:(0,r.K2)((()=>new r.Tm),"ValueConverter")}};function l(e=i.D){const t=(0,s.WQ)((0,a.u)(e),r.sr),n=(0,s.WQ)((0,a.t)({shared:t}),r.e5,c);return t.ServiceRegistry.register(n),{shared:t,Info:n}}(0,r.K2)(l,"createInfoServices")},8731:(e,t,n)=>{n.d(t,{qg:()=>a});n(7123),n(8525),n(9923),n(9732),n(8815),n(655);var r=n(3579),i={},s={info:(0,r.K2)((async()=>{const{createInfoServices:e}=await n.e(7303).then(n.bind(n,7303)),t=e().Info.parser.LangiumParser;i.info=t}),"info"),packet:(0,r.K2)((async()=>{const{createPacketServices:e}=await n.e(8313).then(n.bind(n,694)),t=e().Packet.parser.LangiumParser;i.packet=t}),"packet"),pie:(0,r.K2)((async()=>{const{createPieServices:e}=await n.e(9495).then(n.bind(n,9495)),t=e().Pie.parser.LangiumParser;i.pie=t}),"pie"),architecture:(0,r.K2)((async()=>{const{createArchitectureServices:e}=await n.e(2024).then(n.bind(n,2024)),t=e().Architecture.parser.LangiumParser;i.architecture=t}),"architecture"),gitGraph:(0,r.K2)((async()=>{const{createGitGraphServices:e}=await n.e(9669).then(n.bind(n,9669)),t=e().GitGraph.parser.LangiumParser;i.gitGraph=t}),"gitGraph"),radar:(0,r.K2)((async()=>{const{createRadarServices:e}=await n.e(4564).then(n.bind(n,4564)),t=e().Radar.parser.LangiumParser;i.radar=t}),"radar")};async function a(e,t){const n=s[e];if(!n)throw new Error(`Unknown diagram type: ${e}`);i[e]||await n();const r=i[e].parse(t);if(r.lexerErrors.length>0||r.parserErrors.length>0)throw new o(r);return r.value}(0,r.K2)(a,"parse");var o=class extends Error{constructor(e){super(`Parsing failed: ${e.lexerErrors.map((e=>e.message)).join("\n")} ${e.parserErrors.map((e=>e.message)).join("\n")}`),this.result=e}static{(0,r.K2)(this,"MermaidParseError")}}},8815:(e,t,n)=>{n.d(t,{S:()=>u});var r=n(3579),i=n(4298),s=n(9364),a=n(1294),o=class extends r.mR{static{(0,r.K2)(this,"ArchitectureTokenBuilder")}constructor(){super(["architecture"])}},c=class extends r.dg{static{(0,r.K2)(this,"ArchitectureValueConverter")}runCustomConverter(e,t,n){return"ARCH_ICON"===e.name?t.replace(/[()]/g,"").trim():"ARCH_TEXT_ICON"===e.name?t.replace(/["()]/g,""):"ARCH_TITLE"===e.name?t.replace(/[[\]]/g,"").trim():void 0}},l={parser:{TokenBuilder:(0,r.K2)((()=>new o),"TokenBuilder"),ValueConverter:(0,r.K2)((()=>new c),"ValueConverter")}};function u(e=i.D){const t=(0,s.WQ)((0,a.u)(e),r.sr),n=(0,s.WQ)((0,a.t)({shared:t}),r.jE,l);return t.ServiceRegistry.register(n),{shared:t,Architecture:n}}(0,r.K2)(u,"createArchitectureServices")},9364:(e,t,n)=>{var r;function i(e,t,n,r,i,s,o,c,u){return a([e,t,n,r,i,s,o,c,u].reduce(l,{}))}n.d(t,{WQ:()=>i}),function(e){e.merge=(e,t)=>l(l({},e),t)}(r||(r={}));const s=Symbol("isProxy");function a(e,t){const n=new Proxy({},{deleteProperty:()=>!1,set:()=>{throw new Error("Cannot set property on injected service container")},get:(r,i)=>i===s||c(r,i,e,t||n),getOwnPropertyDescriptor:(r,i)=>(c(r,i,e,t||n),Object.getOwnPropertyDescriptor(r,i)),has:(t,n)=>n in e,ownKeys:()=>[...Object.getOwnPropertyNames(e)]});return n}const o=Symbol();function c(e,t,n,r){if(t in e){if(e[t]instanceof Error)throw new Error("Construction failure. Please make sure that your dependencies are constructable.",{cause:e[t]});if(e[t]===o)throw new Error('Cycle detected. Please make "'+String(t)+'" lazy. Visit https://langium.org/docs/reference/configuration-services/#resolving-cyclic-dependencies');return e[t]}if(t in n){const s=n[t];e[t]=o;try{e[t]="function"==typeof s?s(r):a(s,r)}catch(i){throw e[t]=i instanceof Error?i:void 0,i}return e[t]}}function l(e,t){if(t)for(const[n,r]of Object.entries(t))if(void 0!==r){const t=e[n];e[n]=null!==t&&null!==r&&"object"==typeof t&&"object"==typeof r?l(t,r):r}return e}},9590:(e,t)=>{let n;function r(){if(void 0===n)throw new Error("No runtime abstraction layer installed");return n}Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.install=function(e){if(void 0===e)throw new Error("No runtime abstraction layer provided");n=e}}(r||(r={})),t.default=r},9637:(e,t,n)=>{n.d(t,{ak:()=>V,mT:()=>Pr,LT:()=>Xt,jr:()=>Dr,T6:()=>dr,JG:()=>Mt,wL:()=>M,c$:()=>F,Y2:()=>B,$P:()=>G,Cy:()=>K,Pp:()=>j,BK:()=>H,PW:()=>Ot,my:()=>Zt,jk:()=>kn,Sk:()=>Dt,G:()=>Qt});var r=n(8058),i=n(8207),s=n(6401),a=n(4722),o=n(8585),c=n(53);function l(e){function t(){}t.prototype=e;const n=new t;function r(){return typeof n.bar}return r(),r(),e}const u=function(e,t,n){var r=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(n=n>i?i:n)<0&&(n+=i),i=t>n?0:n-t>>>0,t>>>=0;for(var s=Array(i);++r<i;)s[r]=e[r+t];return s};var d=n(8593);const h=function(e,t,n){var r=null==e?0:e.length;return r?(t=n||void 0===t?1:(0,d.A)(t),u(e,t<0?0:t,r)):[]};var f=n(9703),p=n(2851),m=n(2031),g=n(3767),y=n(8446),T=n(7271),A=n(7422),v=Object.prototype.hasOwnProperty;const R=(0,g.A)((function(e,t){if((0,T.A)(t)||(0,y.A)(t))(0,m.A)(t,(0,A.A)(t),e);else for(var n in t)v.call(t,n)&&(0,p.A)(e,n,t[n])}));var E=n(5572),k=n(3958),$=n(9354),x=n(3973);const I=function(e,t){if(null==e)return{};var n=(0,E.A)((0,x.A)(e),(function(e){return[e]}));return t=(0,k.A)(t),(0,$.A)(e,n,(function(e,n){return t(e,n[0])}))};var S=n(8496),N=n(3098);const C=function(e){return(0,N.A)(e)&&"[object RegExp]"==(0,S.A)(e)};var w=n(2789),L=n(4841),b=L.A&&L.A.isRegExp;const O=b?(0,w.A)(b):C;function _(e){return t=e,(0,f.A)(t.LABEL)&&""!==t.LABEL?e.LABEL:e.name;var t}class P{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){this._definition=e}accept(e){e.visit(this),(0,r.A)(this.definition,(t=>{t.accept(e)}))}}class M extends P{constructor(e){super([]),this.idx=1,R(this,I(e,(e=>void 0!==e)))}set definition(e){}get definition(){return void 0!==this.referencedRule?this.referencedRule.definition:[]}accept(e){e.visit(this)}}class D extends P{constructor(e){super(e.definition),this.orgText="",R(this,I(e,(e=>void 0!==e)))}}class U extends P{constructor(e){super(e.definition),this.ignoreAmbiguities=!1,R(this,I(e,(e=>void 0!==e)))}}class F extends P{constructor(e){super(e.definition),this.idx=1,R(this,I(e,(e=>void 0!==e)))}}class G extends P{constructor(e){super(e.definition),this.idx=1,R(this,I(e,(e=>void 0!==e)))}}class K extends P{constructor(e){super(e.definition),this.idx=1,R(this,I(e,(e=>void 0!==e)))}}class B extends P{constructor(e){super(e.definition),this.idx=1,R(this,I(e,(e=>void 0!==e)))}}class j extends P{constructor(e){super(e.definition),this.idx=1,R(this,I(e,(e=>void 0!==e)))}}class V extends P{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){super(e.definition),this.idx=1,this.ignoreAmbiguities=!1,this.hasPredicates=!1,R(this,I(e,(e=>void 0!==e)))}}class H{constructor(e){this.idx=1,R(this,I(e,(e=>void 0!==e)))}accept(e){e.visit(this)}}function W(e){function t(e){return(0,a.A)(e,W)}if(e instanceof M){const t={type:"NonTerminal",name:e.nonTerminalName,idx:e.idx};return(0,f.A)(e.label)&&(t.label=e.label),t}if(e instanceof U)return{type:"Alternative",definition:t(e.definition)};if(e instanceof F)return{type:"Option",idx:e.idx,definition:t(e.definition)};if(e instanceof G)return{type:"RepetitionMandatory",idx:e.idx,definition:t(e.definition)};if(e instanceof K)return{type:"RepetitionMandatoryWithSeparator",idx:e.idx,separator:W(new H({terminalType:e.separator})),definition:t(e.definition)};if(e instanceof j)return{type:"RepetitionWithSeparator",idx:e.idx,separator:W(new H({terminalType:e.separator})),definition:t(e.definition)};if(e instanceof B)return{type:"Repetition",idx:e.idx,definition:t(e.definition)};if(e instanceof V)return{type:"Alternation",idx:e.idx,definition:t(e.definition)};if(e instanceof H){const t={type:"Terminal",name:e.terminalType.name,label:_(e.terminalType),idx:e.idx};(0,f.A)(e.label)&&(t.terminalLabel=e.label);const n=e.terminalType.PATTERN;return e.terminalType.PATTERN&&(t.pattern=O(n)?n.source:n),t}if(e instanceof D)return{type:"Rule",name:e.name,orgText:e.orgText,definition:t(e.definition)};throw Error("non exhaustive match")}class z{visit(e){const t=e;switch(t.constructor){case M:return this.visitNonTerminal(t);case U:return this.visitAlternative(t);case F:return this.visitOption(t);case G:return this.visitRepetitionMandatory(t);case K:return this.visitRepetitionMandatoryWithSeparator(t);case j:return this.visitRepetitionWithSeparator(t);case B:return this.visitRepetition(t);case V:return this.visitAlternation(t);case H:return this.visitTerminal(t);case D:return this.visitRule(t);default:throw Error("non exhaustive match")}}visitNonTerminal(e){}visitAlternative(e){}visitOption(e){}visitRepetition(e){}visitRepetitionMandatory(e){}visitRepetitionMandatoryWithSeparator(e){}visitRepetitionWithSeparator(e){}visitAlternation(e){}visitTerminal(e){}visitRule(e){}}var Y=n(3736),X=n(6240);const q=function(e,t){var n;return(0,X.A)(e,(function(e,r,i){return!(n=t(e,r,i))})),!!n};var Q=n(2049),Z=n(6832);const J=function(e,t,n){var r=(0,Q.A)(e)?Y.A:q;return n&&(0,Z.A)(e,t,n)&&(t=void 0),r(e,(0,k.A)(t,3))};var ee=n(818),te=Math.max;const ne=function(e,t,n,r){e=(0,y.A)(e)?e:(0,i.A)(e),n=n&&!r?(0,d.A)(n):0;var s=e.length;return n<0&&(n=te(s+n,0)),(0,f.A)(e)?n<=s&&e.indexOf(t,n)>-1:!!s&&(0,ee.A)(e,t,n)>-1};const re=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(!t(e[n],n,e))return!1;return!0};const ie=function(e,t){var n=!0;return(0,X.A)(e,(function(e,r,i){return n=!!t(e,r,i)})),n};const se=function(e,t,n){var r=(0,Q.A)(e)?re:ie;return n&&(0,Z.A)(e,t,n)&&(t=void 0),r(e,(0,k.A)(t,3))};function ae(e,t=[]){return!!(e instanceof F||e instanceof B||e instanceof j)||(e instanceof V?J(e.definition,(e=>ae(e,t))):!(e instanceof M&&ne(t,e))&&(e instanceof P&&(e instanceof M&&t.push(e),se(e.definition,(e=>ae(e,t))))))}function oe(e){if(e instanceof M)return"SUBRULE";if(e instanceof F)return"OPTION";if(e instanceof V)return"OR";if(e instanceof G)return"AT_LEAST_ONE";if(e instanceof K)return"AT_LEAST_ONE_SEP";if(e instanceof j)return"MANY_SEP";if(e instanceof B)return"MANY";if(e instanceof H)return"CONSUME";throw Error("non exhaustive match")}class ce{walk(e,t=[]){(0,r.A)(e.definition,((n,r)=>{const i=h(e.definition,r+1);if(n instanceof M)this.walkProdRef(n,i,t);else if(n instanceof H)this.walkTerminal(n,i,t);else if(n instanceof U)this.walkFlat(n,i,t);else if(n instanceof F)this.walkOption(n,i,t);else if(n instanceof G)this.walkAtLeastOne(n,i,t);else if(n instanceof K)this.walkAtLeastOneSep(n,i,t);else if(n instanceof j)this.walkManySep(n,i,t);else if(n instanceof B)this.walkMany(n,i,t);else{if(!(n instanceof V))throw Error("non exhaustive match");this.walkOr(n,i,t)}}))}walkTerminal(e,t,n){}walkProdRef(e,t,n){}walkFlat(e,t,n){const r=t.concat(n);this.walk(e,r)}walkOption(e,t,n){const r=t.concat(n);this.walk(e,r)}walkAtLeastOne(e,t,n){const r=[new F({definition:e.definition})].concat(t,n);this.walk(e,r)}walkAtLeastOneSep(e,t,n){const r=le(e,t,n);this.walk(e,r)}walkMany(e,t,n){const r=[new F({definition:e.definition})].concat(t,n);this.walk(e,r)}walkManySep(e,t,n){const r=le(e,t,n);this.walk(e,r)}walkOr(e,t,n){const i=t.concat(n);(0,r.A)(e.definition,(e=>{const t=new U({definition:[e]});this.walk(t,i)}))}}function le(e,t,n){return[new F({definition:[new H({terminalType:e.separator})].concat(e.definition)})].concat(t,n)}var ue=n(9902);const de=function(e){return e&&e.length?(0,ue.A)(e):[]};var he=n(4098);function fe(e){if(e instanceof M)return fe(e.referencedRule);if(e instanceof H)return[e.terminalType];if(function(e){return e instanceof U||e instanceof F||e instanceof B||e instanceof G||e instanceof K||e instanceof j||e instanceof H||e instanceof D}(e))return function(e){let t=[];const n=e.definition;let r,i=0,s=n.length>i,a=!0;for(;s&&a;)r=n[i],a=ae(r),t=t.concat(fe(r)),i+=1,s=n.length>i;return de(t)}(e);if(function(e){return e instanceof V}(e))return function(e){const t=(0,a.A)(e.definition,(e=>fe(e)));return de((0,he.A)(t))}(e);throw Error("non exhaustive match")}const pe="_~IN~_";class me extends ce{constructor(e){super(),this.topProd=e,this.follows={}}startWalking(){return this.walk(this.topProd),this.follows}walkTerminal(e,t,n){}walkProdRef(e,t,n){const r=(i=e.referencedRule,s=e.idx,i.name+s+pe+this.topProd.name);var i,s;const a=t.concat(n),o=fe(new U({definition:a}));this.follows[r]=o}}var ge=n(9592),ye=n(5186),Te=n(3068),Ae=n(2634),ve=n(1790);const Re=function(e){if("function"!=typeof e)throw new TypeError("Expected a function");return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}};const Ee=function(e,t){return((0,Q.A)(e)?Ae.A:ve.A)(e,Re((0,k.A)(t,3)))};var ke=n(9610),$e=Math.max;const xe=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=null==n?0:(0,d.A)(n);return i<0&&(i=$e(r+i,0)),(0,ee.A)(e,t,i)};var Ie=n(9463),Se=n(4092),Ne=n(2062),Ce=n(5530),we=n(7809),Le=n(4099);const be=function(e,t,n,r){var i=-1,s=Ce.A,a=!0,o=e.length,c=[],l=t.length;if(!o)return c;n&&(t=(0,E.A)(t,(0,w.A)(n))),r?(s=we.A,a=!1):t.length>=200&&(s=Le.A,a=!1,t=new Ne.A(t));e:for(;++i<o;){var u=e[i],d=null==n?u:n(u);if(u=r||0!==u?u:0,a&&d==d){for(var h=l;h--;)if(t[h]===d)continue e;c.push(u)}else s(t,d,r)||c.push(u)}return c};var Oe=n(3588),_e=n(4326),Pe=n(3533);const Me=(0,_e.A)((function(e,t){return(0,Pe.A)(e)?be(e,(0,Oe.A)(t,1,Pe.A,!0)):[]}));const De=function(e){for(var t=-1,n=null==e?0:e.length,r=0,i=[];++t<n;){var s=e[t];s&&(i[r++]=s)}return i};const Ue=function(e){return e&&e.length?e[0]:void 0};var Fe=n(6145);function Ge(e){console&&console.error&&console.error(`Error: ${e}`)}function Ke(e){console&&console.warn&&console.warn(`Warning: ${e}`)}let Be={};const je=new ye.H;function Ve(e){const t=e.toString();if(Be.hasOwnProperty(t))return Be[t];{const e=je.pattern(t);return Be[t]=e,e}}const He="Complement Sets are not supported for first char optimization",We='Unable to use "first char" lexer optimizations:\n';function ze(e,t=!1){try{const t=Ve(e);return Ye(t.value,{},t.flags.ignoreCase)}catch(n){if(n.message===He)t&&Ke(`${We}\tUnable to optimize: < ${e.toString()} >\n\tComplement Sets cannot be automatically optimized.\n\tThis will disable the lexer's first char optimizations.\n\tSee: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#COMPLEMENT for details.`);else{let n="";t&&(n="\n\tThis will disable the lexer's first char optimizations.\n\tSee: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details."),Ge(`${We}\n\tFailed parsing: < ${e.toString()} >\n\tUsing the @chevrotain/regexp-to-ast library\n\tPlease open an issue at: https://github.com/chevrotain/chevrotain/issues`+n)}}return[]}function Ye(e,t,n){switch(e.type){case"Disjunction":for(let r=0;r<e.value.length;r++)Ye(e.value[r],t,n);break;case"Alternative":const i=e.value;for(let e=0;e<i.length;e++){const s=i[e];switch(s.type){case"EndAnchor":case"GroupBackReference":case"Lookahead":case"NegativeLookahead":case"StartAnchor":case"WordBoundary":case"NonWordBoundary":continue}const a=s;switch(a.type){case"Character":Xe(a.value,t,n);break;case"Set":if(!0===a.complement)throw Error(He);(0,r.A)(a.value,(e=>{if("number"==typeof e)Xe(e,t,n);else{const r=e;if(!0===n)for(let e=r.from;e<=r.to;e++)Xe(e,t,n);else{for(let e=r.from;e<=r.to&&e<yt;e++)Xe(e,t,n);if(r.to>=yt){const e=r.from>=yt?r.from:yt,n=r.to,i=At(e),s=At(n);for(let r=i;r<=s;r++)t[r]=r}}}}));break;case"Group":Ye(a.value,t,n);break;default:throw Error("Non Exhaustive Match")}const o=void 0!==a.quantifier&&0===a.quantifier.atLeast;if("Group"===a.type&&!1===Qe(a)||"Group"!==a.type&&!1===o)break}break;default:throw Error("non exhaustive match!")}return(0,i.A)(t)}function Xe(e,t,n){const r=At(e);t[r]=r,!0===n&&function(e,t){const n=String.fromCharCode(e),r=n.toUpperCase();if(r!==n){const e=At(r.charCodeAt(0));t[e]=e}else{const e=n.toLowerCase();if(e!==n){const n=At(e.charCodeAt(0));t[n]=n}}}(e,t)}function qe(e,t){return(0,Fe.A)(e.value,(e=>{if("number"==typeof e)return ne(t,e);{const n=e;return void 0!==(0,Fe.A)(t,(e=>n.from<=e&&e<=n.to))}}))}function Qe(e){const t=e.quantifier;return!(!t||0!==t.atLeast)||!!e.value&&((0,Q.A)(e.value)?se(e.value,Qe):Qe(e.value))}class Ze extends ye.z{constructor(e){super(),this.targetCharCodes=e,this.found=!1}visitChildren(e){if(!0!==this.found){switch(e.type){case"Lookahead":return void this.visitLookahead(e);case"NegativeLookahead":return void this.visitNegativeLookahead(e)}super.visitChildren(e)}}visitCharacter(e){ne(this.targetCharCodes,e.value)&&(this.found=!0)}visitSet(e){e.complement?void 0===qe(e,this.targetCharCodes)&&(this.found=!0):void 0!==qe(e,this.targetCharCodes)&&(this.found=!0)}}function Je(e,t){if(t instanceof RegExp){const n=Ve(t),r=new Ze(e);return r.visit(n),r.found}return void 0!==(0,Fe.A)(t,(t=>ne(e,t.charCodeAt(0))))}const et="PATTERN",tt="defaultMode",nt="modes";let rt="boolean"==typeof new RegExp("(?:)").sticky;function it(e,t){const n=(t=(0,Te.A)(t,{useSticky:rt,debug:!1,safeMode:!1,positionTracking:"full",lineTerminatorCharacters:["\r","\n"],tracer:(e,t)=>t()})).tracer;let i;n("initCharCodeToOptimizedIndexMap",(()=>{!function(){if((0,s.A)(Tt)){Tt=new Array(65536);for(let e=0;e<65536;e++)Tt[e]=e>255?255+~~(e/255):e}}()})),n("Reject Lexer.NA",(()=>{i=Ee(e,(e=>e[et]===Mt.NA))}));let c,l,u,d,h,p,m,g,y,T,A,v=!1;n("Transform Patterns",(()=>{v=!1,c=(0,a.A)(i,(e=>{const n=e[et];if(O(n)){const e=n.source;return 1!==e.length||"^"===e||"$"===e||"."===e||n.ignoreCase?2!==e.length||"\\"!==e[0]||ne(["d","D","s","S","t","r","n","t","0","c","b","B","f","v","w","W"],e[1])?t.useSticky?lt(n):ct(n):e[1]:e}if((0,ke.A)(n))return v=!0,{exec:n};if("object"==typeof n)return v=!0,n;if("string"==typeof n){if(1===n.length)return n;{const e=n.replace(/[\\^$.*+?()[\]{}|]/g,"\\$&"),r=new RegExp(e);return t.useSticky?lt(r):ct(r)}}throw Error("non exhaustive match")}))})),n("misc mapping",(()=>{l=(0,a.A)(i,(e=>e.tokenTypeIdx)),u=(0,a.A)(i,(e=>{const t=e.GROUP;if(t!==Mt.SKIPPED){if((0,f.A)(t))return t;if((0,ge.A)(t))return!1;throw Error("non exhaustive match")}})),d=(0,a.A)(i,(e=>{const t=e.LONGER_ALT;if(t){return(0,Q.A)(t)?(0,a.A)(t,(e=>xe(i,e))):[xe(i,t)]}})),h=(0,a.A)(i,(e=>e.PUSH_MODE)),p=(0,a.A)(i,(e=>(0,o.A)(e,"POP_MODE")))})),n("Line Terminator Handling",(()=>{const e=mt(t.lineTerminatorCharacters);m=(0,a.A)(i,(e=>!1)),"onlyOffset"!==t.positionTracking&&(m=(0,a.A)(i,(t=>(0,o.A)(t,"LINE_BREAKS")?!!t.LINE_BREAKS:!1===pt(t,e)&&Je(e,t.PATTERN))))})),n("Misc Mapping #2",(()=>{g=(0,a.A)(i,dt),y=(0,a.A)(c,ht),T=(0,Ie.A)(i,((e,t)=>{const n=t.GROUP;return(0,f.A)(n)&&n!==Mt.SKIPPED&&(e[n]=[]),e}),{}),A=(0,a.A)(c,((e,t)=>({pattern:c[t],longerAlt:d[t],canLineTerminator:m[t],isCustom:g[t],short:y[t],group:u[t],push:h[t],pop:p[t],tokenTypeIdx:l[t],tokenType:i[t]})))}));let R=!0,E=[];return t.safeMode||n("First Char Optimization",(()=>{E=(0,Ie.A)(i,((e,n,i)=>{if("string"==typeof n.PATTERN){const t=At(n.PATTERN.charCodeAt(0));gt(e,t,A[i])}else if((0,Q.A)(n.START_CHARS_HINT)){let t;(0,r.A)(n.START_CHARS_HINT,(n=>{const r=At("string"==typeof n?n.charCodeAt(0):n);t!==r&&(t=r,gt(e,r,A[i]))}))}else if(O(n.PATTERN))if(n.PATTERN.unicode)R=!1,t.ensureOptimizations&&Ge(`${We}\tUnable to analyze < ${n.PATTERN.toString()} > pattern.\n\tThe regexp unicode flag is not currently supported by the regexp-to-ast library.\n\tThis will disable the lexer's first char optimizations.\n\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNICODE_OPTIMIZE`);else{const a=ze(n.PATTERN,t.ensureOptimizations);(0,s.A)(a)&&(R=!1),(0,r.A)(a,(t=>{gt(e,t,A[i])}))}else t.ensureOptimizations&&Ge(`${We}\tTokenType: <${n.name}> is using a custom token pattern without providing <start_chars_hint> parameter.\n\tThis will disable the lexer's first char optimizations.\n\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_OPTIMIZE`),R=!1;return e}),[])})),{emptyGroups:T,patternIdxToConfig:A,charCodeToPatternIdxToConfig:E,hasCustom:v,canBeOptimized:R}}function st(e,t){let n=[];const i=function(e){const t=(0,Se.A)(e,(e=>!(0,o.A)(e,et))),n=(0,a.A)(t,(e=>({message:"Token Type: ->"+e.name+"<- missing static 'PATTERN' property",type:_t.MISSING_PATTERN,tokenTypes:[e]}))),r=Me(e,t);return{errors:n,valid:r}}(e);n=n.concat(i.errors);const s=function(e){const t=(0,Se.A)(e,(e=>{const t=e[et];return!(O(t)||(0,ke.A)(t)||(0,o.A)(t,"exec")||(0,f.A)(t))})),n=(0,a.A)(t,(e=>({message:"Token Type: ->"+e.name+"<- static 'PATTERN' can only be a RegExp, a Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.",type:_t.INVALID_PATTERN,tokenTypes:[e]}))),r=Me(e,t);return{errors:n,valid:r}}(i.valid),c=s.valid;return n=n.concat(s.errors),n=n.concat(function(e){let t=[];const n=(0,Se.A)(e,(e=>O(e[et])));return t=t.concat(function(e){class t extends ye.z{constructor(){super(...arguments),this.found=!1}visitEndAnchor(e){this.found=!0}}const n=(0,Se.A)(e,(e=>{const n=e.PATTERN;try{const e=Ve(n),r=new t;return r.visit(e),r.found}catch(r){return at.test(n.source)}})),r=(0,a.A)(n,(e=>({message:"Unexpected RegExp Anchor Error:\n\tToken Type: ->"+e.name+"<- static 'PATTERN' cannot contain end of input anchor '$'\n\tSee chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS\tfor details.",type:_t.EOI_ANCHOR_FOUND,tokenTypes:[e]})));return r}(n)),t=t.concat(function(e){class t extends ye.z{constructor(){super(...arguments),this.found=!1}visitStartAnchor(e){this.found=!0}}const n=(0,Se.A)(e,(e=>{const n=e.PATTERN;try{const e=Ve(n),r=new t;return r.visit(e),r.found}catch(r){return ot.test(n.source)}})),r=(0,a.A)(n,(e=>({message:"Unexpected RegExp Anchor Error:\n\tToken Type: ->"+e.name+"<- static 'PATTERN' cannot contain start of input anchor '^'\n\tSee https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS\tfor details.",type:_t.SOI_ANCHOR_FOUND,tokenTypes:[e]})));return r}(n)),t=t.concat(function(e){const t=(0,Se.A)(e,(e=>{const t=e[et];return t instanceof RegExp&&(t.multiline||t.global)})),n=(0,a.A)(t,(e=>({message:"Token Type: ->"+e.name+"<- static 'PATTERN' may NOT contain global('g') or multiline('m')",type:_t.UNSUPPORTED_FLAGS_FOUND,tokenTypes:[e]})));return n}(n)),t=t.concat(function(e){const t=[];let n=(0,a.A)(e,(n=>(0,Ie.A)(e,((e,r)=>(n.PATTERN.source!==r.PATTERN.source||ne(t,r)||r.PATTERN===Mt.NA||(t.push(r),e.push(r)),e)),[])));n=De(n);const r=(0,Se.A)(n,(e=>e.length>1)),i=(0,a.A)(r,(e=>{const t=(0,a.A)(e,(e=>e.name));return{message:`The same RegExp pattern ->${Ue(e).PATTERN}<-has been used in all of the following Token Types: ${t.join(", ")} <-`,type:_t.DUPLICATE_PATTERNS_FOUND,tokenTypes:e}}));return i}(n)),t=t.concat(function(e){const t=(0,Se.A)(e,(e=>e.PATTERN.test(""))),n=(0,a.A)(t,(e=>({message:"Token Type: ->"+e.name+"<- static 'PATTERN' must not match an empty string",type:_t.EMPTY_MATCH_PATTERN,tokenTypes:[e]})));return n}(n)),t}(c)),n=n.concat(function(e){const t=(0,Se.A)(e,(e=>{if(!(0,o.A)(e,"GROUP"))return!1;const t=e.GROUP;return t!==Mt.SKIPPED&&t!==Mt.NA&&!(0,f.A)(t)})),n=(0,a.A)(t,(e=>({message:"Token Type: ->"+e.name+"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String",type:_t.INVALID_GROUP_TYPE_FOUND,tokenTypes:[e]})));return n}(c)),n=n.concat(function(e,t){const n=(0,Se.A)(e,(e=>void 0!==e.PUSH_MODE&&!ne(t,e.PUSH_MODE))),r=(0,a.A)(n,(e=>({message:`Token Type: ->${e.name}<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->${e.PUSH_MODE}<-which does not exist`,type:_t.PUSH_MODE_DOES_NOT_EXIST,tokenTypes:[e]})));return r}(c,t)),n=n.concat(function(e){const t=[],n=(0,Ie.A)(e,((e,t,n)=>{const r=t.PATTERN;return r===Mt.NA||((0,f.A)(r)?e.push({str:r,idx:n,tokenType:t}):O(r)&&function(e){const t=[".","\\","[","]","|","^","$","(",")","?","*","+","{"];return void 0===(0,Fe.A)(t,(t=>-1!==e.source.indexOf(t)))}(r)&&e.push({str:r.source,idx:n,tokenType:t})),e}),[]);return(0,r.A)(e,((e,i)=>{(0,r.A)(n,(({str:n,idx:r,tokenType:s})=>{if(i<r&&function(e,t){if(O(t)){const n=t.exec(e);return null!==n&&0===n.index}if((0,ke.A)(t))return t(e,0,[],{});if((0,o.A)(t,"exec"))return t.exec(e,0,[],{});if("string"==typeof t)return t===e;throw Error("non exhaustive match")}(n,e.PATTERN)){const n=`Token: ->${s.name}<- can never be matched.\nBecause it appears AFTER the Token Type ->${e.name}<-in the lexer's definition.\nSee https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE`;t.push({message:n,type:_t.UNREACHABLE_PATTERN,tokenTypes:[e,s]})}}))})),t}(c)),n}const at=/[^\\][$]/;const ot=/[^\\[][\^]|^\^/;function ct(e){const t=e.ignoreCase?"i":"";return new RegExp(`^(?:${e.source})`,t)}function lt(e){const t=e.ignoreCase?"iy":"y";return new RegExp(`${e.source}`,t)}function ut(e,t,n){const s=[];let a=!1;const c=De((0,he.A)((0,i.A)(e.modes))),l=Ee(c,(e=>e[et]===Mt.NA)),u=mt(n);return t&&(0,r.A)(l,(e=>{const t=pt(e,u);if(!1!==t){const n=function(e,t){if(t.issue===_t.IDENTIFY_TERMINATOR)return`Warning: unable to identify line terminator usage in pattern.\n\tThe problem is in the <${e.name}> Token Type\n\t Root cause: ${t.errMsg}.\n\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR`;if(t.issue===_t.CUSTOM_LINE_BREAK)return`Warning: A Custom Token Pattern should specify the <line_breaks> option.\n\tThe problem is in the <${e.name}> Token Type\n\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK`;throw Error("non exhaustive match")}(e,t),r={message:n,type:t.issue,tokenType:e};s.push(r)}else(0,o.A)(e,"LINE_BREAKS")?!0===e.LINE_BREAKS&&(a=!0):Je(u,e.PATTERN)&&(a=!0)})),t&&!a&&s.push({message:"Warning: No LINE_BREAKS Found.\n\tThis Lexer has been defined to track line and column information,\n\tBut none of the Token Types can be identified as matching a line terminator.\n\tSee https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS \n\tfor details.",type:_t.NO_LINE_BREAKS_FLAGS}),s}function dt(e){const t=e.PATTERN;if(O(t))return!1;if((0,ke.A)(t))return!0;if((0,o.A)(t,"exec"))return!0;if((0,f.A)(t))return!1;throw Error("non exhaustive match")}function ht(e){return!(!(0,f.A)(e)||1!==e.length)&&e.charCodeAt(0)}const ft={test:function(e){const t=e.length;for(let n=this.lastIndex;n<t;n++){const t=e.charCodeAt(n);if(10===t)return this.lastIndex=n+1,!0;if(13===t)return 10===e.charCodeAt(n+1)?this.lastIndex=n+2:this.lastIndex=n+1,!0}return!1},lastIndex:0};function pt(e,t){if((0,o.A)(e,"LINE_BREAKS"))return!1;if(O(e.PATTERN)){try{Je(t,e.PATTERN)}catch(n){return{issue:_t.IDENTIFY_TERMINATOR,errMsg:n.message}}return!1}if((0,f.A)(e.PATTERN))return!1;if(dt(e))return{issue:_t.CUSTOM_LINE_BREAK};throw Error("non exhaustive match")}function mt(e){return(0,a.A)(e,(e=>(0,f.A)(e)?e.charCodeAt(0):e))}function gt(e,t,n){void 0===e[t]?e[t]=[n]:e[t].push(n)}const yt=256;let Tt=[];function At(e){return e<yt?e:Tt[e]}var vt=n(9008),Rt=n(2302),Et=n(6666);function kt(e){const t=(new Date).getTime(),n=e();return{time:(new Date).getTime()-t,value:n}}function $t(e,t){const n=e.tokenTypeIdx;return n===t.tokenTypeIdx||!0===t.isParent&&!0===t.categoryMatchesMap[n]}function xt(e,t){return e.tokenTypeIdx===t.tokenTypeIdx}let It=1;const St={};function Nt(e){const t=function(e){let t=(0,c.A)(e),n=e,r=!0;for(;r;){n=De((0,he.A)((0,a.A)(n,(e=>e.CATEGORIES))));const e=Me(n,t);t=t.concat(e),(0,s.A)(e)?r=!1:n=e}return t}(e);!function(e){(0,r.A)(e,(e=>{var t;wt(e)||(St[It]=e,e.tokenTypeIdx=It++),Lt(e)&&!(0,Q.A)(e.CATEGORIES)&&(e.CATEGORIES=[e.CATEGORIES]),Lt(e)||(e.CATEGORIES=[]),t=e,(0,o.A)(t,"categoryMatches")||(e.categoryMatches=[]),function(e){return(0,o.A)(e,"categoryMatchesMap")}(e)||(e.categoryMatchesMap={})}))}(t),function(e){(0,r.A)(e,(e=>{Ct([],e)}))}(t),function(e){(0,r.A)(e,(e=>{e.categoryMatches=[],(0,r.A)(e.categoryMatchesMap,((t,n)=>{e.categoryMatches.push(St[n].tokenTypeIdx)}))}))}(t),(0,r.A)(t,(e=>{e.isParent=e.categoryMatches.length>0}))}function Ct(e,t){(0,r.A)(e,(e=>{t.categoryMatchesMap[e.tokenTypeIdx]=!0})),(0,r.A)(t.CATEGORIES,(n=>{const r=e.concat(t);ne(r,n)||Ct(r,n)}))}function wt(e){return(0,o.A)(e,"tokenTypeIdx")}function Lt(e){return(0,o.A)(e,"CATEGORIES")}function bt(e){return(0,o.A)(e,"tokenTypeIdx")}const Ot={buildUnableToPopLexerModeMessage:e=>`Unable to pop Lexer Mode after encountering Token ->${e.image}<- The Mode Stack is empty`,buildUnexpectedCharactersMessage:(e,t,n,r,i)=>`unexpected character: ->${e.charAt(t)}<- at offset: ${t}, skipped ${n} characters.`};var _t;!function(e){e[e.MISSING_PATTERN=0]="MISSING_PATTERN",e[e.INVALID_PATTERN=1]="INVALID_PATTERN",e[e.EOI_ANCHOR_FOUND=2]="EOI_ANCHOR_FOUND",e[e.UNSUPPORTED_FLAGS_FOUND=3]="UNSUPPORTED_FLAGS_FOUND",e[e.DUPLICATE_PATTERNS_FOUND=4]="DUPLICATE_PATTERNS_FOUND",e[e.INVALID_GROUP_TYPE_FOUND=5]="INVALID_GROUP_TYPE_FOUND",e[e.PUSH_MODE_DOES_NOT_EXIST=6]="PUSH_MODE_DOES_NOT_EXIST",e[e.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE=7]="MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE",e[e.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY=8]="MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY",e[e.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST=9]="MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST",e[e.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED=10]="LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED",e[e.SOI_ANCHOR_FOUND=11]="SOI_ANCHOR_FOUND",e[e.EMPTY_MATCH_PATTERN=12]="EMPTY_MATCH_PATTERN",e[e.NO_LINE_BREAKS_FLAGS=13]="NO_LINE_BREAKS_FLAGS",e[e.UNREACHABLE_PATTERN=14]="UNREACHABLE_PATTERN",e[e.IDENTIFY_TERMINATOR=15]="IDENTIFY_TERMINATOR",e[e.CUSTOM_LINE_BREAK=16]="CUSTOM_LINE_BREAK",e[e.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE=17]="MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE"}(_t||(_t={}));const Pt={deferDefinitionErrorsHandling:!1,positionTracking:"full",lineTerminatorsPattern:/\n|\r\n?/g,lineTerminatorCharacters:["\n","\r"],ensureOptimizations:!1,safeMode:!1,errorMessageProvider:Ot,traceInitPerf:!1,skipValidations:!1,recoveryEnabled:!0};Object.freeze(Pt);class Mt{constructor(e,t=Pt){if(this.lexerDefinition=e,this.lexerDefinitionErrors=[],this.lexerDefinitionWarning=[],this.patternIdxToConfig={},this.charCodeToPatternIdxToConfig={},this.modes=[],this.emptyGroups={},this.trackStartLines=!0,this.trackEndLines=!0,this.hasCustom=!1,this.canModeBeOptimized={},this.TRACE_INIT=(e,t)=>{if(!0===this.traceInitPerf){this.traceInitIndent++;const n=new Array(this.traceInitIndent+1).join("\t");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${n}--\x3e <${e}>`);const{time:r,value:i}=kt(t),s=r>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&s(`${n}<-- <${e}> time: ${r}ms`),this.traceInitIndent--,i}return t()},"boolean"==typeof t)throw Error("The second argument to the Lexer constructor is now an ILexerConfig Object.\na boolean 2nd argument is no longer supported");this.config=R({},Pt,t);const n=this.config.traceInitPerf;!0===n?(this.traceInitMaxIdent=1/0,this.traceInitPerf=!0):"number"==typeof n&&(this.traceInitMaxIdent=n,this.traceInitPerf=!0),this.traceInitIndent=-1,this.TRACE_INIT("Lexer Constructor",(()=>{let n,i=!0;this.TRACE_INIT("Lexer Config handling",(()=>{if(this.config.lineTerminatorsPattern===Pt.lineTerminatorsPattern)this.config.lineTerminatorsPattern=ft;else if(this.config.lineTerminatorCharacters===Pt.lineTerminatorCharacters)throw Error("Error: Missing <lineTerminatorCharacters> property on the Lexer config.\n\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS");if(t.safeMode&&t.ensureOptimizations)throw Error('"safeMode" and "ensureOptimizations" flags are mutually exclusive.');this.trackStartLines=/full|onlyStart/i.test(this.config.positionTracking),this.trackEndLines=/full/i.test(this.config.positionTracking),(0,Q.A)(e)?n={modes:{defaultMode:(0,c.A)(e)},defaultMode:tt}:(i=!1,n=(0,c.A)(e))})),!1===this.config.skipValidations&&(this.TRACE_INIT("performRuntimeChecks",(()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(function(e){const t=[];return(0,o.A)(e,tt)||t.push({message:"A MultiMode Lexer cannot be initialized without a <"+tt+"> property in its definition\n",type:_t.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE}),(0,o.A)(e,nt)||t.push({message:"A MultiMode Lexer cannot be initialized without a <modes> property in its definition\n",type:_t.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY}),(0,o.A)(e,nt)&&(0,o.A)(e,tt)&&!(0,o.A)(e.modes,e.defaultMode)&&t.push({message:`A MultiMode Lexer cannot be initialized with a ${tt}: <${e.defaultMode}>which does not exist\n`,type:_t.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST}),(0,o.A)(e,nt)&&(0,r.A)(e.modes,((e,n)=>{(0,r.A)(e,((i,s)=>{if((0,ge.A)(i))t.push({message:`A Lexer cannot be initialized using an undefined Token Type. Mode:<${n}> at index: <${s}>\n`,type:_t.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED});else if((0,o.A)(i,"LONGER_ALT")){const s=(0,Q.A)(i.LONGER_ALT)?i.LONGER_ALT:[i.LONGER_ALT];(0,r.A)(s,(r=>{(0,ge.A)(r)||ne(e,r)||t.push({message:`A MultiMode Lexer cannot be initialized with a longer_alt <${r.name}> on token <${i.name}> outside of mode <${n}>\n`,type:_t.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE})}))}}))})),t}(n,this.trackStartLines,this.config.lineTerminatorCharacters))})),this.TRACE_INIT("performWarningRuntimeChecks",(()=>{this.lexerDefinitionWarning=this.lexerDefinitionWarning.concat(ut(n,this.trackStartLines,this.config.lineTerminatorCharacters))}))),n.modes=n.modes?n.modes:{},(0,r.A)(n.modes,((e,t)=>{n.modes[t]=Ee(e,(e=>(0,ge.A)(e)))}));const u=(0,A.A)(n.modes);if((0,r.A)(n.modes,((e,n)=>{this.TRACE_INIT(`Mode: <${n}> processing`,(()=>{if(this.modes.push(n),!1===this.config.skipValidations&&this.TRACE_INIT("validatePatterns",(()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(st(e,u))})),(0,s.A)(this.lexerDefinitionErrors)){let r;Nt(e),this.TRACE_INIT("analyzeTokenTypes",(()=>{r=it(e,{lineTerminatorCharacters:this.config.lineTerminatorCharacters,positionTracking:t.positionTracking,ensureOptimizations:t.ensureOptimizations,safeMode:t.safeMode,tracer:this.TRACE_INIT})})),this.patternIdxToConfig[n]=r.patternIdxToConfig,this.charCodeToPatternIdxToConfig[n]=r.charCodeToPatternIdxToConfig,this.emptyGroups=R({},this.emptyGroups,r.emptyGroups),this.hasCustom=r.hasCustom||this.hasCustom,this.canModeBeOptimized[n]=r.canBeOptimized}}))})),this.defaultMode=n.defaultMode,!(0,s.A)(this.lexerDefinitionErrors)&&!this.config.deferDefinitionErrorsHandling){const e=(0,a.A)(this.lexerDefinitionErrors,(e=>e.message)).join("-----------------------\n");throw new Error("Errors detected in definition of Lexer:\n"+e)}(0,r.A)(this.lexerDefinitionWarning,(e=>{Ke(e.message)})),this.TRACE_INIT("Choosing sub-methods implementations",(()=>{if(rt?(this.chopInput=vt.A,this.match=this.matchWithTest):(this.updateLastIndex=Rt.A,this.match=this.matchWithExec),i&&(this.handleModes=Rt.A),!1===this.trackStartLines&&(this.computeNewColumn=vt.A),!1===this.trackEndLines&&(this.updateTokenEndLineColumnLocation=Rt.A),/full/i.test(this.config.positionTracking))this.createTokenInstance=this.createFullToken;else if(/onlyStart/i.test(this.config.positionTracking))this.createTokenInstance=this.createStartOnlyToken;else{if(!/onlyOffset/i.test(this.config.positionTracking))throw Error(`Invalid <positionTracking> config option: "${this.config.positionTracking}"`);this.createTokenInstance=this.createOffsetOnlyToken}this.hasCustom?(this.addToken=this.addTokenUsingPush,this.handlePayload=this.handlePayloadWithCustom):(this.addToken=this.addTokenUsingMemberAccess,this.handlePayload=this.handlePayloadNoCustom)})),this.TRACE_INIT("Failed Optimization Warnings",(()=>{const e=(0,Ie.A)(this.canModeBeOptimized,((e,t,n)=>(!1===t&&e.push(n),e)),[]);if(t.ensureOptimizations&&!(0,s.A)(e))throw Error(`Lexer Modes: < ${e.join(", ")} > cannot be optimized.\n\t Disable the "ensureOptimizations" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.\n\t Or inspect the console log for details on how to resolve these issues.`)})),this.TRACE_INIT("clearRegExpParserCache",(()=>{Be={}})),this.TRACE_INIT("toFastProperties",(()=>{l(this)}))}))}tokenize(e,t=this.defaultMode){if(!(0,s.A)(this.lexerDefinitionErrors)){const e=(0,a.A)(this.lexerDefinitionErrors,(e=>e.message)).join("-----------------------\n");throw new Error("Unable to Tokenize because Errors detected in definition of Lexer:\n"+e)}return this.tokenizeInternal(e,t)}tokenizeInternal(e,t){let n,i,s,a,o,c,l,u,d,h,f,p,m,g,y;const T=e,v=T.length;let R=0,E=0;const k=this.hasCustom?0:Math.floor(e.length/10),$=new Array(k),x=[];let I=this.trackStartLines?1:void 0,S=this.trackStartLines?1:void 0;const N=function(e){const t={},n=(0,A.A)(e);return(0,r.A)(n,(n=>{const r=e[n];if(!(0,Q.A)(r))throw Error("non exhaustive match");t[n]=[]})),t}(this.emptyGroups),C=this.trackStartLines,w=this.config.lineTerminatorsPattern;let L=0,b=[],O=[];const _=[],P=[];let M;function D(){return b}function U(e){const t=At(e),n=O[t];return void 0===n?P:n}Object.freeze(P);const F=e=>{if(1===_.length&&void 0===e.tokenType.PUSH_MODE){const t=this.config.errorMessageProvider.buildUnableToPopLexerModeMessage(e);x.push({offset:e.startOffset,line:e.startLine,column:e.startColumn,length:e.image.length,message:t})}else{_.pop();const e=(0,Et.A)(_);b=this.patternIdxToConfig[e],O=this.charCodeToPatternIdxToConfig[e],L=b.length;const t=this.canModeBeOptimized[e]&&!1===this.config.safeMode;M=O&&t?U:D}};function G(e){_.push(e),O=this.charCodeToPatternIdxToConfig[e],b=this.patternIdxToConfig[e],L=b.length,L=b.length;const t=this.canModeBeOptimized[e]&&!1===this.config.safeMode;M=O&&t?U:D}let K;G.call(this,t);const B=this.config.recoveryEnabled;for(;R<v;){c=null;const t=T.charCodeAt(R),r=M(t),A=r.length;for(n=0;n<A;n++){K=r[n];const i=K.pattern;l=null;const d=K.short;if(!1!==d?t===d&&(c=i):!0===K.isCustom?(y=i.exec(T,R,$,N),null!==y?(c=y[0],void 0!==y.payload&&(l=y.payload)):c=null):(this.updateLastIndex(i,R),c=this.match(i,e,R)),null!==c){if(o=K.longerAlt,void 0!==o){const t=o.length;for(s=0;s<t;s++){const t=b[o[s]],n=t.pattern;if(u=null,!0===t.isCustom?(y=n.exec(T,R,$,N),null!==y?(a=y[0],void 0!==y.payload&&(u=y.payload)):a=null):(this.updateLastIndex(n,R),a=this.match(n,e,R)),a&&a.length>c.length){c=a,l=u,K=t;break}}}break}}if(null!==c){if(d=c.length,h=K.group,void 0!==h&&(f=K.tokenTypeIdx,p=this.createTokenInstance(c,R,f,K.tokenType,I,S,d),this.handlePayload(p,l),!1===h?E=this.addToken($,E,p):N[h].push(p)),e=this.chopInput(e,d),R+=d,S=this.computeNewColumn(S,d),!0===C&&!0===K.canLineTerminator){let e,t,n=0;w.lastIndex=0;do{e=w.test(c),!0===e&&(t=w.lastIndex-1,n++)}while(!0===e);0!==n&&(I+=n,S=d-t,this.updateTokenEndLineColumnLocation(p,h,t,n,I,S,d))}this.handleModes(K,F,G,p)}else{const t=R,n=I,r=S;let s=!1===B;for(;!1===s&&R<v;)for(e=this.chopInput(e,1),R++,i=0;i<L;i++){const t=b[i],n=t.pattern,r=t.short;if(!1!==r?T.charCodeAt(R)===r&&(s=!0):!0===t.isCustom?s=null!==n.exec(T,R,$,N):(this.updateLastIndex(n,R),s=null!==n.exec(e)),!0===s)break}if(m=R-t,S=this.computeNewColumn(S,m),g=this.config.errorMessageProvider.buildUnexpectedCharactersMessage(T,t,m,n,r),x.push({offset:t,line:n,column:r,length:m,message:g}),!1===B)break}}return this.hasCustom||($.length=E),{tokens:$,groups:N,errors:x}}handleModes(e,t,n,r){if(!0===e.pop){const i=e.push;t(r),void 0!==i&&n.call(this,i)}else void 0!==e.push&&n.call(this,e.push)}chopInput(e,t){return e.substring(t)}updateLastIndex(e,t){e.lastIndex=t}updateTokenEndLineColumnLocation(e,t,n,r,i,s,a){let o,c;void 0!==t&&(o=n===a-1,c=o?-1:0,1===r&&!0===o||(e.endLine=i+c,e.endColumn=s-1-c))}computeNewColumn(e,t){return e+t}createOffsetOnlyToken(e,t,n,r){return{image:e,startOffset:t,tokenTypeIdx:n,tokenType:r}}createStartOnlyToken(e,t,n,r,i,s){return{image:e,startOffset:t,startLine:i,startColumn:s,tokenTypeIdx:n,tokenType:r}}createFullToken(e,t,n,r,i,s,a){return{image:e,startOffset:t,endOffset:t+a-1,startLine:i,endLine:i,startColumn:s,endColumn:s+a-1,tokenTypeIdx:n,tokenType:r}}addTokenUsingPush(e,t,n){return e.push(n),t}addTokenUsingMemberAccess(e,t,n){return e[t]=n,++t}handlePayloadNoCustom(e,t){}handlePayloadWithCustom(e,t){null!==t&&(e.payload=t)}matchWithTest(e,t,n){return!0===e.test(t)?t.substring(n,e.lastIndex):null}matchWithExec(e,t){const n=e.exec(t);return null!==n?n[0]:null}}function Dt(e){return Ut(e)?e.LABEL:e.name}function Ut(e){return(0,f.A)(e.LABEL)&&""!==e.LABEL}Mt.SKIPPED="This marks a skipped Token pattern, this means each token identified by it willbe consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.",Mt.NA=/NOT_APPLICABLE/;const Ft="parent",Gt="categories",Kt="label",Bt="group",jt="push_mode",Vt="pop_mode",Ht="longer_alt",Wt="line_breaks",zt="start_chars_hint";function Yt(e){return function(e){const t=e.pattern,n={};n.name=e.name,(0,ge.A)(t)||(n.PATTERN=t);if((0,o.A)(e,Ft))throw"The parent property is no longer supported.\nSee: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details.";(0,o.A)(e,Gt)&&(n.CATEGORIES=e[Gt]);Nt([n]),(0,o.A)(e,Kt)&&(n.LABEL=e[Kt]);(0,o.A)(e,Bt)&&(n.GROUP=e[Bt]);(0,o.A)(e,Vt)&&(n.POP_MODE=e[Vt]);(0,o.A)(e,jt)&&(n.PUSH_MODE=e[jt]);(0,o.A)(e,Ht)&&(n.LONGER_ALT=e[Ht]);(0,o.A)(e,Wt)&&(n.LINE_BREAKS=e[Wt]);(0,o.A)(e,zt)&&(n.START_CHARS_HINT=e[zt]);return n}(e)}const Xt=Yt({name:"EOF",pattern:Mt.NA});function qt(e,t,n,r,i,s,a,o){return{image:t,startOffset:n,endOffset:r,startLine:i,endLine:s,startColumn:a,endColumn:o,tokenTypeIdx:e.tokenTypeIdx,tokenType:e}}function Qt(e,t){return $t(e,t)}Nt([Xt]);const Zt={buildMismatchTokenMessage:({expected:e,actual:t,previous:n,ruleName:r})=>`Expecting ${Ut(e)?`--\x3e ${Dt(e)} <--`:`token of type --\x3e ${e.name} <--`} but found --\x3e '${t.image}' <--`,buildNotAllInputParsedMessage:({firstRedundant:e,ruleName:t})=>"Redundant input, expecting EOF but found: "+e.image,buildNoViableAltMessage({expectedPathsPerAlt:e,actual:t,previous:n,customUserDescription:r,ruleName:i}){const s="Expecting: ",o="\nbut found: '"+Ue(t).image+"'";if(r)return s+r+o;{const t=(0,Ie.A)(e,((e,t)=>e.concat(t)),[]),n=(0,a.A)(t,(e=>`[${(0,a.A)(e,(e=>Dt(e))).join(", ")}]`));return s+`one of these possible Token sequences:\n${(0,a.A)(n,((e,t)=>`  ${t+1}. ${e}`)).join("\n")}`+o}},buildEarlyExitMessage({expectedIterationPaths:e,actual:t,customUserDescription:n,ruleName:r}){const i="Expecting: ",s="\nbut found: '"+Ue(t).image+"'";if(n)return i+n+s;return i+`expecting at least one iteration which starts with one of these possible Token sequences::\n  <${(0,a.A)(e,(e=>`[${(0,a.A)(e,(e=>Dt(e))).join(",")}]`)).join(" ,")}>`+s}};Object.freeze(Zt);const Jt={buildRuleNotFoundError:(e,t)=>"Invalid grammar, reference to a rule which is not defined: ->"+t.nonTerminalName+"<-\ninside top level rule: ->"+e.name+"<-"},en={buildDuplicateFoundError(e,t){const n=e.name,r=Ue(t),i=r.idx,s=oe(r),a=(o=r)instanceof H?o.terminalType.name:o instanceof M?o.nonTerminalName:"";var o;let c=`->${s}${i>0?i:""}<- ${a?`with argument: ->${a}<-`:""}\n                  appears more than once (${t.length} times) in the top level rule: ->${n}<-.                  \n                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES \n                  `;return c=c.replace(/[ \t]+/g," "),c=c.replace(/\s\s+/g,"\n"),c},buildNamespaceConflictError:e=>`Namespace conflict found in grammar.\nThe grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <${e.name}>.\nTo resolve this make sure each Terminal and Non-Terminal names are unique\nThis is easy to accomplish by using the convention that Terminal names start with an uppercase letter\nand Non-Terminal names start with a lower case letter.`,buildAlternationPrefixAmbiguityError(e){const t=(0,a.A)(e.prefixPath,(e=>Dt(e))).join(", "),n=0===e.alternation.idx?"":e.alternation.idx;return`Ambiguous alternatives: <${e.ambiguityIndices.join(" ,")}> due to common lookahead prefix\nin <OR${n}> inside <${e.topLevelRule.name}> Rule,\n<${t}> may appears as a prefix path in all these alternatives.\nSee: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX\nFor Further details.`},buildAlternationAmbiguityError(e){const t=(0,a.A)(e.prefixPath,(e=>Dt(e))).join(", "),n=0===e.alternation.idx?"":e.alternation.idx;let r=`Ambiguous Alternatives Detected: <${e.ambiguityIndices.join(" ,")}> in <OR${n}> inside <${e.topLevelRule.name}> Rule,\n<${t}> may appears as a prefix path in all these alternatives.\n`;return r+="See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES\nFor Further details.",r},buildEmptyRepetitionError(e){let t=oe(e.repetition);0!==e.repetition.idx&&(t+=e.repetition.idx);return`The repetition <${t}> within Rule <${e.topLevelRule.name}> can never consume any tokens.\nThis could lead to an infinite loop.`},buildTokenNameError:e=>"deprecated",buildEmptyAlternationError:e=>`Ambiguous empty alternative: <${e.emptyChoiceIdx+1}> in <OR${e.alternation.idx}> inside <${e.topLevelRule.name}> Rule.\nOnly the last alternative may be an empty alternative.`,buildTooManyAlternativesError:e=>`An Alternation cannot have more than 256 alternatives:\n<OR${e.alternation.idx}> inside <${e.topLevelRule.name}> Rule.\n has ${e.alternation.definition.length+1} alternatives.`,buildLeftRecursionError(e){const t=e.topLevelRule.name;return`Left Recursion found in grammar.\nrule: <${t}> can be invoked from itself (directly or indirectly)\nwithout consuming any Tokens. The grammar path that causes this is: \n ${`${t} --\x3e ${(0,a.A)(e.leftRecursionPath,(e=>e.name)).concat([t]).join(" --\x3e ")}`}\n To fix this refactor your grammar to remove the left recursion.\nsee: https://en.wikipedia.org/wiki/LL_parser#Left_factoring.`},buildInvalidRuleNameError:e=>"deprecated",buildDuplicateRuleNameError(e){let t;t=e.topLevelRule instanceof D?e.topLevelRule.name:e.topLevelRule;return`Duplicate definition, rule: ->${t}<- is already defined in the grammar: ->${e.grammarName}<-`}};class tn extends z{constructor(e,t){super(),this.nameToTopRule=e,this.errMsgProvider=t,this.errors=[]}resolveRefs(){(0,r.A)((0,i.A)(this.nameToTopRule),(e=>{this.currTopLevel=e,e.accept(this)}))}visitNonTerminal(e){const t=this.nameToTopRule[e.nonTerminalName];if(t)e.referencedRule=t;else{const t=this.errMsgProvider.buildRuleNotFoundError(this.currTopLevel,e);this.errors.push({message:t,type:Or.UNRESOLVED_SUBRULE_REF,ruleName:this.currTopLevel.name,unresolvedRefName:e.nonTerminalName})}}}var nn=n(8139),rn=n(2528);const sn=function(e,t,n,r){for(var i=-1,s=null==e?0:e.length;++i<s;){var a=e[i];t(r,a,n(a),e)}return r};const an=function(e,t,n,r){return(0,X.A)(e,(function(e,i,s){t(r,e,n(e),s)})),r};const on=function(e,t){return function(n,r){var i=(0,Q.A)(n)?sn:an,s=t?t():{};return i(n,e,(0,k.A)(r,2),s)}};var cn=Object.prototype.hasOwnProperty;const ln=on((function(e,t,n){cn.call(e,n)?e[n].push(t):(0,rn.A)(e,n,[t])}));const un=function(e,t,n){var r=null==e?0:e.length;return r?(t=n||void 0===t?1:(0,d.A)(t),u(e,0,(t=r-t)<0?0:t)):[]};class dn extends ce{constructor(e,t){super(),this.topProd=e,this.path=t,this.possibleTokTypes=[],this.nextProductionName="",this.nextProductionOccurrence=0,this.found=!1,this.isAtEndOfPath=!1}startWalking(){if(this.found=!1,this.path.ruleStack[0]!==this.topProd.name)throw Error("The path does not start with the walker's top Rule!");return this.ruleStack=(0,c.A)(this.path.ruleStack).reverse(),this.occurrenceStack=(0,c.A)(this.path.occurrenceStack).reverse(),this.ruleStack.pop(),this.occurrenceStack.pop(),this.updateExpectedNext(),this.walk(this.topProd),this.possibleTokTypes}walk(e,t=[]){this.found||super.walk(e,t)}walkProdRef(e,t,n){if(e.referencedRule.name===this.nextProductionName&&e.idx===this.nextProductionOccurrence){const r=t.concat(n);this.updateExpectedNext(),this.walk(e.referencedRule,r)}}updateExpectedNext(){(0,s.A)(this.ruleStack)?(this.nextProductionName="",this.nextProductionOccurrence=0,this.isAtEndOfPath=!0):(this.nextProductionName=this.ruleStack.pop(),this.nextProductionOccurrence=this.occurrenceStack.pop())}}class hn extends dn{constructor(e,t){super(e,t),this.path=t,this.nextTerminalName="",this.nextTerminalOccurrence=0,this.nextTerminalName=this.path.lastTok.name,this.nextTerminalOccurrence=this.path.lastTokOccurrence}walkTerminal(e,t,n){if(this.isAtEndOfPath&&e.terminalType.name===this.nextTerminalName&&e.idx===this.nextTerminalOccurrence&&!this.found){const e=t.concat(n),r=new U({definition:e});this.possibleTokTypes=fe(r),this.found=!0}}}class fn extends ce{constructor(e,t){super(),this.topRule=e,this.occurrence=t,this.result={token:void 0,occurrence:void 0,isEndOfRule:void 0}}startWalking(){return this.walk(this.topRule),this.result}}class pn extends fn{walkMany(e,t,n){if(e.idx===this.occurrence){const e=Ue(t.concat(n));this.result.isEndOfRule=void 0===e,e instanceof H&&(this.result.token=e.terminalType,this.result.occurrence=e.idx)}else super.walkMany(e,t,n)}}class mn extends fn{walkManySep(e,t,n){if(e.idx===this.occurrence){const e=Ue(t.concat(n));this.result.isEndOfRule=void 0===e,e instanceof H&&(this.result.token=e.terminalType,this.result.occurrence=e.idx)}else super.walkManySep(e,t,n)}}class gn extends fn{walkAtLeastOne(e,t,n){if(e.idx===this.occurrence){const e=Ue(t.concat(n));this.result.isEndOfRule=void 0===e,e instanceof H&&(this.result.token=e.terminalType,this.result.occurrence=e.idx)}else super.walkAtLeastOne(e,t,n)}}class yn extends fn{walkAtLeastOneSep(e,t,n){if(e.idx===this.occurrence){const e=Ue(t.concat(n));this.result.isEndOfRule=void 0===e,e instanceof H&&(this.result.token=e.terminalType,this.result.occurrence=e.idx)}else super.walkAtLeastOneSep(e,t,n)}}function Tn(e,t,n=[]){n=(0,c.A)(n);let i=[],a=0;function o(r){const s=Tn(r.concat(h(e,a+1)),t,n);return i.concat(s)}for(;n.length<t&&a<e.length;){const t=e[a];if(t instanceof U)return o(t.definition);if(t instanceof M)return o(t.definition);if(t instanceof F)i=o(t.definition);else{if(t instanceof G){return o(t.definition.concat([new B({definition:t.definition})]))}if(t instanceof K){return o([new U({definition:t.definition}),new B({definition:[new H({terminalType:t.separator})].concat(t.definition)})])}if(t instanceof j){const e=t.definition.concat([new B({definition:[new H({terminalType:t.separator})].concat(t.definition)})]);i=o(e)}else if(t instanceof B){const e=t.definition.concat([new B({definition:t.definition})]);i=o(e)}else{if(t instanceof V)return(0,r.A)(t.definition,(e=>{!1===(0,s.A)(e.definition)&&(i=o(e.definition))})),i;if(!(t instanceof H))throw Error("non exhaustive match");n.push(t.terminalType)}}a++}return i.push({partialPath:n,suffixDef:h(e,a)}),i}function An(e,t,n,r){const i="EXIT_NONE_TERMINAL",a=[i],o="EXIT_ALTERNATIVE";let l=!1;const u=t.length,d=u-r-1,f=[],p=[];for(p.push({idx:-1,def:e,ruleStack:[],occurrenceStack:[]});!(0,s.A)(p);){const e=p.pop();if(e===o){l&&(0,Et.A)(p).idx<=d&&p.pop();continue}const r=e.def,m=e.idx,g=e.ruleStack,y=e.occurrenceStack;if((0,s.A)(r))continue;const T=r[0];if(T===i){const e={idx:m,def:h(r),ruleStack:un(g),occurrenceStack:un(y)};p.push(e)}else if(T instanceof H)if(m<u-1){const e=m+1;if(n(t[e],T.terminalType)){const t={idx:e,def:h(r),ruleStack:g,occurrenceStack:y};p.push(t)}}else{if(m!==u-1)throw Error("non exhaustive match");f.push({nextTokenType:T.terminalType,nextTokenOccurrence:T.idx,ruleStack:g,occurrenceStack:y}),l=!0}else if(T instanceof M){const e=(0,c.A)(g);e.push(T.nonTerminalName);const t=(0,c.A)(y);t.push(T.idx);const n={idx:m,def:T.definition.concat(a,h(r)),ruleStack:e,occurrenceStack:t};p.push(n)}else if(T instanceof F){const e={idx:m,def:h(r),ruleStack:g,occurrenceStack:y};p.push(e),p.push(o);const t={idx:m,def:T.definition.concat(h(r)),ruleStack:g,occurrenceStack:y};p.push(t)}else if(T instanceof G){const e=new B({definition:T.definition,idx:T.idx}),t={idx:m,def:T.definition.concat([e],h(r)),ruleStack:g,occurrenceStack:y};p.push(t)}else if(T instanceof K){const e=new H({terminalType:T.separator}),t=new B({definition:[e].concat(T.definition),idx:T.idx}),n={idx:m,def:T.definition.concat([t],h(r)),ruleStack:g,occurrenceStack:y};p.push(n)}else if(T instanceof j){const e={idx:m,def:h(r),ruleStack:g,occurrenceStack:y};p.push(e),p.push(o);const t=new H({terminalType:T.separator}),n=new B({definition:[t].concat(T.definition),idx:T.idx}),i={idx:m,def:T.definition.concat([n],h(r)),ruleStack:g,occurrenceStack:y};p.push(i)}else if(T instanceof B){const e={idx:m,def:h(r),ruleStack:g,occurrenceStack:y};p.push(e),p.push(o);const t=new B({definition:T.definition,idx:T.idx}),n={idx:m,def:T.definition.concat([t],h(r)),ruleStack:g,occurrenceStack:y};p.push(n)}else if(T instanceof V)for(let t=T.definition.length-1;t>=0;t--){const e={idx:m,def:T.definition[t].definition.concat(h(r)),ruleStack:g,occurrenceStack:y};p.push(e),p.push(o)}else if(T instanceof U)p.push({idx:m,def:T.definition.concat(h(r)),ruleStack:g,occurrenceStack:y});else{if(!(T instanceof D))throw Error("non exhaustive match");p.push(vn(T,m,g,y))}}return f}function vn(e,t,n,r){const i=(0,c.A)(n);i.push(e.name);const s=(0,c.A)(r);return s.push(1),{idx:t,def:e.definition,ruleStack:i,occurrenceStack:s}}var Rn;function En(e){if(e instanceof F||"Option"===e)return Rn.OPTION;if(e instanceof B||"Repetition"===e)return Rn.REPETITION;if(e instanceof G||"RepetitionMandatory"===e)return Rn.REPETITION_MANDATORY;if(e instanceof K||"RepetitionMandatoryWithSeparator"===e)return Rn.REPETITION_MANDATORY_WITH_SEPARATOR;if(e instanceof j||"RepetitionWithSeparator"===e)return Rn.REPETITION_WITH_SEPARATOR;if(e instanceof V||"Alternation"===e)return Rn.ALTERNATION;throw Error("non exhaustive match")}function kn(e){const{occurrence:t,rule:n,prodType:r,maxLookahead:i}=e,s=En(r);return s===Rn.ALTERNATION?bn(t,n,i):On(t,n,s,i)}function $n(e,t,n,i){const s=e.length,c=se(e,(e=>se(e,(e=>1===e.length))));if(t)return function(t){const r=(0,a.A)(t,(e=>e.GATE));for(let i=0;i<s;i++){const t=e[i],s=t.length,a=r[i];if(void 0===a||!1!==a.call(this))e:for(let e=0;e<s;e++){const r=t[e],s=r.length;for(let e=0;e<s;e++){const t=this.LA(e+1);if(!1===n(t,r[e]))continue e}return i}}};if(c&&!i){const t=(0,a.A)(e,(e=>(0,he.A)(e))),n=(0,Ie.A)(t,((e,t,n)=>((0,r.A)(t,(t=>{(0,o.A)(e,t.tokenTypeIdx)||(e[t.tokenTypeIdx]=n),(0,r.A)(t.categoryMatches,(t=>{(0,o.A)(e,t)||(e[t]=n)}))})),e)),{});return function(){const e=this.LA(1);return n[e.tokenTypeIdx]}}return function(){for(let t=0;t<s;t++){const r=e[t],i=r.length;e:for(let e=0;e<i;e++){const i=r[e],s=i.length;for(let e=0;e<s;e++){const t=this.LA(e+1);if(!1===n(t,i[e]))continue e}return t}}}}function xn(e,t,n){const i=se(e,(e=>1===e.length)),a=e.length;if(i&&!n){const t=(0,he.A)(e);if(1===t.length&&(0,s.A)(t[0].categoryMatches)){const e=t[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===e}}{const e=(0,Ie.A)(t,((e,t,n)=>(e[t.tokenTypeIdx]=!0,(0,r.A)(t.categoryMatches,(t=>{e[t]=!0})),e)),[]);return function(){const t=this.LA(1);return!0===e[t.tokenTypeIdx]}}}return function(){e:for(let n=0;n<a;n++){const r=e[n],i=r.length;for(let e=0;e<i;e++){const n=this.LA(e+1);if(!1===t(n,r[e]))continue e}return!0}return!1}}!function(e){e[e.OPTION=0]="OPTION",e[e.REPETITION=1]="REPETITION",e[e.REPETITION_MANDATORY=2]="REPETITION_MANDATORY",e[e.REPETITION_MANDATORY_WITH_SEPARATOR=3]="REPETITION_MANDATORY_WITH_SEPARATOR",e[e.REPETITION_WITH_SEPARATOR=4]="REPETITION_WITH_SEPARATOR",e[e.ALTERNATION=5]="ALTERNATION"}(Rn||(Rn={}));class In extends ce{constructor(e,t,n){super(),this.topProd=e,this.targetOccurrence=t,this.targetProdType=n}startWalking(){return this.walk(this.topProd),this.restDef}checkIsTarget(e,t,n,r){return e.idx===this.targetOccurrence&&this.targetProdType===t&&(this.restDef=n.concat(r),!0)}walkOption(e,t,n){this.checkIsTarget(e,Rn.OPTION,t,n)||super.walkOption(e,t,n)}walkAtLeastOne(e,t,n){this.checkIsTarget(e,Rn.REPETITION_MANDATORY,t,n)||super.walkOption(e,t,n)}walkAtLeastOneSep(e,t,n){this.checkIsTarget(e,Rn.REPETITION_MANDATORY_WITH_SEPARATOR,t,n)||super.walkOption(e,t,n)}walkMany(e,t,n){this.checkIsTarget(e,Rn.REPETITION,t,n)||super.walkOption(e,t,n)}walkManySep(e,t,n){this.checkIsTarget(e,Rn.REPETITION_WITH_SEPARATOR,t,n)||super.walkOption(e,t,n)}}class Sn extends z{constructor(e,t,n){super(),this.targetOccurrence=e,this.targetProdType=t,this.targetRef=n,this.result=[]}checkIsTarget(e,t){e.idx!==this.targetOccurrence||this.targetProdType!==t||void 0!==this.targetRef&&e!==this.targetRef||(this.result=e.definition)}visitOption(e){this.checkIsTarget(e,Rn.OPTION)}visitRepetition(e){this.checkIsTarget(e,Rn.REPETITION)}visitRepetitionMandatory(e){this.checkIsTarget(e,Rn.REPETITION_MANDATORY)}visitRepetitionMandatoryWithSeparator(e){this.checkIsTarget(e,Rn.REPETITION_MANDATORY_WITH_SEPARATOR)}visitRepetitionWithSeparator(e){this.checkIsTarget(e,Rn.REPETITION_WITH_SEPARATOR)}visitAlternation(e){this.checkIsTarget(e,Rn.ALTERNATION)}}function Nn(e){const t=new Array(e);for(let n=0;n<e;n++)t[n]=[];return t}function Cn(e){let t=[""];for(let n=0;n<e.length;n++){const r=e[n],i=[];for(let e=0;e<t.length;e++){const n=t[e];i.push(n+"_"+r.tokenTypeIdx);for(let e=0;e<r.categoryMatches.length;e++){const t="_"+r.categoryMatches[e];i.push(n+t)}}t=i}return t}function wn(e,t,n){for(let r=0;r<e.length;r++){if(r===n)continue;const i=e[r];for(let e=0;e<t.length;e++){if(!0===i[t[e]])return!1}}return!0}function Ln(e,t){const n=(0,a.A)(e,(e=>Tn([e],1))),i=Nn(n.length),o=(0,a.A)(n,(e=>{const t={};return(0,r.A)(e,(e=>{const n=Cn(e.partialPath);(0,r.A)(n,(e=>{t[e]=!0}))})),t}));let c=n;for(let a=1;a<=t;a++){const e=c;c=Nn(e.length);for(let n=0;n<e.length;n++){const l=e[n];for(let e=0;e<l.length;e++){const u=l[e].partialPath,d=l[e].suffixDef,h=Cn(u);if(wn(o,h,n)||(0,s.A)(d)||u.length===t){const e=i[n];if(!1===_n(e,u)){e.push(u);for(let e=0;e<h.length;e++){const t=h[e];o[n][t]=!0}}}else{const e=Tn(d,a+1,u);c[n]=c[n].concat(e),(0,r.A)(e,(e=>{const t=Cn(e.partialPath);(0,r.A)(t,(e=>{o[n][e]=!0}))}))}}}}return i}function bn(e,t,n,r){const i=new Sn(e,Rn.ALTERNATION,r);return t.accept(i),Ln(i.result,n)}function On(e,t,n,r){const i=new Sn(e,n);t.accept(i);const s=i.result,a=new In(t,e,n).startWalking();return Ln([new U({definition:s}),new U({definition:a})],r)}function _n(e,t){e:for(let n=0;n<e.length;n++){const r=e[n];if(r.length===t.length){for(let e=0;e<r.length;e++){const n=t[e],i=r[e];if(!1===(n===i||void 0!==i.categoryMatchesMap[n.tokenTypeIdx]))continue e}return!0}}return!1}function Pn(e){return se(e,(e=>se(e,(e=>se(e,(e=>(0,s.A)(e.categoryMatches)))))))}function Mn(e,t,n,s){const o=(0,nn.A)(e,(e=>function(e,t){const n=new Fn;e.accept(n);const r=n.allProductions,s=ln(r,Dn),o=I(s,(e=>e.length>1)),c=(0,a.A)((0,i.A)(o),(n=>{const r=Ue(n),i=t.buildDuplicateFoundError(e,n),s=oe(r),a={message:i,type:Or.DUPLICATE_PRODUCTIONS,ruleName:e.name,dslName:s,occurrence:r.idx},o=Un(r);return o&&(a.parameter=o),a}));return c}(e,n))),c=function(e,t,n){const i=[],s=(0,a.A)(t,(e=>e.name));return(0,r.A)(e,(e=>{const t=e.name;if(ne(s,t)){const r=n.buildNamespaceConflictError(e);i.push({message:r,type:Or.CONFLICT_TOKENS_RULES_NAMESPACE,ruleName:t})}})),i}(e,t,n),l=(0,nn.A)(e,(e=>function(e,t){const n=new Bn;e.accept(n);const r=n.alternations,i=(0,nn.A)(r,(n=>n.definition.length>255?[{message:t.buildTooManyAlternativesError({topLevelRule:e,alternation:n}),type:Or.TOO_MANY_ALTS,ruleName:e.name,occurrence:n.idx}]:[]));return i}(e,n))),u=(0,nn.A)(e,(t=>function(e,t,n,r){const i=[],s=(0,Ie.A)(t,((t,n)=>n.name===e.name?t+1:t),0);if(s>1){const t=r.buildDuplicateRuleNameError({topLevelRule:e,grammarName:n});i.push({message:t,type:Or.DUPLICATE_RULE_NAME,ruleName:e.name})}return i}(t,e,s,n)));return o.concat(c,l,u)}function Dn(e){return`${oe(e)}_#_${e.idx}_#_${Un(e)}`}function Un(e){return e instanceof H?e.terminalType.name:e instanceof M?e.nonTerminalName:""}class Fn extends z{constructor(){super(...arguments),this.allProductions=[]}visitNonTerminal(e){this.allProductions.push(e)}visitOption(e){this.allProductions.push(e)}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}visitAlternation(e){this.allProductions.push(e)}visitTerminal(e){this.allProductions.push(e)}}function Gn(e,t,n,r=[]){const i=[],a=Kn(t.definition);if((0,s.A)(a))return[];{const t=e.name;ne(a,e)&&i.push({message:n.buildLeftRecursionError({topLevelRule:e,leftRecursionPath:r}),type:Or.LEFT_RECURSION,ruleName:t});const s=Me(a,r.concat([e])),o=(0,nn.A)(s,(t=>{const i=(0,c.A)(r);return i.push(t),Gn(e,t,n,i)}));return i.concat(o)}}function Kn(e){let t=[];if((0,s.A)(e))return t;const n=Ue(e);if(n instanceof M)t.push(n.referencedRule);else if(n instanceof U||n instanceof F||n instanceof G||n instanceof K||n instanceof j||n instanceof B)t=t.concat(Kn(n.definition));else if(n instanceof V)t=(0,he.A)((0,a.A)(n.definition,(e=>Kn(e.definition))));else if(!(n instanceof H))throw Error("non exhaustive match");const r=ae(n),i=e.length>1;if(r&&i){const n=h(e);return t.concat(Kn(n))}return t}class Bn extends z{constructor(){super(...arguments),this.alternations=[]}visitAlternation(e){this.alternations.push(e)}}function jn(e,t,n){const i=new Bn;e.accept(i);let s=i.alternations;s=Ee(s,(e=>!0===e.ignoreAmbiguities));const o=(0,nn.A)(s,(i=>{const s=i.idx,o=i.maxLookahead||t,c=bn(s,e,o,i),l=function(e,t,n,i){const s=[],o=(0,Ie.A)(e,((n,i,a)=>(!0===t.definition[a].ignoreAmbiguities||(0,r.A)(i,(i=>{const o=[a];(0,r.A)(e,((e,n)=>{a!==n&&_n(e,i)&&!0!==t.definition[n].ignoreAmbiguities&&o.push(n)})),o.length>1&&!_n(s,i)&&(s.push(i),n.push({alts:o,path:i}))})),n)),[]),c=(0,a.A)(o,(e=>{const r=(0,a.A)(e.alts,(e=>e+1));return{message:i.buildAlternationAmbiguityError({topLevelRule:n,alternation:t,ambiguityIndices:r,prefixPath:e.path}),type:Or.AMBIGUOUS_ALTS,ruleName:n.name,occurrence:t.idx,alternatives:e.alts}}));return c}(c,i,e,n),u=function(e,t,n,r){const i=(0,Ie.A)(e,((e,t,n)=>{const r=(0,a.A)(t,(e=>({idx:n,path:e})));return e.concat(r)}),[]),s=De((0,nn.A)(i,(e=>{if(!0===t.definition[e.idx].ignoreAmbiguities)return[];const s=e.idx,o=e.path,c=(0,Se.A)(i,(e=>{return!0!==t.definition[e.idx].ignoreAmbiguities&&e.idx<s&&(n=e.path,r=o,n.length<r.length&&se(n,((e,t)=>{const n=r[t];return e===n||n.categoryMatchesMap[e.tokenTypeIdx]})));var n,r}));return(0,a.A)(c,(e=>{const i=[e.idx+1,s+1],a=0===t.idx?"":t.idx;return{message:r.buildAlternationPrefixAmbiguityError({topLevelRule:n,alternation:t,ambiguityIndices:i,prefixPath:e.path}),type:Or.AMBIGUOUS_PREFIX_ALTS,ruleName:n.name,occurrence:a,alternatives:i}}))})));return s}(c,i,e,n);return l.concat(u)}));return o}class Vn extends z{constructor(){super(...arguments),this.allProductions=[]}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}}function Hn(e){const t=(0,Te.A)(e,{errMsgProvider:Jt}),n={};return(0,r.A)(e.rules,(e=>{n[e.name]=e})),function(e,t){const n=new tn(e,t);return n.resolveRefs(),n.errors}(n,t.errMsgProvider)}const Wn="MismatchedTokenException",zn="NoViableAltException",Yn="EarlyExitException",Xn="NotAllInputParsedException",qn=[Wn,zn,Yn,Xn];function Qn(e){return ne(qn,e.name)}Object.freeze(qn);class Zn extends Error{constructor(e,t){super(e),this.token=t,this.resyncedTokens=[],Object.setPrototypeOf(this,new.target.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}}class Jn extends Zn{constructor(e,t,n){super(e,t),this.previousToken=n,this.name=Wn}}class er extends Zn{constructor(e,t,n){super(e,t),this.previousToken=n,this.name=zn}}class tr extends Zn{constructor(e,t){super(e,t),this.name=Xn}}class nr extends Zn{constructor(e,t,n){super(e,t),this.previousToken=n,this.name=Yn}}const rr={},ir="InRuleRecoveryException";class sr extends Error{constructor(e){super(e),this.name=ir}}function ar(e,t,n,r,i,s,a){const o=this.getKeyForAutomaticLookahead(r,i);let c=this.firstAfterRepMap[o];if(void 0===c){const e=this.getCurrRuleFullName();c=new s(this.getGAstProductions()[e],i).startWalking(),this.firstAfterRepMap[o]=c}let l=c.token,u=c.occurrence;const d=c.isEndOfRule;1===this.RULE_STACK.length&&d&&void 0===l&&(l=Xt,u=1),void 0!==l&&void 0!==u&&this.shouldInRepetitionRecoveryBeTried(l,u,a)&&this.tryInRepetitionRecovery(e,t,n,l)}const or=1024,cr=1280,lr=1536;function ur(e,t,n){return n|t|e}class dr{constructor(e){var t;this.maxLookahead=null!==(t=null==e?void 0:e.maxLookahead)&&void 0!==t?t:Lr.maxLookahead}validate(e){const t=this.validateNoLeftRecursion(e.rules);if((0,s.A)(t)){const n=this.validateEmptyOrAlternatives(e.rules),r=this.validateAmbiguousAlternationAlternatives(e.rules,this.maxLookahead),i=this.validateSomeNonEmptyLookaheadPath(e.rules,this.maxLookahead);return[...t,...n,...r,...i]}return t}validateNoLeftRecursion(e){return(0,nn.A)(e,(e=>Gn(e,e,en)))}validateEmptyOrAlternatives(e){return(0,nn.A)(e,(e=>function(e,t){const n=new Bn;e.accept(n);const r=n.alternations;return(0,nn.A)(r,(n=>{const r=un(n.definition);return(0,nn.A)(r,((r,i)=>{const a=An([r],[],$t,1);return(0,s.A)(a)?[{message:t.buildEmptyAlternationError({topLevelRule:e,alternation:n,emptyChoiceIdx:i}),type:Or.NONE_LAST_EMPTY_ALT,ruleName:e.name,occurrence:n.idx,alternative:i+1}]:[]}))}))}(e,en)))}validateAmbiguousAlternationAlternatives(e,t){return(0,nn.A)(e,(e=>jn(e,t,en)))}validateSomeNonEmptyLookaheadPath(e,t){return function(e,t,n){const i=[];return(0,r.A)(e,(e=>{const a=new Vn;e.accept(a);const o=a.allProductions;(0,r.A)(o,(r=>{const a=En(r),o=r.maxLookahead||t,c=On(r.idx,e,a,o)[0];if((0,s.A)((0,he.A)(c))){const t=n.buildEmptyRepetitionError({topLevelRule:e,repetition:r});i.push({message:t,type:Or.NO_NON_EMPTY_LOOKAHEAD,ruleName:e.name})}}))})),i}(e,t,en)}buildLookaheadForAlternation(e){return function(e,t,n,r,i,s){const a=bn(e,t,n);return s(a,r,Pn(a)?xt:$t,i)}(e.prodOccurrence,e.rule,e.maxLookahead,e.hasPredicates,e.dynamicTokensEnabled,$n)}buildLookaheadForOptional(e){return function(e,t,n,r,i,s){const a=On(e,t,i,n),o=Pn(a)?xt:$t;return s(a[0],o,r)}(e.prodOccurrence,e.rule,e.maxLookahead,e.dynamicTokensEnabled,En(e.prodType),xn)}}const hr=new class extends z{constructor(){super(...arguments),this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}reset(){this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}visitOption(e){this.dslMethods.option.push(e)}visitRepetitionWithSeparator(e){this.dslMethods.repetitionWithSeparator.push(e)}visitRepetitionMandatory(e){this.dslMethods.repetitionMandatory.push(e)}visitRepetitionMandatoryWithSeparator(e){this.dslMethods.repetitionMandatoryWithSeparator.push(e)}visitRepetition(e){this.dslMethods.repetition.push(e)}visitAlternation(e){this.dslMethods.alternation.push(e)}};function fr(e,t){!0===isNaN(e.startOffset)?(e.startOffset=t.startOffset,e.endOffset=t.endOffset):e.endOffset<t.endOffset==!0&&(e.endOffset=t.endOffset)}function pr(e,t){!0===isNaN(e.startOffset)?(e.startOffset=t.startOffset,e.startColumn=t.startColumn,e.startLine=t.startLine,e.endOffset=t.endOffset,e.endColumn=t.endColumn,e.endLine=t.endLine):e.endOffset<t.endOffset==!0&&(e.endOffset=t.endOffset,e.endColumn=t.endColumn,e.endLine=t.endLine)}function mr(e,t){Object.defineProperty(e,"name",{enumerable:!1,configurable:!0,writable:!1,value:t})}function gr(e,t){const n=(0,A.A)(e),r=n.length;for(let i=0;i<r;i++){const r=e[n[i]],s=r.length;for(let e=0;e<s;e++){const n=r[e];void 0===n.tokenTypeIdx&&this[n.name](n.children,t)}}}function yr(e,t){const n=function(){};mr(n,e+"BaseSemantics");const r={visit:function(e,t){if((0,Q.A)(e)&&(e=e[0]),!(0,ge.A)(e))return this[e.name](e.children,t)},validateVisitor:function(){const e=function(e,t){const n=function(e,t){const n=(0,Se.A)(t,(t=>!1===(0,ke.A)(e[t]))),r=(0,a.A)(n,(t=>({msg:`Missing visitor method: <${t}> on ${e.constructor.name} CST Visitor.`,type:Tr.MISSING_METHOD,methodName:t})));return De(r)}(e,t);return n}(this,t);if(!(0,s.A)(e)){const t=(0,a.A)(e,(e=>e.msg));throw Error(`Errors Detected in CST Visitor <${this.constructor.name}>:\n\t${t.join("\n\n").replace(/\n/g,"\n\t")}`)}}};return(n.prototype=r).constructor=n,n._RULE_NAMES=t,n}var Tr;!function(e){e[e.REDUNDANT_METHOD=0]="REDUNDANT_METHOD",e[e.MISSING_METHOD=1]="MISSING_METHOD"}(Tr||(Tr={}));var Ar=n(3149);const vr={description:"This Object indicates the Parser is during Recording Phase"};Object.freeze(vr);const Rr=!0,Er=Math.pow(2,8)-1,kr=Yt({name:"RECORDING_PHASE_TOKEN",pattern:Mt.NA});Nt([kr]);const $r=qt(kr,"This IToken indicates the Parser is in Recording Phase\n\tSee: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details",-1,-1,-1,-1,-1,-1);Object.freeze($r);const xr={name:"This CSTNode indicates the Parser is in Recording Phase\n\tSee: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details",children:{}};function Ir(e,t,n,r=!1){Cr(n);const i=(0,Et.A)(this.recordingProdStack),s=(0,ke.A)(t)?t:t.DEF,a=new e({definition:[],idx:n});return r&&(a.separator=t.SEP),(0,o.A)(t,"MAX_LOOKAHEAD")&&(a.maxLookahead=t.MAX_LOOKAHEAD),this.recordingProdStack.push(a),s.call(this),i.definition.push(a),this.recordingProdStack.pop(),vr}function Sr(e,t){Cr(t);const n=(0,Et.A)(this.recordingProdStack),i=!1===(0,Q.A)(e),s=!1===i?e:e.DEF,a=new V({definition:[],idx:t,ignoreAmbiguities:i&&!0===e.IGNORE_AMBIGUITIES});(0,o.A)(e,"MAX_LOOKAHEAD")&&(a.maxLookahead=e.MAX_LOOKAHEAD);const c=J(s,(e=>(0,ke.A)(e.GATE)));return a.hasPredicates=c,n.definition.push(a),(0,r.A)(s,(e=>{const t=new U({definition:[]});a.definition.push(t),(0,o.A)(e,"IGNORE_AMBIGUITIES")?t.ignoreAmbiguities=e.IGNORE_AMBIGUITIES:(0,o.A)(e,"GATE")&&(t.ignoreAmbiguities=!0),this.recordingProdStack.push(t),e.ALT.call(this),this.recordingProdStack.pop()})),vr}function Nr(e){return 0===e?"":`${e}`}function Cr(e){if(e<0||e>Er){const t=new Error(`Invalid DSL Method idx value: <${e}>\n\tIdx value must be a none negative value smaller than ${Er+1}`);throw t.KNOWN_RECORDER_ERROR=!0,t}}const wr=qt(Xt,"",NaN,NaN,NaN,NaN,NaN,NaN);Object.freeze(wr);const Lr=Object.freeze({recoveryEnabled:!1,maxLookahead:3,dynamicTokensEnabled:!1,outputCst:!0,errorMessageProvider:Zt,nodeLocationTracking:"none",traceInitPerf:!1,skipValidations:!1}),br=Object.freeze({recoveryValueFunc:()=>{},resyncEnabled:!0});var Or,_r;function Pr(e=void 0){return function(){return e}}!function(e){e[e.INVALID_RULE_NAME=0]="INVALID_RULE_NAME",e[e.DUPLICATE_RULE_NAME=1]="DUPLICATE_RULE_NAME",e[e.INVALID_RULE_OVERRIDE=2]="INVALID_RULE_OVERRIDE",e[e.DUPLICATE_PRODUCTIONS=3]="DUPLICATE_PRODUCTIONS",e[e.UNRESOLVED_SUBRULE_REF=4]="UNRESOLVED_SUBRULE_REF",e[e.LEFT_RECURSION=5]="LEFT_RECURSION",e[e.NONE_LAST_EMPTY_ALT=6]="NONE_LAST_EMPTY_ALT",e[e.AMBIGUOUS_ALTS=7]="AMBIGUOUS_ALTS",e[e.CONFLICT_TOKENS_RULES_NAMESPACE=8]="CONFLICT_TOKENS_RULES_NAMESPACE",e[e.INVALID_TOKEN_NAME=9]="INVALID_TOKEN_NAME",e[e.NO_NON_EMPTY_LOOKAHEAD=10]="NO_NON_EMPTY_LOOKAHEAD",e[e.AMBIGUOUS_PREFIX_ALTS=11]="AMBIGUOUS_PREFIX_ALTS",e[e.TOO_MANY_ALTS=12]="TOO_MANY_ALTS",e[e.CUSTOM_LOOKAHEAD_VALIDATION=13]="CUSTOM_LOOKAHEAD_VALIDATION"}(Or||(Or={}));class Mr{static performSelfAnalysis(e){throw Error("The **static** `performSelfAnalysis` method has been deprecated.\t\nUse the **instance** method with the same name instead.")}performSelfAnalysis(){this.TRACE_INIT("performSelfAnalysis",(()=>{let e;this.selfAnalysisDone=!0;const t=this.className;this.TRACE_INIT("toFastProps",(()=>{l(this)})),this.TRACE_INIT("Grammar Recording",(()=>{try{this.enableRecording(),(0,r.A)(this.definedRulesNames,(e=>{const t=this[e].originalGrammarAction;let n;this.TRACE_INIT(`${e} Rule`,(()=>{n=this.topLevelRuleRecord(e,t)})),this.gastProductionsCache[e]=n}))}finally{this.disableRecording()}}));let n=[];if(this.TRACE_INIT("Grammar Resolving",(()=>{n=Hn({rules:(0,i.A)(this.gastProductionsCache)}),this.definitionErrors=this.definitionErrors.concat(n)})),this.TRACE_INIT("Grammar Validations",(()=>{if((0,s.A)(n)&&!1===this.skipValidations){const n=(e={rules:(0,i.A)(this.gastProductionsCache),tokenTypes:(0,i.A)(this.tokensMap),errMsgProvider:en,grammarName:t},Mn((e=(0,Te.A)(e,{errMsgProvider:en})).rules,e.tokenTypes,e.errMsgProvider,e.grammarName)),r=function(e){const t=e.lookaheadStrategy.validate({rules:e.rules,tokenTypes:e.tokenTypes,grammarName:e.grammarName});return(0,a.A)(t,(e=>Object.assign({type:Or.CUSTOM_LOOKAHEAD_VALIDATION},e)))}({lookaheadStrategy:this.lookaheadStrategy,rules:(0,i.A)(this.gastProductionsCache),tokenTypes:(0,i.A)(this.tokensMap),grammarName:t});this.definitionErrors=this.definitionErrors.concat(n,r)}var e})),(0,s.A)(this.definitionErrors)&&(this.recoveryEnabled&&this.TRACE_INIT("computeAllProdsFollows",(()=>{const e=function(e){const t={};return(0,r.A)(e,(e=>{const n=new me(e).startWalking();R(t,n)})),t}((0,i.A)(this.gastProductionsCache));this.resyncFollows=e})),this.TRACE_INIT("ComputeLookaheadFunctions",(()=>{var e,t;null===(t=(e=this.lookaheadStrategy).initialize)||void 0===t||t.call(e,{rules:(0,i.A)(this.gastProductionsCache)}),this.preComputeLookaheadFunctions((0,i.A)(this.gastProductionsCache))}))),!Mr.DEFER_DEFINITION_ERRORS_HANDLING&&!(0,s.A)(this.definitionErrors))throw e=(0,a.A)(this.definitionErrors,(e=>e.message)),new Error(`Parser Definition Errors detected:\n ${e.join("\n-------------------------------\n")}`)}))}constructor(e,t){this.definitionErrors=[],this.selfAnalysisDone=!1;const n=this;if(n.initErrorHandler(t),n.initLexerAdapter(),n.initLooksAhead(t),n.initRecognizerEngine(e,t),n.initRecoverable(t),n.initTreeBuilder(t),n.initContentAssist(),n.initGastRecorder(t),n.initPerformanceTracer(t),(0,o.A)(t,"ignoredIssues"))throw new Error("The <ignoredIssues> IParserConfig property has been deprecated.\n\tPlease use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.\n\tSee: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES\n\tFor further details.");this.skipValidations=(0,o.A)(t,"skipValidations")?t.skipValidations:Lr.skipValidations}}Mr.DEFER_DEFINITION_ERRORS_HANDLING=!1,_r=Mr,[class{initRecoverable(e){this.firstAfterRepMap={},this.resyncFollows={},this.recoveryEnabled=(0,o.A)(e,"recoveryEnabled")?e.recoveryEnabled:Lr.recoveryEnabled,this.recoveryEnabled&&(this.attemptInRepetitionRecovery=ar)}getTokenToInsert(e){const t=qt(e,"",NaN,NaN,NaN,NaN,NaN,NaN);return t.isInsertedInRecovery=!0,t}canTokenTypeBeInsertedInRecovery(e){return!0}canTokenTypeBeDeletedInRecovery(e){return!0}tryInRepetitionRecovery(e,t,n,r){const i=this.findReSyncTokenType(),s=this.exportLexerState(),a=[];let o=!1;const c=this.LA(1);let l=this.LA(1);const u=()=>{const e=this.LA(0),t=this.errorMessageProvider.buildMismatchTokenMessage({expected:r,actual:c,previous:e,ruleName:this.getCurrRuleFullName()}),n=new Jn(t,c,this.LA(0));n.resyncedTokens=un(a),this.SAVE_ERROR(n)};for(;!o;){if(this.tokenMatcher(l,r))return void u();if(n.call(this))return u(),void e.apply(this,t);this.tokenMatcher(l,i)?o=!0:(l=this.SKIP_TOKEN(),this.addToResyncTokens(l,a))}this.importLexerState(s)}shouldInRepetitionRecoveryBeTried(e,t,n){return!1!==n&&!this.tokenMatcher(this.LA(1),e)&&!this.isBackTracking()&&!this.canPerformInRuleRecovery(e,this.getFollowsForInRuleRecovery(e,t))}getFollowsForInRuleRecovery(e,t){const n=this.getCurrentGrammarPath(e,t);return this.getNextPossibleTokenTypes(n)}tryInRuleRecovery(e,t){if(this.canRecoverWithSingleTokenInsertion(e,t))return this.getTokenToInsert(e);if(this.canRecoverWithSingleTokenDeletion(e)){const e=this.SKIP_TOKEN();return this.consumeToken(),e}throw new sr("sad sad panda")}canPerformInRuleRecovery(e,t){return this.canRecoverWithSingleTokenInsertion(e,t)||this.canRecoverWithSingleTokenDeletion(e)}canRecoverWithSingleTokenInsertion(e,t){if(!this.canTokenTypeBeInsertedInRecovery(e))return!1;if((0,s.A)(t))return!1;const n=this.LA(1);return void 0!==(0,Fe.A)(t,(e=>this.tokenMatcher(n,e)))}canRecoverWithSingleTokenDeletion(e){return!!this.canTokenTypeBeDeletedInRecovery(e)&&this.tokenMatcher(this.LA(2),e)}isInCurrentRuleReSyncSet(e){const t=this.getCurrFollowKey(),n=this.getFollowSetFromFollowKey(t);return ne(n,e)}findReSyncTokenType(){const e=this.flattenFollowSet();let t=this.LA(1),n=2;for(;;){const r=(0,Fe.A)(e,(e=>Qt(t,e)));if(void 0!==r)return r;t=this.LA(n),n++}}getCurrFollowKey(){if(1===this.RULE_STACK.length)return rr;const e=this.getLastExplicitRuleShortName(),t=this.getLastExplicitRuleOccurrenceIndex(),n=this.getPreviousExplicitRuleShortName();return{ruleName:this.shortRuleNameToFullName(e),idxInCallingRule:t,inRule:this.shortRuleNameToFullName(n)}}buildFullFollowKeyStack(){const e=this.RULE_STACK,t=this.RULE_OCCURRENCE_STACK;return(0,a.A)(e,((n,r)=>0===r?rr:{ruleName:this.shortRuleNameToFullName(n),idxInCallingRule:t[r],inRule:this.shortRuleNameToFullName(e[r-1])}))}flattenFollowSet(){const e=(0,a.A)(this.buildFullFollowKeyStack(),(e=>this.getFollowSetFromFollowKey(e)));return(0,he.A)(e)}getFollowSetFromFollowKey(e){if(e===rr)return[Xt];const t=e.ruleName+e.idxInCallingRule+pe+e.inRule;return this.resyncFollows[t]}addToResyncTokens(e,t){return this.tokenMatcher(e,Xt)||t.push(e),t}reSyncTo(e){const t=[];let n=this.LA(1);for(;!1===this.tokenMatcher(n,e);)n=this.SKIP_TOKEN(),this.addToResyncTokens(n,t);return un(t)}attemptInRepetitionRecovery(e,t,n,r,i,s,a){}getCurrentGrammarPath(e,t){return{ruleStack:this.getHumanReadableRuleStack(),occurrenceStack:(0,c.A)(this.RULE_OCCURRENCE_STACK),lastTok:e,lastTokOccurrence:t}}getHumanReadableRuleStack(){return(0,a.A)(this.RULE_STACK,(e=>this.shortRuleNameToFullName(e)))}},class{initLooksAhead(e){this.dynamicTokensEnabled=(0,o.A)(e,"dynamicTokensEnabled")?e.dynamicTokensEnabled:Lr.dynamicTokensEnabled,this.maxLookahead=(0,o.A)(e,"maxLookahead")?e.maxLookahead:Lr.maxLookahead,this.lookaheadStrategy=(0,o.A)(e,"lookaheadStrategy")?e.lookaheadStrategy:new dr({maxLookahead:this.maxLookahead}),this.lookAheadFuncsCache=new Map}preComputeLookaheadFunctions(e){(0,r.A)(e,(e=>{this.TRACE_INIT(`${e.name} Rule Lookahead`,(()=>{const{alternation:t,repetition:n,option:i,repetitionMandatory:s,repetitionMandatoryWithSeparator:a,repetitionWithSeparator:o}=function(e){hr.reset(),e.accept(hr);const t=hr.dslMethods;return hr.reset(),t}(e);(0,r.A)(t,(t=>{const n=0===t.idx?"":t.idx;this.TRACE_INIT(`${oe(t)}${n}`,(()=>{const n=this.lookaheadStrategy.buildLookaheadForAlternation({prodOccurrence:t.idx,rule:e,maxLookahead:t.maxLookahead||this.maxLookahead,hasPredicates:t.hasPredicates,dynamicTokensEnabled:this.dynamicTokensEnabled}),r=ur(this.fullRuleNameToShort[e.name],256,t.idx);this.setLaFuncCache(r,n)}))})),(0,r.A)(n,(t=>{this.computeLookaheadFunc(e,t.idx,768,"Repetition",t.maxLookahead,oe(t))})),(0,r.A)(i,(t=>{this.computeLookaheadFunc(e,t.idx,512,"Option",t.maxLookahead,oe(t))})),(0,r.A)(s,(t=>{this.computeLookaheadFunc(e,t.idx,or,"RepetitionMandatory",t.maxLookahead,oe(t))})),(0,r.A)(a,(t=>{this.computeLookaheadFunc(e,t.idx,lr,"RepetitionMandatoryWithSeparator",t.maxLookahead,oe(t))})),(0,r.A)(o,(t=>{this.computeLookaheadFunc(e,t.idx,cr,"RepetitionWithSeparator",t.maxLookahead,oe(t))}))}))}))}computeLookaheadFunc(e,t,n,r,i,s){this.TRACE_INIT(`${s}${0===t?"":t}`,(()=>{const s=this.lookaheadStrategy.buildLookaheadForOptional({prodOccurrence:t,rule:e,maxLookahead:i||this.maxLookahead,dynamicTokensEnabled:this.dynamicTokensEnabled,prodType:r}),a=ur(this.fullRuleNameToShort[e.name],n,t);this.setLaFuncCache(a,s)}))}getKeyForAutomaticLookahead(e,t){return ur(this.getLastExplicitRuleShortName(),e,t)}getLaFuncFromCache(e){return this.lookAheadFuncsCache.get(e)}setLaFuncCache(e,t){this.lookAheadFuncsCache.set(e,t)}},class{initTreeBuilder(e){if(this.CST_STACK=[],this.outputCst=e.outputCst,this.nodeLocationTracking=(0,o.A)(e,"nodeLocationTracking")?e.nodeLocationTracking:Lr.nodeLocationTracking,this.outputCst)if(/full/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=pr,this.setNodeLocationFromNode=pr,this.cstPostRule=Rt.A,this.setInitialNodeLocation=this.setInitialNodeLocationFullRecovery):(this.setNodeLocationFromToken=Rt.A,this.setNodeLocationFromNode=Rt.A,this.cstPostRule=this.cstPostRuleFull,this.setInitialNodeLocation=this.setInitialNodeLocationFullRegular);else if(/onlyOffset/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=fr,this.setNodeLocationFromNode=fr,this.cstPostRule=Rt.A,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRecovery):(this.setNodeLocationFromToken=Rt.A,this.setNodeLocationFromNode=Rt.A,this.cstPostRule=this.cstPostRuleOnlyOffset,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRegular);else{if(!/none/i.test(this.nodeLocationTracking))throw Error(`Invalid <nodeLocationTracking> config option: "${e.nodeLocationTracking}"`);this.setNodeLocationFromToken=Rt.A,this.setNodeLocationFromNode=Rt.A,this.cstPostRule=Rt.A,this.setInitialNodeLocation=Rt.A}else this.cstInvocationStateUpdate=Rt.A,this.cstFinallyStateUpdate=Rt.A,this.cstPostTerminal=Rt.A,this.cstPostNonTerminal=Rt.A,this.cstPostRule=Rt.A}setInitialNodeLocationOnlyOffsetRecovery(e){e.location={startOffset:NaN,endOffset:NaN}}setInitialNodeLocationOnlyOffsetRegular(e){e.location={startOffset:this.LA(1).startOffset,endOffset:NaN}}setInitialNodeLocationFullRecovery(e){e.location={startOffset:NaN,startLine:NaN,startColumn:NaN,endOffset:NaN,endLine:NaN,endColumn:NaN}}setInitialNodeLocationFullRegular(e){const t=this.LA(1);e.location={startOffset:t.startOffset,startLine:t.startLine,startColumn:t.startColumn,endOffset:NaN,endLine:NaN,endColumn:NaN}}cstInvocationStateUpdate(e){const t={name:e,children:Object.create(null)};this.setInitialNodeLocation(t),this.CST_STACK.push(t)}cstFinallyStateUpdate(){this.CST_STACK.pop()}cstPostRuleFull(e){const t=this.LA(0),n=e.location;n.startOffset<=t.startOffset==1?(n.endOffset=t.endOffset,n.endLine=t.endLine,n.endColumn=t.endColumn):(n.startOffset=NaN,n.startLine=NaN,n.startColumn=NaN)}cstPostRuleOnlyOffset(e){const t=this.LA(0),n=e.location;n.startOffset<=t.startOffset==1?n.endOffset=t.endOffset:n.startOffset=NaN}cstPostTerminal(e,t){const n=this.CST_STACK[this.CST_STACK.length-1];var r,i,s;i=t,s=e,void 0===(r=n).children[s]?r.children[s]=[i]:r.children[s].push(i),this.setNodeLocationFromToken(n.location,t)}cstPostNonTerminal(e,t){const n=this.CST_STACK[this.CST_STACK.length-1];!function(e,t,n){void 0===e.children[t]?e.children[t]=[n]:e.children[t].push(n)}(n,t,e),this.setNodeLocationFromNode(n.location,e.location)}getBaseCstVisitorConstructor(){if((0,ge.A)(this.baseCstVisitorConstructor)){const e=yr(this.className,(0,A.A)(this.gastProductionsCache));return this.baseCstVisitorConstructor=e,e}return this.baseCstVisitorConstructor}getBaseCstVisitorConstructorWithDefaults(){if((0,ge.A)(this.baseCstVisitorWithDefaultsConstructor)){const e=function(e,t,n){const i=function(){};mr(i,e+"BaseSemanticsWithDefaults");const s=Object.create(n.prototype);return(0,r.A)(t,(e=>{s[e]=gr})),(i.prototype=s).constructor=i,i}(this.className,(0,A.A)(this.gastProductionsCache),this.getBaseCstVisitorConstructor());return this.baseCstVisitorWithDefaultsConstructor=e,e}return this.baseCstVisitorWithDefaultsConstructor}getLastExplicitRuleShortName(){const e=this.RULE_STACK;return e[e.length-1]}getPreviousExplicitRuleShortName(){const e=this.RULE_STACK;return e[e.length-2]}getLastExplicitRuleOccurrenceIndex(){const e=this.RULE_OCCURRENCE_STACK;return e[e.length-1]}},class{initLexerAdapter(){this.tokVector=[],this.tokVectorLength=0,this.currIdx=-1}set input(e){if(!0!==this.selfAnalysisDone)throw Error("Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.");this.reset(),this.tokVector=e,this.tokVectorLength=e.length}get input(){return this.tokVector}SKIP_TOKEN(){return this.currIdx<=this.tokVector.length-2?(this.consumeToken(),this.LA(1)):wr}LA(e){const t=this.currIdx+e;return t<0||this.tokVectorLength<=t?wr:this.tokVector[t]}consumeToken(){this.currIdx++}exportLexerState(){return this.currIdx}importLexerState(e){this.currIdx=e}resetLexerState(){this.currIdx=-1}moveToTerminatedState(){this.currIdx=this.tokVector.length-1}getLexerPosition(){return this.exportLexerState()}},class{initRecognizerEngine(e,t){if(this.className=this.constructor.name,this.shortRuleNameToFull={},this.fullRuleNameToShort={},this.ruleShortNameIdx=256,this.tokenMatcher=xt,this.subruleIdx=0,this.definedRulesNames=[],this.tokensMap={},this.isBackTrackingStack=[],this.RULE_STACK=[],this.RULE_OCCURRENCE_STACK=[],this.gastProductionsCache={},(0,o.A)(t,"serializedGrammar"))throw Error("The Parser's configuration can no longer contain a <serializedGrammar> property.\n\tSee: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0\n\tFor Further details.");if((0,Q.A)(e)){if((0,s.A)(e))throw Error("A Token Vocabulary cannot be empty.\n\tNote that the first argument for the parser constructor\n\tis no longer a Token vector (since v4.0).");if("number"==typeof e[0].startOffset)throw Error("The Parser constructor no longer accepts a token vector as the first argument.\n\tSee: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0\n\tFor Further details.")}if((0,Q.A)(e))this.tokensMap=(0,Ie.A)(e,((e,t)=>(e[t.name]=t,e)),{});else if((0,o.A)(e,"modes")&&se((0,he.A)((0,i.A)(e.modes)),bt)){const t=(0,he.A)((0,i.A)(e.modes)),n=de(t);this.tokensMap=(0,Ie.A)(n,((e,t)=>(e[t.name]=t,e)),{})}else{if(!(0,Ar.A)(e))throw new Error("<tokensDictionary> argument must be An Array of Token constructors, A dictionary of Token constructors or an IMultiModeLexerDefinition");this.tokensMap=(0,c.A)(e)}this.tokensMap.EOF=Xt;const n=(0,o.A)(e,"modes")?(0,he.A)((0,i.A)(e.modes)):(0,i.A)(e),r=se(n,(e=>(0,s.A)(e.categoryMatches)));this.tokenMatcher=r?xt:$t,Nt((0,i.A)(this.tokensMap))}defineRule(e,t,n){if(this.selfAnalysisDone)throw Error(`Grammar rule <${e}> may not be defined after the 'performSelfAnalysis' method has been called'\nMake sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.`);const r=(0,o.A)(n,"resyncEnabled")?n.resyncEnabled:br.resyncEnabled,i=(0,o.A)(n,"recoveryValueFunc")?n.recoveryValueFunc:br.recoveryValueFunc,s=this.ruleShortNameIdx<<12;let a;return this.ruleShortNameIdx++,this.shortRuleNameToFull[s]=e,this.fullRuleNameToShort[e]=s,a=!0===this.outputCst?function(...n){try{this.ruleInvocationStateUpdate(s,e,this.subruleIdx),t.apply(this,n);const r=this.CST_STACK[this.CST_STACK.length-1];return this.cstPostRule(r),r}catch(a){return this.invokeRuleCatch(a,r,i)}finally{this.ruleFinallyStateUpdate()}}:function(...n){try{return this.ruleInvocationStateUpdate(s,e,this.subruleIdx),t.apply(this,n)}catch(a){return this.invokeRuleCatch(a,r,i)}finally{this.ruleFinallyStateUpdate()}},Object.assign(a,{ruleName:e,originalGrammarAction:t})}invokeRuleCatch(e,t,n){const r=1===this.RULE_STACK.length,i=t&&!this.isBackTracking()&&this.recoveryEnabled;if(Qn(e)){const t=e;if(i){const r=this.findReSyncTokenType();if(this.isInCurrentRuleReSyncSet(r)){if(t.resyncedTokens=this.reSyncTo(r),this.outputCst){const e=this.CST_STACK[this.CST_STACK.length-1];return e.recoveredNode=!0,e}return n(e)}if(this.outputCst){const e=this.CST_STACK[this.CST_STACK.length-1];e.recoveredNode=!0,t.partialCstResult=e}throw t}if(r)return this.moveToTerminatedState(),n(e);throw t}throw e}optionInternal(e,t){const n=this.getKeyForAutomaticLookahead(512,t);return this.optionInternalLogic(e,t,n)}optionInternalLogic(e,t,n){let r,i=this.getLaFuncFromCache(n);if("function"!=typeof e){r=e.DEF;const t=e.GATE;if(void 0!==t){const e=i;i=()=>t.call(this)&&e.call(this)}}else r=e;if(!0===i.call(this))return r.call(this)}atLeastOneInternal(e,t){const n=this.getKeyForAutomaticLookahead(or,e);return this.atLeastOneInternalLogic(e,t,n)}atLeastOneInternalLogic(e,t,n){let r,i=this.getLaFuncFromCache(n);if("function"!=typeof t){r=t.DEF;const e=t.GATE;if(void 0!==e){const t=i;i=()=>e.call(this)&&t.call(this)}}else r=t;if(!0!==i.call(this))throw this.raiseEarlyExitException(e,Rn.REPETITION_MANDATORY,t.ERR_MSG);{let e=this.doSingleRepetition(r);for(;!0===i.call(this)&&!0===e;)e=this.doSingleRepetition(r)}this.attemptInRepetitionRecovery(this.atLeastOneInternal,[e,t],i,or,e,gn)}atLeastOneSepFirstInternal(e,t){const n=this.getKeyForAutomaticLookahead(lr,e);this.atLeastOneSepFirstInternalLogic(e,t,n)}atLeastOneSepFirstInternalLogic(e,t,n){const r=t.DEF,i=t.SEP;if(!0!==this.getLaFuncFromCache(n).call(this))throw this.raiseEarlyExitException(e,Rn.REPETITION_MANDATORY_WITH_SEPARATOR,t.ERR_MSG);{r.call(this);const t=()=>this.tokenMatcher(this.LA(1),i);for(;!0===this.tokenMatcher(this.LA(1),i);)this.CONSUME(i),r.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,i,t,r,yn],t,lr,e,yn)}}manyInternal(e,t){const n=this.getKeyForAutomaticLookahead(768,e);return this.manyInternalLogic(e,t,n)}manyInternalLogic(e,t,n){let r,i=this.getLaFuncFromCache(n);if("function"!=typeof t){r=t.DEF;const e=t.GATE;if(void 0!==e){const t=i;i=()=>e.call(this)&&t.call(this)}}else r=t;let s=!0;for(;!0===i.call(this)&&!0===s;)s=this.doSingleRepetition(r);this.attemptInRepetitionRecovery(this.manyInternal,[e,t],i,768,e,pn,s)}manySepFirstInternal(e,t){const n=this.getKeyForAutomaticLookahead(cr,e);this.manySepFirstInternalLogic(e,t,n)}manySepFirstInternalLogic(e,t,n){const r=t.DEF,i=t.SEP;if(!0===this.getLaFuncFromCache(n).call(this)){r.call(this);const t=()=>this.tokenMatcher(this.LA(1),i);for(;!0===this.tokenMatcher(this.LA(1),i);)this.CONSUME(i),r.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,i,t,r,mn],t,cr,e,mn)}}repetitionSepSecondInternal(e,t,n,r,i){for(;n();)this.CONSUME(t),r.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,t,n,r,i],n,lr,e,i)}doSingleRepetition(e){const t=this.getLexerPosition();return e.call(this),this.getLexerPosition()>t}orInternal(e,t){const n=this.getKeyForAutomaticLookahead(256,t),r=(0,Q.A)(e)?e:e.DEF,i=this.getLaFuncFromCache(n).call(this,r);if(void 0!==i)return r[i].ALT.call(this);this.raiseNoAltException(t,e.ERR_MSG)}ruleFinallyStateUpdate(){if(this.RULE_STACK.pop(),this.RULE_OCCURRENCE_STACK.pop(),this.cstFinallyStateUpdate(),0===this.RULE_STACK.length&&!1===this.isAtEndOfInput()){const e=this.LA(1),t=this.errorMessageProvider.buildNotAllInputParsedMessage({firstRedundant:e,ruleName:this.getCurrRuleFullName()});this.SAVE_ERROR(new tr(t,e))}}subruleInternal(e,t,n){let r;try{const i=void 0!==n?n.ARGS:void 0;return this.subruleIdx=t,r=e.apply(this,i),this.cstPostNonTerminal(r,void 0!==n&&void 0!==n.LABEL?n.LABEL:e.ruleName),r}catch(i){throw this.subruleInternalError(i,n,e.ruleName)}}subruleInternalError(e,t,n){throw Qn(e)&&void 0!==e.partialCstResult&&(this.cstPostNonTerminal(e.partialCstResult,void 0!==t&&void 0!==t.LABEL?t.LABEL:n),delete e.partialCstResult),e}consumeInternal(e,t,n){let r;try{const t=this.LA(1);!0===this.tokenMatcher(t,e)?(this.consumeToken(),r=t):this.consumeInternalError(e,t,n)}catch(i){r=this.consumeInternalRecovery(e,t,i)}return this.cstPostTerminal(void 0!==n&&void 0!==n.LABEL?n.LABEL:e.name,r),r}consumeInternalError(e,t,n){let r;const i=this.LA(0);throw r=void 0!==n&&n.ERR_MSG?n.ERR_MSG:this.errorMessageProvider.buildMismatchTokenMessage({expected:e,actual:t,previous:i,ruleName:this.getCurrRuleFullName()}),this.SAVE_ERROR(new Jn(r,t,i))}consumeInternalRecovery(e,t,n){if(!this.recoveryEnabled||"MismatchedTokenException"!==n.name||this.isBackTracking())throw n;{const i=this.getFollowsForInRuleRecovery(e,t);try{return this.tryInRuleRecovery(e,i)}catch(r){throw r.name===ir?n:r}}}saveRecogState(){const e=this.errors,t=(0,c.A)(this.RULE_STACK);return{errors:e,lexerState:this.exportLexerState(),RULE_STACK:t,CST_STACK:this.CST_STACK}}reloadRecogState(e){this.errors=e.errors,this.importLexerState(e.lexerState),this.RULE_STACK=e.RULE_STACK}ruleInvocationStateUpdate(e,t,n){this.RULE_OCCURRENCE_STACK.push(n),this.RULE_STACK.push(e),this.cstInvocationStateUpdate(t)}isBackTracking(){return 0!==this.isBackTrackingStack.length}getCurrRuleFullName(){const e=this.getLastExplicitRuleShortName();return this.shortRuleNameToFull[e]}shortRuleNameToFullName(e){return this.shortRuleNameToFull[e]}isAtEndOfInput(){return this.tokenMatcher(this.LA(1),Xt)}reset(){this.resetLexerState(),this.subruleIdx=0,this.isBackTrackingStack=[],this.errors=[],this.RULE_STACK=[],this.CST_STACK=[],this.RULE_OCCURRENCE_STACK=[]}},class{ACTION(e){return e.call(this)}consume(e,t,n){return this.consumeInternal(t,e,n)}subrule(e,t,n){return this.subruleInternal(t,e,n)}option(e,t){return this.optionInternal(t,e)}or(e,t){return this.orInternal(t,e)}many(e,t){return this.manyInternal(e,t)}atLeastOne(e,t){return this.atLeastOneInternal(e,t)}CONSUME(e,t){return this.consumeInternal(e,0,t)}CONSUME1(e,t){return this.consumeInternal(e,1,t)}CONSUME2(e,t){return this.consumeInternal(e,2,t)}CONSUME3(e,t){return this.consumeInternal(e,3,t)}CONSUME4(e,t){return this.consumeInternal(e,4,t)}CONSUME5(e,t){return this.consumeInternal(e,5,t)}CONSUME6(e,t){return this.consumeInternal(e,6,t)}CONSUME7(e,t){return this.consumeInternal(e,7,t)}CONSUME8(e,t){return this.consumeInternal(e,8,t)}CONSUME9(e,t){return this.consumeInternal(e,9,t)}SUBRULE(e,t){return this.subruleInternal(e,0,t)}SUBRULE1(e,t){return this.subruleInternal(e,1,t)}SUBRULE2(e,t){return this.subruleInternal(e,2,t)}SUBRULE3(e,t){return this.subruleInternal(e,3,t)}SUBRULE4(e,t){return this.subruleInternal(e,4,t)}SUBRULE5(e,t){return this.subruleInternal(e,5,t)}SUBRULE6(e,t){return this.subruleInternal(e,6,t)}SUBRULE7(e,t){return this.subruleInternal(e,7,t)}SUBRULE8(e,t){return this.subruleInternal(e,8,t)}SUBRULE9(e,t){return this.subruleInternal(e,9,t)}OPTION(e){return this.optionInternal(e,0)}OPTION1(e){return this.optionInternal(e,1)}OPTION2(e){return this.optionInternal(e,2)}OPTION3(e){return this.optionInternal(e,3)}OPTION4(e){return this.optionInternal(e,4)}OPTION5(e){return this.optionInternal(e,5)}OPTION6(e){return this.optionInternal(e,6)}OPTION7(e){return this.optionInternal(e,7)}OPTION8(e){return this.optionInternal(e,8)}OPTION9(e){return this.optionInternal(e,9)}OR(e){return this.orInternal(e,0)}OR1(e){return this.orInternal(e,1)}OR2(e){return this.orInternal(e,2)}OR3(e){return this.orInternal(e,3)}OR4(e){return this.orInternal(e,4)}OR5(e){return this.orInternal(e,5)}OR6(e){return this.orInternal(e,6)}OR7(e){return this.orInternal(e,7)}OR8(e){return this.orInternal(e,8)}OR9(e){return this.orInternal(e,9)}MANY(e){this.manyInternal(0,e)}MANY1(e){this.manyInternal(1,e)}MANY2(e){this.manyInternal(2,e)}MANY3(e){this.manyInternal(3,e)}MANY4(e){this.manyInternal(4,e)}MANY5(e){this.manyInternal(5,e)}MANY6(e){this.manyInternal(6,e)}MANY7(e){this.manyInternal(7,e)}MANY8(e){this.manyInternal(8,e)}MANY9(e){this.manyInternal(9,e)}MANY_SEP(e){this.manySepFirstInternal(0,e)}MANY_SEP1(e){this.manySepFirstInternal(1,e)}MANY_SEP2(e){this.manySepFirstInternal(2,e)}MANY_SEP3(e){this.manySepFirstInternal(3,e)}MANY_SEP4(e){this.manySepFirstInternal(4,e)}MANY_SEP5(e){this.manySepFirstInternal(5,e)}MANY_SEP6(e){this.manySepFirstInternal(6,e)}MANY_SEP7(e){this.manySepFirstInternal(7,e)}MANY_SEP8(e){this.manySepFirstInternal(8,e)}MANY_SEP9(e){this.manySepFirstInternal(9,e)}AT_LEAST_ONE(e){this.atLeastOneInternal(0,e)}AT_LEAST_ONE1(e){return this.atLeastOneInternal(1,e)}AT_LEAST_ONE2(e){this.atLeastOneInternal(2,e)}AT_LEAST_ONE3(e){this.atLeastOneInternal(3,e)}AT_LEAST_ONE4(e){this.atLeastOneInternal(4,e)}AT_LEAST_ONE5(e){this.atLeastOneInternal(5,e)}AT_LEAST_ONE6(e){this.atLeastOneInternal(6,e)}AT_LEAST_ONE7(e){this.atLeastOneInternal(7,e)}AT_LEAST_ONE8(e){this.atLeastOneInternal(8,e)}AT_LEAST_ONE9(e){this.atLeastOneInternal(9,e)}AT_LEAST_ONE_SEP(e){this.atLeastOneSepFirstInternal(0,e)}AT_LEAST_ONE_SEP1(e){this.atLeastOneSepFirstInternal(1,e)}AT_LEAST_ONE_SEP2(e){this.atLeastOneSepFirstInternal(2,e)}AT_LEAST_ONE_SEP3(e){this.atLeastOneSepFirstInternal(3,e)}AT_LEAST_ONE_SEP4(e){this.atLeastOneSepFirstInternal(4,e)}AT_LEAST_ONE_SEP5(e){this.atLeastOneSepFirstInternal(5,e)}AT_LEAST_ONE_SEP6(e){this.atLeastOneSepFirstInternal(6,e)}AT_LEAST_ONE_SEP7(e){this.atLeastOneSepFirstInternal(7,e)}AT_LEAST_ONE_SEP8(e){this.atLeastOneSepFirstInternal(8,e)}AT_LEAST_ONE_SEP9(e){this.atLeastOneSepFirstInternal(9,e)}RULE(e,t,n=br){if(ne(this.definedRulesNames,e)){const t={message:en.buildDuplicateRuleNameError({topLevelRule:e,grammarName:this.className}),type:Or.DUPLICATE_RULE_NAME,ruleName:e};this.definitionErrors.push(t)}this.definedRulesNames.push(e);const r=this.defineRule(e,t,n);return this[e]=r,r}OVERRIDE_RULE(e,t,n=br){const r=function(e,t,n){const r=[];let i;return ne(t,e)||(i=`Invalid rule override, rule: ->${e}<- cannot be overridden in the grammar: ->${n}<-as it is not defined in any of the super grammars `,r.push({message:i,type:Or.INVALID_RULE_OVERRIDE,ruleName:e})),r}(e,this.definedRulesNames,this.className);this.definitionErrors=this.definitionErrors.concat(r);const i=this.defineRule(e,t,n);return this[e]=i,i}BACKTRACK(e,t){return function(){this.isBackTrackingStack.push(1);const n=this.saveRecogState();try{return e.apply(this,t),!0}catch(r){if(Qn(r))return!1;throw r}finally{this.reloadRecogState(n),this.isBackTrackingStack.pop()}}}getGAstProductions(){return this.gastProductionsCache}getSerializedGastProductions(){return e=(0,i.A)(this.gastProductionsCache),(0,a.A)(e,W);var e}},class{initErrorHandler(e){this._errors=[],this.errorMessageProvider=(0,o.A)(e,"errorMessageProvider")?e.errorMessageProvider:Lr.errorMessageProvider}SAVE_ERROR(e){if(Qn(e))return e.context={ruleStack:this.getHumanReadableRuleStack(),ruleOccurrenceStack:(0,c.A)(this.RULE_OCCURRENCE_STACK)},this._errors.push(e),e;throw Error("Trying to save an Error which is not a RecognitionException")}get errors(){return(0,c.A)(this._errors)}set errors(e){this._errors=e}raiseEarlyExitException(e,t,n){const r=this.getCurrRuleFullName(),i=On(e,this.getGAstProductions()[r],t,this.maxLookahead)[0],s=[];for(let o=1;o<=this.maxLookahead;o++)s.push(this.LA(o));const a=this.errorMessageProvider.buildEarlyExitMessage({expectedIterationPaths:i,actual:s,previous:this.LA(0),customUserDescription:n,ruleName:r});throw this.SAVE_ERROR(new nr(a,this.LA(1),this.LA(0)))}raiseNoAltException(e,t){const n=this.getCurrRuleFullName(),r=bn(e,this.getGAstProductions()[n],this.maxLookahead),i=[];for(let o=1;o<=this.maxLookahead;o++)i.push(this.LA(o));const s=this.LA(0),a=this.errorMessageProvider.buildNoViableAltMessage({expectedPathsPerAlt:r,actual:i,previous:s,customUserDescription:t,ruleName:this.getCurrRuleFullName()});throw this.SAVE_ERROR(new er(a,this.LA(1),s))}},class{initContentAssist(){}computeContentAssist(e,t){const n=this.gastProductionsCache[e];if((0,ge.A)(n))throw Error(`Rule ->${e}<- does not exist in this grammar.`);return An([n],t,this.tokenMatcher,this.maxLookahead)}getNextPossibleTokenTypes(e){const t=Ue(e.ruleStack),n=this.getGAstProductions()[t];return new hn(n,e).startWalking()}},class{initGastRecorder(e){this.recordingProdStack=[],this.RECORDING_PHASE=!1}enableRecording(){this.RECORDING_PHASE=!0,this.TRACE_INIT("Enable Recording",(()=>{for(let e=0;e<10;e++){const t=e>0?e:"";this[`CONSUME${t}`]=function(t,n){return this.consumeInternalRecord(t,e,n)},this[`SUBRULE${t}`]=function(t,n){return this.subruleInternalRecord(t,e,n)},this[`OPTION${t}`]=function(t){return this.optionInternalRecord(t,e)},this[`OR${t}`]=function(t){return this.orInternalRecord(t,e)},this[`MANY${t}`]=function(t){this.manyInternalRecord(e,t)},this[`MANY_SEP${t}`]=function(t){this.manySepFirstInternalRecord(e,t)},this[`AT_LEAST_ONE${t}`]=function(t){this.atLeastOneInternalRecord(e,t)},this[`AT_LEAST_ONE_SEP${t}`]=function(t){this.atLeastOneSepFirstInternalRecord(e,t)}}this.consume=function(e,t,n){return this.consumeInternalRecord(t,e,n)},this.subrule=function(e,t,n){return this.subruleInternalRecord(t,e,n)},this.option=function(e,t){return this.optionInternalRecord(t,e)},this.or=function(e,t){return this.orInternalRecord(t,e)},this.many=function(e,t){this.manyInternalRecord(e,t)},this.atLeastOne=function(e,t){this.atLeastOneInternalRecord(e,t)},this.ACTION=this.ACTION_RECORD,this.BACKTRACK=this.BACKTRACK_RECORD,this.LA=this.LA_RECORD}))}disableRecording(){this.RECORDING_PHASE=!1,this.TRACE_INIT("Deleting Recording methods",(()=>{const e=this;for(let t=0;t<10;t++){const n=t>0?t:"";delete e[`CONSUME${n}`],delete e[`SUBRULE${n}`],delete e[`OPTION${n}`],delete e[`OR${n}`],delete e[`MANY${n}`],delete e[`MANY_SEP${n}`],delete e[`AT_LEAST_ONE${n}`],delete e[`AT_LEAST_ONE_SEP${n}`]}delete e.consume,delete e.subrule,delete e.option,delete e.or,delete e.many,delete e.atLeastOne,delete e.ACTION,delete e.BACKTRACK,delete e.LA}))}ACTION_RECORD(e){}BACKTRACK_RECORD(e,t){return()=>!0}LA_RECORD(e){return wr}topLevelRuleRecord(e,t){try{const n=new D({definition:[],name:e});return n.name=e,this.recordingProdStack.push(n),t.call(this),this.recordingProdStack.pop(),n}catch(n){if(!0!==n.KNOWN_RECORDER_ERROR)try{n.message=n.message+'\n\t This error was thrown during the "grammar recording phase" For more info see:\n\thttps://chevrotain.io/docs/guide/internals.html#grammar-recording'}catch(r){throw n}throw n}}optionInternalRecord(e,t){return Ir.call(this,F,e,t)}atLeastOneInternalRecord(e,t){Ir.call(this,G,t,e)}atLeastOneSepFirstInternalRecord(e,t){Ir.call(this,K,t,e,Rr)}manyInternalRecord(e,t){Ir.call(this,B,t,e)}manySepFirstInternalRecord(e,t){Ir.call(this,j,t,e,Rr)}orInternalRecord(e,t){return Sr.call(this,e,t)}subruleInternalRecord(e,t,n){if(Cr(t),!e||!1===(0,o.A)(e,"ruleName")){const n=new Error(`<SUBRULE${Nr(t)}> argument is invalid expecting a Parser method reference but got: <${JSON.stringify(e)}>\n inside top level rule: <${this.recordingProdStack[0].name}>`);throw n.KNOWN_RECORDER_ERROR=!0,n}const r=(0,Et.A)(this.recordingProdStack),i=e.ruleName,s=new M({idx:t,nonTerminalName:i,label:null==n?void 0:n.LABEL,referencedRule:void 0});return r.definition.push(s),this.outputCst?xr:vr}consumeInternalRecord(e,t,n){if(Cr(t),!wt(e)){const n=new Error(`<CONSUME${Nr(t)}> argument is invalid expecting a TokenType reference but got: <${JSON.stringify(e)}>\n inside top level rule: <${this.recordingProdStack[0].name}>`);throw n.KNOWN_RECORDER_ERROR=!0,n}const r=(0,Et.A)(this.recordingProdStack),i=new H({idx:t,terminalType:e,label:null==n?void 0:n.LABEL});return r.definition.push(i),$r}},class{initPerformanceTracer(e){if((0,o.A)(e,"traceInitPerf")){const t=e.traceInitPerf,n="number"==typeof t;this.traceInitMaxIdent=n?t:1/0,this.traceInitPerf=n?t>0:t}else this.traceInitMaxIdent=0,this.traceInitPerf=Lr.traceInitPerf;this.traceInitIndent=-1}TRACE_INIT(e,t){if(!0===this.traceInitPerf){this.traceInitIndent++;const n=new Array(this.traceInitIndent+1).join("\t");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${n}--\x3e <${e}>`);const{time:r,value:i}=kt(t),s=r>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&s(`${n}<-- <${e}> time: ${r}ms`),this.traceInitIndent--,i}return t()}}].forEach((e=>{const t=e.prototype;Object.getOwnPropertyNames(t).forEach((n=>{if("constructor"===n)return;const r=Object.getOwnPropertyDescriptor(t,n);r&&(r.get||r.set)?Object.defineProperty(_r.prototype,n,r):_r.prototype[n]=e.prototype[n]}))}));class Dr extends Mr{constructor(e,t=Lr){const n=(0,c.A)(t);n.outputCst=!1,super(e,n)}}},9683:(e,t,n)=>{n.d(t,{DM:()=>p,OP:()=>m,SD:()=>a,Uo:()=>d,VN:()=>u,XG:()=>o,YE:()=>c,cQ:()=>l,jm:()=>h});var r=n(2479),i=n(1719),s=n(6373);function a(e){for(const[t,n]of Object.entries(e))t.startsWith("$")||(Array.isArray(n)?n.forEach(((n,i)=>{(0,r.ng)(n)&&(n.$container=e,n.$containerProperty=t,n.$containerIndex=i)})):(0,r.ng)(n)&&(n.$container=e,n.$containerProperty=t))}function o(e,t){let n=e;for(;n;){if(t(n))return n;n=n.$container}}function c(e){const t=l(e).$document;if(!t)throw new Error("AST node has no document.");return t}function l(e){for(;e.$container;)e=e.$container;return e}function u(e,t){if(!e)throw new Error("Node must be an AstNode.");const n=null==t?void 0:t.range;return new i.fq((()=>({keys:Object.keys(e),keyIndex:0,arrayIndex:0})),(t=>{for(;t.keyIndex<t.keys.length;){const i=t.keys[t.keyIndex];if(!i.startsWith("$")){const s=e[i];if((0,r.ng)(s)){if(t.keyIndex++,f(s,n))return{done:!1,value:s}}else if(Array.isArray(s)){for(;t.arrayIndex<s.length;){const e=s[t.arrayIndex++];if((0,r.ng)(e)&&f(e,n))return{done:!1,value:e}}t.arrayIndex=0}}t.keyIndex++}return i.Rf}))}function d(e,t){if(!e)throw new Error("Root node must be an AstNode.");return new i.Vj(e,(e=>u(e,t)))}function h(e,t){if(!e)throw new Error("Root node must be an AstNode.");return(null==t?void 0:t.range)&&!f(e,t.range)?new i.Vj(e,(()=>[])):new i.Vj(e,(e=>u(e,t)),{includeRoot:!0})}function f(e,t){var n;if(!t)return!0;const r=null===(n=e.$cstNode)||void 0===n?void 0:n.range;return!!r&&(0,s.r4)(r,t)}function p(e){return new i.fq((()=>({keys:Object.keys(e),keyIndex:0,arrayIndex:0})),(t=>{for(;t.keyIndex<t.keys.length;){const n=t.keys[t.keyIndex];if(!n.startsWith("$")){const i=e[n];if((0,r.A_)(i))return t.keyIndex++,{done:!1,value:{reference:i,container:e,property:n}};if(Array.isArray(i)){for(;t.arrayIndex<i.length;){const s=t.arrayIndex++,a=i[s];if((0,r.A_)(a))return{done:!1,value:{reference:a,container:e,property:n,index:s}}}t.arrayIndex=0}}t.keyIndex++}return i.Rf}))}function m(e,t){const n=e.getTypeMetaData(t.$type),r=t;for(const i of n.properties)void 0!==i.defaultValue&&void 0===r[i.name]&&(r[i.name]=g(i.defaultValue))}function g(e){return Array.isArray(e)?[...e.map(g)]:e}},9732:(e,t,n)=>{n.d(t,{f:()=>u});var r=n(3579),i=n(4298),s=n(9364),a=n(1294),o=class extends r.mR{static{(0,r.K2)(this,"PieTokenBuilder")}constructor(){super(["pie","showData"])}},c=class extends r.dg{static{(0,r.K2)(this,"PieValueConverter")}runCustomConverter(e,t,n){if("PIE_SECTION_LABEL"===e.name)return t.replace(/"/g,"").trim()}},l={parser:{TokenBuilder:(0,r.K2)((()=>new o),"TokenBuilder"),ValueConverter:(0,r.K2)((()=>new c),"ValueConverter")}};function u(e=i.D){const t=(0,s.WQ)((0,a.u)(e),r.sr),n=(0,s.WQ)((0,a.t)({shared:t}),r.KX,l);return t.ServiceRegistry.register(n),{shared:t,Pie:n}}(0,r.K2)(u,"createPieServices")},9850:(e,t,n)=>{t.Qi=t.XO=void 0;const r=n(9590),i=n(966),s=n(2676);var a;!function(e){e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:s.Event.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:s.Event.None}),e.is=function(t){const n=t;return n&&(n===e.None||n===e.Cancelled||i.boolean(n.isCancellationRequested)&&!!n.onCancellationRequested)}}(a||(t.XO=a={}));const o=Object.freeze((function(e,t){const n=(0,r.default)().timer.setTimeout(e.bind(t),0);return{dispose(){n.dispose()}}}));class c{constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?o:(this._emitter||(this._emitter=new s.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}}t.Qi=class{get token(){return this._token||(this._token=new c),this._token}cancel(){this._token?this._token.cancel():this._token=a.Cancelled}dispose(){this._token?this._token instanceof c&&this._token.dispose():this._token=a.None}}},9923:(e,t,n)=>{n.d(t,{$:()=>l});var r=n(3579),i=n(4298),s=n(9364),a=n(1294),o=class extends r.mR{static{(0,r.K2)(this,"PacketTokenBuilder")}constructor(){super(["packet-beta"])}},c={parser:{TokenBuilder:(0,r.K2)((()=>new o),"TokenBuilder"),ValueConverter:(0,r.K2)((()=>new r.Tm),"ValueConverter")}};function l(e=i.D){const t=(0,s.WQ)((0,a.u)(e),r.sr),n=(0,s.WQ)((0,a.t)({shared:t}),r.AM,c);return t.ServiceRegistry.register(n),{shared:t,Packet:n}}(0,r.K2)(l,"createPacketServices")}}]);