"use strict";(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[7098],{4532:(n,e,s)=>{s.r(e),s.d(e,{default:()=>l});s(6540);var r=s(5500),o=s(2565),t=s(3025),c=s(2831),i=s(1463),a=s(4848);function d(n){const{version:e}=n;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.A,{version:e.version,tag:(0,o.k)(e.pluginId,e.version)}),(0,a.jsx)(r.be,{children:e.noIndex&&(0,a.jsx)("meta",{name:"robots",content:"noindex, nofollow"})})]})}function u(n){const{version:e,route:s}=n;return(0,a.jsx)(r.e3,{className:e.className,children:(0,a.jsx)(t.n,{version:e,children:(0,c.v)(s.routes)})})}function l(n){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d,{...n}),(0,a.jsx)(u,{...n})]})}}}]);