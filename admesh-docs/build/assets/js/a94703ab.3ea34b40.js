"use strict";(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[9048],{1377:(e,t,n)=>{n.r(t),n.d(t,{default:()=>pe});var a=n(6540),o=n(4164),i=n(5500),s=n(7559),r=n(6972),c=n(609),l=n(1312),d=n(3104),u=n(5062);const m={backToTopButton:"backToTopButton_sjWU",backToTopButtonShow:"backToTopButtonShow_xfvO"};var h=n(4848);function b(){const{shown:e,scrollToTop:t}=function({threshold:e}){const[t,n]=(0,a.useState)(!1),o=(0,a.useRef)(!1),{startScroll:i,cancelScroll:s}=(0,d.gk)();return(0,d.Mq)((({scrollY:t},a)=>{const i=a?.scrollY;i&&(o.current?o.current=!1:t>=i?(s(),n(!1)):t<e?n(!1):t+window.innerHeight<document.documentElement.scrollHeight&&n(!0))})),(0,u.$)((e=>{e.location.hash&&(o.current=!0,n(!1))})),{shown:t,scrollToTop:()=>i(0)}}({threshold:300});return(0,h.jsx)("button",{"aria-label":(0,l.T)({id:"theme.BackToTopButton.buttonAriaLabel",message:"Scroll back to top",description:"The ARIA label for the back to top button"}),className:(0,o.A)("clean-btn",s.G.common.backToTopButton,m.backToTopButton,e&&m.backToTopButtonShow),type:"button",onClick:t})}var p=n(3109),x=n(6347),f=n(4581),j=n(6342),v=n(3465);function _(e){return(0,h.jsx)("svg",{width:"20",height:"20","aria-hidden":"true",...e,children:(0,h.jsxs)("g",{fill:"#7a7a7a",children:[(0,h.jsx)("path",{d:"M9.992 10.023c0 .2-.062.399-.172.547l-4.996 7.492a.982.982 0 01-.828.454H1c-.55 0-1-.453-1-1 0-.2.059-.403.168-.551l4.629-6.942L.168 3.078A.939.939 0 010 2.528c0-.548.45-.997 1-.997h2.996c.352 0 .649.18.828.45L9.82 9.472c.11.148.172.347.172.55zm0 0"}),(0,h.jsx)("path",{d:"M19.98 10.023c0 .2-.058.399-.168.547l-4.996 7.492a.987.987 0 01-.828.454h-3c-.547 0-.996-.453-.996-1 0-.2.059-.403.168-.551l4.625-6.942-4.625-6.945a.939.939 0 01-.168-.55 1 1 0 01.996-.997h3c.348 0 .649.18.828.45l4.996 7.492c.11.148.168.347.168.55zm0 0"})]})})}const g="collapseSidebarButton_PEFL",A="collapseSidebarButtonIcon_kv0_";function C({onClick:e}){return(0,h.jsx)("button",{type:"button",title:(0,l.T)({id:"theme.docs.sidebar.collapseButtonTitle",message:"Collapse sidebar",description:"The title attribute for collapse button of doc sidebar"}),"aria-label":(0,l.T)({id:"theme.docs.sidebar.collapseButtonAriaLabel",message:"Collapse sidebar",description:"The title attribute for collapse button of doc sidebar"}),className:(0,o.A)("button button--secondary button--outline",g),onClick:e,children:(0,h.jsx)(_,{className:A})})}var k=n(5041),S=n(9532);const T=Symbol("EmptyContext"),N=a.createContext(T);function I({children:e}){const[t,n]=(0,a.useState)(null),o=(0,a.useMemo)((()=>({expandedItem:t,setExpandedItem:n})),[t]);return(0,h.jsx)(N.Provider,{value:o,children:e})}var y=n(1422),B=n(9169),w=n(8774),L=n(2303);function E({collapsed:e,categoryLabel:t,onClick:n}){return(0,h.jsx)("button",{"aria-label":e?(0,l.T)({id:"theme.DocSidebarItem.expandCategoryAriaLabel",message:"Expand sidebar category '{label}'",description:"The ARIA label to expand the sidebar category"},{label:t}):(0,l.T)({id:"theme.DocSidebarItem.collapseCategoryAriaLabel",message:"Collapse sidebar category '{label}'",description:"The ARIA label to collapse the sidebar category"},{label:t}),"aria-expanded":!e,type:"button",className:"clean-btn menu__caret",onClick:n})}function M({item:e,onItemClick:t,activePath:n,level:i,index:c,...l}){const{items:d,label:u,collapsible:m,className:b,href:p}=e,{docs:{sidebar:{autoCollapseCategories:x}}}=(0,j.p)(),f=function(e){const t=(0,L.A)();return(0,a.useMemo)((()=>e.href&&!e.linkUnlisted?e.href:!t&&e.collapsible?(0,r.Nr)(e):void 0),[e,t])}(e),v=(0,r.w8)(e,n),_=(0,B.ys)(p,n),{collapsed:g,setCollapsed:A}=(0,y.u)({initialState:()=>!!m&&(!v&&e.collapsed)}),{expandedItem:C,setExpandedItem:k}=function(){const e=(0,a.useContext)(N);if(e===T)throw new S.dV("DocSidebarItemsExpandedStateProvider");return e}(),I=(e=!g)=>{k(e?null:c),A(e)};return function({isActive:e,collapsed:t,updateCollapsed:n}){const o=(0,S.ZC)(e);(0,a.useEffect)((()=>{e&&!o&&t&&n(!1)}),[e,o,t,n])}({isActive:v,collapsed:g,updateCollapsed:I}),(0,a.useEffect)((()=>{m&&null!=C&&C!==c&&x&&A(!0)}),[m,C,c,A,x]),(0,h.jsxs)("li",{className:(0,o.A)(s.G.docs.docSidebarItemCategory,s.G.docs.docSidebarItemCategoryLevel(i),"menu__list-item",{"menu__list-item--collapsed":g},b),children:[(0,h.jsxs)("div",{className:(0,o.A)("menu__list-item-collapsible",{"menu__list-item-collapsible--active":_}),children:[(0,h.jsx)(w.A,{className:(0,o.A)("menu__link",{"menu__link--sublist":m,"menu__link--sublist-caret":!p&&m,"menu__link--active":v}),onClick:m?n=>{t?.(e),p?_?(n.preventDefault(),I()):I(!1):(n.preventDefault(),I())}:()=>{t?.(e)},"aria-current":_?"page":void 0,role:m&&!p?"button":void 0,"aria-expanded":m&&!p?!g:void 0,href:m?f??"#":f,...l,children:u}),p&&m&&(0,h.jsx)(E,{collapsed:g,categoryLabel:u,onClick:e=>{e.preventDefault(),I()}})]}),(0,h.jsx)(y.N,{lazy:!0,as:"ul",className:"menu__list",collapsed:g,children:(0,h.jsx)(V,{items:d,tabIndex:g?-1:0,onItemClick:t,activePath:n,level:i+1})})]})}var H=n(6654),G=n(3186);const P="menuExternalLink_NmtK";function R({item:e,onItemClick:t,activePath:n,level:a,index:i,...c}){const{href:l,label:d,className:u,autoAddBaseUrl:m}=e,b=(0,r.w8)(e,n),p=(0,H.A)(l);return(0,h.jsx)("li",{className:(0,o.A)(s.G.docs.docSidebarItemLink,s.G.docs.docSidebarItemLinkLevel(a),"menu__list-item",u),children:(0,h.jsxs)(w.A,{className:(0,o.A)("menu__link",!p&&P,{"menu__link--active":b}),autoAddBaseUrl:m,"aria-current":b?"page":void 0,to:l,...p&&{onClick:t?()=>t(e):void 0},...c,children:[d,!p&&(0,h.jsx)(G.A,{})]})},d)}const W="menuHtmlItem_M9Kj";function D({item:e,level:t,index:n}){const{value:a,defaultStyle:i,className:r}=e;return(0,h.jsx)("li",{className:(0,o.A)(s.G.docs.docSidebarItemLink,s.G.docs.docSidebarItemLinkLevel(t),i&&[W,"menu__list-item"],r),dangerouslySetInnerHTML:{__html:a}},n)}function F({item:e,...t}){switch(e.type){case"category":return(0,h.jsx)(M,{item:e,...t});case"html":return(0,h.jsx)(D,{item:e,...t});default:return(0,h.jsx)(R,{item:e,...t})}}function U({items:e,...t}){const n=(0,r.Y)(e,t.activePath);return(0,h.jsx)(I,{children:n.map(((e,n)=>(0,h.jsx)(F,{item:e,index:n,...t},n)))})}const V=(0,a.memo)(U),Y="menu_SIkG",K="menuWithAnnouncementBar_GW3s";function z({path:e,sidebar:t,className:n}){const i=function(){const{isActive:e}=(0,k.M)(),[t,n]=(0,a.useState)(e);return(0,d.Mq)((({scrollY:t})=>{e&&n(0===t)}),[e]),e&&t}();return(0,h.jsx)("nav",{"aria-label":(0,l.T)({id:"theme.docs.sidebar.navAriaLabel",message:"Docs sidebar",description:"The ARIA label for the sidebar navigation"}),className:(0,o.A)("menu thin-scrollbar",Y,i&&K,n),children:(0,h.jsx)("ul",{className:(0,o.A)(s.G.docs.docSidebarMenu,"menu__list"),children:(0,h.jsx)(V,{items:t,activePath:e,level:1})})})}const q="sidebar_njMd",O="sidebarWithHideableNavbar_wUlq",J="sidebarHidden_VK0M",Q="sidebarLogo_isFc";function X({path:e,sidebar:t,onCollapse:n,isHidden:a}){const{navbar:{hideOnScroll:i},docs:{sidebar:{hideable:s}}}=(0,j.p)();return(0,h.jsxs)("div",{className:(0,o.A)(q,i&&O,a&&J),children:[i&&(0,h.jsx)(v.A,{tabIndex:-1,className:Q}),(0,h.jsx)(z,{path:e,sidebar:t}),s&&(0,h.jsx)(C,{onClick:n})]})}const Z=a.memo(X);var $=n(5600),ee=n(2069);const te=({sidebar:e,path:t})=>{const n=(0,ee.M)();return(0,h.jsx)("ul",{className:(0,o.A)(s.G.docs.docSidebarMenu,"menu__list"),children:(0,h.jsx)(V,{items:e,activePath:t,onItemClick:e=>{"category"===e.type&&e.href&&n.toggle(),"link"===e.type&&n.toggle()},level:1})})};function ne(e){return(0,h.jsx)($.GX,{component:te,props:e})}const ae=a.memo(ne);function oe(e){const t=(0,f.l)(),n="desktop"===t||"ssr"===t,a="mobile"===t;return(0,h.jsxs)(h.Fragment,{children:[n&&(0,h.jsx)(Z,{...e}),a&&(0,h.jsx)(ae,{...e})]})}const ie={expandButton:"expandButton_TmdG",expandButtonIcon:"expandButtonIcon_i1dp"};function se({toggleSidebar:e}){return(0,h.jsx)("div",{className:ie.expandButton,title:(0,l.T)({id:"theme.docs.sidebar.expandButtonTitle",message:"Expand sidebar",description:"The ARIA label and title attribute for expand button of doc sidebar"}),"aria-label":(0,l.T)({id:"theme.docs.sidebar.expandButtonAriaLabel",message:"Expand sidebar",description:"The ARIA label and title attribute for expand button of doc sidebar"}),tabIndex:0,role:"button",onKeyDown:e,onClick:e,children:(0,h.jsx)(_,{className:ie.expandButtonIcon})})}const re={docSidebarContainer:"docSidebarContainer_YfHR",docSidebarContainerHidden:"docSidebarContainerHidden_DPk8",sidebarViewport:"sidebarViewport_aRkj"};function ce({children:e}){const t=(0,c.t)();return(0,h.jsx)(a.Fragment,{children:e},t?.name??"noSidebar")}function le({sidebar:e,hiddenSidebarContainer:t,setHiddenSidebarContainer:n}){const{pathname:i}=(0,x.zy)(),[r,c]=(0,a.useState)(!1),l=(0,a.useCallback)((()=>{r&&c(!1),!r&&(0,p.O)()&&c(!0),n((e=>!e))}),[n,r]);return(0,h.jsx)("aside",{className:(0,o.A)(s.G.docs.docSidebarContainer,re.docSidebarContainer,t&&re.docSidebarContainerHidden),onTransitionEnd:e=>{e.currentTarget.classList.contains(re.docSidebarContainer)&&t&&c(!0)},children:(0,h.jsx)(ce,{children:(0,h.jsxs)("div",{className:(0,o.A)(re.sidebarViewport,r&&re.sidebarViewportHidden),children:[(0,h.jsx)(oe,{sidebar:e,path:i,onCollapse:l,isHidden:r}),r&&(0,h.jsx)(se,{toggleSidebar:l})]})})})}const de={docMainContainer:"docMainContainer_TBSr",docMainContainerEnhanced:"docMainContainerEnhanced_lQrH",docItemWrapperEnhanced:"docItemWrapperEnhanced_JWYK"};function ue({hiddenSidebarContainer:e,children:t}){const n=(0,c.t)();return(0,h.jsx)("main",{className:(0,o.A)(de.docMainContainer,(e||!n)&&de.docMainContainerEnhanced),children:(0,h.jsx)("div",{className:(0,o.A)("container padding-top--md padding-bottom--lg",de.docItemWrapper,e&&de.docItemWrapperEnhanced),children:t})})}const me={docRoot:"docRoot_UBD9",docsWrapper:"docsWrapper_hBAB"};function he({children:e}){const t=(0,c.t)(),[n,o]=(0,a.useState)(!1);return(0,h.jsxs)("div",{className:me.docsWrapper,children:[(0,h.jsx)(b,{}),(0,h.jsxs)("div",{className:me.docRoot,children:[t&&(0,h.jsx)(le,{sidebar:t.items,hiddenSidebarContainer:n,setHiddenSidebarContainer:o}),(0,h.jsx)(ue,{hiddenSidebarContainer:n,children:e})]})]})}var be=n(3363);function pe(e){const t=(0,r.B5)(e);if(!t)return(0,h.jsx)(be.A,{});const{docElement:n,sidebarName:a,sidebarItems:l}=t;return(0,h.jsx)(i.e3,{className:(0,o.A)(s.G.page.docsDocPage),children:(0,h.jsx)(c.V,{name:a,items:l,children:(0,h.jsx)(he,{children:n})})})}},3363:(e,t,n)=>{n.d(t,{A:()=>r});n(6540);var a=n(4164),o=n(1312),i=n(1107),s=n(4848);function r({className:e}){return(0,s.jsx)("main",{className:(0,a.A)("container margin-vert--xl",e),children:(0,s.jsx)("div",{className:"row",children:(0,s.jsxs)("div",{className:"col col--6 col--offset-3",children:[(0,s.jsx)(i.A,{as:"h1",className:"hero__title",children:(0,s.jsx)(o.A,{id:"theme.NotFound.title",description:"The title of the 404 page",children:"Page Not Found"})}),(0,s.jsx)("p",{children:(0,s.jsx)(o.A,{id:"theme.NotFound.p1",description:"The first paragraph of the 404 page",children:"We could not find what you were looking for."})}),(0,s.jsx)("p",{children:(0,s.jsx)(o.A,{id:"theme.NotFound.p2",description:"The 2nd paragraph of the 404 page",children:"Please contact the owner of the site that linked you to the original URL and let them know their link is broken."})})]})})})}}}]);