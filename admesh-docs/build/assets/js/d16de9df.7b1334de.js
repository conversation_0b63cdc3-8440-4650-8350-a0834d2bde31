"use strict";(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[6944],{364:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>c,contentTitle:()=>a,default:()=>m,frontMatter:()=>o,metadata:()=>i,toc:()=>d});const i=JSON.parse('{"id":"ai-integration/overview","title":"AI Agent Integration Overview","description":"AdMesh provides enterprise-grade integration capabilities for AI applications, conversational interfaces, and intelligent agent systems. This guide covers technical implementation patterns for integrating AdMesh into AI platforms.","source":"@site/docs/ai-integration/overview.md","sourceDirName":"ai-integration","slug":"/ai-integration/overview","permalink":"/ai-integration/overview","draft":false,"unlisted":false,"editUrl":"https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/ai-integration/overview.md","tags":[],"version":"current","sidebarPosition":1,"frontMatter":{"sidebar_position":1},"sidebar":"tutorialSidebar","previous":{"title":"UI SDK Installation","permalink":"/ui-sdk/installation"},"next":{"title":"Authentication","permalink":"/api/authentication"}}');var s=t(4848),r=t(8453);const o={sidebar_position:1},a="AI Agent Integration Overview",c={},d=[{value:"AI Agent Integration Benefits",id:"ai-agent-integration-benefits",level:2},{value:"AI-First Architecture",id:"ai-first-architecture",level:3},{value:"Integration Capabilities",id:"integration-capabilities",level:3},{value:"Integration Patterns",id:"integration-patterns",level:2},{value:"1. Conversational AI Pattern",id:"1-conversational-ai-pattern",level:3},{value:"2. Auto-Recommendation Pattern",id:"2-auto-recommendation-pattern",level:3},{value:"3. Enhanced Search Pattern",id:"3-enhanced-search-pattern",level:3},{value:"Supported AI Platforms",id:"supported-ai-platforms",level:2},{value:"ChatGPT Plugin Integration",id:"chatgpt-plugin-integration",level:3},{value:"Claude Integration",id:"claude-integration",level:3},{value:"Replica.io Integration",id:"replicaio-integration",level:3},{value:"Implementation Strategies",id:"implementation-strategies",level:2},{value:"1. Intent-Based Recommendations",id:"1-intent-based-recommendations",level:3},{value:"2. Citation-Based Display",id:"2-citation-based-display",level:3},{value:"3. Auto-Triggered Recommendations",id:"3-auto-triggered-recommendations",level:3},{value:"Best Practices for AI Integration",id:"best-practices-for-ai-integration",level:2},{value:"\ud83c\udfaf Context Awareness",id:"-context-awareness",level:3},{value:"\ud83d\udd04 Non-Intrusive Display",id:"-non-intrusive-display",level:3},{value:"\ud83d\udcca Performance Optimization",id:"-performance-optimization",level:3},{value:"\ud83c\udfa8 User Experience",id:"-user-experience",level:3},{value:"Common Integration Patterns",id:"common-integration-patterns",level:2},{value:"Pattern 1: Post-Response Recommendations",id:"pattern-1-post-response-recommendations",level:3},{value:"Pattern 2: Inline Citations",id:"pattern-2-inline-citations",level:3},{value:"Pattern 3: Proactive Suggestions",id:"pattern-3-proactive-suggestions",level:3},{value:"Next Steps",id:"next-steps",level:2}];function l(e){const n={a:"a",code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",hr:"hr",li:"li",mermaid:"mermaid",ol:"ol",p:"p",pre:"pre",strong:"strong",ul:"ul",...(0,r.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.header,{children:(0,s.jsx)(n.h1,{id:"ai-agent-integration-overview",children:"AI Agent Integration Overview"})}),"\n",(0,s.jsx)(n.p,{children:"AdMesh provides enterprise-grade integration capabilities for AI applications, conversational interfaces, and intelligent agent systems. This guide covers technical implementation patterns for integrating AdMesh into AI platforms."}),"\n",(0,s.jsx)(n.h2,{id:"ai-agent-integration-benefits",children:"AI Agent Integration Benefits"}),"\n",(0,s.jsx)(n.h3,{id:"ai-first-architecture",children:"AI-First Architecture"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Intent Detection Engine"})," - Automated query analysis and categorization"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Contextual Analysis"})," - Context-aware recommendation generation"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Natural Language Processing"})," - Conversational interface compatibility"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Citation Integration"})," - Numbered reference system for recommendations"]}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"integration-capabilities",children:"Integration Capabilities"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Multi-SDK Support"})," - Python, TypeScript, and React UI components"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Asynchronous Operations"})," - Non-blocking real-time chat integration"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Error Handling"})," - Comprehensive fallback mechanisms"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Automated Recommendations"})," - Context-triggered suggestion generation"]}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"integration-patterns",children:"Integration Patterns"}),"\n",(0,s.jsx)(n.h3,{id:"1-conversational-ai-pattern",children:"1. Conversational AI Pattern"}),"\n",(0,s.jsx)(n.p,{children:"Implementation for chatbots and AI assistants requiring product recommendations within conversational flows."}),"\n",(0,s.jsx)(n.mermaid,{value:"graph LR\n    A[User Query] --\x3e B[AI Agent]\n    B --\x3e C[Intent Detection]\n    C --\x3e D[AdMesh API]\n    D --\x3e E[Recommendations]\n    E --\x3e F[Citation Display]\n    F --\x3e G[User Interaction]"}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Implementation Flow:"})}),"\n",(0,s.jsxs)(n.ol,{children:["\n",(0,s.jsx)(n.li,{children:'User query: "Enterprise CRM solution requirements"'}),"\n",(0,s.jsxs)(n.li,{children:["AI agent detects intent: ",(0,s.jsx)(n.code,{children:"best_for_use_case"})]}),"\n",(0,s.jsx)(n.li,{children:"AdMesh returns relevant CRM recommendations"}),"\n",(0,s.jsx)(n.li,{children:"AI displays recommendations as numbered citations"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"2-auto-recommendation-pattern",children:"2. Auto-Recommendation Pattern"}),"\n",(0,s.jsx)(n.p,{children:"Automated product suggestion system based on conversation context analysis without explicit user requests."}),"\n",(0,s.jsx)(n.mermaid,{value:"graph LR\n    A[Conversation] --\x3e B[Context Analysis]\n    B --\x3e C[Trigger Detection]\n    C --\x3e D[AdMesh API]\n    D --\x3e E[Auto-Display]\n    E --\x3e F[User Engagement]"}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Implementation Flow:"})}),"\n",(0,s.jsxs)(n.ol,{children:["\n",(0,s.jsx)(n.li,{children:"User discusses business operational challenges"}),"\n",(0,s.jsx)(n.li,{children:"AI detects relevant product categories"}),"\n",(0,s.jsx)(n.li,{children:"AdMesh automatically provides contextual suggestions"}),"\n",(0,s.jsx)(n.li,{children:"Recommendations display as floating widgets"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"3-enhanced-search-pattern",children:"3. Enhanced Search Pattern"}),"\n",(0,s.jsx)(n.p,{children:"Enhance existing search and discovery features with AI-powered recommendations."}),"\n",(0,s.jsx)(n.mermaid,{value:"graph LR\n    A[Search Query] --\x3e B[Primary Results]\n    B --\x3e C[AdMesh Enhancement]\n    C --\x3e D[Enriched Results]\n    D --\x3e E[User Selection]"}),"\n",(0,s.jsx)(n.h2,{id:"supported-ai-platforms",children:"Supported AI Platforms"}),"\n",(0,s.jsx)(n.h3,{id:"chatgpt-plugin-integration",children:"ChatGPT Plugin Integration"}),"\n",(0,s.jsx)(n.p,{children:"AdMesh integration for ChatGPT plugins and OpenAI-based applications:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-python",children:'import openai\nfrom admesh import Admesh\n\ndef chatgpt_plugin_handler(user_query):\n    # Execute ChatGPT API call\n    gpt_response = openai.ChatCompletion.create(\n        model="gpt-4",\n        messages=[{"role": "user", "content": user_query}]\n    )\n\n    # Analyze response for product recommendation opportunities\n    if contains_product_intent(gpt_response.choices[0].message.content):\n        # Execute AdMesh recommendation request\n        admesh_client = Admesh()\n        recommendations = admesh_client.recommend.get_recommendations(\n            query=user_query,\n            format="auto"\n        )\n\n        # Enhance response with recommendation citations\n        return enhance_with_citations(gpt_response, recommendations)\n\n    return gpt_response\n'})}),"\n",(0,s.jsx)(n.h3,{id:"claude-integration",children:"Claude Integration"}),"\n",(0,s.jsx)(n.p,{children:"Anthropic Claude integration for intelligent recommendation systems:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-python",children:'import anthropic\nfrom admesh import AsyncAdmesh\n\nasync def claude_with_recommendations(user_query):\n    # Execute Claude API request\n    claude = anthropic.Anthropic()\n    claude_response = await claude.messages.create(\n        model="claude-3-sonnet-20240229",\n        messages=[{"role": "user", "content": user_query}]\n    )\n\n    # Analyze response for product recommendation opportunities\n    if should_recommend_products(claude_response.content):\n        admesh_client = AsyncAdmesh()\n        recommendations = await admesh_client.recommend.get_recommendations(\n            query=user_query,\n            format="auto"\n        )\n\n        return add_product_citations(claude_response, recommendations)\n\n    return claude_response\n'})}),"\n",(0,s.jsx)(n.h3,{id:"replicaio-integration",children:"Replica.io Integration"}),"\n",(0,s.jsx)(n.p,{children:"AI companion and character-based interaction integration:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-typescript",children:"import { AdMeshAutoRecommendationWidget } from 'admesh-ui-sdk';\n\nfunction ReplicaAIWithRecommendations() {\n  const [autoRecommendations, setAutoRecommendations] = useState([]);\n\n  const handleReplicaResponse = async (userMessage, aiResponse) => {\n    // Analyze AI response for product recommendation opportunities\n    const intent = await analyzeForProductIntent(aiResponse);\n\n    if (intent.shouldRecommend) {\n      const recommendations = await getAdMeshRecommendations(intent.query);\n      setAutoRecommendations(recommendations);\n    }\n  };\n\n  return (\n    <div>\n      {/* Replica.io chat interface implementation */}\n      <ReplicaChat onResponse={handleReplicaResponse} />\n\n      {/* Automated recommendation widget */}\n      <AdMeshAutoRecommendationWidget\n        recommendations={autoRecommendations}\n        autoShow={true}\n        position=\"bottom-right\"\n      />\n    </div>\n  );\n}\n"})}),"\n",(0,s.jsx)(n.h2,{id:"implementation-strategies",children:"Implementation Strategies"}),"\n",(0,s.jsx)(n.h3,{id:"1-intent-based-recommendations",children:"1. Intent-Based Recommendations"}),"\n",(0,s.jsx)(n.p,{children:"Detect user intent and provide contextual recommendations:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-python",children:"from admesh import Admesh\nimport re\n\nclass IntentBasedRecommendations:\n    def __init__(self):\n        self.client = Admesh()\n        self.intent_patterns = {\n            'compare_products': r'(compare|vs|versus|difference between)',\n            'best_for_use_case': r'(best|recommend|suggest|need)',\n            'trial_demo': r'(try|demo|test|trial)',\n            'budget_conscious': r'(cheap|affordable|budget|free)'\n        }\n    \n    def detect_intent(self, query):\n        for intent, pattern in self.intent_patterns.items():\n            if re.search(pattern, query.lower()):\n                return intent\n        return 'general'\n    \n    async def get_contextual_recommendations(self, query, conversation_history=None):\n        intent = self.detect_intent(query)\n        \n        # Enhance query with conversation context\n        if conversation_history:\n            enhanced_query = self.enhance_with_context(query, conversation_history)\n        else:\n            enhanced_query = query\n        \n        recommendations = await self.client.recommend.get_recommendations(\n            query=enhanced_query,\n            format=\"auto\",\n            intent_type=intent\n        )\n        \n        return recommendations\n"})}),"\n",(0,s.jsx)(n.h3,{id:"2-citation-based-display",children:"2. Citation-Based Display"}),"\n",(0,s.jsx)(n.p,{children:"Display recommendations as academic-style citations:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-tsx",children:"import { AdMeshCitationUnit } from 'admesh-ui-sdk';\n\nfunction AIResponseWithCitations({ aiResponse, recommendations }) {\n  return (\n    <div className=\"ai-response\">\n      <AdMeshCitationUnit\n        recommendations={recommendations}\n        conversationText={aiResponse}\n        citationStyle=\"numbered\"\n        showCitationList={true}\n        onRecommendationClick={(adId, link) => {\n          // Track citation clicks\n          trackCitationClick(adId);\n          window.open(link, '_blank');\n        }}\n      />\n    </div>\n  );\n}\n"})}),"\n",(0,s.jsx)(n.h3,{id:"3-auto-triggered-recommendations",children:"3. Auto-Triggered Recommendations"}),"\n",(0,s.jsx)(n.p,{children:"Automatically show recommendations based on conversation analysis:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-python",children:"class AutoRecommendationEngine:\n    def __init__(self):\n        self.admesh_client = Admesh()\n        self.trigger_keywords = [\n            'software', 'tool', 'platform', 'service', 'solution',\n            'app', 'system', 'product', 'recommend', 'suggest'\n        ]\n    \n    def should_trigger_recommendations(self, text):\n        \"\"\"Analyze text to determine if recommendations should be shown\"\"\"\n        text_lower = text.lower()\n        keyword_count = sum(1 for keyword in self.trigger_keywords if keyword in text_lower)\n        return keyword_count >= 2\n    \n    async def process_conversation(self, user_message, ai_response):\n        combined_text = f\"{user_message} {ai_response}\"\n        \n        if self.should_trigger_recommendations(combined_text):\n            # Extract key terms for recommendation query\n            query = self.extract_recommendation_query(combined_text)\n            \n            recommendations = await self.admesh_client.recommend.get_recommendations(\n                query=query,\n                format=\"auto\",\n                max_recommendations=3\n            )\n            \n            return {\n                'show_recommendations': True,\n                'recommendations': recommendations.response.recommendations,\n                'trigger_reason': f\"Detected product discussion: {query}\"\n            }\n        \n        return {'show_recommendations': False}\n"})}),"\n",(0,s.jsx)(n.h2,{id:"best-practices-for-ai-integration",children:"Best Practices for AI Integration"}),"\n",(0,s.jsx)(n.h3,{id:"-context-awareness",children:"\ud83c\udfaf Context Awareness"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Conversation History"})," - Use previous messages to enhance recommendations"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"User Preferences"})," - Remember user's stated preferences and constraints"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Session Context"})," - Consider the overall conversation topic and goals"]}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"-non-intrusive-display",children:"\ud83d\udd04 Non-Intrusive Display"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Timing"})," - Show recommendations at natural conversation breaks"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Relevance"})," - Only show highly relevant suggestions (match score > 0.8)"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Dismissible"})," - Allow users to easily dismiss recommendations"]}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"-performance-optimization",children:"\ud83d\udcca Performance Optimization"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Async Operations"})," - Use async/await to avoid blocking chat responses"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Caching"})," - Cache recommendations for similar queries"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Fallback Handling"})," - Gracefully handle API failures"]}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"-user-experience",children:"\ud83c\udfa8 User Experience"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Clear Attribution"})," - Show why recommendations were suggested"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Easy Interaction"})," - Make it simple to explore and click recommendations"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Feedback Loop"})," - Learn from user interactions to improve suggestions"]}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"common-integration-patterns",children:"Common Integration Patterns"}),"\n",(0,s.jsx)(n.h3,{id:"pattern-1-post-response-recommendations",children:"Pattern 1: Post-Response Recommendations"}),"\n",(0,s.jsx)(n.p,{children:"Show recommendations after AI provides an answer:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-python",children:"async def ai_chat_with_recommendations(user_query):\n    # Get AI response first\n    ai_response = await get_ai_response(user_query)\n    \n    # Then get recommendations\n    recommendations = await get_admesh_recommendations(user_query)\n    \n    return {\n        'ai_response': ai_response,\n        'recommendations': recommendations,\n        'display_mode': 'post_response'\n    }\n"})}),"\n",(0,s.jsx)(n.h3,{id:"pattern-2-inline-citations",children:"Pattern 2: Inline Citations"}),"\n",(0,s.jsx)(n.p,{children:"Embed recommendations directly in AI responses:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-python",children:"async def ai_response_with_citations(user_query):\n    # Get recommendations first\n    recommendations = await get_admesh_recommendations(user_query)\n    \n    # Generate AI response with citation placeholders\n    ai_response = await generate_response_with_citations(user_query, recommendations)\n    \n    return {\n        'response_with_citations': ai_response,\n        'recommendations': recommendations,\n        'display_mode': 'inline_citations'\n    }\n"})}),"\n",(0,s.jsx)(n.h3,{id:"pattern-3-proactive-suggestions",children:"Pattern 3: Proactive Suggestions"}),"\n",(0,s.jsx)(n.p,{children:"Automatically suggest products during conversation:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-python",children:"async def proactive_recommendation_system(conversation_history):\n    # Analyze conversation for product opportunities\n    opportunities = analyze_conversation_for_products(conversation_history)\n    \n    if opportunities:\n        recommendations = await get_admesh_recommendations(opportunities['query'])\n        \n        return {\n            'trigger': True,\n            'recommendations': recommendations,\n            'context': opportunities['context'],\n            'display_mode': 'floating_widget'\n        }\n    \n    return {'trigger': False}\n"})}),"\n",(0,s.jsx)(n.h2,{id:"next-steps",children:"Next Steps"}),"\n",(0,s.jsx)(n.p,{children:"Explore integration resources:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:(0,s.jsx)(n.a,{href:"/python-sdk/installation",children:"Python SDK"})})," - Backend SDK implementation"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:(0,s.jsx)(n.a,{href:"/typescript-sdk/installation",children:"TypeScript SDK"})})," - Node.js SDK implementation"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:(0,s.jsx)(n.a,{href:"/ui-sdk/installation",children:"UI SDK"})})," - Frontend component integration"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:(0,s.jsx)(n.a,{href:"/examples/ai-assistant",children:"Examples"})})," - Complete implementation examples"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:(0,s.jsx)(n.a,{href:"/api/authentication",children:"API Reference"})})," - Complete API documentation"]}),"\n"]}),"\n",(0,s.jsx)(n.hr,{}),"\n",(0,s.jsxs)(n.p,{children:["Ready to build intelligent recommendation systems? Start with our ",(0,s.jsx)(n.a,{href:"/getting-started/overview",children:"Getting Started Guide"}),"!"]})]})}function m(e={}){const{wrapper:n}={...(0,r.R)(),...e.components};return n?(0,s.jsx)(n,{...e,children:(0,s.jsx)(l,{...e})}):l(e)}}}]);