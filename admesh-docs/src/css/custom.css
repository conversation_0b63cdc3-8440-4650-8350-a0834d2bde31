/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* You can override the default Infima variables here. */
:root {
  --ifm-color-primary: #8b5cf6;
  --ifm-color-primary-dark: #7c3aed;
  --ifm-color-primary-darker: #7c3aed;
  --ifm-color-primary-darkest: #6d28d9;
  --ifm-color-primary-light: #a78bfa;
  --ifm-color-primary-lighter: #a78bfa;
  --ifm-color-primary-lightest: #c4b5fd;
  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
}

/* For readability concerns, you should choose a lighter palette in dark mode. */
[data-theme='dark'] {
  --ifm-color-primary: #a78bfa;
  --ifm-color-primary-dark: #9333ea;
  --ifm-color-primary-darker: #8b5cf6;
  --ifm-color-primary-darkest: #7c3aed;
  --ifm-color-primary-light: #c4b5fd;
  --ifm-color-primary-lighter: #ddd6fe;
  --ifm-color-primary-lightest: #ede9fe;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
}

/* Custom styles for AdMesh branding */
.navbar__brand {
  font-weight: 700;
}

.hero__title {
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Code block improvements */
.prism-code {
  border-radius: 8px;
}

/* Custom badge styles */
.badge--primary {
  background-color: var(--ifm-color-primary);
}

.badge--success {
  background-color: #10b981;
}

.badge--warning {
  background-color: #f59e0b;
}

/* API endpoint styling */
.api-endpoint {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.875rem;
  font-weight: 600;
}

.api-endpoint--get {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.api-endpoint--post {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* SDK comparison table */
.sdk-comparison {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.sdk-comparison th,
.sdk-comparison td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid var(--ifm-table-border-color);
}

.sdk-comparison th {
  background-color: var(--ifm-table-head-background);
  font-weight: 600;
}

/* Feature highlight boxes */
.feature-box {
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1rem 0;
  border-left: 4px solid var(--ifm-color-primary);
  background-color: var(--ifm-color-emphasis-100);
}

.feature-box--tip {
  border-left-color: #10b981;
  background-color: rgba(16, 185, 129, 0.05);
}

.feature-box--warning {
  border-left-color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.05);
}

.feature-box--danger {
  border-left-color: #ef4444;
  background-color: rgba(239, 68, 68, 0.05);
}

/* Interactive code examples */
.code-example {
  position: relative;
}

.code-example__tabs {
  display: flex;
  border-bottom: 1px solid var(--ifm-table-border-color);
  margin-bottom: 0;
}

.code-example__tab {
  padding: 8px 16px;
  cursor: pointer;
  border: none;
  background: none;
  color: var(--ifm-color-content-secondary);
  font-size: 0.875rem;
  font-weight: 500;
}

.code-example__tab--active {
  color: var(--ifm-color-primary);
  border-bottom: 2px solid var(--ifm-color-primary);
}

/* Copy button improvements */
.clean-btn {
  transition: all 0.2s ease;
}

.clean-btn:hover {
  transform: translateY(-1px);
}

/* Responsive improvements */
@media (max-width: 768px) {
  .hero__title {
    font-size: 2rem;
  }
  
  .api-endpoint {
    font-size: 0.75rem;
    padding: 2px 6px;
  }
  
  .sdk-comparison {
    font-size: 0.875rem;
  }
  
  .feature-box {
    padding: 1rem;
  }
}

/* Dark mode specific adjustments */
[data-theme='dark'] .feature-box {
  background-color: var(--ifm-color-emphasis-200);
}

[data-theme='dark'] .feature-box--tip {
  background-color: rgba(16, 185, 129, 0.1);
}

[data-theme='dark'] .feature-box--warning {
  background-color: rgba(245, 158, 11, 0.1);
}

[data-theme='dark'] .feature-box--danger {
  background-color: rgba(239, 68, 68, 0.1);
}

/* Loading animation for interactive examples */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--ifm-color-emphasis-300);
  border-radius: 50%;
  border-top-color: var(--ifm-color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
