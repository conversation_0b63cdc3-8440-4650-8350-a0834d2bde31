{"buildCommand": "npm run build", "outputDirectory": "build", "framework": "<PERSON>cusaurus", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}